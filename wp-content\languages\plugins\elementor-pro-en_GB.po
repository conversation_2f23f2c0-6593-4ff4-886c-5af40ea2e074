# Translation of Elementor Pro in English (UK)
# This file is distributed under the same license as the Elementor Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-07-03 07:27:15+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.3.1\n"
"Language: en_GB\n"
"Project-Id-Version: Elementor Pro\n"

#: modules/mega-menu/widgets/mega-menu.php:702
msgid "Note: Scroll menu items if they don’t fit into their parent container."
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:752
msgid "Note: Item layout will switch to dropdown on any screen smaller than the selected breakpoint."
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:786
msgid "Distance from content"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1369
msgid "Toggle Icon"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1633
msgid "Distance from dropdown"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1731
msgid "Dropdown Menu"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1747
msgid "Styles apply to items when the menu switches to dropdown layout"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1844
msgid "Dropdown Box"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1854
msgid "Style the dropdown box that contains menu items."
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1961
msgid "Menu | Open (Enter or Space) | Return (Escape) | Other Menu Items (Arrow, Home & End Keys)"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1990
#: modules/mega-menu/widgets/mega-menu.php:2044
msgid "Menu Toggle | Open (Enter or Space) | Return (Escape)"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:2165
#: modules/mega-menu/widgets/mega-menu.php:2386
msgid "Expand: "
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:27
msgid "WordPress Menu"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:363
msgid "Text  Align"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:44
#: modules/nested-carousel/widgets/nested-carousel.php:112
msgid "Slide #1"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:50
#: modules/nested-carousel/widgets/nested-carousel.php:115
msgid "Slide #2"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:56
#: modules/nested-carousel/widgets/nested-carousel.php:118
msgid "Slide #3"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:67
msgid "Slide #%d"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:95
#: modules/nested-carousel/widgets/nested-carousel.php:96
msgid "Slide Title"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:107
msgid "Carousel Items"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:152
msgid "The Autoplay is inactive while editing. Preview your page to see it in action."
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:155
msgid "Infinite scroll is inactive while editing. Preview your page to see it in action."
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:158
msgid "Offset is inactive while editing. Preview your page to see it in action."
msgstr ""

#: modules/notes/admin-page.php:84
msgid "The note you are looking for was not found."
msgstr ""

#: modules/page-transitions/module.php:469
msgid "Preloader Delay"
msgstr ""

#: modules/popup/display-settings/timing.php:75
msgid "Per"
msgstr ""

#: modules/popup/display-settings/timing.php:78
msgid "Persisting"
msgstr ""

#: modules/popup/display-settings/timing.php:79
msgid "Session"
msgstr ""

#: modules/popup/display-settings/timing.php:80
msgid "Day"
msgstr ""

#: modules/popup/display-settings/timing.php:81
msgid "Week"
msgstr ""

#: modules/popup/display-settings/timing.php:82
msgid "Month"
msgstr ""

#: modules/popup/display-settings/timing.php:244
msgid "Schedule date and time"
msgstr ""

#: modules/popup/display-settings/timing.php:250
msgid "Timezone"
msgstr ""

#: modules/popup/display-settings/timing.php:254
msgid "Visitor"
msgstr ""

#: modules/popup/document.php:757
msgid "Accessible navigation"
msgstr ""

#: modules/popup/document.php:760
msgid "Allow keyboard tab navigation for accessibility"
msgstr ""

#. translators: %s: Post title.
#: modules/posts/skins/skin-base.php:986
msgid "Read more about %s"
msgstr ""

#: modules/posts/widgets/posts-base.php:287
msgid "Individual Pagination"
msgstr ""

#: modules/posts/widgets/posts-base.php:306
msgid "For multiple Posts Widgets on the same page, toggle this on to control the pagination for each individually. Note: It affects the page's URL structure."
msgstr ""

#: modules/query-control/controls/group-control-query.php:351
msgid "Last Modified"
msgstr ""

#: modules/slides/widgets/slides.php:1385
#: modules/slides/widgets/slides.php:1458
msgid "Previous slide"
msgstr ""

#: modules/slides/widgets/slides.php:1389
#: modules/slides/widgets/slides.php:1462
msgid "Next slide"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:766
msgid "Open table of contents"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:777
msgid "Close table of contents"
msgstr ""

#: modules/theme-builder/widgets/site-logo.php:115
msgid "Change Site Logo"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:82
msgid "Picture Size"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:845
msgid "Author picture"
msgstr ""

#: modules/theme-elements/widgets/search-form.php:840
msgid "Close this search box."
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:646
msgid "Video Position"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:135
msgid "Shop"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:72
#: modules/woocommerce/widgets/menu-cart.php:326
msgid "Custom Icon"
msgstr ""

#: assets/js/app.js:3771
#: core/app/modules/site-editor/assets/js/pages/import.js:74
msgid "Warning: JSON or ZIP files may be unsafe"
msgstr ""

#: assets/js/app.js:3772
#: core/app/modules/site-editor/assets/js/pages/import.js:75
msgid "Uploading JSON or ZIP files from unknown sources can be harmful and put your site at risk. For maximum safety, upload only JSON or ZIP files from trusted sources."
msgstr ""

#: assets/js/app.js:3774
#: core/app/modules/site-editor/assets/js/pages/import.js:77
msgid "Continue"
msgstr ""

#: assets/js/app.js:3791
#: core/app/modules/site-editor/assets/js/pages/import.js:90
msgid "Do not show this message again"
msgstr ""

#: assets/js/custom-code.js:2624
msgid "Write me code"
msgstr ""

#: assets/js/form-submission-admin.js:5206
msgid "No data"
msgstr ""

#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:63
msgid "Select a widget"
msgstr ""

#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:84
#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:141
msgid "Select a taxonomy"
msgstr ""

#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:135
msgid "No taxonomies found"
msgstr ""

#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:178
msgid "We are experiencing technical difficulties on our end. Please try again to reconnect."
msgstr ""

#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:184
msgid "OK"
msgstr ""

#: assets/js/notes/notes-app.js:1811
msgid "Could not load the panel."
msgstr ""

#. translators: 1: Link open tag, 2: Link close tag.
#: modules/gallery/widgets/gallery.php:288
msgid "Manage your site’s lightbox settings in the %1$sLightbox panel%2$s."
msgstr ""

#: modules/gallery/widgets/gallery.php:1420
msgid "Gallery filter"
msgstr ""

#: modules/global-widget/views/panel-template.php:10
msgid "Your Global Widget is locked"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:459
msgid "Sequence Duration"
msgstr ""

#. translators: %s: Template ID.
#: modules/library/classes/shortcode.php:39
msgid "Elementor template shortcode for template %s"
msgstr ""

#: modules/loop-builder/views/cta-template.php:51
msgid "Loop Carousel starts with a template."
msgstr ""

#: modules/loop-builder/widgets/base.php:224
msgid "Load Type"
msgstr ""

#: modules/loop-builder/widgets/base.php:228
msgid "Page Reload"
msgstr ""

#: modules/loop-builder/widgets/base.php:229
msgid "AJAX"
msgstr ""

#: modules/loop-builder/widgets/base.php:246
msgid "Autoscroll"
msgstr ""

#: modules/loop-builder/widgets/base.php:261
msgid "Autoscroll offset"
msgstr ""

#: modules/loop-builder/widgets/base.php:328
msgid "For multiple Loop Widgets on the same page, toggle this on to control the pagination for each individually. Note: It affects the page's URL structure."
msgstr ""

#: modules/loop-builder/widgets/loop-carousel.php:22
msgid "Loop Carousel"
msgstr ""

#: modules/loop-builder/widgets/loop-carousel.php:81
#: modules/nested-carousel/widgets/nested-carousel.php:181
msgid "Gap between slides"
msgstr ""

#: modules/loop-builder/widgets/loop-carousel.php:241
#: modules/loop-builder/widgets/loop-grid.php:309
msgid "Edit Handle Selector"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:95
msgid "Equal height"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:115
msgid "Apply an alternate template"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:170
msgid "Position in grid"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:182
msgid "Note: Repeat the alternate template once every chosen number of items."
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:193
msgid "Apply Once"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:208
msgid "Column Span"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:238
msgid "Note: Item will span across a number of columns."
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:250
msgid "Note: The Masonry option combined with Column Span might cause unexpected results and break the layout."
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:261
msgid "Static item position"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:276
msgid "Note: Static Items remain in place when new items are added to grid. Other items appear according to query settings."
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:288
msgid "Templates"
msgstr ""

#: modules/loop-filter/module.php:43
#: modules/loop-filter/widgets/taxonomy-filter.php:34
msgid "Taxonomy Filter"
msgstr ""

#: modules/loop-filter/module.php:45
msgid "Taxonomy Filter is a powerful tool that enables users to easily filter and sort their posts and product categories. %1$sLearn More%2$s"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:56
msgid "Selected loop grid"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:108
#: modules/loop-filter/widgets/taxonomy-filter.php:183
msgid "Item Alignment"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:148
#: modules/loop-filter/widgets/taxonomy-filter.php:222
msgid "Title Alignment"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:270
msgid "Displayed Elements"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:277
msgid "Empty Items"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:288
msgid "Taxonomy Children"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:318
msgid "First Item"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:329
msgid "First Item Title"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:345
msgid "Number of taxonomies"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:373
msgid "Scroll items if they don’t fit into their parent container"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:596
msgid "Choose a Loop Grid to view the Taxonomy Filter."
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:597
msgid "Please select a taxonomy."
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:598
msgid "No taxonomy terms found."
msgstr ""

#: modules/mega-menu/module.php:40
msgid "Create beautiful menus and mega menus with new nested capabilities. Mega menus are ideal for websites with complex navigation structures and unique designs. %1$sLearn More%2$s"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:77
#: modules/mega-menu/widgets/mega-menu.php:249
msgid "Item #1"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:83
#: modules/mega-menu/widgets/mega-menu.php:252
msgid "Item #2"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:89
#: modules/mega-menu/widgets/mega-menu.php:255
msgid "Item #3"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:100
msgid "Item #%d"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:168
msgid "Item Title"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:192
msgid "Dropdown Content"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:194
msgid "OFF"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:195
msgid "ON"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:197
msgid "Click on the menu item to edit its dropdown content."
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:216
#: modules/mega-menu/widgets/mega-menu.php:420
msgid "Active Icon"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:230
msgid "CSS ID"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:289
msgid "Content Horizontal Position"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:304
msgid "Item Layout"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:317
#: modules/mega-menu/widgets/mega-menu.php:353
msgid "Item Position"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:386
msgid "Dropdown Indicator"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:447
msgid "Dropdown Effect"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:453
msgid "Open On"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:471
msgid "The hover effect is inactive while editing. Preview your page to see it in action."
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:488
msgid "Fade in"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:694
msgid "Additional Settings"
msgstr ""

#. translators: 1: Title opening tag, 2: Title closing tag
#: elementor-pro.php:159
msgid "%1$sDon’t miss out on the new version of Elementor%2$s Update to the latest version of Elementor to enjoy new features, better performance and compatibility."
msgstr ""

#: license/admin.php:289
msgid "Cancelled"
msgstr ""

#: license/admin.php:292
msgid "Request Locked"
msgstr ""

#: modules/announcements/module.php:31
msgid "Keep Your Website’s Shine On"
msgstr ""

#: modules/announcements/module.php:32
msgid ""
"<p>Your Elementor Pro subscription has expired. Renew it now to regain access to the Pro features that elevate your website.</p>\n"
"\t\t\t\t<ul>\n"
"\t\t\t\t\t<li>Manage and edit every part of your website, including pages, templates, headers, footers, and more.</li>\n"
"\t\t\t\t\t<li>Increase engagement and conversion with Elementor’s marketing features including Forms, and Popups.</li>\n"
"\t\t\t\t\t<li>Update your website’s content and design using Elementor Pro’s professional widgets and features for any need.</li>\n"
"\t\t\t\t\t<li>Keep your website secure and compatible by updating your Elementor Pro website to the latest version.</li>\n"
"\t\t\t\t</ul>"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:285
#: modules/flip-box/widgets/flip-box.php:449
#: modules/pricing/widgets/price-list.php:161
#: modules/slides/widgets/slides.php:559
msgid "Description HTML Tag"
msgstr ""

#. translators: 1: Link opening tag, 2: Link opening tag, 3: Link closing tag.
#: modules/custom-css/module.php:113
msgid "Use %1$scustom CSS%3$s to style your content or add %2$sthe \"selector\" prefix%3$s to target specific elements."
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-date-time.php:25
#: modules/dynamic-tags/pods/tags/pods-date-time.php:25
msgid "Date Time Field"
msgstr ""

#: modules/dynamic-tags/tags/post-excerpt.php:39
msgid "Apply to post content"
msgstr ""

#: modules/forms/classes/ajax-handler.php:39
msgid "Your submission was successful."
msgstr ""

#: modules/forms/classes/ajax-handler.php:40
msgid "Your submission failed because of an error."
msgstr ""

#: modules/forms/classes/ajax-handler.php:42
msgid "Your submission failed because the form is invalid."
msgstr ""

#: modules/forms/classes/ajax-handler.php:43
msgid "Your submission failed because of a server error."
msgstr ""

#: modules/forms/submissions/admin-menu-items/submissions-promotion-menu-item.php:43
msgid "Collect Your Form Submissions"
msgstr ""

#: modules/forms/submissions/admin-menu-items/submissions-promotion-menu-item.php:47
msgid "Save and manage all of your form submissions in one single place. All within a simple, intuitive place."
msgstr ""

#: modules/forms/submissions/data/responses/query-failed-response.php:13
msgid "Could not retrieve query data."
msgstr ""

#: modules/forms/widgets/form.php:930
msgid "Form Validation"
msgstr ""

#: modules/forms/widgets/form.php:933
msgid "Browser Default"
msgstr ""

#: modules/forms/widgets/form.php:974
msgid "Form Error"
msgstr ""

#: modules/forms/widgets/form.php:992
msgid "Server Error"
msgstr ""

#: modules/forms/widgets/form.php:1028
msgid "Required Field"
msgstr ""

#: elementor-pro.php:113
msgid "Activate the Elementor plugin to start using all of Elementor Pro plugin’s features."
msgstr ""

#: elementor-pro.php:122
msgid "Elementor Pro plugin requires installing the Elementor plugin"
msgstr ""

#: elementor-pro.php:123
msgid "Install and activate the Elementor plugin to access all the Pro features."
msgstr ""

#: elementor-pro.php:124
msgid "Install Now"
msgstr ""

#. translators: 1: Title opening tag, 2: Title closing tag
#: elementor-pro.php:140
msgid "%1$sElementor Pro requires newer version of the Elementor plugin%2$s Update the Elementor plugin to reactivate the Elementor Pro plugin."
msgstr ""

#: elementor-pro.php:144 elementor-pro.php:163
msgid "Update Now"
msgstr ""

#: base/base-carousel-trait.php:22
msgid "Slides on display"
msgstr ""

#: base/base-carousel-trait.php:39
msgid "Slides on scroll"
msgstr ""

#: base/base-carousel-trait.php:55
msgid "Equal Height"
msgstr ""

#: base/base-carousel-trait.php:97
msgid "Scroll Speed"
msgstr ""

#: base/base-carousel-trait.php:111
msgid "Pause on hover"
msgstr ""

#: base/base-carousel-trait.php:129
msgid "Pause on interaction"
msgstr ""

#: base/base-carousel-trait.php:144
msgid "Infinite scroll"
msgstr ""

#: base/base-carousel-trait.php:186
msgid "Offset Sides"
msgstr ""

#: base/base-carousel-trait.php:212
msgid "Offset Width"
msgstr ""

#: base/base-carousel-trait.php:260
msgid "Previous Arrow"
msgstr ""

#: base/base-carousel-trait.php:325 base/base-carousel-trait.php:372
#: base/base-carousel-trait.php:487 base/base-carousel-trait.php:534
#: base/base-carousel-trait.php:1208 base/base-carousel-trait.php:1271
msgid "Orientation"
msgstr ""

#: base/base-carousel-trait.php:423
msgid "Next Arrow"
msgstr ""

#: base/base-carousel-trait.php:600
msgid "Icons"
msgstr ""

#: base/base-carousel-trait.php:844 base/base-carousel-trait.php:947
msgid "Space From slides"
msgstr ""

#: base/base-carousel-trait.php:1183
msgid "Custom Position"
msgstr ""

#: core/editor/editor.php:45
msgid "Extend Elementor With Apps"
msgstr ""

#: core/editor/editor.php:46
msgid "Explore Apps"
msgstr ""

#: elementor-pro.php:112
msgid "You're not using Elementor Pro yet!"
msgstr ""

#: assets/js/5c03292ae33aceaec8d9.bundle.js:79
msgid "Save Changes"
msgstr ""

#: assets/js/5c03292ae33aceaec8d9.bundle.js:80
msgid "Would you like to save the changes you've made?"
msgstr ""

#: assets/js/5c03292ae33aceaec8d9.bundle.js:87
msgid "Discard"
msgstr ""

#: assets/js/5c03292ae33aceaec8d9.bundle.js:150
msgid "Start typing its name"
msgstr ""

#: assets/js/editor.js:3598
msgid "global"
msgstr ""

#: assets/js/notes/notes-app.js:312
msgid "Leave a Note"
msgstr ""

#: assets/js/notes/notes-app.js:434
msgid "Delete this reply?"
msgstr ""

#: assets/js/notes/notes-app.js:434
msgid "Delete this note?"
msgstr ""

#: assets/js/notes/notes-app.js:434
msgid "Deleted replies can't be recovered."
msgstr ""

#: assets/js/notes/notes-app.js:434
msgid "Deleted notes can't be recovered."
msgstr ""

#: assets/js/notes/notes-app.js:487
msgid "Mark as unread"
msgstr ""

#: assets/js/notes/notes-app.js:487
msgid "Mark as read"
msgstr ""

#: assets/js/notes/notes-app.js:576
msgid "Re-open"
msgstr ""

#: assets/js/notes/notes-app.js:576
msgid "Resolve"
msgstr ""

#: assets/js/notes/notes-app.js:622
msgid "Seen by"
msgstr ""

#: assets/js/notes/notes-app.js:730
msgid "Supported in \"https\" sites only"
msgstr ""

#: assets/js/notes/notes-app.js:731
msgid "Copy Link"
msgstr ""

#: assets/js/notes/notes-app.js:790
msgid "Type your reply. Use @ to mention..."
msgstr ""

#: assets/js/notes/notes-app.js:790
msgid "Type a note. Use @ to mention..."
msgstr ""

#: assets/js/notes/notes-app.js:876
msgid "Noted on:"
msgstr ""

#: assets/js/notes/notes-app.js:1065
msgid "Reply"
msgstr ""

#: assets/js/notes/notes-app.js:1399
msgid "Give access to Notes"
msgstr ""

#: assets/js/notes/notes-app.js:1399
msgid "Can't mention them"
msgstr ""

#: assets/js/notes/notes-app.js:1430
msgid "Contact the site admin to give this person the right permissions."
msgstr ""

#: assets/js/notes/notes-app.js:1434
msgid "This person needs: (1) permission to view this post, as well as (2) access to use Notes."
msgstr ""

#: assets/js/notes/notes-app.js:1436
msgid "They need permission to view this post."
msgstr ""

#: assets/js/notes/notes-app.js:1525
msgid "Can't find someone?"
msgstr ""

#: assets/js/notes/notes-app.js:1525
msgid "Add them from the"
msgstr ""

#: assets/js/notes/notes-app.js:1527
msgid "WP Dashboard"
msgstr ""

#: assets/js/notes/notes-app.js:1527
msgid "Ask the site admin to add them"
msgstr ""

#: assets/js/notes/notes-app.js:1640
msgid "Some notes are not shown."
msgstr ""

#: assets/js/notes/notes-app.js:1640
msgid "This page contains notes on elements that are still in draft mode."
msgstr ""

#: assets/js/notes/notes-app.js:1700
msgid "Open page in a new tab"
msgstr ""

#: assets/js/notes/notes-app.js:1736
msgid "Close notes mode"
msgstr ""

#: assets/js/notes/notes-app.js:1772
msgid "Share your thoughts with a Note"
msgstr ""

#: assets/js/notes/notes-app.js:1772
msgid "Select an element on the page to leave a comment, ask a question, etc."
msgstr ""

#: assets/js/notes/notes-app.js:1811
msgid "Please refresh the page and try again."
msgstr ""

#: assets/js/notes/notes-app.js:1873
msgid "Refresh"
msgstr ""

#: assets/js/notes/notes-app.js:1942
msgid "%s replies"
msgstr ""

#: assets/js/notes/notes-app.js:2053
msgid "Current page"
msgstr ""

#: assets/js/notes/notes-app.js:2055
msgid "All site"
msgstr ""

#: assets/js/notes/notes-app.js:2065
msgid "All notes"
msgstr ""

#: assets/js/notes/notes-app.js:2067
msgid "Only yours"
msgstr ""

#: assets/js/notes/notes-app.js:2077
msgid "Show resolved"
msgstr ""

#: assets/js/notes/notes-app.js:2087
msgid "Show unread only"
msgstr ""

#: assets/js/notes/notes-app.js:2158
msgid "Notes Panel"
msgstr ""

#: assets/js/notes/notes-app.js:6678 assets/js/notes/notes.js:481
msgid "(deleted user)"
msgstr ""

#. translators: 1: Saved templates link opening tag, 2: Link closing tag.
#: modules/woocommerce/widgets/cart.php:498
msgid "Replaces the default WooCommerce Empty Cart screen with a custom template. (Don’t have one? Head over to %1$sSaved Templates%2$s)"
msgstr ""

#: modules/woocommerce/widgets/cart.php:514
#: modules/woocommerce/widgets/my-account.php:273
msgid "Choose template"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2957
msgid "Login Button"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3246
msgid "Apply Button"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:243
msgid "Customize Dashboard"
msgstr ""

#. translators: 1: Saved templates link opening tag. 2: Link closing tag.
#: modules/woocommerce/widgets/my-account.php:257
msgid "Replaces the default WooCommerce customer dashboard screen with a custom template. (Don't have one? Head over to %1$sSaved Templates%2$s.)"
msgstr ""

#: modules/woocommerce/widgets/products.php:184
msgid "You may also like..."
msgstr ""

#: modules/woocommerce/widgets/products.php:185
msgid "You may be interested in..."
msgstr ""

#: modules/woocommerce/traits/products-trait.php:63
msgid "Cross-Sells"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:170
msgid "Note: The Related Products Query is available when creating a Single Product template"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:182
msgid "Note: The Upsells Query is available when creating a Single Product template"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:194
msgid "Note: The Cross-Sells Query is available when creating a Cart page"
msgstr ""

#: modules/woocommerce/widgets/cart.php:273
#: modules/woocommerce/widgets/checkout.php:3000
#: modules/woocommerce/widgets/checkout.php:3295
msgid "Note: This control will only affect screen sizes Tablet and below"
msgstr ""

#: modules/woocommerce/widgets/cart.php:482
msgid "Customize empty cart"
msgstr ""

#. translators: 1: Elementor's integrations settings link opening tab, 2: Link
#. closing tag.
#: modules/payments/widgets/stripe-button.php:520
msgid "Complete the entire checkout experience on your site with a mock payment method, using the Stripe Test key in the %1$sIntegrations Settings%2$s."
msgstr ""

#: modules/popup/admin-menu-items/popups-promotion-menu-item.php:53
msgid "Get Popup Builder"
msgstr ""

#: modules/popup/admin-menu-items/popups-promotion-menu-item.php:57
msgid "Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Go pro and start designing your popups today."
msgstr ""

#: modules/posts/skins/skin-base.php:278
msgid "Apply to custom Excerpt"
msgstr ""

#: modules/posts/skins/skin-base.php:321
#: modules/woocommerce/widgets/products-base.php:524
msgid "Automatically align buttons"
msgstr ""

#: modules/query-control/controls/template-query.php:60
msgid "Create template"
msgstr ""

#: modules/query-control/controls/template-query.php:68
msgid "Edit template"
msgstr ""

#: modules/role-manager/module.php:92
msgid "Want to give access only to content?"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:318
msgid "Desktop (or smaller)"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/theme-builder/widgets/site-title.php:80
msgid "To edit the title of your site, go to %1$sSite Identity%2$s."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/theme-elements/widgets/breadcrumbs.php:88
msgid "Additional settings are available in the Yoast SEO %1$sBreadcrumbs Panel%2$s"
msgstr ""

#. translators: %s: <br />.
#: modules/payments/module.php:442
msgid "Please note: The Stripe name and logos are trademarks or service marks of Stripe, Inc. or its affiliates in the U.S. and other countries. %s Other names may be trademarks of their respective owners."
msgstr ""

#: modules/payments/widgets/stripe-button.php:34
msgid "Stripe Button"
msgstr ""

#: modules/payments/widgets/stripe-button.php:58
msgctxt "Currency"
msgid "AED"
msgstr ""

#: modules/payments/widgets/stripe-button.php:59
msgctxt "Currency"
msgid "AFN"
msgstr ""

#: modules/payments/widgets/stripe-button.php:60
msgctxt "Currency"
msgid "ALL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:61
msgctxt "Currency"
msgid "AMD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:62
msgctxt "Currency"
msgid "ANG"
msgstr ""

#: modules/payments/widgets/stripe-button.php:63
msgctxt "Currency"
msgid "AOA"
msgstr ""

#: modules/payments/widgets/stripe-button.php:64
msgctxt "Currency"
msgid "ARS"
msgstr ""

#: modules/payments/widgets/stripe-button.php:66
msgctxt "Currency"
msgid "AWG"
msgstr ""

#: modules/payments/widgets/stripe-button.php:67
msgctxt "Currency"
msgid "AZN"
msgstr ""

#: modules/payments/widgets/stripe-button.php:68
msgctxt "Currency"
msgid "BAM"
msgstr ""

#: modules/payments/widgets/stripe-button.php:69
msgctxt "Currency"
msgid "BBD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:70
msgctxt "Currency"
msgid "BDT"
msgstr ""

#: modules/payments/widgets/stripe-button.php:71
msgctxt "Currency"
msgid "BGN"
msgstr ""

#: modules/payments/widgets/stripe-button.php:72
msgctxt "Currency"
msgid "BIF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:73
msgctxt "Currency"
msgid "BMD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:74
msgctxt "Currency"
msgid "BND"
msgstr ""

#: modules/payments/widgets/stripe-button.php:75
msgctxt "Currency"
msgid "BOB"
msgstr ""

#: modules/payments/widgets/stripe-button.php:76
msgctxt "Currency"
msgid "BRL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:77
msgctxt "Currency"
msgid "BSD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:78
msgctxt "Currency"
msgid "BWP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:79
msgctxt "Currency"
msgid "BYN"
msgstr ""

#: modules/payments/widgets/stripe-button.php:80
msgctxt "Currency"
msgid "BZD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:82
msgctxt "Currency"
msgid "CDF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:84
msgctxt "Currency"
msgid "CLP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:85
msgctxt "Currency"
msgid "CNY"
msgstr ""

#: modules/payments/widgets/stripe-button.php:86
msgctxt "Currency"
msgid "COP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:87
msgctxt "Currency"
msgid "CRC"
msgstr ""

#: modules/payments/widgets/stripe-button.php:88
msgctxt "Currency"
msgid "CVE"
msgstr ""

#: modules/payments/widgets/stripe-button.php:90
msgctxt "Currency"
msgid "DJF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:92
msgctxt "Currency"
msgid "DOP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:93
msgctxt "Currency"
msgid "DZD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:94
msgctxt "Currency"
msgid "EGP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:95
msgctxt "Currency"
msgid "ETB"
msgstr ""

#: modules/payments/widgets/stripe-button.php:97
msgctxt "Currency"
msgid "FJD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:98
msgctxt "Currency"
msgid "FKP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:100
msgctxt "Currency"
msgid "GEL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:101
msgctxt "Currency"
msgid "GIP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:102
msgctxt "Currency"
msgid "GMD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:103
msgctxt "Currency"
msgid "GNF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:104
msgctxt "Currency"
msgid "GTQ"
msgstr ""

#: modules/payments/widgets/stripe-button.php:105
msgctxt "Currency"
msgid "GYD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:107
msgctxt "Currency"
msgid "HNL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:108
msgctxt "Currency"
msgid "HRK"
msgstr ""

#: modules/payments/widgets/stripe-button.php:109
msgctxt "Currency"
msgid "HTG"
msgstr ""

#: modules/payments/widgets/stripe-button.php:110
msgctxt "Currency"
msgid "IDR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:112
msgctxt "Currency"
msgid "INR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:113
msgctxt "Currency"
msgid "ISK"
msgstr ""

#: modules/payments/widgets/stripe-button.php:114
msgctxt "Currency"
msgid "JMD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:116
msgctxt "Currency"
msgid "KES"
msgstr ""

#: modules/payments/widgets/stripe-button.php:117
msgctxt "Currency"
msgid "KGS"
msgstr ""

#: modules/payments/widgets/stripe-button.php:118
msgctxt "Currency"
msgid "KHR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:119
msgctxt "Currency"
msgid "KMF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:120
msgctxt "Currency"
msgid "KRW"
msgstr ""

#: modules/payments/widgets/stripe-button.php:121
msgctxt "Currency"
msgid "KYD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:122
msgctxt "Currency"
msgid "KZT"
msgstr ""

#: modules/payments/widgets/stripe-button.php:123
msgctxt "Currency"
msgid "LAK"
msgstr ""

#: modules/payments/widgets/stripe-button.php:124
msgctxt "Currency"
msgid "LBP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:125
msgctxt "Currency"
msgid "LKR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:126
msgctxt "Currency"
msgid "LRD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:127
msgctxt "Currency"
msgid "LSL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:128
msgctxt "Currency"
msgid "MAD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:129
msgctxt "Currency"
msgid "MDL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:130
msgctxt "Currency"
msgid "MGA"
msgstr ""

#: modules/payments/widgets/stripe-button.php:131
msgctxt "Currency"
msgid "MKD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:132
msgctxt "Currency"
msgid "MMK"
msgstr ""

#: modules/payments/widgets/stripe-button.php:133
msgctxt "Currency"
msgid "MNT"
msgstr ""

#: modules/payments/widgets/stripe-button.php:134
msgctxt "Currency"
msgid "MOP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:135
msgctxt "Currency"
msgid "MRO"
msgstr ""

#: modules/payments/widgets/stripe-button.php:136
msgctxt "Currency"
msgid "MUR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:137
msgctxt "Currency"
msgid "MVR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:138
msgctxt "Currency"
msgid "MWK"
msgstr ""

#: modules/payments/widgets/stripe-button.php:140
msgctxt "Currency"
msgid "MYR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:141
msgctxt "Currency"
msgid "MZN"
msgstr ""

#: modules/payments/widgets/stripe-button.php:142
msgctxt "Currency"
msgid "NAD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:143
msgctxt "Currency"
msgid "NGN"
msgstr ""

#: modules/payments/widgets/stripe-button.php:144
msgctxt "Currency"
msgid "NIO"
msgstr ""

#: modules/payments/widgets/stripe-button.php:146
msgctxt "Currency"
msgid "NPR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:148
msgctxt "Currency"
msgid "PAB"
msgstr ""

#: modules/payments/widgets/stripe-button.php:149
msgctxt "Currency"
msgid "PEN"
msgstr ""

#: modules/payments/widgets/stripe-button.php:150
msgctxt "Currency"
msgid "PGK"
msgstr ""

#: modules/payments/widgets/stripe-button.php:152
msgctxt "Currency"
msgid "PKR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:154
msgctxt "Currency"
msgid "PYG"
msgstr ""

#: modules/payments/widgets/stripe-button.php:155
msgctxt "Currency"
msgid "QAR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:156
msgctxt "Currency"
msgid "RON"
msgstr ""

#: modules/payments/widgets/stripe-button.php:157
msgctxt "Currency"
msgid "RSD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:159
msgctxt "Currency"
msgid "RWF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:160
msgctxt "Currency"
msgid "SAR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:161
msgctxt "Currency"
msgid "SBD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:162
msgctxt "Currency"
msgid "SCR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:165
msgctxt "Currency"
msgid "SHP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:166
msgctxt "Currency"
msgid "SLL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:167
msgctxt "Currency"
msgid "SOS"
msgstr ""

#: modules/payments/widgets/stripe-button.php:168
msgctxt "Currency"
msgid "SRD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:169
msgctxt "Currency"
msgid "STD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:170
msgctxt "Currency"
msgid "SZL"
msgstr ""

#: modules/payments/widgets/stripe-button.php:172
msgctxt "Currency"
msgid "TJS"
msgstr ""

#: modules/payments/widgets/stripe-button.php:173
msgctxt "Currency"
msgid "TOP"
msgstr ""

#: modules/payments/widgets/stripe-button.php:175
msgctxt "Currency"
msgid "TTD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:177
msgctxt "Currency"
msgid "TZS"
msgstr ""

#: modules/payments/widgets/stripe-button.php:178
msgctxt "Currency"
msgid "UAH"
msgstr ""

#: modules/payments/widgets/stripe-button.php:179
msgctxt "Currency"
msgid "UYU"
msgstr ""

#: modules/payments/widgets/stripe-button.php:180
msgctxt "Currency"
msgid "UZS"
msgstr ""

#: modules/payments/widgets/stripe-button.php:181
msgctxt "Currency"
msgid "VND"
msgstr ""

#: modules/payments/widgets/stripe-button.php:182
msgctxt "Currency"
msgid "VUV"
msgstr ""

#: modules/payments/widgets/stripe-button.php:183
msgctxt "Currency"
msgid "WST"
msgstr ""

#: modules/payments/widgets/stripe-button.php:184
msgctxt "Currency"
msgid "XAF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:185
msgctxt "Currency"
msgid "XCD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:186
msgctxt "Currency"
msgid "XOF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:187
msgctxt "Currency"
msgid "XPF"
msgstr ""

#: modules/payments/widgets/stripe-button.php:188
msgctxt "Currency"
msgid "YER"
msgstr ""

#: modules/payments/widgets/stripe-button.php:189
msgctxt "Currency"
msgid "ZAR"
msgstr ""

#: modules/payments/widgets/stripe-button.php:190
msgctxt "Currency"
msgid "ZMW"
msgstr ""

#: modules/payments/widgets/stripe-button.php:203
#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:177
msgid "Something went wrong"
msgstr ""

#: modules/payments/widgets/stripe-button.php:214
msgid "Gateway not connected. Contact seller"
msgstr ""

#. translators: 1: Elementor's integrations settings link opening tab, 2: Link
#. closing tag.
#: modules/payments/widgets/stripe-button.php:294
msgid "For this widget to work, you need to set your Stripe API keys in the %1$sIntegrations Settings%2$s."
msgstr ""

#: modules/payments/widgets/stripe-button.php:320
msgid "Product Name"
msgstr ""

#. translators: 1: Stripe api key explanation link opening tag, 2: Link closing
#. tag.
#: modules/payments/widgets/stripe-button.php:336
msgid "Notice! Please make sure to meet Stripe's guidelines regarding minimum charge amounts. %1$s Learn more. %2$s"
msgstr ""

#: modules/payments/widgets/stripe-button.php:343
msgid "USD"
msgstr ""

#: modules/payments/widgets/stripe-button.php:400
#: modules/payments/widgets/stripe-button.php:413
msgid "Tax Rate"
msgstr ""

#: modules/payments/widgets/stripe-button.php:406
#: modules/payments/widgets/stripe-button.php:419
msgid "To manage these options, go to your Stripe account > Products >  Tax Rates."
msgstr ""

#: modules/payments/widgets/stripe-button.php:497
msgid "These messages override Stripe's error messages."
msgstr ""

#: modules/payments/widgets/stripe-button.php:497
msgid "Use them on your live site - not while testing."
msgstr ""

#: modules/payments/widgets/stripe-button.php:509
msgid "Stripe test environment"
msgstr ""

#: modules/notes/module.php:39
msgid "Creates a dedicated workspace for your team and other stakeholders to leave comments and replies on your website while it's in progress. Notifications for mentions, replies, etc. are sent by email, and all notes are stored in your site's database."
msgstr ""

#: modules/notes/notifications/base-notes-notification.php:82
msgid "via Elementor"
msgstr ""

#. translators: 1: Note ID, 2: Site name, 3: Page name.
#: modules/notes/notifications/user-mentioned-notification.php:15
msgid "New mention in Note #%1$s on %2$s - %3$s"
msgstr ""

#. translators: 1: User display name, 2: Page name, 3: Site name.
#: modules/notes/notifications/user-mentioned-notification.php:28
msgid "%1$s mentioned you on %2$s at %3$s"
msgstr ""

#. translators: 1: Note ID, 2: Site name, 3: Page name.
#: modules/notes/notifications/user-replied-notification.php:15
msgid "New reply in Note #%1$s on %2$s - %3$s"
msgstr ""

#. translators: 1: User display name, 2: Page name, 3: Site name.
#: modules/notes/notifications/user-replied-notification.php:28
msgid "%1$s replied to a note on %2$s at %3$s"
msgstr ""

#. translators: 1: Note ID, 2: Site name, 3: Page name.
#: modules/notes/notifications/user-resolved-notification.php:15
msgid "Note #%1$s resolved on %2$s - %3$s"
msgstr ""

#. translators: 1: User display name, 2: Page name, 3: Site name.
#: modules/notes/notifications/user-resolved-notification.php:28
msgid "%1$s resolved a note on %2$s at %3$s"
msgstr ""

#: modules/notes/notifications/views/email.php:61
msgid "Take me there"
msgstr ""

#: modules/notes/notifications/views/email.php:75
msgid "Don't want to receive these notifications?"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/notes/notifications/views/email.php:83
msgid "Contact the site admin or turn off notifications for Notes in your %1$suser profile%2$s."
msgstr ""

#: modules/notes/notifications/views/email.php:103
msgid "Elementor Logo"
msgstr ""

#: modules/notes/user/capabilities.php:133
#: modules/notes/user/personal-data.php:41
msgid "Elementor Notes"
msgstr ""

#: modules/notes/user/capabilities.php:138
msgid "Permissions"
msgstr ""

#: modules/notes/user/capabilities.php:150
msgid "Allow user to access the Notes feature."
msgstr ""

#: modules/notes/user/capabilities.php:153
msgid "* Please note that this user will be able to see the list of all site users as part of the tagging ability in Notes."
msgstr ""

#: modules/notes/user/delete-user.php:56 modules/notes/user/preferences.php:56
msgid "Elementor - Notes"
msgstr ""

#: modules/notes/user/delete-user.php:60
msgid "Please note that this user has notes/replies in Elementor Notes feature."
msgstr ""

#: modules/notes/user/delete-user.php:68
msgid "Once deleted, those notes will remain and include an indication that this user was deleted. If you wish, you can manually delete this user's notes."
msgstr ""

#: modules/notes/user/personal-data.php:97
msgid "Parent ID"
msgstr ""

#: modules/notes/user/preferences.php:61
msgid "Notifications"
msgstr ""

#: modules/notes/user/preferences.php:67
msgid "Enable Elementor Notes notifications"
msgstr ""

#: modules/notes/utils.php:98
msgid "Home page"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/page-transitions/module.php:116
msgid "This feature is currently an experiment, you can turn it on in Elementor > Settings > %1$sExperiments%2$s."
msgstr ""

#: modules/page-transitions/module.php:245
msgid "Slide Out Right"
msgstr ""

#: modules/payments/classes/payment-button.php:320
msgid "Choose a page or add a URL"
msgstr ""

#: modules/payments/module.php:92 modules/payments/module.php:289
msgid "Something went wrong, please refresh the page."
msgstr ""

#: modules/payments/module.php:325
msgid "You have not entered a valid secret key for this environment, Please add a valid secret key"
msgstr ""

#: modules/payments/module.php:354
msgid "shipping fee"
msgstr ""

#: modules/payments/module.php:396
msgid "Stripe"
msgstr ""

#: modules/payments/module.php:397
msgid "Insert the API keys provided in the stripe admin dashboard to start collecting payments on your website using Stripe."
msgstr ""

#: modules/payments/module.php:398
msgid "These keys will serve as your default API key for all stripe implementations on your site."
msgstr ""

#: modules/payments/module.php:402
msgid "Test Secret key"
msgstr ""

#. translators: 1: Link to stripe api key explanation, 2: Link closing tag.
#: modules/payments/module.php:407
msgid "Enter your test secret key %1$slink%2$s."
msgstr ""

#: modules/payments/module.php:416
msgid "Validate Test API Key"
msgstr ""

#: modules/payments/module.php:420
msgid "Live Secret key"
msgstr ""

#. translators: 1: Link to stripe api key explanation, 2: Link closing tag.
#: modules/payments/module.php:425
msgid "Enter your Live secret key %1$slink%2$s."
msgstr ""

#: modules/payments/module.php:434
msgid "Validate Live API Key"
msgstr ""

#. translators: %s: The value of min field.
#: modules/forms/fields/number.php:88
msgid "The field value must be greater than or equal to %s."
msgstr ""

#: modules/forms/fields/tel.php:35
msgid "The field accepts only numbers and phone characters (#, -, *, etc)."
msgstr ""

#: modules/forms/fields/time.php:88
msgid "The field should be in HH:MM format."
msgstr ""

#: modules/loop-builder/documents/loop.php:40
msgid "Loop Item"
msgstr ""

#: modules/loop-builder/documents/loop.php:44
msgid "Loop Items"
msgstr ""

#: modules/loop-builder/documents/loop.php:53
msgid "What is a loop?"
msgstr ""

#: modules/loop-builder/documents/loop.php:54
msgid "A Loop is a layout you can customize to display recurring dynamic content - like listings, posts, portfolios, products, , etc."
msgstr ""

#: modules/loop-builder/documents/loop.php:55
msgid "Start by creating a master item. All the other instances in the grid will match this design. Then go back to the widget in the editor panel and assign both a template and a source of content. Your grid should populate automatically."
msgstr ""

#: modules/loop-builder/documents/loop.php:185
msgid "Recommended"
msgstr ""

#: modules/loop-builder/documents/loop.php:335
msgid "Source Type"
msgstr ""

#: modules/loop-builder/documents/loop.php:363
msgid "This affects the types of widgets and templates you can use for your master item."
msgstr ""

#: modules/loop-builder/documents/loop.php:414
msgid "Preview a specific post or item"
msgstr ""

#: modules/loop-builder/module.php:146
msgid "Add New Loop Template"
msgstr ""

#: modules/loop-builder/module.php:161
msgid "Choose source type"
msgstr ""

#: modules/loop-builder/skins/skin-loop-base.php:34
msgid "Loop Base"
msgstr ""

#: modules/loop-builder/views/cta-template.php:18
msgid "Loop Grid starts with a template."
msgstr ""

#: modules/loop-builder/views/cta-template.php:22
#: modules/loop-builder/views/cta-template.php:55
msgid "Either choose an existing template or create a new one and use it as the main item for your loop."
msgstr ""

#: modules/loop-builder/views/cta-template.php:26
#: modules/loop-builder/views/cta-template.php:59
msgid "Create a template"
msgstr ""

#: modules/loop-builder/widgets/base.php:95
msgid "Choose template type"
msgstr ""

#: modules/loop-builder/widgets/base.php:119
#: modules/loop-builder/widgets/loop-grid.php:134
msgid "Choose a template"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:66
msgid "Items Per Page"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:332
msgid "Gap between columns"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:350
msgid "Gap between rows"
msgstr ""

#: modules/loop-builder/widgets/loop-grid.php:23
#: assets/js/loop-filter-editor.5f9cd7711bffedc1976a.bundle.js:71
msgid "Loop Grid"
msgstr ""

#: modules/notes/admin-bar.php:26 modules/notes/module.php:38
#: assets/js/notes/notes.js:1430 assets/js/notes/notes.js:1533
#: assets/js/packages/editor-notes/editor-notes.js:107
#: assets/js/packages/editor-notes/editor-notes.strings.js:1
msgid "Notes"
msgstr ""

#: modules/notes/admin-page.php:45 modules/notes/admin-page.php:46
msgid "Notes Proxy"
msgstr ""

#: modules/notes/admin-page.php:86
msgid "Go to WP Dashboard"
msgstr ""

#: modules/notes/admin-page.php:87
msgid "View Site"
msgstr ""

#: modules/notes/admin-page.php:101
msgid "You are not autorized to view this Note. Please contact your admin."
msgstr ""

#: license/admin.php:36
msgid "Want to keep creating secure and high-performing websites? Renew your subscription to regain access to all of the Elementor Pro widgets, templates, updates & more"
msgstr ""

#: license/admin.php:231
msgid "Check license status"
msgstr ""

#. translators: 1: Bold text opening tag, 2: Bold text closing tag, 3: Link
#. opening tag, 4: Link closing tag.
#: license/admin.php:326 license/api.php:365
msgid "%1$sYour Elementor Pro license has expired.%2$s Want to keep creating secure and high-performing websites? Renew your subscription to regain access to all of the Elementor Pro widgets, templates, updates & more. %3$sRenew now%4$s"
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-fonts-promotion-menu-item.php:46
msgid "Add Your Custom Fonts"
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-fonts-promotion-menu-item.php:50
msgid "Custom Fonts allows you to add your self-hosted fonts and use them on your Elementor projects to create a unique brand language."
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-icons-promotion-menu-item.php:46
msgid "Add Your Custom Icons"
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-icons-promotion-menu-item.php:50
msgid "Don't rely solely on the FontAwesome icons everyone else is using! Differentiate your website and your style with custom icons you can upload from your favorite icons source."
msgstr ""

#: modules/custom-code/admin-menu-items/custom-code-promotion-menu-item.php:46
msgid "Add Your Custom Code"
msgstr ""

#: modules/custom-code/admin-menu-items/custom-code-promotion-menu-item.php:50
msgid "Custom Code is a tool gives you one place where you can insert scripts, rather than dealing with dozens of different plugins and deal with code."
msgstr ""

#: core/behaviors/feature-lock.php:41
msgid "Pro"
msgstr ""

#: core/behaviors/feature-lock.php:44
msgid "You need an active Elementor Pro license"
msgstr ""

#: core/behaviors/feature-lock.php:45
msgid "Your Elementor Pro license is inactive. To access premium Elementor widgets, templates, support & plugin updates activate your Pro license."
msgstr ""

#: core/behaviors/feature-lock.php:49 core/editor/notice-bar.php:56
#: core/editor/notice-bar.php:104 core/editor/promotion.php:35
#: license/admin.php:448
#: modules/assets-manager/asset-types/admin-menu-items/custom-fonts-promotion-menu-item.php:33
#: modules/assets-manager/asset-types/admin-menu-items/custom-icons-promotion-menu-item.php:33
#: modules/custom-code/admin-menu-items/custom-code-promotion-menu-item.php:33
#: modules/forms/submissions/admin-menu-items/submissions-promotion-menu-item.php:38
#: modules/popup/admin-menu-items/popups-promotion-menu-item.php:22
#: modules/role-manager/module.php:80
msgid "Renew now"
msgstr ""

#: core/editor/notice-bar.php:52
msgid "Renew to unlock all Elementor Pro features"
msgstr ""

#: core/editor/notice-bar.php:58
msgid "Already renewed?"
msgstr ""

#: core/editor/notice-bar.php:62
msgid "Reload Editor"
msgstr ""

#. translators: %s: Widget title.
#: core/editor/promotion.php:22
msgid "%s Widget"
msgstr ""

#. translators: %s: Widget title.
#: core/editor/promotion.php:25
msgid "Renew your Elementor Pro subscription to get %s and dozens more Pro widgets to expand your web-creation toolbox."
msgstr ""

#. translators: %s: Widget title.
#: core/editor/promotion.php:30
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr ""

#: license/admin.php:35
msgid "Your Elementor Pro license has expired."
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:1592
msgid "You need at least one WooCommerce order to preview the order here."
msgstr "You need at least one WooCommerce order to preview the order here."

#: assets/js/editor.js:6580
msgid "Want to save this as your purchase summary page?"
msgstr ""

#: assets/js/editor.js:6581
msgid "Changes you make here will override your WooCommerce default purchase summary page."
msgstr ""

#: assets/js/editor.js:6582
msgid "You've updated your summary page."
msgstr ""

#: assets/js/editor.js:6583
msgid "<h3>Set up a purchase summary page</h3><br>This page shows payment and order details. To set one up, go to Site Settings."
msgstr ""

#: assets/js/editor.js:6584
msgid "<h3>Sorry, something went wrong.</h3><br>To define a purchase summary page for your site, head over to Site Settings."
msgstr ""

#: assets/js/form-submission-admin.js:4572
msgid "%d submission moved to Trash."
msgid_plural "%d submissions moved to Trash."
msgstr[0] ""
msgstr[1] ""

#: assets/js/form-submission-admin.js:4579
msgid "%d submission permanently deleted."
msgid_plural "%d submissions permanently deleted."
msgstr[0] ""
msgstr[1] ""

#: assets/js/form-submission-admin.js:4586
msgid "Submission has been successfully updated."
msgid_plural "%d submissions have been successfully updated."
msgstr[0] ""
msgstr[1] ""

#: assets/js/form-submission-admin.js:4593
msgid "%d submission restored from Trash."
msgid_plural "%d submissions restored from Trash."
msgstr[0] ""
msgstr[1] ""

#: modules/woocommerce/widgets/purchase-summary.php:883
msgid "Account Title"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://go.elementor.com/wp-dash-wp-plugins-author-uri/"
msgstr ""

#: license/admin.php:528
msgid "(unavailable)"
msgstr ""

#: modules/page-transitions/module.php:152
msgid "Preview Page Transition"
msgstr ""

#: modules/page-transitions/module.php:177
#: modules/page-transitions/module.php:914
msgid "Page Transitions"
msgstr ""

#: modules/page-transitions/module.php:191
msgid "This is the page color behind your loading animation"
msgstr ""

#: modules/page-transitions/module.php:212
msgid "Fade In Down"
msgstr ""

#: modules/page-transitions/module.php:213
msgid "Fade In Right"
msgstr ""

#: modules/page-transitions/module.php:214
msgid "Fade In Up"
msgstr ""

#: modules/page-transitions/module.php:215
msgid "Fade In Left"
msgstr ""

#: modules/page-transitions/module.php:239
msgid "Fade Out Down"
msgstr ""

#: modules/page-transitions/module.php:241
msgid "Fade Out Up"
msgstr ""

#: modules/page-transitions/module.php:242
msgid "Fade Out Left"
msgstr ""

#: modules/page-transitions/module.php:294
msgid "Preloader"
msgstr ""

#: modules/page-transitions/module.php:351
msgid "Circle Dashed"
msgstr ""

#: modules/page-transitions/module.php:352
msgid "Bouncing Dots"
msgstr ""

#: modules/page-transitions/module.php:353
msgid "Pulsing Dots"
msgstr ""

#: modules/page-transitions/module.php:354
#: modules/page-transitions/module.php:381
msgid "Pulse"
msgstr ""

#: modules/page-transitions/module.php:355
msgid "Overlap"
msgstr ""

#: modules/page-transitions/module.php:356
msgid "Spinners"
msgstr ""

#: modules/page-transitions/module.php:357
msgid "Nested Spinners"
msgstr ""

#: modules/page-transitions/module.php:358
msgid "Opposing Nested Spinners"
msgstr ""

#: modules/page-transitions/module.php:359
msgid "Opposing Nested Rings"
msgstr ""

#: base/base-carousel-trait.php:998 modules/page-transitions/module.php:360
msgid "Progress Bar"
msgstr ""

#: modules/page-transitions/module.php:361
msgid "Two Way Progress Bar"
msgstr ""

#: modules/page-transitions/module.php:362
msgid "Repeating Bar"
msgstr ""

#: modules/page-transitions/module.php:378
msgid "Spinning"
msgstr ""

#: modules/page-transitions/module.php:379
msgid "Bounce"
msgstr ""

#: modules/page-transitions/module.php:380
msgid "Flash"
msgstr ""

#: modules/page-transitions/module.php:382
msgid "Rubber Band"
msgstr ""

#: modules/page-transitions/module.php:383
msgid "Shake"
msgstr ""

#: modules/page-transitions/module.php:384
msgid "Head Shake"
msgstr ""

#: modules/page-transitions/module.php:385
msgid "Swing"
msgstr ""

#: modules/page-transitions/module.php:386
msgid "Tada"
msgstr ""

#: modules/page-transitions/module.php:387
msgid "Wobble"
msgstr ""

#: modules/page-transitions/module.php:388
msgid "Jello"
msgstr ""

#: modules/page-transitions/module.php:899
msgid "Customize entrance and exit animations for every page on your site, add a preloader with predefined animations and icons or upload your own images."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/social/classes/facebook-sdk-manager.php:74
msgid "For visitors from the EU, Facebook widgets will only work for site visitors if they have logged into Facebook and consented to cookies. %1$sLearn more%2$s"
msgstr ""

#: modules/woocommerce/module.php:746
msgid "This is how an error notice would look."
msgstr ""

#: modules/woocommerce/module.php:747 modules/woocommerce/module.php:757
msgid "Here's a link"
msgstr ""

#: modules/woocommerce/module.php:756
msgid "This is what a WooCommerce message notice looks like."
msgstr ""

#: modules/woocommerce/module.php:766
msgid "This is how WooCommerce provides an info notice."
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:122
#: modules/woocommerce/widgets/purchase-summary.php:27
#: modules/woocommerce/widgets/purchase-summary.php:274
msgid "Purchase Summary"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:159
msgid "Notices"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:167
msgid "Here's where you can customize how notices form WooCommerce will appear for your customers"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:176
msgid "Notice Type"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:180
#: modules/woocommerce/settings/settings-woocommerce.php:196
msgid "Error Notices"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:181
#: modules/woocommerce/settings/settings-woocommerce.php:267
msgid "Message Notices"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:182
#: modules/woocommerce/settings/settings-woocommerce.php:340
msgid "Info Notices"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:444
msgid "Notice Text"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:496
msgid "Notice Box"
msgstr ""

#: modules/woocommerce/widgets/cart.php:853
#: modules/woocommerce/widgets/checkout.php:963
#: modules/woocommerce/widgets/my-account.php:897
msgid "Forms"
msgstr ""

#: modules/woocommerce/widgets/notices.php:19
#: modules/woocommerce/widgets/notices.php:43
msgid "WooCommerce Notices"
msgstr ""

#: modules/woocommerce/widgets/notices.php:51
msgid "Drop this widget anywhere on the page or template where you want notices to appear."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/woocommerce/widgets/notices.php:62
msgid "To change the design of your notices, go to your %1$sWooCommerce Settings%2$s"
msgstr ""

#. translators: 1: Bold text opening tag, 2: Bold text closing tag.
#: modules/woocommerce/widgets/notices.php:76
msgid "%1$sNote:%2$s You can only add the Notices widget once per page."
msgstr ""

#: modules/woocommerce/widgets/notices.php:104
msgid "This is an example of a WooCommerce notice. (You won't see this while previewing your site.)"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:46
#: modules/woocommerce/widgets/purchase-summary.php:53
#: modules/woocommerce/widgets/purchase-summary.php:567
msgid "Confirmation Message"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:72
msgid "Thank You. Your order has been received."
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:113
#: modules/woocommerce/widgets/purchase-summary.php:683
msgid "Payment Details"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:125
msgid "Order Number:"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:132
msgid "Date:"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:137
msgid "Order Date:"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:149
msgid "Order Email:"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:156
msgid "Total"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:161
msgid "Order Total:"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:173
msgid "Payment Method:"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:182
#: modules/woocommerce/widgets/purchase-summary.php:855
msgid "Bank Details"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:194
msgid "Our Bank Details"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:419
msgid "Preview order with"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:431
msgid "Order ID"
msgstr ""

#: modules/woocommerce/widgets/purchase-summary.php:437
msgid "Note: To find an order ID, go to the WP dashboard: WooCommerce > Orders"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:178
#: modules/woocommerce/widgets/my-account.php:179
msgid "Logout"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:690
msgid "Section Titles"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:744
#: modules/woocommerce/widgets/purchase-summary.php:655
msgid "General Text"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:771
msgid "Login Messages"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:1307
#: modules/woocommerce/widgets/purchase-summary.php:286
#: modules/woocommerce/widgets/purchase-summary.php:1080
msgid "Order Details"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:1335
#: modules/woocommerce/widgets/purchase-summary.php:1107
msgid "Titles &amp; Totals"
msgstr ""

#: assets/js/app.js:2146 assets/js/app.js:2507 assets/js/custom-code.js:354
#: core/app/modules/site-editor/assets/js/context/conditions.js:280
#: core/app/modules/site-editor/assets/js/context/templates.js:136
msgid "Error:"
msgstr ""

#: assets/js/app.js:2880
#: core/app/modules/site-editor/assets/js/molecules/site-template-footer.js:12
msgid "Edit Conditions"
msgstr ""

#: assets/js/app.js:3137
#: core/app/modules/site-editor/assets/js/organisms/site-templates.js:61
msgid "No Templates found. Want to create one?"
msgstr ""

#: assets/js/app.js:3143 assets/js/app.js:3595 assets/js/app.js:3767
#: assets/js/custom-code.js:1100
#: core/app/modules/site-editor/assets/js/organisms/site-templates.js:71
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions-rows.js:52
#: core/app/modules/site-editor/assets/js/pages/import.js:66
msgid "Go Back"
msgstr ""

#: assets/js/app.js:3239
#: core/app/modules/site-editor/assets/js/pages/add-new.js:46
msgid "Start customizing every part of your site"
msgstr ""

#: assets/js/app.js:3659 assets/js/form-submission-admin.js:5151
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions.js:14
msgid "Not Found"
msgstr ""

#: assets/js/app.js:3668
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions.js:24
msgid "Import template"
msgstr ""

#: assets/js/app.js:3672
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions.js:27
msgid "Where Do You Want to Display Your Template?"
msgstr ""

#: assets/js/app.js:3674
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions.js:30
msgid "Set the conditions that determine where your template is used throughout your site."
msgstr ""

#: assets/js/app.js:3753
#: core/app/modules/site-editor/assets/js/pages/import.js:48
msgid "Your template was imported"
msgstr ""

#: assets/js/app.js:3792
#: core/app/modules/site-editor/assets/js/pages/import.js:96
msgid "Import Template To Your Library"
msgstr ""

#: assets/js/app.js:3793
#: core/app/modules/site-editor/assets/js/pages/import.js:97
msgid "Drag & Drop your .JSON or .zip template file"
msgstr ""

#: assets/js/app.js:3794
#: core/app/modules/site-editor/assets/js/pages/import.js:98
msgid "or"
msgstr ""

#: assets/js/app.js:3905
#: core/app/modules/site-editor/assets/js/pages/templates.js:11
msgid "Your Site's Global Parts"
msgstr ""

#: assets/js/app.js:3949
#: core/app/modules/site-editor/assets/js/part-actions/dialog-delete.js:22
msgid "Move Item To Trash"
msgstr ""

#: assets/js/app.js:3950
#: core/app/modules/site-editor/assets/js/part-actions/dialog-delete.js:23
msgid "Are you sure you want to move this item to trash:"
msgstr ""

#: assets/js/app.js:3952 assets/js/form-submission-admin.js:4822
#: assets/js/form-submission-admin.js:5254
#: core/app/modules/site-editor/assets/js/part-actions/dialog-delete.js:25
msgid "Move to Trash"
msgstr ""

#: assets/js/app.js:4012
#: core/app/modules/site-editor/assets/js/part-actions/dialog-rename.js:32
msgid "Rename Site Part"
msgstr ""

#: assets/js/app.js:4013
#: core/app/modules/site-editor/assets/js/part-actions/dialog-rename.js:33
msgid "Change"
msgstr ""

#: assets/js/app.js:4088
#: core/app/modules/site-editor/assets/js/part-actions/dialogs-and-buttons.js:38
msgid "Export"
msgstr ""

#: assets/js/app.js:4098
#: core/app/modules/site-editor/assets/js/part-actions/dialogs-and-buttons.js:54
msgid "Rename"
msgstr ""

#: assets/js/app.js:4158
#: core/app/modules/site-editor/assets/js/site-editor.js:23
msgid "import"
msgstr ""

#: assets/js/app.js:4186
#: core/app/modules/site-editor/assets/js/site-editor.js:47
msgid "Theme Builder could not be loaded"
msgstr ""

#: assets/js/app.js:4221
#: core/app/modules/site-editor/assets/js/site-editor.js:69
msgid "Switch to table view"
msgstr ""

#: assets/js/custom-code.js:2615
msgid "Manage and create all of your custom code here.<br />Organize all of your custom code and incorporate code snippets in your site. Add tracking codes, meta titles, and other scripts. Set display conditions, locations, and priority all from one place."
msgstr ""

#: assets/js/custom-code.js:2615 assets/js/notes/notes-app.js:1420
msgid "Learn more"
msgstr ""

#: assets/js/custom-code.js:1184
msgid "Elementor Custom-Code #"
msgstr ""

#: assets/js/custom-code.js:1335
msgid "Where Do You Want to Display Your Code?"
msgstr ""

#: assets/js/custom-code.js:1337
msgid "Set the conditions that determine where your code snippet is used throughout your site."
msgstr ""

#: assets/js/custom-code.js:1337
msgid "For example, choose 'Entire Site' to display the code snippet across your site."
msgstr ""

#: assets/js/editor.js:6553
msgid "Want to save this as your checkout page?"
msgstr ""

#: assets/js/editor.js:6554 assets/js/editor.js:6563 assets/js/editor.js:6572
msgid "Changes you make here will override your existing WooCommerce settings."
msgstr ""

#: assets/js/editor.js:6555
msgid "You've updated your checkout page."
msgstr ""

#: assets/js/editor.js:6556
msgid "<h3>Set up a checkout page</h3><br>Without a checkout page, visitors can't complete transactions on your site. To set one up, go to Site Settings."
msgstr ""

#: assets/js/editor.js:6557
msgid "<h3>Sorry, something went wrong.</h3><br>To define a checkout page for your site, head over to Site Settings."
msgstr ""

#: assets/js/editor.js:6562
msgid "Want to save this as your cart page?"
msgstr ""

#: assets/js/editor.js:6564
msgid "You've updated your cart page."
msgstr ""

#: assets/js/editor.js:6565
msgid "<h3>Set up a cart page</h3><br>The cart page shows an order summary. To set one up, go to Site Settings."
msgstr ""

#: assets/js/editor.js:6566
msgid "<h3>Sorry, something went wrong.</h3><br>To define a cart page for your site, head over to Site Settings."
msgstr ""

#: assets/js/editor.js:6571
msgid "Want to save this as your my account page?"
msgstr ""

#: assets/js/editor.js:6573
msgid "You've updated your my account page."
msgstr ""

#: assets/js/editor.js:6574
msgid "<h3>Set up a My Account page</h3><br>Without it, customers can't update their billing details, review past orders, etc. To set up My Account, go to Site Settings."
msgstr ""

#: assets/js/editor.js:6575
msgid "<h3>Sorry, something went wrong.</h3><br>To define a my account page for your site, head over to Site Settings."
msgstr ""

#: assets/js/editor.js:6721
msgid "No thanks"
msgstr ""

#: assets/js/form-submission-admin.js:1578
msgid "Select bulk action"
msgstr ""

#: assets/js/form-submission-admin.js:1585
msgid "Bulk actions"
msgstr ""

#: assets/js/form-submission-admin.js:1728
msgid "All Time"
msgstr ""

#: assets/js/form-submission-admin.js:1735
msgid "Today"
msgstr ""

#: assets/js/form-submission-admin.js:1742
msgid "Yesterday"
msgstr ""

#: assets/js/form-submission-admin.js:1749
msgid "Last 7 days"
msgstr ""

#: assets/js/form-submission-admin.js:1756
msgid "Last 30 days"
msgstr ""

#: assets/js/form-submission-admin.js:1821
msgid "Start Date"
msgstr ""

#: assets/js/form-submission-admin.js:1835
msgid "End Date"
msgstr ""

#: assets/js/form-submission-admin.js:1890
msgid "Export All to CSV"
msgstr ""

#: assets/js/form-submission-admin.js:1891
msgid "Export Filtered to CSV"
msgstr ""

#: assets/js/form-submission-admin.js:1892
msgid "Export Selected to CSV"
msgstr ""

#: assets/js/form-submission-admin.js:1914
msgid "Click to Cancel"
msgstr ""

#: assets/js/form-submission-admin.js:1956
msgid "Actions Log"
msgstr ""

#: assets/js/form-submission-admin.js:1963
msgid "No form actions."
msgstr ""

#: assets/js/form-submission-admin.js:2116
msgid "Undo"
msgstr ""

#: assets/js/form-submission-admin.js:2122
msgid "Dismiss this notice."
msgstr ""

#: assets/js/form-submission-admin.js:2218
msgid "items"
msgstr ""

#: modules/nested-carousel/widgets/nested-carousel.php:319
#: modules/nested-carousel/widgets/nested-carousel.php:374
#: assets/js/form-submission-admin.js:2237
#: assets/js/loop-carousel.8c8c442ebf9839e07d4e.bundle.js:57
#: assets/js/preloaded-elements-handlers.js:3205
msgid "of"
msgstr ""

#: assets/js/form-submission-admin.js:2317
#: assets/js/form-submission-admin.js:2365
msgid "All Pages"
msgstr ""

#: assets/js/form-submission-admin.js:2394
msgid "Filter by Page"
msgstr ""

#: assets/js/form-submission-admin.js:2546
msgid "Search..."
msgstr ""

#: assets/js/form-submission-admin.js:2609
msgid "Show more details"
msgstr ""

#: assets/js/form-submission-admin.js:2614
msgid "Succeed"
msgstr ""

#: assets/js/form-submission-admin.js:4567
msgid "Something went wrong, please try again later."
msgstr ""

#: assets/js/form-submission-admin.js:4606
msgid "Action completed successfully."
msgstr ""

#: assets/js/form-submission-admin.js:4607
msgid "Action failed to run, please check your integration."
msgstr ""

#: assets/js/form-submission-admin.js:4769
#: assets/js/form-submission-admin.js:4837
msgid "Restore"
msgstr ""

#: assets/js/form-submission-admin.js:4784
#: assets/js/form-submission-admin.js:4829
#: assets/js/form-submission-admin.js:5254
msgid "Delete Permanently"
msgstr ""

#: assets/js/form-submission-admin.js:4793
#: assets/js/form-submission-admin.js:4808
msgid "Mark as Read"
msgstr ""

#: assets/js/form-submission-admin.js:4800
#: assets/js/form-submission-admin.js:4815
msgid "Mark as Unread"
msgstr ""

#: assets/js/form-submission-admin.js:4847
msgid "Main"
msgstr ""

#: assets/js/form-submission-admin.js:4851
msgid "Actions Status"
msgstr ""

#: assets/js/form-submission-admin.js:4867
msgid "Submission Date"
msgstr ""

#: assets/js/form-submission-admin.js:4966
msgid "All Forms"
msgstr ""

#: assets/js/form-submission-admin.js:4976
msgid "Filter by form"
msgstr ""

#: assets/js/form-submission-admin.js:4996
msgid "Filter by date"
msgstr ""

#: assets/js/form-submission-admin.js:5005
msgid "No submissions found."
msgstr ""

#: assets/js/form-submission-admin.js:5174
msgid "Submission"
msgstr ""

#: assets/js/form-submission-admin.js:5225
msgid "Page:"
msgstr ""

#: assets/js/form-submission-admin.js:5233
msgid "Create Date:"
msgstr ""

#: assets/js/form-submission-admin.js:5235
msgid "Update Date:"
msgstr ""

#: assets/js/form-submission-admin.js:5237
msgid "User Name:"
msgstr ""

#: assets/js/form-submission-admin.js:5239
msgid "User IP:"
msgstr ""

#: assets/js/form-submission-admin.js:5241
msgid "User Agent:"
msgstr ""

#: modules/woocommerce/widgets/cart.php:782
#: modules/woocommerce/widgets/checkout.php:915
#: modules/woocommerce/widgets/checkout.php:2851
#: modules/woocommerce/widgets/my-account.php:853
msgid "Links"
msgstr ""

#: modules/woocommerce/widgets/cart.php:824
#: modules/woocommerce/widgets/checkout.php:886
#: modules/woocommerce/widgets/my-account.php:825
msgid "Radio Buttons"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1368
#: modules/woocommerce/widgets/my-account.php:1424
#: modules/woocommerce/widgets/purchase-summary.php:1196
msgid "Product Link"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1410
#: modules/woocommerce/widgets/checkout.php:1496
#: modules/woocommerce/widgets/my-account.php:552
#: modules/woocommerce/widgets/my-account.php:1466
#: modules/woocommerce/widgets/purchase-summary.php:797
#: modules/woocommerce/widgets/purchase-summary.php:1022
#: modules/woocommerce/widgets/purchase-summary.php:1238
msgid "Dividers"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1442
msgid "Quantity Borders"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1474
msgid "Remove icon"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1546
#: modules/woocommerce/widgets/checkout.php:1527
msgid "Titles & Totals"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1573
#: modules/woocommerce/widgets/checkout.php:1554
msgid "Divider Total"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1771
#: modules/woocommerce/widgets/checkout.php:1756
msgid "Customize"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1795
msgid "Select sections of the cart to customize:"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1809
#: modules/woocommerce/widgets/checkout.php:2401
msgid "Customize: Order Summary"
msgstr ""

#: modules/woocommerce/widgets/cart.php:1929
msgid "Customize: Totals"
msgstr ""

#: modules/woocommerce/widgets/cart.php:2153
#: modules/woocommerce/widgets/checkout.php:3460
msgid "Customize: Coupon"
msgstr ""

#: modules/woocommerce/widgets/cart.php:2328
#: modules/woocommerce/widgets/checkout.php:4057
msgid "Coupon code"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:89
msgid "The Order Summary and Payment sections will remain in place while scrolling."
msgstr ""

#: modules/woocommerce/widgets/checkout.php:131
#: modules/woocommerce/widgets/checkout.php:140
#: modules/woocommerce/widgets/checkout.php:141
msgid "Billing and Shipping Details"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:131
#: modules/woocommerce/widgets/checkout.php:140
#: modules/woocommerce/widgets/checkout.php:141
#: modules/woocommerce/widgets/checkout.php:1770
#: modules/woocommerce/widgets/purchase-summary.php:320
#: modules/woocommerce/widgets/purchase-summary.php:332
msgid "Billing Details"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:225
#: modules/woocommerce/widgets/checkout.php:3034
msgid "Repeater State - hidden"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:233
#: modules/woocommerce/widgets/checkout.php:3101
msgid "Note: This content cannot be changed due to local regulations."
msgstr ""

#: modules/woocommerce/widgets/checkout.php:245
#: modules/woocommerce/widgets/checkout.php:3042
#: modules/woocommerce/widgets/checkout.php:3113
msgid "Note: This label and placeholder are taken from the Billing section. You can change it there."
msgstr ""

#: modules/woocommerce/widgets/checkout.php:257
#: modules/woocommerce/widgets/checkout.php:3125
msgid "Form Items"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:410
#: modules/woocommerce/widgets/checkout.php:411
msgid "Order Notes"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:412
msgid "Notes about your order, e.g. special notes for delivery."
msgstr ""

#: modules/woocommerce/widgets/checkout.php:431
#: modules/woocommerce/widgets/checkout.php:440
msgid "Your Order"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:481
#: modules/woocommerce/widgets/checkout.php:1791
#: modules/woocommerce/widgets/purchase-summary.php:168
msgid "Payment"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:489
msgid "Terms &amp; Conditions"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:499
msgid "I have read and agree to the website"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:210
#: modules/woocommerce/settings/settings-woocommerce.php:281
#: modules/woocommerce/widgets/checkout.php:509
#: modules/woocommerce/widgets/checkout.php:2919
#: modules/woocommerce/widgets/checkout.php:3202
msgid "Link Text"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:512
msgid "terms and conditions"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:523
#: modules/woocommerce/widgets/checkout.php:1586
msgid "Purchase Button"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:738
msgid "Secondary Titles"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:858
#: modules/woocommerce/widgets/checkout.php:3429
#: modules/woocommerce/widgets/my-account.php:798
msgid "Checkboxes"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:1765
#: modules/woocommerce/widgets/checkout.php:2900
msgid "Returning Customer"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:1776
#: modules/woocommerce/widgets/purchase-summary.php:366
msgid "Shipping Address"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:1797
msgid "Select sections of the checkout to customize:"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:1811
msgid "Customize: Returning Customer"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:1929
#: modules/woocommerce/widgets/checkout.php:3578
msgid "Secondary Title"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2057
msgid "Customize: Billing Details"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2238
msgid "Customize: Additional Information"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2581
#: modules/woocommerce/widgets/checkout.php:2824
msgid "Radio Button"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2609
msgid "Customize: Payment"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2727
msgid "Info Box"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2909
#: modules/woocommerce/widgets/checkout.php:3972
msgid "Returning customer?"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:2921
#: modules/woocommerce/widgets/checkout.php:3972
msgid "Click here to login"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3013
#: modules/woocommerce/widgets/purchase-summary.php:378
msgid "Shipping Details"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3022
msgid "Ship to a different address?"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3146
msgid "Create an Account"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3155
msgid "Create an account?"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3189
#: modules/woocommerce/widgets/checkout.php:4051
msgid "Have a coupon?"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3204
#: modules/woocommerce/widgets/checkout.php:4051
msgid "Click here to enter your coupon code"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3311
msgid "Customize: Shipping Address"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3670
#: modules/woocommerce/widgets/checkout.php:3726
msgid "Company Name"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3674
#: modules/woocommerce/widgets/checkout.php:3730
msgid "Country / Region"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3678
#: modules/woocommerce/widgets/checkout.php:3734
msgid "Street Address"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3682
#: modules/woocommerce/widgets/checkout.php:3738
msgid "Post Code"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3686
#: modules/woocommerce/widgets/checkout.php:3742
msgid "Town / City"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3690
#: modules/woocommerce/widgets/checkout.php:3746
msgid "State"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3694
msgid "Phone"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3698
msgid "Email Address"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:3975
msgid "If you have shopped with us before, please enter your details below. If you are a new customer, please proceed to the Billing section."
msgstr ""

#: modules/woocommerce/widgets/checkout.php:4004
msgid "Remember me"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:4053
msgid "If you have a coupon code, please apply it below."
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:183
msgid "Cart Type"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:186
msgid "Side Cart"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:187
msgid "Mini Cart"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:198
msgid "Open Cart"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:213
#: modules/woocommerce/widgets/menu-cart.php:241
msgid "Cart Position"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:299
#: modules/woocommerce/widgets/menu-cart.php:1136
msgid "Close Cart"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:308
msgid "Close Icon"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:388
#: modules/woocommerce/widgets/menu-cart.php:1219
msgid "Remove Item"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:437
msgid "Price and Quantity"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:471
msgid "Cart Dividers"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:690
msgid "Automatically Open Cart"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:692
msgid "Open the cart every time an item is added."
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:704
msgid "Automatically Update Cart"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:710
msgid "Updates to the cart (e.g., a removed item) via Ajax. The cart will update without refreshing the whole page."
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:1362
msgid "Divider Style"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:2121
msgid "Empty Cart Message Color"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:78
msgid "Tabs Position"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:118
msgid "Tab Name"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:129
msgid "Note: By default, only your last order is displayed while editing the orders section. You can see other orders on your live site or in the WooCommerce orders section"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:153
#: modules/woocommerce/widgets/my-account.php:154
msgid "Dashboard"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:158
#: modules/woocommerce/widgets/my-account.php:159
msgid "Orders"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:163
#: modules/woocommerce/widgets/my-account.php:164
#: modules/woocommerce/widgets/purchase-summary.php:228
#: modules/woocommerce/widgets/purchase-summary.php:240
msgid "Downloads"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:168
#: modules/woocommerce/widgets/my-account.php:169
msgid "Addresses"
msgstr ""

#: modules/woocommerce/widgets/my-account.php:173
#: modules/woocommerce/widgets/my-account.php:174
msgid "Account Details"
msgstr ""

#. translators: 1: Bold text opening tag, 2: Bold text closing tag.
#: modules/woocommerce/module.php:686
msgid "%1$sError:%2$s The nonce security check didn’t pass. Please reload the page and try again. You may want to try clearing your browser cache as a last attempt."
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:54
msgid "Select the pages you want to use as your default WooCommerce shop pages"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:73
#: modules/woocommerce/settings/settings-woocommerce.php:86
#: modules/woocommerce/settings/settings-woocommerce.php:99
#: modules/woocommerce/settings/settings-woocommerce.php:112
#: modules/woocommerce/settings/settings-woocommerce.php:125
#: modules/woocommerce/settings/settings-woocommerce.php:138
msgid "Select a page"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:109
msgid "Terms & Conditions"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:149
msgid "Note: Changes you make here will also be reflected in the WooCommerce settings on your WP dashboard"
msgstr ""

#: modules/woocommerce/widgets/cart.php:67
#: modules/woocommerce/widgets/checkout.php:73
msgid "Two columns"
msgstr ""

#: modules/woocommerce/widgets/cart.php:68
#: modules/woocommerce/widgets/checkout.php:74
msgid "One column"
msgstr ""

#: modules/woocommerce/widgets/cart.php:78
#: modules/woocommerce/widgets/checkout.php:84
msgid "Sticky Right Column"
msgstr ""

#: modules/woocommerce/widgets/cart.php:82
msgid "This option will allow the right column (e.g, Cart Totals) to be sticky while scrolling."
msgstr ""

#: modules/woocommerce/widgets/cart.php:120
#: modules/woocommerce/widgets/cart.php:1233
#: modules/woocommerce/widgets/cart.php:1779
#: modules/woocommerce/widgets/checkout.php:1412
#: modules/woocommerce/widgets/checkout.php:1781
msgid "Order Summary"
msgstr ""

#: modules/woocommerce/widgets/cart.php:131
msgid "Update Cart Button"
msgstr ""

#: modules/woocommerce/widgets/cart.php:143
#: modules/woocommerce/widgets/cart.php:144
msgid "Update Cart"
msgstr ""

#: modules/woocommerce/widgets/cart.php:190
#: modules/woocommerce/widgets/cart.php:197
#: modules/woocommerce/widgets/cart.php:1784
#: modules/woocommerce/widgets/checkout.php:1786
#: modules/woocommerce/widgets/checkout.php:3169
#: modules/woocommerce/widgets/checkout.php:3176
msgid "Coupon"
msgstr ""

#: modules/woocommerce/widgets/cart.php:209
msgid "Apply Coupon Button"
msgstr ""

#: modules/woocommerce/widgets/cart.php:224
#: modules/woocommerce/widgets/cart.php:225
#: modules/woocommerce/widgets/cart.php:2331
msgid "Apply coupon"
msgstr ""

#: modules/woocommerce/widgets/cart.php:288
#: modules/woocommerce/widgets/cart.php:1518
#: modules/woocommerce/widgets/cart.php:1789
msgid "Totals"
msgstr ""

#: modules/woocommerce/widgets/cart.php:295
#: modules/woocommerce/widgets/checkout.php:138
#: modules/woocommerce/widgets/checkout.php:302
#: modules/woocommerce/widgets/checkout.php:438
#: modules/woocommerce/widgets/checkout.php:2907
#: modules/woocommerce/widgets/checkout.php:3020
#: modules/woocommerce/widgets/checkout.php:3153
#: modules/woocommerce/widgets/checkout.php:3187
#: modules/woocommerce/widgets/products.php:192
msgid "Section Title"
msgstr ""

#: modules/woocommerce/widgets/cart.php:300
#: modules/woocommerce/widgets/cart.php:301
msgid "Cart Totals"
msgstr ""

#: modules/woocommerce/widgets/cart.php:334
msgid "Update Shipping Button"
msgstr ""

#: modules/woocommerce/widgets/cart.php:347
#: modules/woocommerce/widgets/cart.php:348
#: assets/js/form-submission-admin.js:5262
msgid "Update"
msgstr ""

#: modules/woocommerce/widgets/cart.php:391
#: modules/woocommerce/widgets/cart.php:1605
msgid "Checkout Button"
msgstr ""

#: modules/woocommerce/widgets/cart.php:403
#: modules/woocommerce/widgets/cart.php:404
msgid "Proceed to Checkout"
msgstr ""

#: modules/woocommerce/widgets/cart.php:455
msgid "Update Cart Automatically"
msgstr ""

#: modules/woocommerce/widgets/cart.php:473
msgid "Changes to the cart will update automatically."
msgstr ""

#: modules/woocommerce/widgets/cart.php:684
#: modules/woocommerce/widgets/cart.php:1261
#: modules/woocommerce/widgets/checkout.php:685
#: modules/woocommerce/widgets/purchase-summary.php:602
#: modules/woocommerce/widgets/purchase-summary.php:711
#: modules/woocommerce/widgets/purchase-summary.php:936
msgid "Titles"
msgstr ""

#: modules/woocommerce/widgets/cart.php:737
#: modules/woocommerce/widgets/checkout.php:784
msgid "Descriptions"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: license/admin.php:595
msgid "Log in to %1$syour account%2$s to get your license key."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: license/admin.php:603
msgid "If you don't yet have a license key, %1$sget Elementor Pro now%2$s."
msgstr ""

#. translators: 1: Bold text opening tag, 2: Bold text closing tag, 3: Link
#. opening tag, 4: Link closing tag.
#: license/api.php:357
msgid "%1$sYou have no more activations left.%2$s %3$sPlease upgrade to a more advanced license%4$s (you'll only need to cover the difference)."
msgstr ""

#: license/notices/trial-expired-notice.php:31
msgid "Your trial has expired"
msgstr ""

#: license/notices/trial-expired-notice.php:32
msgid "Want to continue using Pro to build your website? Choose the plan that's right for you!"
msgstr ""

#: license/notices/trial-expired-notice.php:34
msgid "Go Pro"
msgstr ""

#. translators: %s: Days left to trial expiration.
#: license/notices/trial-period-notice.php:34
msgid "Your trial expires in %s"
msgstr ""

#: license/notices/trial-period-notice.php:44
msgid "Find the plan that matches your needs and enjoy Pro widgets, templates, and support for a whole year."
msgstr ""

#: license/notices/trial-period-notice.php:46
msgid "Choose your plan"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:175
msgid "Enter Your %1$sTypeKit Project ID%2$s."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:113
msgid "Enter Your %1$sFont Awesome Pro Kit ID%2$s."
msgstr ""

#. translators: %s: Body opening tag.
#: modules/custom-code/custom-code-metabox.php:68
msgid "%s - Start"
msgstr ""

#. translators: %s: Body closing tag.
#: modules/custom-code/custom-code-metabox.php:73
msgid "%s - End"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:171
msgid "Always load jQuery"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:173
msgid "If your snippet includes jQuery, this will ensure it will work for all visitors. It may have a minor impact on loading speed."
msgstr ""

#: modules/custom-code/module.php:363
msgid "Add pixels, meta tags and any other scripts to your site."
msgstr ""

#: modules/custom-code/module.php:363
msgid "Learn more about adding custom code"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/forms/actions/activecampaign.php:304
#: modules/forms/actions/convertkit.php:253 modules/forms/actions/drip.php:292
#: modules/forms/actions/getresponse.php:301
#: modules/forms/actions/mailchimp.php:454
#: modules/forms/actions/mailerlite.php:252
msgid "To integrate with our forms you need an %1$sAPI Key%2$s."
msgstr ""

#. translators: 1: Integration label, 2: Link opening tag, 3: Link closing tag.
#: modules/forms/classes/integration-base.php:22
msgid "Set your %1$s in the %2$sIntegrations Settings%3$s."
msgstr ""

#. translators: 1: Integration label, 2: Link opening tag, 3: Link closing tag.
#: modules/forms/classes/integration-base.php:31
msgid "You are using %1$s set in the %2$sIntegrations Settings%3$s."
msgstr ""

#. translators: 1: User display name, 2: Link opening tag, 3: Link closing tag.
#: modules/forms/widgets/login.php:859
msgid "You are Logged in as %1$s (%2$sLogout%3$s)"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/nav-menu/widgets/nav-menu.php:85
msgid "Go to the %1$sMenus screen%2$s to manage your menus."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/nav-menu/widgets/nav-menu.php:99
msgid "Go to the %1$sMenus screen%2$s to create one."
msgstr ""

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#. translators: 1: Breakpoint label, 2: `<` character, 3: Breakpoint value.
#: modules/mega-menu/widgets/mega-menu.php:740
#: modules/nav-menu/widgets/nav-menu.php:322
#: modules/table-of-contents/widgets/table-of-contents.php:311
msgid "%1$s (%2$s %3$dpx)"
msgstr ""

#: modules/posts/data/controller.php:28
msgid "Document doesn't exist"
msgstr ""

#: modules/posts/data/controller.php:39
msgid "Posts widget doesn't exist"
msgstr ""

#: modules/posts/traits/button-widget-trait.php:60
msgid "Info"
msgstr ""

#: modules/posts/traits/button-widget-trait.php:61
msgid "Success"
msgstr ""

#: modules/posts/traits/button-widget-trait.php:62
msgid "Warning"
msgstr ""

#: modules/posts/traits/button-widget-trait.php:63
msgid "Danger"
msgstr ""

#: modules/posts/widgets/posts-base.php:70
#: modules/posts/widgets/posts-base.php:85
#: modules/posts/widgets/posts-base.php:381
msgid "No More Posts Message"
msgstr ""

#: modules/posts/widgets/posts-base.php:122
msgid "Spinner Color"
msgstr ""

#: modules/posts/widgets/posts-base.php:321
msgid "Spinner"
msgstr ""

#: modules/posts/widgets/posts-base.php:364
msgid "Load More"
msgstr ""

#: modules/posts/widgets/posts-base.php:449
msgid "No more posts message"
msgstr ""

#: modules/posts/widgets/posts-base.php:451
msgid "No more posts to show"
msgstr ""

#: modules/posts/widgets/posts-base.php:853
msgid "Load on Click"
msgstr ""

#: modules/posts/widgets/posts-base.php:854
msgid "Infinite Scroll"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:25
#: modules/progress-tracker/widgets/progress-tracker.php:44
msgid "Progress Tracker"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:52
msgid "Tracker Type"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:57
msgid "Circular"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:66
msgid "Progress relative to"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:83
msgid "Add the CSS ID or Class of a specific element on this page to track its progress separately"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:96
msgid "Note: You can only track progress relative to Post Content on a single post template."
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:143
msgid "Percentage Position"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:172
msgid "Tracker"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:202
msgid "Progress Indicator"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:278
msgid "Progress Color"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:357
msgid "Tracker Background"
msgstr ""

#: modules/scroll-snap/module.php:30 modules/scroll-snap/module.php:50
#: modules/scroll-snap/module.php:58
msgid "Scroll Snap"
msgstr ""

#: modules/scroll-snap/module.php:31
msgid "Customize how visitors scroll through your site. Scroll Snap makes the viewport stop or pause on a specific position of a section when scrolling ends."
msgstr ""

#: modules/announcements/module.php:51 modules/page-transitions/module.php:907
#: modules/scroll-snap/module.php:33 assets/js/app.js:3145
#: assets/js/app.js:3597 assets/js/app.js:3763 assets/js/custom-code.js:1102
#: assets/js/notes/notes-app.js:1775
#: core/app/modules/site-editor/assets/js/organisms/site-templates.js:73
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions-rows.js:54
#: core/app/modules/site-editor/assets/js/pages/import.js:62
msgid "Learn More"
msgstr ""

#: modules/scroll-snap/module.php:62
msgid "Scroll Snap makes the viewport stop on a specific position of a section when scrolling ends."
msgstr ""

#: modules/scroll-snap/module.php:74
msgid "Snap Position"
msgstr ""

#: modules/scroll-snap/module.php:96
msgid "Scroll Padding"
msgstr ""

#: modules/scroll-snap/module.php:117
msgid "Scroll Snap Stop"
msgstr ""

#: modules/scroll-snap/module.php:121
msgid "Always"
msgstr ""

#. translators: 1: Setting Page Link opening tag, 2: Link closing tag.
#: modules/social/classes/facebook-sdk-manager.php:43
msgid "Set your Facebook App ID in the %1$sIntegrations Settings%2$s"
msgstr ""

#. translators: 1: App ID, 2: Setting Page Link opening tag, 3: Link closing
#. tag.
#: modules/social/classes/facebook-sdk-manager.php:51
msgid "You are connected to Facebook App %1$s, %2$sChange App%3$s"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/social/classes/facebook-sdk-manager.php:124
msgid "Facebook SDK lets you connect to your %1$sdedicated application%2$s so you can track the Facebook Widgets analytics on your site."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/social/classes/facebook-sdk-manager.php:139
msgid "Remember to add the domain to your %1$sApp Domains%2$s"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:394
msgid "Loader Color"
msgstr ""

#. translators: 1: Comment author link, 2: Span open tag, 3: Span closing tag.
#: modules/theme-builder/skins/post-comments-skin-classic.php:440
msgid "%1$s %2$ssays:%3$s"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:80
msgid "Vimeo"
msgstr ""

#: core/app/modules/site-editor/module.php:116
msgid "Entering the Theme Builder through WP Dashboard > Templates > Theme Builder opens the New theme builder by default. But don’t worry, you can always view the WP styled version of the screen with a simple click of a button."
msgstr ""

#: core/editor/notice-bar.php:29
msgid "Heads up! You are using a free trial. Want to enjoy Pro widgets & templates for a whole year?"
msgstr ""

#: core/editor/notice-bar.php:30
msgid "Go Pro now"
msgstr ""

#: core/editor/notice-bar.php:40
msgid "Your trial has expired. Miss your favorite Elementor Pro features?"
msgstr ""

#: core/editor/notice-bar.php:41
msgid "Upgrade now"
msgstr ""

#: elementor-pro.php:114
msgid "Activate Now"
msgstr ""

#: license/admin.php:440
msgid "Renew your license today, to keep getting feature updates, premium support, Pro widgets & unlimited access to the template library."
msgstr ""

#. translators: %s: Discount percent.
#: license/admin.php:436
msgid "Renew your license today, and get an exclusive, time-limited %s discount."
msgstr ""

#. Description of the plugin
msgid "Elevate your designs and unlock the full power of Elementor. Gain access to dozens of Pro widgets and kits, Theme Builder, Pop Ups, Forms and WooCommerce building capabilities."
msgstr ""

#. Author of the plugin
msgid "Elementor.com"
msgstr ""

#: core/app/modules/site-editor/module.php:115
msgid "Default to New Theme Builder"
msgstr ""

#: core/upgrade/manager.php:25
msgid "Elementor Pro Data Updater"
msgstr ""

#: modules/forms/submissions/actions/save-to-database.php:46
msgid "Collected Submissions will be saved to Elementor > <a href=\"%s\" target=\"_blank\" rel=\"noreferrer\">Submissions</a>"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:235
msgid "POSITION"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:242
msgid "Horizontal Orientation"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:279
msgid "Vertical Orientation"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:316
msgid "Custom Tooltip Properties"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:321
msgid "Set custom Tooltip opening that will only affect this specific hotspot."
msgstr ""

#: modules/hotspot/widgets/hotspot.php:394
msgid "Text Wrap"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:433
msgid "Soft Beat"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:434
msgid "Expand"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:483
#: modules/hotspot/widgets/hotspot.php:829
msgid "Tooltip"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:526
#: modules/mega-menu/widgets/mega-menu.php:458
msgid "Click"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:540
msgid "Fade In/Out"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:541
msgid "Fade Grow"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:542
msgid "Fade By Direction"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:543
msgid "Slide By Direction"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:546
msgid "Enter your image caption"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:764
msgid "Box Color"
msgstr ""

#: modules/payments/classes/payment-button.php:64
#: modules/payments/widgets/stripe-button.php:65
msgctxt "Currency"
msgid "AUD"
msgstr ""

#: modules/payments/classes/payment-button.php:65
#: modules/payments/widgets/stripe-button.php:81
msgctxt "Currency"
msgid "CAD"
msgstr ""

#: modules/payments/classes/payment-button.php:66
#: modules/payments/widgets/stripe-button.php:89
msgctxt "Currency"
msgid "CZK"
msgstr ""

#: modules/payments/classes/payment-button.php:67
#: modules/payments/widgets/stripe-button.php:91
msgctxt "Currency"
msgid "DKK"
msgstr ""

#: modules/payments/classes/payment-button.php:68
#: modules/payments/widgets/stripe-button.php:96
msgctxt "Currency"
msgid "EUR"
msgstr ""

#: modules/payments/classes/payment-button.php:69
#: modules/payments/widgets/stripe-button.php:106
msgctxt "Currency"
msgid "HKD"
msgstr ""

#: modules/payments/classes/payment-button.php:70
msgctxt "Currency"
msgid "HUF"
msgstr ""

#: modules/payments/classes/payment-button.php:71
#: modules/payments/widgets/stripe-button.php:111
msgctxt "Currency"
msgid "ILS"
msgstr ""

#: modules/payments/classes/payment-button.php:72
#: modules/payments/widgets/stripe-button.php:115
msgctxt "Currency"
msgid "JPY"
msgstr ""

#: modules/payments/classes/payment-button.php:73
#: modules/payments/widgets/stripe-button.php:139
msgctxt "Currency"
msgid "MXN"
msgstr ""

#: modules/payments/classes/payment-button.php:74
#: modules/payments/widgets/stripe-button.php:145
msgctxt "Currency"
msgid "NOK"
msgstr ""

#: modules/payments/classes/payment-button.php:75
#: modules/payments/widgets/stripe-button.php:147
msgctxt "Currency"
msgid "NZD"
msgstr ""

#: modules/payments/classes/payment-button.php:76
#: modules/payments/widgets/stripe-button.php:151
msgctxt "Currency"
msgid "PHP"
msgstr ""

#: modules/payments/classes/payment-button.php:77
#: modules/payments/widgets/stripe-button.php:153
msgctxt "Currency"
msgid "PLN"
msgstr ""

#: modules/payments/classes/payment-button.php:79
#: modules/payments/widgets/stripe-button.php:158
msgctxt "Currency"
msgid "RUB"
msgstr ""

#: modules/payments/classes/payment-button.php:80
#: modules/payments/widgets/stripe-button.php:164
msgctxt "Currency"
msgid "SGD"
msgstr ""

#: modules/payments/classes/payment-button.php:81
#: modules/payments/widgets/stripe-button.php:163
msgctxt "Currency"
msgid "SEK"
msgstr ""

#: modules/payments/classes/payment-button.php:82
#: modules/payments/widgets/stripe-button.php:83
msgctxt "Currency"
msgid "CHF"
msgstr ""

#: modules/payments/classes/payment-button.php:83
#: modules/payments/widgets/stripe-button.php:176
msgctxt "Currency"
msgid "TWD"
msgstr ""

#: modules/payments/classes/payment-button.php:84
#: modules/payments/widgets/stripe-button.php:171
msgctxt "Currency"
msgid "THB"
msgstr ""

#: modules/payments/classes/payment-button.php:85
#: modules/payments/widgets/stripe-button.php:174
msgctxt "Currency"
msgid "TRY"
msgstr ""

#: modules/payments/classes/payment-button.php:86
#: modules/payments/widgets/stripe-button.php:191
msgctxt "Currency"
msgid "USD"
msgstr ""

#: modules/theme-builder/documents/footer.php:27
msgid "Footers"
msgstr ""

#: modules/theme-builder/documents/header.php:28
msgid "Headers"
msgstr ""

#: modules/theme-builder/documents/section.php:26
#: modules/video-playlist/widgets/video-playlist.php:1205
#: modules/woocommerce/widgets/cart.php:568
#: modules/woocommerce/widgets/checkout.php:567
#: modules/woocommerce/widgets/my-account.php:585
#: modules/woocommerce/widgets/purchase-summary.php:446
msgid "Sections"
msgstr ""

#: modules/theme-builder/documents/single-base.php:28
#: modules/theme-builder/documents/single.php:19
msgid "Singles"
msgstr ""

#: modules/theme-builder/documents/single-page.php:22
msgid "Single Pages"
msgstr ""

#: modules/theme-builder/documents/single-post.php:19
msgid "Single Posts"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:27
msgid "Video Playlist"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:41
#: modules/video-playlist/widgets/video-playlist.php:64
#: modules/video-playlist/widgets/video-playlist.php:65
msgid "Playlist"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:62
#: modules/video-playlist/widgets/video-playlist.php:702
msgid "Playlist Name"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:79
msgid "YouTube"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:81
msgid "Self Hosted"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:95
msgid "Paste URL"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:129
msgid "Get Video Data"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:152
msgid "Choose File"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:202
msgid "Add Your Text Here"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:223
msgid "Thumbnail"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:240
msgid "Contents Tabs "
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:257
#: modules/video-playlist/widgets/video-playlist.php:352
msgid "Tab #1"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:270
msgid "Add some content for each one of your videos, like a description, transcript or external links.To add, remove or edit tab names, go to Tabs."
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:283
#: modules/video-playlist/widgets/video-playlist.php:366
msgid "Tab #2"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:310
msgid "Playlist Items"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:315
#: modules/video-playlist/widgets/video-playlist.php:321
#: modules/video-playlist/widgets/video-playlist.php:327
msgid "Sample Video"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:350
msgid "Tab 1 Name"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:364
msgid "Tab 2 Name"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:378
msgid "Collapsible"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:392
msgid "Read More Label"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:397
#: modules/video-playlist/widgets/video-playlist.php:398
#: modules/video-playlist/widgets/video-playlist.php:1438
msgid "Show More"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:408
msgid "Read Less Label"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:413
#: modules/video-playlist/widgets/video-playlist.php:414
msgid "Show Less"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:452
#: modules/video-playlist/widgets/video-playlist.php:459
msgid "Image Overlay"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:533
msgid "On Load"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:544
msgid "Next Up"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:555
msgid "Indicate Watched"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:565
#: modules/video-playlist/widgets/video-playlist.php:744
msgid "Video Count"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:611
msgid "Watched Icon"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:694
msgid "Top Bar"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:779
#: modules/video-playlist/widgets/video-playlist.php:1710
msgid "Videos"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:292
#: modules/progress-tracker/widgets/progress-tracker.php:448
#: modules/video-playlist/widgets/video-playlist.php:946
#: modules/video-playlist/widgets/video-playlist.php:1149
#: modules/video-playlist/widgets/video-playlist.php:1260
msgctxt "Border Control"
msgid "Solid"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:293
#: modules/progress-tracker/widgets/progress-tracker.php:449
#: modules/video-playlist/widgets/video-playlist.php:947
#: modules/video-playlist/widgets/video-playlist.php:1150
#: modules/video-playlist/widgets/video-playlist.php:1261
msgctxt "Border Control"
msgid "Double"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:294
#: modules/progress-tracker/widgets/progress-tracker.php:450
#: modules/video-playlist/widgets/video-playlist.php:948
#: modules/video-playlist/widgets/video-playlist.php:1151
#: modules/video-playlist/widgets/video-playlist.php:1262
msgctxt "Border Control"
msgid "Dotted"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:295
#: modules/progress-tracker/widgets/progress-tracker.php:451
#: modules/video-playlist/widgets/video-playlist.php:949
#: modules/video-playlist/widgets/video-playlist.php:1152
#: modules/video-playlist/widgets/video-playlist.php:1263
msgctxt "Border Control"
msgid "Dashed"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:296
#: modules/progress-tracker/widgets/progress-tracker.php:452
#: modules/video-playlist/widgets/video-playlist.php:950
#: modules/video-playlist/widgets/video-playlist.php:1153
#: modules/video-playlist/widgets/video-playlist.php:1264
msgctxt "Border Control"
msgid "Groove"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:287
#: modules/progress-tracker/widgets/progress-tracker.php:443
#: modules/video-playlist/widgets/video-playlist.php:1255
#: modules/woocommerce/settings/settings-woocommerce.php:657
#: modules/woocommerce/widgets/cart.php:595
#: modules/woocommerce/widgets/cart.php:1165
#: modules/woocommerce/widgets/cart.php:1848
#: modules/woocommerce/widgets/cart.php:1967
#: modules/woocommerce/widgets/cart.php:2191
#: modules/woocommerce/widgets/checkout.php:594
#: modules/woocommerce/widgets/checkout.php:1344
#: modules/woocommerce/widgets/checkout.php:1849
#: modules/woocommerce/widgets/checkout.php:2095
#: modules/woocommerce/widgets/checkout.php:2276
#: modules/woocommerce/widgets/checkout.php:2439
#: modules/woocommerce/widgets/checkout.php:2647
#: modules/woocommerce/widgets/checkout.php:3349
#: modules/woocommerce/widgets/checkout.php:3498
#: modules/woocommerce/widgets/menu-cart.php:1055
#: modules/woocommerce/widgets/my-account.php:467
#: modules/woocommerce/widgets/my-account.php:612
#: modules/woocommerce/widgets/my-account.php:1474
#: modules/woocommerce/widgets/my-account.php:1652
#: modules/woocommerce/widgets/purchase-summary.php:473
#: modules/woocommerce/widgets/purchase-summary.php:805
#: modules/woocommerce/widgets/purchase-summary.php:1030
#: modules/woocommerce/widgets/purchase-summary.php:1246
#: modules/woocommerce/widgets/purchase-summary.php:1426
msgid "Border Type"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:1779
msgid "Play Video"
msgstr ""

#: modules/woocommerce/documents/product-archive.php:31
msgid "Products Archives"
msgstr ""

#: modules/woocommerce/documents/product-post.php:42
msgid "Product Posts"
msgstr ""

#: modules/woocommerce/documents/product.php:32
msgid "Single Products"
msgstr ""

#: modules/global-widget/documents/widget.php:38
msgid "Global Widgets"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:29
#: modules/hotspot/widgets/hotspot.php:59
#: modules/hotspot/widgets/hotspot.php:414
#: modules/hotspot/widgets/hotspot.php:670
msgid "Hotspot"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:116
msgid "Icon Start"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:120
msgid "Icon End"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:168
msgid "Custom Hotspot Size"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:173
msgid "Set custom Hotspot size that will only affect this specific hotspot."
msgstr ""

#: modules/hotspot/widgets/hotspot.php:224
msgid "Tooltip Content"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:226
msgid "Add Your Tooltip Text Here"
msgstr ""

#: modules/payments/classes/payment-button.php:78
#: modules/payments/widgets/stripe-button.php:99
msgctxt "Currency"
msgid "GBP"
msgstr "GBP"

#: modules/pricing/widgets/price-table.php:102
msgctxt "Currency"
msgid "Dollar"
msgstr ""

#: modules/pricing/widgets/price-table.php:103
msgctxt "Currency"
msgid "Euro"
msgstr ""

#: modules/pricing/widgets/price-table.php:104
msgctxt "Currency"
msgid "Baht"
msgstr ""

#: modules/pricing/widgets/price-table.php:105
msgctxt "Currency"
msgid "Franc"
msgstr ""

#: modules/pricing/widgets/price-table.php:106
msgctxt "Currency"
msgid "Guilder"
msgstr ""

#: modules/pricing/widgets/price-table.php:107
msgctxt "Currency"
msgid "Krona"
msgstr ""

#: modules/pricing/widgets/price-table.php:108
msgctxt "Currency"
msgid "Lira"
msgstr ""

#: modules/pricing/widgets/price-table.php:109
msgctxt "Currency"
msgid "Peseta"
msgstr ""

#: modules/pricing/widgets/price-table.php:110
msgctxt "Currency"
msgid "Peso"
msgstr ""

#: modules/pricing/widgets/price-table.php:111
msgctxt "Currency"
msgid "Pound Sterling"
msgstr ""

#: modules/pricing/widgets/price-table.php:112
msgctxt "Currency"
msgid "Real"
msgstr ""

#: modules/pricing/widgets/price-table.php:113
msgctxt "Currency"
msgid "Ruble"
msgstr ""

#: modules/pricing/widgets/price-table.php:114
msgctxt "Currency"
msgid "Rupee"
msgstr ""

#: modules/pricing/widgets/price-table.php:115
msgctxt "Currency"
msgid "Rupee (Indian)"
msgstr ""

#: modules/pricing/widgets/price-table.php:116
msgctxt "Currency"
msgid "Shekel"
msgstr ""

#: modules/pricing/widgets/price-table.php:117
msgctxt "Currency"
msgid "Yen/Yuan"
msgstr ""

#: modules/pricing/widgets/price-table.php:118
msgctxt "Currency"
msgid "Won"
msgstr ""

#: modules/payments/widgets/paypal-button.php:245
msgid "Custom (Advanced)"
msgstr ""

#: modules/payments/widgets/paypal-button.php:254
msgid "PayPal Account"
msgstr ""

#: modules/payments/widgets/paypal-button.php:259
msgid "Transactions made through your PayPal button will be registered under this account."
msgstr ""

#: modules/payments/widgets/paypal-button.php:271
msgid "SDK Token"
msgstr ""

#: modules/payments/widgets/paypal-button.php:323
msgid "Sandbox Email Account"
msgstr ""

#: modules/payments/widgets/paypal-button.php:328
msgid "This is the address given to you by PayPal when you set up a sandbox with your developer account. You can use the sandbox to test your purchase flow."
msgstr ""

#: modules/forms/module.php:153
msgid "Never lose another submission! Using “Actions After Submit” you can now choose to save all submissions to an internal database."
msgstr ""

#: modules/forms/submissions/actions/save-to-database.php:27
#: modules/forms/submissions/actions/save-to-database.php:34
msgid "Collect Submissions"
msgstr ""

#: modules/forms/submissions/admin-menu-items/submissions-menu-item.php:17
#: modules/forms/submissions/admin-menu-items/submissions-menu-item.php:21
#: modules/forms/submissions/admin-menu-items/submissions-menu-item.php:39
#: modules/forms/submissions/admin-menu-items/submissions-promotion-menu-item.php:15
#: modules/forms/submissions/admin-menu-items/submissions-promotion-menu-item.php:19
#: modules/forms/submissions/component.php:81
#: modules/forms/submissions/component.php:154
msgid "Submissions"
msgstr ""

#: assets/js/form-submission-admin.js:4661
msgid "Unread"
msgstr ""

#: assets/js/form-submission-admin.js:4664
msgid "Read"
msgstr ""

#: assets/js/app.js:4093 assets/js/form-submission-admin.js:4667
#: assets/js/form-submission-admin.js:4776
#: core/app/modules/site-editor/assets/js/part-actions/dialogs-and-buttons.js:46
msgid "Trash"
msgstr ""

#: modules/forms/submissions/data/controller.php:291
#: modules/forms/submissions/data/controller.php:347
msgid "Submission not found."
msgstr ""

#: modules/forms/submissions/data/endpoints/export.php:81
msgid "There is nothing to export."
msgstr ""

#: modules/forms/submissions/data/endpoints/restore.php:117
msgid "Submission not found or not in trash."
msgstr ""

#: modules/forms/submissions/export/csv-export.php:88
msgid "Form Name (ID)"
msgstr ""

#: modules/forms/submissions/export/csv-export.php:89
msgid "Submission ID"
msgstr ""

#: modules/forms/submissions/export/csv-export.php:90
#: modules/forms/submissions/personal-data.php:63
#: modules/notes/user/personal-data.php:109
msgid "Created At"
msgstr ""

#: modules/forms/submissions/export/csv-export.php:91
msgid "User ID"
msgstr ""

#: modules/forms/submissions/export/csv-export.php:93
#: modules/forms/submissions/personal-data.php:51
msgid "User IP"
msgstr ""

#: modules/forms/submissions/export/csv-export.php:94
msgid "Referrer"
msgstr ""

#: modules/forms/submissions/personal-data.php:19
msgid "Elementor Submissions"
msgstr ""

#: modules/forms/submissions/personal-data.php:55
msgid "Referer"
msgstr ""

#: modules/forms/submissions/personal-data.php:67
msgid "Created At GMT"
msgstr ""

#: modules/forms/submissions/personal-data.php:71
msgid "Updated At"
msgstr ""

#: modules/forms/submissions/personal-data.php:75
msgid "Updated At GMT"
msgstr ""

#: modules/payments/classes/payment-button.php:94
msgid "No payment method connected. Contact seller."
msgstr ""

#: modules/forms/classes/ajax-handler.php:58
#: modules/payments/classes/payment-button.php:110
msgid "Unknown error."
msgstr ""

#: modules/payments/classes/payment-button.php:119
msgid "Transaction Type"
msgstr ""

#: modules/payments/classes/payment-button.php:124
msgid "Donation"
msgstr ""

#: modules/payments/classes/payment-button.php:125
msgid "Subscription"
msgstr ""

#: modules/payments/classes/payment-button.php:136
msgid "Item Name"
msgstr ""

#: modules/payments/classes/payment-button.php:174
msgid "Donation Amount"
msgstr ""

#: modules/payments/classes/payment-button.php:178
msgid "Any Amount"
msgstr ""

#: modules/payments/classes/payment-button.php:179
msgid "Fixed"
msgstr ""

#: modules/payments/classes/payment-button.php:190
msgid "Amount"
msgstr ""

#: modules/payments/classes/payment-button.php:206
#: modules/payments/widgets/stripe-button.php:328
msgid "Currency"
msgstr ""

#: modules/payments/classes/payment-button.php:216
msgid "Billing Cycle"
msgstr ""

#: modules/payments/classes/payment-button.php:220
msgid "Daily"
msgstr ""

#: modules/payments/classes/payment-button.php:221
msgid "Weekly"
msgstr ""

#: modules/payments/classes/payment-button.php:223
msgid "Yearly"
msgstr ""

#: modules/payments/classes/payment-button.php:235
msgid "Auto Renewal"
msgstr ""

#: modules/payments/classes/payment-button.php:260
#: modules/payments/widgets/stripe-button.php:387
msgid "Shipping Price"
msgstr ""

#: modules/payments/classes/payment-button.php:275
msgid "Tax"
msgstr ""

#: modules/payments/classes/payment-button.php:291
msgid "Tax Percentage"
msgstr ""

#: modules/payments/classes/payment-button.php:317
msgid "Redirect After Success"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:181
msgid "Paste URL or type"
msgstr ""

#: modules/payments/classes/payment-button.php:333
msgid "Sandbox"
msgstr ""

#. translators: %s: Merchant name.
#: modules/payments/classes/payment-button.php:348
msgid "Open %s In New Tab"
msgstr ""

#. translators: %s: Merchant name.
#: modules/payments/classes/payment-button.php:392
msgid "%s Not Connected"
msgstr ""

#. translators: %s: Merchant name.
#: modules/payments/classes/payment-button.php:479
msgid "%s Not Connected Color"
msgstr ""

#: modules/payments/widgets/paypal-button.php:39
msgid "PayPal Button"
msgstr ""

#: modules/payments/widgets/paypal-button.php:233
#: modules/payments/widgets/stripe-button.php:284
msgid "Pricing & Payments"
msgstr ""

#: modules/payments/widgets/paypal-button.php:240
msgid "Merchant Account"
msgstr ""

#: modules/payments/widgets/paypal-button.php:244
msgid "Default (Simple)"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:156
msgid "Define where the Custom Code will appear"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:185
#: modules/custom-code/module.php:373
msgid "Priority"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:187
msgid "Define in which order the Custom Code will appear"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:403
msgid "Custom code"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:418
msgid "Conditions:"
msgstr ""

#: modules/custom-code/admin-menu-items/custom-code-menu-item.php:18
#: modules/custom-code/admin-menu-items/custom-code-promotion-menu-item.php:38
#: modules/custom-code/admin-menu-items/custom-code-promotion-menu-item.php:42
#: modules/custom-code/document.php:25 modules/custom-code/module.php:72
#: modules/custom-code/module.php:135 modules/custom-code/module.php:136
msgid "Custom Code"
msgstr ""

#: modules/custom-code/module.php:137
msgid "Add new"
msgstr ""

#: modules/custom-code/module.php:138
msgid "New code"
msgstr ""

#: modules/custom-code/module.php:139
msgid "Edit code"
msgstr ""

#: modules/custom-code/module.php:174 modules/custom-code/module.php:177
msgid "Custom code updated."
msgstr ""

#: modules/custom-code/module.php:178
msgid "Revision restored."
msgstr ""

#: modules/custom-code/module.php:179
msgid "Custom code published."
msgstr ""

#: modules/custom-code/module.php:180
msgid "Custom code saved."
msgstr ""

#: modules/custom-code/module.php:181
msgid "Custom code submitted."
msgstr ""

#. translators: %s: The scheduled date.
#: modules/custom-code/module.php:184
msgid "Custom code scheduled for %s."
msgstr ""

#: modules/custom-code/module.php:185
msgid "M j, Y @ G:i"
msgstr ""

#: modules/custom-code/module.php:187
msgid "Custom code draft updated."
msgstr ""

#: modules/custom-code/module.php:199
msgid "%s custom code updated."
msgid_plural "%s custom codes updated."
msgstr[0] ""
msgstr[1] ""

#: modules/custom-code/module.php:200
msgid "%s custom code cannot be not updated, someone else is editing it."
msgid_plural "%s custom codes cannot be not updated, someone else is editing them."
msgstr[0] ""
msgstr[1] ""

#: modules/custom-code/module.php:201
msgid "%s custom code permanently deleted."
msgid_plural "%s custom codes permanently deleted."
msgstr[0] ""
msgstr[1] ""

#: modules/custom-code/module.php:202
msgid "%s custom code moved to trash."
msgid_plural "%s custom codes moved to trash."
msgstr[0] ""
msgstr[1] ""

#: modules/custom-code/module.php:203
msgid "%s custom code restored."
msgid_plural "%s custom code restored."
msgstr[0] ""
msgstr[1] ""

#: modules/popup/display-settings/timing.php:209
msgid "Show on browsers"
msgstr ""

#: modules/popup/display-settings/timing.php:217
msgid "All Browsers"
msgstr ""

#: modules/popup/display-settings/timing.php:230
msgid "Internet Explorer"
msgstr ""

#: modules/popup/display-settings/timing.php:231
msgid "Chrome"
msgstr ""

#: modules/popup/display-settings/timing.php:232
msgid "Edge"
msgstr ""

#: modules/popup/display-settings/timing.php:233
msgid "Firefox"
msgstr ""

#: modules/popup/display-settings/timing.php:234
msgid "Safari"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:559
msgid "Max Height"
msgstr ""

#: license/api.php:243
msgid "Another check is in progress."
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:203
#: modules/animated-headline/widgets/animated-headline.php:235
#: modules/video-playlist/widgets/video-playlist.php:210
#: modules/video-playlist/widgets/video-playlist.php:574
#: modules/video-playlist/widgets/video-playlist.php:844
#: modules/video-playlist/widgets/video-playlist.php:1051
msgid "Duration"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:220
msgid "Delay"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:462
msgid "Selected Text"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:474
msgid "Selection Color"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:20
#: modules/code-highlight/widgets/code-highlight.php:79
msgid "Code Highlight"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:138
msgid "Language"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:149
msgid "Code"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:164
msgid "Line Numbers"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:174
msgid "Copy to Clipboard"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:186
msgid "Highlight Lines"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:211
msgid "Theme"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:251
msgid "Font Size"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:57
msgid "Head"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:58
msgid "Body Start"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:59
msgid "Body End"
msgstr ""

#: assets/js/editor.js:258 assets/js/loop.cfa59b67362d5bf08739.bundle.js:97
#: assets/js/preloaded-elements-handlers.js:532 assets/js/preview.js:99
msgid "Edit %s"
msgstr ""

#: modules/woocommerce/documents/product.php:41
msgid "What is a Single Product Template?"
msgstr ""

#: modules/theme-builder/documents/single-page.php:31
msgid "What is a Single Page Template?"
msgstr ""

#: modules/theme-builder/documents/single-post.php:28
msgid "What is a Single Post Template?"
msgstr ""

#: modules/theme-builder/module.php:355
msgid "Meet the New Theme Builder: More Intuitive and Visual Than Ever"
msgstr ""

#: modules/theme-builder/module.php:356
msgid "With the new Theme Builder you can visually manage every part of your site intuitively, making the task of designing a complete website that much easier"
msgstr ""

#: modules/theme-builder/module.php:358
msgid "Try it Now"
msgstr ""

#: modules/woocommerce/documents/product-archive.php:49
msgid "What is a Products Archive Template?"
msgstr ""

#: modules/theme-builder/documents/error-404.php:32
msgid "What is a 404 Page Template?"
msgstr ""

#: modules/theme-builder/documents/footer.php:36
msgid "What is a Footer Template?"
msgstr ""

#: modules/theme-builder/documents/header.php:37
msgid "What is a Header Template?"
msgstr ""

#: core/app/modules/site-editor/data/endpoints/templates-conditions.php:48
msgid "Error while saving conditions."
msgstr ""

#: core/app/modules/site-editor/data/endpoints/templates-conditions.php:78
msgid "Cannot save those conditions."
msgstr ""

#. translators: %s: User display name.
#: core/app/modules/site-editor/data/responses/lock-error-response.php:16
msgid "%s is currently editing this template, please try again later"
msgstr ""

#: modules/theme-builder/documents/archive.php:39
msgid "What is an Archive Template?"
msgstr ""

#: modules/theme-builder/documents/single-post.php:15
msgid "Single Post"
msgstr ""

#: modules/theme-builder/documents/single-post.php:29
msgid "A single post template allows you to easily design the layout and style of posts, ensuring a design consistency throughout all your blog posts, for example."
msgstr ""

#: modules/theme-builder/documents/single-post.php:30
msgid "You can create multiple single post templates, and assign each to a different category."
msgstr ""

#: modules/theme-builder/documents/theme-document.php:75
msgid "Congrats! Your Site Part is Live"
msgstr ""

#: assets/js/editor.js:5804
msgid "Open Site Editor"
msgstr ""

#: assets/js/editor.js:5810
msgid "View Live Site"
msgstr ""

#: modules/woocommerce/documents/product-archive.php:27
msgid "Products Archive"
msgstr ""

#: modules/woocommerce/documents/product-archive.php:50
msgid "A products archive template allows you to easily design the layout and style of your WooCommerce shop page or other product archive pages - those pages that show a list of products, which may be filtered by terms such as categories, tags, etc."
msgstr ""

#: modules/woocommerce/documents/product-archive.php:51
msgid "You can create multiple products archive templates, and assign each to different categories of products. This gives you the freedom to customize the appearance for each type of product being shown."
msgstr ""

#: modules/woocommerce/documents/product.php:42
msgid "A single product template allows you to easily design the layout and style of WooCommerce single product pages, and apply that template to various conditions that you assign."
msgstr ""

#: modules/woocommerce/documents/product.php:43
msgid "You can create multiple single product templates, and assign each to different types of products, enabling a custom design for each group of similar products."
msgstr ""

#: modules/theme-builder/documents/archive.php:41
msgid "If you’d like a different style for a specific category, it’s easy to create a separate archive template whose condition is to only display when users are viewing that category’s list of posts."
msgstr ""

#: modules/theme-builder/documents/error-404.php:19
#: modules/theme-builder/documents/error-404.php:23
msgid "Error 404"
msgstr ""

#: modules/theme-builder/documents/error-404.php:33
msgid "A 404 page template allows you to easily design the layout and style of the page that is displayed when a visitor arrives at a page that does not exist."
msgstr ""

#: modules/theme-builder/documents/error-404.php:34
msgid "Keep your site's visitors happy when they get lost by displaying your recent posts, a search bar, or any information that might help the user find what they were looking for."
msgstr ""

#: modules/theme-builder/documents/footer.php:37
msgid "The footer template allows you to easily design and edit custom WordPress footers without the limits of your theme’s footer design constraints"
msgstr ""

#: modules/theme-builder/documents/footer.php:38
msgid "You can create multiple footers, and assign each to different areas of your site."
msgstr ""

#: modules/theme-builder/documents/header.php:38
msgid "The header template allows you to easily design and edit custom WordPress headers so you are no longer constrained by your theme’s header design limitations."
msgstr ""

#: modules/theme-builder/documents/header.php:39
msgid "You can create multiple headers, and assign each to different areas of your site."
msgstr ""

#: modules/theme-builder/documents/search-results.php:36
msgid "What is a Search Results Template?"
msgstr ""

#: modules/theme-builder/documents/search-results.php:37
msgid "You can easily control the layout and design of the Search Results page with the Search Results template, which is simply a special archive template just for displaying search results."
msgstr ""

#: modules/theme-builder/documents/search-results.php:38
msgid "You can customize the message if there are no results for the search term."
msgstr ""

#: modules/theme-builder/documents/single-page.php:18
msgid "Single Page"
msgstr ""

#: modules/theme-builder/documents/single-page.php:32
msgid "A single page template allows you to easily create the layout and style of pages, ensuring design consistency across all the pages of your site."
msgstr ""

#: modules/theme-builder/documents/single-page.php:33
msgid "You can create multiple single page templates, and assign each to different areas of your site."
msgstr ""

#: core/app/modules/site-editor/data/endpoints/templates.php:156
#: assets/js/app.js:2360 assets/js/custom-code.js:568
#: core/app/modules/site-editor/assets/js/context/services/conditions-config.js:123
msgid "No instances"
msgstr ""

#: modules/posts/skins/skin-base.php:375
msgid "Date Modified"
msgstr ""

#: modules/theme-builder/documents/archive.php:40
msgid "An archive template allows you to easily design the layout and style of archive pages - those pages that show a list of posts (e.g. a blog’s list of recent posts), which may be filtered by terms such as authors, categories, tags, search results, etc."
msgstr ""

#: modules/lottie/widgets/lottie.php:327
msgid "Link Timeout"
msgstr ""

#: modules/lottie/widgets/lottie.php:400
msgid "Play Speed"
msgstr ""

#. translators: %s: Renewal discount.
#: core/editor/notice-bar.php:94
msgid "Your Elementor Pro license is about to expire. Renew now and get an exclusive, time-limited %s discount."
msgstr ""

#: core/editor/notice-bar.php:98
msgid "Your Elementor Pro license is about to expire. Renew now and get updates, support, Pro widgets & templates for another year."
msgstr ""

#: modules/forms/fields/step.php:20
msgid "Step"
msgstr ""

#: modules/forms/fields/step.php:65 modules/forms/widgets/form.php:1415
#: modules/forms/widgets/form.php:1524
msgid "Previous Button"
msgstr ""

#: modules/forms/fields/step.php:79
msgid "Next Button"
msgstr ""

#: modules/forms/fields/step.php:96
msgid "Visible only if selected step type contains \"Icon\""
msgstr ""

#: modules/forms/widgets/form.php:653
msgid "Step Buttons"
msgstr ""

#: modules/forms/widgets/form.php:830
msgid "Steps Settings"
msgstr ""

#: modules/forms/widgets/form.php:887
msgid "Display Percentage"
msgstr ""

#: modules/forms/widgets/form.php:1362 modules/forms/widgets/form.php:1473
msgid "Next & Submit Button"
msgstr ""

#: modules/forms/widgets/form.php:1664
msgid "Steps"
msgstr ""

#: modules/forms/widgets/form.php:1788
msgid "Inactive"
msgstr ""

#: modules/forms/widgets/form.php:1858
msgid "Completed"
msgstr ""

#: modules/forms/widgets/form.php:1898
msgid "Divider Width"
msgstr ""

#: modules/forms/widgets/form.php:1923
msgid "Divider Gap"
msgstr ""

#: modules/forms/widgets/form.php:2033
#: modules/payments/classes/payment-button.php:280
#: modules/progress-tracker/widgets/progress-tracker.php:131
#: modules/progress-tracker/widgets/progress-tracker.php:554
msgid "Percentage"
msgstr ""

#: modules/lottie/widgets/lottie.php:33 modules/lottie/widgets/lottie.php:50
#: modules/lottie/widgets/lottie.php:513
msgid "Lottie"
msgstr ""

#: modules/lottie/widgets/lottie.php:61 modules/lottie/widgets/lottie.php:70
#: modules/video-playlist/widgets/video-playlist.php:141
msgid "External URL"
msgstr ""

#: modules/lottie/widgets/lottie.php:78 modules/lottie/widgets/lottie.php:187
#: modules/video-playlist/widgets/video-playlist.php:115
#: modules/video-playlist/widgets/video-playlist.php:185
msgid "Enter your URL"
msgstr ""

#: modules/lottie/widgets/lottie.php:86
msgid "Upload JSON File"
msgstr ""

#: modules/lottie/widgets/lottie.php:143
msgid "Custom Caption"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:522
#: modules/lottie/widgets/lottie.php:212
msgid "Trigger"
msgstr ""

#: modules/lottie/widgets/lottie.php:218
#: modules/woocommerce/widgets/menu-cart.php:202
msgid "On Hover"
msgstr ""

#: modules/lottie/widgets/lottie.php:219
msgid "Scroll"
msgstr ""

#: modules/lottie/widgets/lottie.php:267
#: modules/motion-fx/controls-group.php:160
msgid "Effects Relative To"
msgstr ""

#: modules/lottie/widgets/lottie.php:285
msgid "Loop"
msgstr ""

#: modules/loop-builder/widgets/loop-carousel.php:189
msgid "Number of slides"
msgstr ""

#: modules/lottie/widgets/lottie.php:350
msgid "Redirect to link after selected timeout"
msgstr ""

#: modules/lottie/widgets/lottie.php:361
msgid "On Hover Out"
msgstr ""

#: modules/lottie/widgets/lottie.php:370 modules/lottie/widgets/lottie.php:457
msgid "Reverse"
msgstr ""

#: modules/lottie/widgets/lottie.php:371
msgid "Pause"
msgstr ""

#: modules/lottie/widgets/lottie.php:380
msgid "Hover Area"
msgstr ""

#: modules/lottie/widgets/lottie.php:389
msgid "Column"
msgstr ""

#: modules/lottie/widgets/lottie.php:427
msgid "Start Point"
msgstr ""

#: modules/lottie/widgets/lottie.php:442
msgid "End Point"
msgstr ""

#: modules/lottie/widgets/lottie.php:484
msgid "Renderer"
msgstr ""

#: modules/lottie/widgets/lottie.php:488
msgid "SVG"
msgstr ""

#: modules/carousel/widgets/base.php:273
msgid "Lazyload"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:263
msgid "Could not unzip or empty archive."
msgstr ""

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:36
#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:165
msgid "Adobe Fonts (TypeKit)"
msgstr ""

#: assets/js/admin.js:410
msgid "Choose a font to publish."
msgstr ""

#: assets/js/admin.js:192
msgid "Upload an icon set to publish."
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-color.php:18
msgid "Color Picker Field"
msgstr ""

#: modules/posts/skins/skin-base.php:343
msgid "Open in new window"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:79
msgctxt "CPT Name"
msgid "Custom Fonts"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:80
msgctxt "CPT Singular Name"
msgid "Font"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:81
#: modules/assets-manager/asset-types/icons-manager.php:74
#: assets/js/app.js:3858
#: core/app/modules/site-editor/assets/js/pages/template-type.js:24
msgid "Add New"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:82
msgid "Add New Font"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:83
msgid "Edit Font"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:84
msgid "New Font"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:85
msgid "All Fonts"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:86
msgid "View Font"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:87
msgid "Search Font"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:88
msgid "No fonts found"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:89
msgid "No fonts found in trash"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:91
msgctxt "CPT Menu Name"
msgid "Custom Fonts"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:72
msgctxt "CPT Name"
msgid "Custom Icons"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:73
msgctxt "CPT Singular Name"
msgid "Icon Set"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:75
msgid "Add New Icon Set"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:76
msgid "Edit Icon Set"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:77
msgid "New Icon Set"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:78
msgid "All Icons"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:79
msgid "View Icon"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:80
msgid "Search Icon Set"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:81
msgid "No icons found"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:82
msgid "No icons found in trash"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:84
msgctxt "CPT Menu Name"
msgid "Custom Icons"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:350
msgid "The \"Collapse\" option should only be used if the Table of Contents is made sticky"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:537
msgid "Separator Width"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:704
msgid "Marker"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:81
msgid "Font Awesome - Duotone Pro"
msgstr ""

#: modules/gallery/widgets/gallery.php:150
#: modules/lottie/widgets/lottie.php:499
#: modules/video-playlist/widgets/video-playlist.php:626
msgid "Lazy Load"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:538
#: modules/mega-menu/widgets/mega-menu.php:1361
#: modules/nav-menu/widgets/nav-menu.php:1480
msgid "Menu Toggle"
msgstr ""

#: assets/js/preloaded-elements-handlers.js:8314
#: assets/js/table-of-contents.a5b4da6c85e3d3fbaa67.bundle.js:278
msgid "No headings were found on this page."
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:26
#: modules/table-of-contents/widgets/table-of-contents.php:65
#: modules/table-of-contents/widgets/table-of-contents.php:78
msgid "Table of Contents"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:110
msgid "Anchors By Tags"
msgstr ""

#: modules/lottie/widgets/lottie.php:391
#: modules/table-of-contents/widgets/table-of-contents.php:130
msgid "Container"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:136
msgid "This control confines the Table of Contents to heading elements under a specific container"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:152
msgid "Anchors By Selector"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:154
msgid "CSS selectors, in a comma-separated list"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:168
msgid "Marker View"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:173
msgid "Bullets"
msgstr ""

#: modules/code-highlight/widgets/code-highlight.php:199
#: modules/table-of-contents/widgets/table-of-contents.php:223
msgid "Word Wrap"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:233
msgid "Minimize Box"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:272
msgid "Minimize Icon"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:323
msgid "Minimized On"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:348
msgid "Collapse Subitems"
msgstr ""

#: core/admin/admin.php:180 core/admin/admin.php:182
msgid "Reinstall"
msgstr ""

#: core/admin/admin.php:197
msgid "Error occurred, The version selected is invalid. Try selecting different version."
msgstr ""

#: modules/blockquote/widgets/blockquote.php:103
#: modules/carousel/widgets/reviews.php:655
#: modules/carousel/widgets/reviews.php:668
#: modules/carousel/widgets/reviews.php:676
#: modules/carousel/widgets/reviews.php:684
#: modules/carousel/widgets/testimonial-carousel.php:573
#: modules/carousel/widgets/testimonial-carousel.php:581
#: modules/carousel/widgets/testimonial-carousel.php:589
#: modules/slides/widgets/slides.php:258
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr ""

#: modules/blockquote/widgets/blockquote.php:103
#: modules/call-to-action/widgets/call-to-action.php:275
#: modules/flip-box/widgets/flip-box.php:168
#: modules/flip-box/widgets/flip-box.php:293
#: modules/pricing/widgets/price-list.php:116
#: modules/pricing/widgets/price-list.php:122
#: modules/pricing/widgets/price-list.php:128
#: modules/slides/widgets/slides.php:489 modules/slides/widgets/slides.php:495
#: modules/slides/widgets/slides.php:501
#: modules/theme-elements/widgets/author-box.php:235
msgid "Lorem ipsum dolor sit amet consectetur adipiscing elit dolor"
msgstr ""

#: modules/dynamic-tags/pods/tags/pods-numeric.php:17
msgid "Numeric"
msgstr ""

#: modules/dynamic-tags/tags/post-custom-field.php:57
msgid "Custom Key"
msgstr ""

#: modules/dynamic-tags/tags/user-profile-picture.php:13
msgid "User Profile Picture"
msgstr ""

#: modules/forms/actions/mailchimp.php:127
msgid "Add comma separated tags"
msgstr ""

#: modules/forms/actions/mailerlite.php:108
msgid "Allow Resubscribe"
msgstr ""

#: modules/forms/classes/recaptcha-v3-handler.php:118
msgid "reCAPTCHA V3 validation failed, suspected as abusive usage"
msgstr ""

#: modules/forms/widgets/form.php:1177
msgid "HTML Field"
msgstr ""

#: modules/gallery/widgets/gallery.php:39
msgid "Gallery"
msgstr ""

#: modules/gallery/widgets/gallery.php:98
#: modules/gallery/widgets/gallery.php:124
msgid "New Gallery"
msgstr ""

#: modules/gallery/widgets/gallery.php:119
msgid "Galleries"
msgstr ""

#: modules/gallery/widgets/gallery.php:164
msgid "Grid"
msgstr ""

#: modules/gallery/widgets/gallery.php:215
msgid "Row Height"
msgstr ""

#: modules/gallery/widgets/gallery.php:308
msgid "Aspect Ratio"
msgstr ""

#: modules/gallery/widgets/gallery.php:350
msgid "\"All\" Filter"
msgstr ""

#: modules/gallery/widgets/gallery.php:360
msgid "\"All\" Filter Label"
msgstr ""

#: modules/posts/skins/skin-content-base.php:28
msgid "Full Content"
msgstr ""

#: modules/posts/skins/skin-content-base.php:45
msgid "Show Thumbnail"
msgstr ""

#: modules/woocommerce/module.php:538
msgid "Mini Cart Template"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:704
#: modules/woocommerce/module.php:544
msgid "Disable"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:705
#: modules/woocommerce/module.php:545
msgid "Enable"
msgstr ""

#: modules/woocommerce/module.php:547
msgid "Set to `Disable` in order to use your Theme's or WooCommerce's mini-cart template instead of Elementor's."
msgstr ""

#: modules/woocommerce/widgets/add-to-cart.php:81
msgid "Please note that switching on this option will disable some of the design controls."
msgstr ""

#: modules/forms/widgets/form.php:369
msgid "To view the validation badge, switch to preview mode"
msgstr ""

#: license/admin.php:37 license/admin.php:172 license/admin.php:517
#: license/admin.php:550 modules/announcements/module.php:45
msgid "Renew Now"
msgstr ""

#: core/editor/notice-bar.php:72
msgid "Activate Your License and Get Access to Premium Elementor Templates, Support & Plugin Updates."
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:203
msgid "Invalid SVG Format, file not uploaded for security reasons"
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-icons-promotion-menu-item.php:38
#: modules/assets-manager/asset-types/admin-menu-items/custom-icons-promotion-menu-item.php:42
#: modules/assets-manager/asset-types/icons-manager.php:185
#: modules/assets-manager/asset-types/icons/custom-icons.php:23
msgid "Custom Icons"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:33
#: modules/assets-manager/asset-types/icons/custom-icons.php:403
msgid "Icon Set"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:65
msgid "Your Fontello, IcoMoon or Fontastic .zip file"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:200
msgid "Only zip files are allowed"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:287
msgid "Incompatible archive"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:336
msgid "The zip file provided is not supported!"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:404
#: modules/assets-manager/asset-types/icons/templates.php:14
msgid "CSS Prefix"
msgstr ""

#: modules/assets-manager/asset-types/icons/custom-icons.php:410
msgid "Enter Icon Set Name"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:18
#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:103
msgid "Font Awesome Pro"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:33
msgid "Font Awesome - Regular Pro"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:45
msgid "Font Awesome - Solid Pro"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:57
msgid "Font Awesome - Brands Pro"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:69
msgid "Font Awesome - Light Pro"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:104
msgid "Font Awesome, the web's most popular icon set and toolkit, Pro Integration"
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:128
msgid "Validate Kit ID"
msgstr ""

#: modules/assets-manager/asset-types/icons/icon-sets/fontastic.php:24
msgid "Fontastic"
msgstr ""

#: modules/assets-manager/asset-types/icons/icon-sets/fontello.php:23
msgid "Fontello"
msgstr ""

#: modules/assets-manager/asset-types/icons/icon-sets/icomoon.php:23
msgid "Icomoon"
msgstr ""

#: modules/assets-manager/asset-types/icons/templates.php:8
msgid "Created on"
msgstr ""

#: modules/assets-manager/asset-types/icons/templates.php:15
msgid "Icons Count"
msgstr ""

#: modules/assets-manager/asset-types/icons/templates.php:20
msgid "The Icon Set prefix already exists in your site. In order to avoid conflicts we recommend to use a unique prefix per Icon Set."
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:106
#: modules/assets-manager/asset-types/icons-manager.php:109
#: modules/assets-manager/asset-types/icons-manager.php:115
msgid "Icon Set updated."
msgstr ""

#. translators: %s: Date and time of the revision.
#: modules/assets-manager/asset-types/icons-manager.php:111
msgid "Icon Set restored to revision from %s"
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:112
#: modules/assets-manager/asset-types/icons-manager.php:113
msgid "Icon Set saved."
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:114
msgid "Icon Set submitted."
msgstr ""

#: modules/assets-manager/asset-types/icons-manager.php:116
msgid "Icon Set draft updated."
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-icons-menu-item.php:18
#: modules/assets-manager/asset-types/icons-manager.php:219
msgctxt "Elementor Font"
msgid "Custom Icons"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:217
msgid "Drag & Drop to Upload"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:230
msgid "Click here to browse"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:233
msgid "Uploading&hellip;"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:234
msgid "Done!"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:235
msgid "Error!"
msgstr ""

#: modules/forms/actions/mailchimp.php:86
msgid "Audience"
msgstr ""

#: modules/forms/classes/recaptcha-handler.php:173
msgid "Invalid form, reCAPTCHA validation failed."
msgstr ""

#: modules/forms/classes/recaptcha-v3-handler.php:52
msgid "To use reCAPTCHA V3, you need to add the API Key and complete the setup process in Dashboard > Elementor > Settings > Integrations > reCAPTCHA V3."
msgstr ""

#: modules/forms/classes/recaptcha-v3-handler.php:57
#: modules/forms/classes/recaptcha-v3-handler.php:128
msgid "reCAPTCHA V3"
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/forms/classes/recaptcha-v3-handler.php:61
msgid "%1$sreCAPTCHA V3%2$s is a free service by Google that protects your website from spam and abuse. It does this while letting your valid users pass through with ease."
msgstr ""

#: modules/forms/classes/recaptcha-v3-handler.php:80
msgid "Score Threshold"
msgstr ""

#: modules/forms/classes/recaptcha-v3-handler.php:90
msgid "Score threshold should be a value between 0 and 1, default: 0.5"
msgstr ""

#: modules/forms/widgets/form.php:365
msgid "Bottom Right"
msgstr ""

#: modules/forms/widgets/form.php:366
msgid "Bottom Left"
msgstr ""

#: modules/lottie/widgets/lottie.php:276
#: modules/motion-fx/controls-group.php:165
#: modules/progress-tracker/widgets/progress-tracker.php:70
msgid "Entire Page"
msgstr ""

#: assets/js/editor.js:3959
msgid "Motion Effects"
msgstr ""

#: modules/page-transitions/module.php:231 modules/popup/document.php:374
msgid "Exit Animation"
msgstr ""

#: modules/query-control/controls/group-control-query.php:113
msgid "Terms are items in a taxonomy. The available taxonomies are: Categories, Tags, Formats and custom taxonomies."
msgstr ""

#. translators: %s: Singular taxonomy label.
#: modules/theme-builder/conditions/any-child-of-term.php:18
msgid "Any Child %s Of"
msgstr ""

#. translators: %s: Singular taxonomy label.
#: modules/theme-builder/conditions/child-of-term.php:18
msgid "Direct Child %s Of"
msgstr ""

#: modules/woocommerce/widgets/products.php:117
msgid "Ordering is not available if this widget is placed in your front page. Visible on frontend only."
msgstr ""

#: modules/theme-builder/views/panel-template.php:64 assets/js/app.js:3674
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions.js:32
msgid "For example, choose 'Entire Site' to display the template across your site."
msgstr ""

#: license/admin.php:274
msgid "Want to deactivate the license for any reason?"
msgstr ""

#. translators: %s: Example license key.
#: license/admin.php:624
msgid "Your license key should look something like this: %s"
msgstr ""

#. translators: %s: The [all-fields] shortcode.
#: modules/forms/actions/email.php:88
msgid "By default, all form fields are sent via %s shortcode. To customize sent fields, copy the shortcode that appears inside each field and paste it above."
msgstr ""

#. translators: 1: Link opening tag, 2: Link closing tag.
#: modules/forms/classes/recaptcha-handler.php:56
msgid "%1$sreCAPTCHA%2$s is a free service by Google that protects your website from spam and abuse. It does this while letting your valid users pass through with ease."
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:96
msgid "There are no menus in your site."
msgstr ""

#: modules/theme-builder/views/panel-template.php:58
msgid "Set the conditions that determine where your %s is used throughout your site."
msgstr ""

#: modules/query-control/controls/group-control-query.php:323
msgid "Setting a ‘Before’ date will show all the posts published until the chosen date (inclusive)."
msgstr ""

#: modules/query-control/controls/group-control-query.php:340
msgid "Setting an ‘After’ date will show all the posts published since the chosen date (inclusive)."
msgstr ""

#: modules/motion-fx/controls-group.php:75
msgid "X Anchor Point"
msgstr ""

#: modules/motion-fx/controls-group.php:101
msgid "Y Anchor Point"
msgstr ""

#: modules/motion-fx/controls-group.php:146
msgid "Apply Effects On"
msgstr ""

#: modules/motion-fx/controls-group.php:175
msgid "Mouse Effects"
msgstr ""

#: modules/motion-fx/controls-group.php:198
msgid "Vertical Scroll"
msgstr ""

#: modules/motion-fx/controls-group.php:209
#: modules/motion-fx/controls-group.php:252
#: modules/motion-fx/controls-group.php:388
#: modules/motion-fx/controls-group.php:434
#: modules/motion-fx/controls-group.php:483
#: modules/motion-fx/controls-group.php:510
msgid "Speed"
msgstr ""

#: modules/lottie/widgets/lottie.php:216 modules/lottie/widgets/lottie.php:229
#: modules/lottie/widgets/lottie.php:275
#: modules/motion-fx/controls-group.php:164
#: modules/motion-fx/controls-group.php:222
#: modules/motion-fx/controls-group.php:265
#: modules/motion-fx/controls-group.php:312
#: modules/motion-fx/controls-group.php:358
#: modules/motion-fx/controls-group.php:401
#: modules/motion-fx/controls-group.php:447
msgid "Viewport"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:354
#: modules/mega-menu/widgets/mega-menu.php:700
#: modules/motion-fx/controls-group.php:241
msgid "Horizontal Scroll"
msgstr ""

#: modules/motion-fx/controls-group.php:247
#: modules/motion-fx/controls-group.php:383
msgid "To Left"
msgstr ""

#: modules/motion-fx/controls-group.php:248
#: modules/motion-fx/controls-group.php:384
msgid "To Right"
msgstr ""

#: modules/motion-fx/controls-group.php:284
msgid "Transparency"
msgstr ""

#: modules/motion-fx/controls-group.php:298
#: modules/motion-fx/controls-group.php:345
msgid "Level"
msgstr ""

#: modules/motion-fx/controls-group.php:377
#: modules/page-transitions/module.php:560
msgid "Rotate"
msgstr ""

#: modules/motion-fx/controls-group.php:420
msgid "Scale"
msgstr ""

#: modules/motion-fx/controls-group.php:471
msgid "Mouse Track"
msgstr ""

#: modules/motion-fx/controls-group.php:478
#: modules/motion-fx/controls-group.php:506
msgid "Opposite"
msgstr ""

#: modules/motion-fx/controls-group.php:479
#: modules/motion-fx/controls-group.php:505
msgid "Direct"
msgstr ""

#: modules/motion-fx/controls-group.php:498
msgid "3D Tilt"
msgstr ""

#: modules/popup/display-settings/timing.php:93
msgid "On Open"
msgstr ""

#: modules/popup/display-settings/timing.php:94
msgid "On Close"
msgstr ""

#: modules/popup/document.php:768
msgid "Open By Selector"
msgstr ""

#: modules/popup/document.php:770
msgid "#id, .class"
msgstr ""

#: modules/popup/document.php:771
msgid "In order to open a popup on selector click, please set your Popup Conditions"
msgstr ""

#: modules/query-control/controls/group-control-query.php:93
msgid "Include By"
msgstr ""

#: modules/query-control/controls/group-control-query.php:97
#: modules/query-control/controls/group-control-query.php:112
#: modules/query-control/controls/group-control-query.php:175
#: modules/query-control/controls/group-control-query.php:211
#: modules/query-control/controls/group-control-related.php:40
#: modules/woocommerce/traits/products-trait.php:82
#: modules/woocommerce/traits/products-trait.php:87
msgid "Term"
msgstr ""

#: modules/query-control/controls/group-control-query.php:169
msgid "Exclude By"
msgstr ""

#: modules/query-control/controls/group-control-query.php:259
msgid "Set to Yes to avoid duplicate posts from showing up. This only effects the frontend."
msgstr ""

#: modules/query-control/controls/group-control-query.php:291
msgid "Past Day"
msgstr ""

#: modules/query-control/controls/group-control-query.php:292
msgid "Past Week"
msgstr ""

#: modules/query-control/controls/group-control-query.php:293
msgid "Past Month"
msgstr ""

#: modules/query-control/controls/group-control-query.php:294
msgid "Past Quarter"
msgstr ""

#: modules/query-control/controls/group-control-query.php:295
msgid "Past Year"
msgstr ""

#: modules/query-control/controls/group-control-query.php:383
msgid "Ignore Sticky Posts"
msgstr ""

#: modules/query-control/controls/group-control-query.php:389
msgid "Sticky-posts ordering is visible on frontend only"
msgstr ""

#: modules/query-control/controls/group-control-related.php:31
msgid "Related"
msgstr ""

#: modules/query-control/controls/group-control-related.php:64
msgid "Displayed if no relevant results are found. Manual selection display order is random"
msgstr ""

#: modules/woocommerce/widgets/archive-products-deprecated.php:26
msgid "Archive Products (deprecated)"
msgstr ""

#: modules/woocommerce/widgets/archive-products-deprecated.php:54
msgid "Note that these layout settings will override settings made in Appearance > Customize"
msgstr ""

#: modules/woocommerce/widgets/archive-products.php:75
msgid "Looks like you are using WooCommerce, while your theme does not support it. Please consider switching themes."
msgstr ""

#: modules/woocommerce/widgets/archive-products.php:85
msgid "To change the Products Archive’s layout, go to Appearance > Customize."
msgstr ""

#: modules/woocommerce/widgets/archive-products.php:94
msgid "The editor preview might look different from the live site. Please make sure to check the frontend."
msgstr ""

#: modules/forms/actions/discord.php:170
msgid "Elementor Forms"
msgstr ""

#: modules/global-widget/views/panel-template.php:25 assets/js/app.js:2149
#: assets/js/app.js:2510 assets/js/custom-code.js:357
#: core/app/modules/site-editor/assets/js/context/conditions.js:284
#: core/app/modules/site-editor/assets/js/context/templates.js:140
msgid "Loading"
msgstr ""

#. translators: %s: Connected user.
#: license/admin.php:252
msgid "You're connected as %s."
msgstr ""

#: license/admin.php:276
msgid "Disconnect"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:352
msgid "Play"
msgstr ""

#: modules/dynamic-tags/tags/user-info.php:19
msgid "User Info"
msgstr ""

#: modules/dynamic-tags/tags/user-info.php:78
#: modules/forms/widgets/form.php:439 modules/notes/user/personal-data.php:93
#: modules/theme-elements/widgets/sitemap.php:263
#: assets/js/form-submission-admin.js:4863
msgid "ID"
msgstr ""

#: modules/dynamic-tags/tags/user-info.php:86
msgid "User Meta"
msgstr ""

#: modules/forms/widgets/form.php:700 modules/forms/widgets/form.php:2304
#: modules/forms/widgets/form.php:2533
#: modules/theme-elements/widgets/search-form.php:929
msgid "Submit"
msgstr ""

#: assets/js/form-submission-admin.js:5153
msgid "Loading..."
msgstr ""

#: modules/popup/display-settings/timing.php:31
msgid "Show after X page views"
msgstr ""

#: modules/popup/display-settings/timing.php:37
msgid "Page Views"
msgstr ""

#: modules/popup/display-settings/timing.php:45
msgid "Show after X sessions"
msgstr ""

#: modules/popup/display-settings/timing.php:51
msgid "Sessions"
msgstr ""

#: modules/popup/display-settings/timing.php:59
msgid "Show up to X times"
msgstr ""

#: modules/lottie/widgets/lottie.php:300
#: modules/popup/display-settings/timing.php:65
msgid "Times"
msgstr ""

#: modules/popup/display-settings/timing.php:101
msgid "When arriving from specific URL"
msgstr ""

#: modules/popup/display-settings/timing.php:111
msgid "Regex"
msgstr ""

#: modules/popup/display-settings/timing.php:126
msgid "Show when arriving from"
msgstr ""

#: modules/popup/display-settings/timing.php:135
msgid "Search Engines"
msgstr ""

#: modules/popup/display-settings/timing.php:136
msgid "External Links"
msgstr ""

#: modules/popup/display-settings/timing.php:137
msgid "Internal Links"
msgstr ""

#: modules/popup/display-settings/timing.php:144
msgid "Hide for logged in users"
msgstr ""

#: modules/popup/display-settings/timing.php:152
msgid "All Users"
msgstr ""

#: modules/popup/display-settings/timing.php:172
msgid "Select Roles"
msgstr ""

#: modules/popup/display-settings/timing.php:195
msgid "Show on devices"
msgstr ""

#: modules/popup/display-settings/triggers.php:30
msgid "On Page Load"
msgstr ""

#: modules/popup/display-settings/triggers.php:36
#: modules/popup/display-settings/triggers.php:64
#: modules/popup/display-settings/triggers.php:109
msgid "Within"
msgstr ""

#: modules/popup/display-settings/triggers.php:45
msgid "On Scroll"
msgstr ""

#: modules/popup/display-settings/triggers.php:76
msgid "On Scroll To Element"
msgstr ""

#: modules/popup/display-settings/triggers.php:82
#: modules/progress-tracker/widgets/progress-tracker.php:72
#: modules/progress-tracker/widgets/progress-tracker.php:81
msgid "Selector"
msgstr ""

#: modules/lottie/widgets/lottie.php:217
#: modules/popup/display-settings/triggers.php:89
#: modules/woocommerce/widgets/menu-cart.php:201
msgid "On Click"
msgstr ""

#: modules/popup/display-settings/triggers.php:94
msgid "Clicks"
msgstr ""

#: modules/popup/display-settings/triggers.php:103
msgid "After Inactivity"
msgstr ""

#: modules/popup/display-settings/triggers.php:118
msgid "On Page Exit Intent"
msgstr ""

#: modules/popup/document.php:45 modules/popup/document.php:424
#: modules/popup/form-action.php:20 modules/popup/form-action.php:27
#: modules/popup/form-action.php:50 modules/popup/module.php:88
#: modules/popup/tag.php:21 modules/popup/tag.php:65
msgid "Popup"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:272 modules/popup/document.php:195
msgid "Fit To Content"
msgstr ""

#: modules/popup/document.php:196
msgid "Fit To Screen"
msgstr ""

#: modules/popup/document.php:211
msgid "Custom Height"
msgstr ""

#: modules/popup/document.php:239
msgid "Content Position"
msgstr ""

#: modules/popup/document.php:349 modules/popup/document.php:514
msgid "Close Button"
msgstr ""

#: modules/page-transitions/module.php:204 modules/popup/document.php:364
msgid "Entrance Animation"
msgstr ""

#: modules/popup/document.php:693
msgid "Show Close Button After"
msgstr ""

#: modules/popup/document.php:708
msgid "Automatically Close After"
msgstr ""

#: modules/popup/document.php:720
msgid "Prevent Closing on Overlay"
msgstr ""

#: modules/popup/document.php:729
msgid "Prevent Closing on ESC key"
msgstr ""

#: modules/popup/document.php:738
msgid "Disable Page Scrolling"
msgstr ""

#: modules/popup/document.php:747
msgid "Avoid Multiple Popups"
msgstr ""

#: modules/popup/document.php:749
msgid "If the user has seen another popup on the page hide this popup"
msgstr ""

#: modules/popup/form-action.php:41 modules/popup/tag.php:55
msgid "Open Popup"
msgstr ""

#: modules/popup/form-action.php:42 modules/popup/tag.php:56
msgid "Close Popup"
msgstr ""

#: modules/popup/form-action.php:74 modules/popup/tag.php:90
msgid "Don't Show Again"
msgstr ""

#: modules/popup/admin-menu-items/popups-menu-item.php:18
#: modules/popup/admin-menu-items/popups-promotion-menu-item.php:45
#: modules/popup/admin-menu-items/popups-promotion-menu-item.php:49
#: modules/popup/document.php:49 modules/popup/module.php:137
#: modules/popup/module.php:159 modules/popup/module.php:167
#: assets/js/editor.js:4348
msgid "Popups"
msgstr ""

#: assets/js/editor.js:4509
#: assets/js/packages/editor-documents-extended/editor-documents-extended.js:233
#: assets/js/packages/editor-documents-extended/editor-documents-extended.strings.js:1
msgid "Triggers"
msgstr ""

#: assets/js/editor.js:4514
#: assets/js/packages/editor-documents-extended/editor-documents-extended.js:251
#: assets/js/packages/editor-documents-extended/editor-documents-extended.strings.js:2
msgid "Advanced Rules"
msgstr ""

#: assets/js/editor.js:4510
msgid "What action the user needs to do for the popup to open."
msgstr ""

#: assets/js/editor.js:4515
msgid "Requirements that have to be met for the popup to open."
msgstr ""

#: modules/popup/tag.php:57
msgid "Toggle Popup"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:610
msgid "Share on %s"
msgstr ""

#: modules/slides/widgets/slides.php:684
msgid "Transition Speed"
msgstr ""

#: modules/social/widgets/facebook-page.php:81
msgid "Cover Photo"
msgstr ""

#: assets/js/editor.js:6099
msgid "Publish Settings"
msgstr ""

#: assets/js/custom-code.js:1331 assets/js/editor.js:5689
msgid "Conditions"
msgstr ""

#: assets/js/editor.js:5696
msgid "Apply current template to these pages."
msgstr ""

#: assets/js/app.js:3619 assets/js/custom-code.js:1124 assets/js/editor.js:6106
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions-rows.js:79
msgid "Save & Close"
msgstr ""

#: core/app/modules/site-editor/module.php:148
#: modules/theme-builder/admin-menu-items/theme-builder-menu-item.php:17
#: modules/theme-builder/module.php:324 modules/theme-builder/module.php:485
msgid "Theme Builder"
msgstr ""

#: core/app/modules/site-editor/module.php:177
#: modules/theme-builder/module.php:307
msgid "Add New Theme Template"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:522
msgid "Prev"
msgstr ""

#: modules/woocommerce/widgets/product-meta.php:303
#: modules/woocommerce/widgets/product-meta.php:336
msgid "Plural"
msgstr ""

#: modules/woocommerce/widgets/product-meta.php:315
#: modules/woocommerce/widgets/product-meta.php:326
#: modules/woocommerce/widgets/product-meta.php:401
msgid "Tag"
msgstr ""

#: modules/payments/classes/payment-button.php:148
#: modules/woocommerce/widgets/product-meta.php:348
#: modules/woocommerce/widgets/product-meta.php:357
#: modules/woocommerce/widgets/product-meta.php:359
#: modules/woocommerce/widgets/product-meta.php:397
msgid "SKU"
msgstr ""

#: license/admin.php:291 modules/woocommerce/widgets/product-meta.php:369
msgid "Missing"
msgstr ""

#: core/behaviors/feature-lock.php:50 core/editor/notice-bar.php:73
#: core/editor/promotion.php:39 license/admin.php:220 license/admin.php:384
#: license/admin.php:509 license/admin.php:581
#: modules/assets-manager/asset-types/admin-menu-items/custom-fonts-promotion-menu-item.php:34
#: modules/assets-manager/asset-types/admin-menu-items/custom-icons-promotion-menu-item.php:34
#: modules/custom-code/admin-menu-items/custom-code-promotion-menu-item.php:34
#: modules/forms/submissions/admin-menu-items/submissions-promotion-menu-item.php:39
#: modules/popup/admin-menu-items/popups-promotion-menu-item.php:23
#: modules/role-manager/module.php:80
msgid "Connect & Activate"
msgstr ""

#: license/admin.php:258
msgid "Want to activate this website by a different license?"
msgstr ""

#: license/admin.php:269
msgid "Switch Account"
msgstr ""

#: license/api.php:378
msgid "Your license is invalid for this domain. Please check your key again."
msgstr ""

#: modules/forms/actions/discord.php:44 modules/forms/actions/slack.php:45
msgid "Click here for Instructions"
msgstr ""

#: modules/forms/actions/discord.php:111 modules/forms/actions/slack.php:127
msgid "Form Data"
msgstr ""

#: modules/forms/actions/discord.php:120 modules/forms/actions/slack.php:136
msgid "Timestamp"
msgstr ""

#: modules/dynamic-tags/module.php:142
msgid "Actions"
msgstr "Actions"

#. translators: %s: Post type label.
#: modules/theme-builder/conditions/post-type-by-author.php:34
msgid "%s By Author"
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-file.php:17
msgid "File Field"
msgstr ""

#: core/utils.php:203
msgid "Page Not Found"
msgstr ""

#: core/connect/apps/activate.php:56
msgid "Please connect to Elementor in order to activate license."
msgstr ""

#: core/connect/apps/activate.php:79
msgid "License key is missing."
msgstr ""

#: core/connect/apps/activate.php:104
msgid "License has been activated successfully."
msgstr ""

#: license/admin.php:577
msgid "Activate Manually"
msgstr ""

#: modules/carousel/widgets/reviews.php:22
msgid "Reviews"
msgstr ""

#: modules/carousel/widgets/reviews.php:231
#: modules/carousel/widgets/reviews.php:653
msgid "Review"
msgstr ""

#: modules/carousel/widgets/reviews.php:404
msgid "Unmarked Style"
msgstr ""

#: modules/carousel/widgets/reviews.php:412
msgid "Outline"
msgstr ""

#: modules/carousel/widgets/reviews.php:473
msgid "Unmarked Color"
msgstr ""

#: modules/countdown/widgets/countdown.php:51
msgid "Evergreen Timer"
msgstr ""

#: modules/countdown/widgets/countdown.php:262
msgid "Actions After Expire"
msgstr ""

#: modules/countdown/widgets/countdown.php:267
msgid "Show Message"
msgstr ""

#: modules/countdown/widgets/countdown.php:294
msgid "Redirect URL"
msgstr ""

#: modules/custom-attributes/module.php:67
msgid "Attributes"
msgstr ""

#: modules/custom-attributes/module.php:75
msgid "Custom Attributes"
msgstr ""

#: modules/custom-attributes/module.php:80
msgid "key|value"
msgstr ""

#. translators: %s: The `|` separate char.
#: modules/custom-attributes/module.php:83
msgid "Set custom attributes for the wrapper element. Each attribute in a separate line. Separate attribute key from the value using %s character."
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:19
msgid "Contact URL"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:40
msgid "SMS"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:41
msgid "WhatsApp"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:42
msgid "Skype"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:43
msgid "Messenger"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:44
msgid "Viber"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:45
msgid "Waze"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:46
msgid "Google Calendar"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:47
msgid "Outlook Calendar"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:48
msgid "Yahoo Calendar"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:67
#: modules/forms/actions/email.php:61
msgid "Subject"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:189
#: modules/dynamic-tags/tags/contact-url.php:106
#: modules/dynamic-tags/tags/user-info.php:80
#: modules/forms/actions/discord.php:55 modules/forms/actions/slack.php:71
msgid "Username"
msgstr "Username"

#: modules/dynamic-tags/tags/contact-url.php:117
#: modules/dynamic-tags/tags/contact-url.php:133
#: modules/popup/form-action.php:37 modules/popup/tag.php:51
msgid "Action"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:120
msgid "Contact"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:121
msgid "Add"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:136
msgid "Call"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:137
msgid "Chat"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:138
msgid "Show Profile"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:139
msgid "Add to Contacts"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:140
msgid "Send Voice Mail"
msgstr ""

#: modules/dynamic-tags/tags/featured-image-data.php:31
msgid "Featured Image Data"
msgstr ""

#: modules/dynamic-tags/tags/featured-image-data.php:83
msgid "Data"
msgstr ""

#: modules/dynamic-tags/tags/featured-image-data.php:88
#: modules/gallery/widgets/gallery.php:501
#: modules/gallery/widgets/gallery.php:518
msgid "Alt"
msgstr ""

#: modules/dynamic-tags/tags/featured-image-data.php:91
msgid "File URL"
msgstr ""

#: modules/dynamic-tags/tags/featured-image-data.php:92
msgid "Attachment URL"
msgstr ""

#: modules/dynamic-tags/tags/lightbox.php:69
msgid "Video URL"
msgstr ""

#: modules/dynamic-tags/tags/request-parameter.php:19
msgid "Request Parameter"
msgstr ""

#: modules/dynamic-tags/tags/request-parameter.php:74
msgid "Parameter Name"
msgstr ""

#: modules/forms/actions/discord.php:19 modules/forms/actions/discord.php:26
msgid "Discord"
msgstr ""

#: modules/forms/actions/discord.php:69
msgid "Avatar URL"
msgstr ""

#: modules/forms/actions/discord.php:167 modules/forms/actions/slack.php:191
msgid "A new Submission"
msgstr ""

#: modules/forms/actions/mailerlite.php:28
#: modules/forms/actions/mailerlite.php:35
#: modules/forms/actions/mailerlite.php:243
msgid "MailerLite"
msgstr ""

#: modules/forms/actions/mailerlite.php:81
msgid "Group"
msgstr ""

#: modules/forms/actions/slack.php:20 modules/forms/actions/slack.php:27
msgid "Slack"
msgstr ""

#: modules/forms/actions/discord.php:44 modules/forms/actions/slack.php:45
msgid "Enter the webhook URL that will receive the form's submitted data."
msgstr ""

#: modules/forms/actions/slack.php:57
msgid "Channel"
msgstr ""

#: modules/forms/module.php:152
msgid "Form Submissions"
msgstr ""

#: modules/forms/actions/slack.php:85
msgid "Pre Text"
msgstr ""

#: modules/forms/actions/discord.php:168 modules/forms/actions/slack.php:190
msgid "A new Form Submission has been received"
msgstr ""

#: modules/forms/widgets/form.php:406
#: modules/woocommerce/widgets/checkout.php:210
#: modules/woocommerce/widgets/checkout.php:383
#: modules/woocommerce/widgets/checkout.php:3086
msgid "Default Value"
msgstr ""

#: modules/theme-builder/conditions/any-child-of.php:15
msgid "Any Child Of"
msgstr ""

#: modules/theme-builder/conditions/child-of.php:25
msgid "Direct Child Of"
msgstr ""

#. translators: %s: Taxonomy label.
#: modules/theme-builder/conditions/in-sub-term.php:27
msgid "In Child %s"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:588
#: modules/theme-elements/widgets/post-info.php:648
#: modules/theme-elements/widgets/sitemap.php:362
msgid "Indent"
msgstr ""

#: modules/theme-elements/widgets/sitemap.php:30
#: modules/theme-elements/widgets/sitemap.php:45
msgid "Sitemap"
msgstr ""

#: modules/theme-elements/widgets/sitemap.php:109
msgid "Protected Posts"
msgstr ""

#: modules/theme-elements/widgets/sitemap.php:165
msgid "Add nofollow"
msgstr "Add nofollow"

#: modules/theme-elements/widgets/sitemap.php:195
msgid "Post Type"
msgstr ""

#: modules/table-of-contents/widgets/table-of-contents.php:338
#: modules/theme-elements/widgets/sitemap.php:300
msgid "Hierarchical View"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:299
#: modules/theme-elements/widgets/sitemap.php:309
msgid "Depth"
msgstr ""

#: modules/theme-elements/widgets/sitemap.php:336
msgid "Pages"
msgstr "Pages"

#: modules/theme-elements/widgets/sitemap.php:463
msgid "Bullet"
msgstr ""

#: modules/theme-elements/widgets/sitemap.php:492
msgid "Disc"
msgstr ""

#: core/admin/admin.php:267 modules/usage/integrations-reporter.php:13
msgid "Integrations"
msgstr "Integrations"

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:62
msgid "Project not found."
msgstr ""

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:76
msgid "No project data was returned."
msgstr ""

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:158
msgid "Get Project ID"
msgstr ""

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:161
msgid "Sync Project"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:711
msgid "Centered Slides"
msgstr ""

#: modules/pricing/widgets/price-list.php:445
#: modules/video-playlist/widgets/video-playlist.php:796
#: modules/video-playlist/widgets/video-playlist.php:1005
msgid "Item"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:55
#: modules/woocommerce/widgets/menu-cart.php:56
#: modules/woocommerce/widgets/menu-cart.php:57
msgid "Basket"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:58
#: modules/woocommerce/widgets/menu-cart.php:59
#: modules/woocommerce/widgets/menu-cart.php:60
msgid "Bag"
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-gallery.php:18
#: modules/dynamic-tags/pods/tags/pods-gallery.php:19
#: modules/dynamic-tags/toolset/tags/toolset-gallery.php:19
msgid "Gallery Field"
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-image.php:19
#: modules/dynamic-tags/pods/tags/pods-image.php:19
#: modules/dynamic-tags/toolset/tags/toolset-image.php:19
msgid "Image Field"
msgstr ""

#: modules/dynamic-tags/pods/tags/pods-date.php:17
#: modules/dynamic-tags/toolset/tags/toolset-date.php:17
msgid "Date Field"
msgstr ""

#: modules/woocommerce/widgets/breadcrumb.php:18
msgid "WooCommerce Breadcrumbs"
msgstr ""

#: modules/woocommerce/widgets/categories.php:23
msgid "Product Categories"
msgstr ""

#: modules/woocommerce/widgets/categories.php:79
msgid "Current Subcategories"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:62
#: modules/woocommerce/widgets/product-upsell.php:20
#: modules/woocommerce/widgets/product-upsell.php:36
msgid "Upsells"
msgstr ""

#: modules/woocommerce/tags/category-image.php:16
#: modules/woocommerce/widgets/category-image.php:21
msgid "Category Image"
msgstr ""

#: modules/dynamic-tags/module.php:139
#: modules/dynamic-tags/tags/internal-url.php:79
msgid "Media"
msgstr ""

#: base/base-carousel-trait.php:175
#: modules/loop-filter/widgets/taxonomy-filter.php:85
#: modules/motion-fx/controls-group.php:201
#: modules/motion-fx/controls-group.php:244
#: modules/motion-fx/controls-group.php:287
#: modules/motion-fx/controls-group.php:334
#: modules/motion-fx/controls-group.php:380
#: modules/motion-fx/controls-group.php:423
#: modules/motion-fx/controls-group.php:474
#: modules/motion-fx/controls-group.php:501
#: modules/popup/display-settings/triggers.php:51
#: modules/progress-tracker/widgets/progress-tracker.php:108
msgid "Direction"
msgstr ""

#: modules/dynamic-tags/module.php:151
#: modules/woocommerce/conditions/woocommerce.php:22
#: modules/woocommerce/documents/product-archive.php:133
#: modules/woocommerce/documents/product.php:150
#: modules/woocommerce/module.php:183 modules/woocommerce/module.php:534
#: modules/woocommerce/settings/settings-woocommerce.php:26
msgid "WooCommerce"
msgstr ""

#: modules/woocommerce/widgets/product-price.php:103
msgid "Sale Price"
msgstr ""

#: modules/woocommerce/widgets/products-base.php:334
msgid "Regular Price"
msgstr ""

#: modules/dynamic-tags/tags/internal-url.php:28
msgid "Internal URL"
msgstr ""

#: modules/dynamic-tags/tags/page-title.php:20
#: modules/forms/classes/form-record.php:185
#: modules/theme-builder/widgets/page-title.php:29
msgid "Page Title"
msgstr ""

#: modules/dynamic-tags/tags/page-title.php:69
msgid "Show Home Title"
msgstr ""

#: modules/dynamic-tags/tags/post-gallery.php:18
msgid "Post Image Attachments"
msgstr ""

#: modules/dynamic-tags/toolset/module.php:110
#: modules/dynamic-tags/toolset/tags/toolset-date.php:17
#: modules/dynamic-tags/toolset/tags/toolset-gallery.php:19
#: modules/dynamic-tags/toolset/tags/toolset-image.php:19
#: modules/dynamic-tags/toolset/tags/toolset-text.php:15
#: modules/dynamic-tags/toolset/tags/toolset-url.php:19
msgid "Toolset"
msgstr ""

#: assets/js/editor.js:2267
msgid "%s Field"
msgstr ""

#: modules/forms/widgets/form.php:166
msgid "Enter each option in a separate line. To differentiate between label and value, separate them with a pipe char (\"|\"). For example: First Name|f_name"
msgstr ""

#: modules/forms/widgets/form.php:760
#: modules/posts/traits/button-widget-trait.php:200
msgid "Button ID"
msgstr ""

#: modules/forms/widgets/form.php:766
#: modules/mega-menu/widgets/mega-menu.php:236
#: modules/posts/traits/button-widget-trait.php:209
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Add your custom id WITHOUT the Pound key. e.g: my-id"

#: modules/forms/widgets/form.php:1629
msgid "Success Message Color"
msgstr ""

#: modules/forms/widgets/form.php:1640
#: modules/payments/classes/payment-button.php:466
msgid "Error Message Color"
msgstr ""

#: modules/forms/widgets/form.php:1651
msgid "Inline Message Color"
msgstr ""

#: modules/forms/widgets/login.php:176
msgid "Redirect After Logout"
msgstr ""

#: modules/posts/skins/skin-classic.php:87
msgid "Content Padding"
msgstr ""

#: modules/query-control/controls/group-control-query.php:393
msgid "Query ID"
msgstr ""

#: modules/query-control/controls/group-control-query.php:399
msgid "Give your Query a custom unique id to allow server side filtering"
msgstr ""

#: modules/query-control/controls/group-control-query.php:256
#: modules/query-control/module.php:109
#: modules/woocommerce/widgets/products-deprecated.php:194
msgid "Avoid Duplicates"
msgstr ""

#: modules/query-control/module.php:112
#: modules/woocommerce/widgets/products-deprecated.php:197
msgid "Set to Yes to avoid duplicate posts from showing up on the page. This only affects the frontend."
msgstr ""

#: modules/social/widgets/facebook-button.php:137
#: modules/social/widgets/facebook-comments.php:87
msgid "URL Format"
msgstr ""

#: modules/social/widgets/facebook-button.php:140
#: modules/social/widgets/facebook-comments.php:90
msgid "Plain Permalink"
msgstr ""

#: modules/social/widgets/facebook-button.php:141
#: modules/social/widgets/facebook-comments.php:91
msgid "Pretty Permalink"
msgstr ""

#: modules/sticky/module.php:119
msgid "Effects Offset"
msgstr ""

#: modules/sticky/module.php:160
msgid "Stay In Column"
msgstr ""

#: modules/theme-builder/conditions/by-author.php:25
msgid "By Author"
msgstr ""

#. translators: %s: Widget name.
#: modules/theme-builder/documents/single-base.php:173
msgid "The %s Widget was not found in your template."
msgstr ""

#: modules/posts/skins/skin-content-base.php:278
msgid "Pages:"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:165
msgid "In same Term"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:171
msgid "Indicates whether next post must be within the same taxonomy term as the current post, this lets you set a taxonomy per each post type"
msgstr ""

#: modules/woocommerce/conditions/product-archive.php:39
#: modules/woocommerce/documents/product-archive.php:129
msgid "Product Archive"
msgstr ""

#: modules/woocommerce/conditions/product-archive.php:43
msgid "All Product Archives"
msgstr ""

#: modules/woocommerce/conditions/shop-page.php:25
msgid "Shop Page"
msgstr ""

#: modules/woocommerce/conditions/woocommerce.php:26
msgid "Entire Shop"
msgstr ""

#: modules/woocommerce/documents/product-post.php:38
msgid "Product Post"
msgstr ""

#: modules/woocommerce/documents/product.php:28
msgid "Single Product"
msgstr ""

#: modules/theme-builder/views/panel-template.php:75
#: modules/woocommerce/wc-templates/cart/mini-cart.php:57
msgid "Remove this item"
msgstr ""

#: modules/woocommerce/wc-templates/cart/mini-cart.php:72
msgid "No products in the cart."
msgstr "No products in the basket."

#: modules/woocommerce/widgets/menu-cart.php:136
#: modules/woocommerce/widgets/menu-cart.php:1310
msgid "Subtotal"
msgstr ""

#: modules/payments/classes/payment-button.php:123
#: modules/payments/widgets/stripe-button.php:312
#: modules/woocommerce/settings/settings-woocommerce.php:83
#: modules/woocommerce/widgets/checkout.php:28
#: modules/woocommerce/widgets/menu-cart.php:557
#: modules/woocommerce/widgets/menu-cart.php:1922
msgid "Checkout"
msgstr ""

#: modules/woocommerce/tags/product-gallery.php:16
msgid "Product Gallery"
msgstr ""

#: modules/woocommerce/tags/product-image.php:22
msgid "Product Image"
msgstr ""

#: modules/payments/widgets/stripe-button.php:351
#: modules/woocommerce/tags/product-price.php:16
#: modules/woocommerce/widgets/menu-cart.php:1507
#: modules/woocommerce/widgets/product-price.php:20
msgid "Product Price"
msgstr ""

#: modules/woocommerce/tags/product-rating.php:16
#: modules/woocommerce/widgets/product-rating.php:18
msgid "Product Rating"
msgstr ""

#: modules/woocommerce/tags/product-rating.php:24
msgid "Average Rating"
msgstr ""

#: modules/woocommerce/tags/product-rating.php:25
msgid "Rating Count"
msgstr ""

#: modules/woocommerce/tags/product-rating.php:26
msgid "Review Count"
msgstr ""

#: modules/woocommerce/tags/product-sale.php:16
msgid "Product Sale"
msgstr ""

#: modules/woocommerce/tags/product-short-description.php:14
msgid "Product Short Description"
msgstr ""

#: modules/woocommerce/tags/product-sku.php:14
msgid "Product SKU"
msgstr ""

#: modules/woocommerce/tags/product-stock.php:16
#: modules/woocommerce/widgets/product-stock.php:18
msgid "Product Stock"
msgstr ""

#: modules/woocommerce/tags/product-stock.php:42
msgid "Show Text"
msgstr ""

#: modules/woocommerce/tags/product-terms.php:16
msgid "Product Terms"
msgstr ""

#: modules/woocommerce/tags/product-title.php:14
#: modules/woocommerce/widgets/menu-cart.php:1423
#: modules/woocommerce/widgets/product-title.php:21
msgid "Product Title"
msgstr ""

#: modules/woocommerce/widgets/add-to-cart.php:25
msgid "Custom Add To Cart"
msgstr "Custom Add to Basket"

#: modules/woocommerce/widgets/archive-products-deprecated.php:41
#: modules/woocommerce/widgets/archive-products.php:21
msgid "Archive Products"
msgstr ""

#: modules/woocommerce/widgets/products.php:104
msgid "Allow Order"
msgstr ""

#: modules/woocommerce/widgets/products.php:129
msgid "Show Result Count"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:57
msgid "Latest Products"
msgstr ""

#: modules/woocommerce/widgets/archive-description.php:49
#: modules/woocommerce/widgets/breadcrumb.php:47
#: modules/woocommerce/widgets/categories.php:174
#: modules/woocommerce/widgets/product-add-to-cart.php:206
#: modules/woocommerce/widgets/product-data-tabs.php:44
#: modules/woocommerce/widgets/product-images.php:45
#: modules/woocommerce/widgets/product-meta.php:44
#: modules/woocommerce/widgets/product-price.php:45
#: modules/woocommerce/widgets/product-rating.php:43
#: modules/woocommerce/widgets/product-short-description.php:43
#: modules/woocommerce/widgets/product-stock.php:43
#: modules/woocommerce/widgets/products-base.php:31
msgid "The style of this widget is often affected by your theme and plugins. If you experience any such issue, try to switch to a basic theme and deactivate related plugins."
msgstr ""

#: modules/woocommerce/module.php:846
#: modules/woocommerce/skins/skin-loop-product.php:30
#: modules/woocommerce/widgets/categories.php:165
#: modules/woocommerce/widgets/menu-cart.php:1414
#: modules/woocommerce/widgets/products-base.php:22
#: modules/woocommerce/widgets/products-deprecated.php:70
#: modules/woocommerce/widgets/products.php:26
msgid "Products"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:26
msgid "Menu Cart"
msgstr "Menu Basket"

#: modules/woocommerce/widgets/menu-cart.php:42
#: modules/woocommerce/widgets/menu-cart.php:726
msgid "Menu Icon"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:106
#: modules/woocommerce/widgets/menu-cart.php:973
msgid "Items Indicator"
msgstr ""

#: modules/woocommerce/module.php:237
#: modules/woocommerce/settings/settings-woocommerce.php:70
#: modules/woocommerce/widgets/cart.php:27
#: modules/woocommerce/widgets/menu-cart.php:52
#: modules/woocommerce/widgets/menu-cart.php:53
#: modules/woocommerce/widgets/menu-cart.php:54
#: modules/woocommerce/widgets/menu-cart.php:176
#: modules/woocommerce/widgets/menu-cart.php:682
#: modules/woocommerce/widgets/menu-cart.php:1036
msgid "Cart"
msgstr "Basket"

#: modules/woocommerce/widgets/menu-cart.php:397
msgid "Remove Item Icon"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:665
#: modules/woocommerce/widgets/base-widget.php:111
#: modules/woocommerce/widgets/menu-cart.php:1063
#: modules/woocommerce/widgets/menu-cart.php:1370
#: modules/woocommerce/widgets/menu-cart.php:1582
msgid "Groove"
msgstr ""

#: modules/forms/widgets/form.php:583 modules/forms/widgets/form.php:1324
#: modules/woocommerce/widgets/cart.php:1035
#: modules/woocommerce/widgets/menu-cart.php:491
#: modules/woocommerce/widgets/menu-cart.php:1642
#: modules/woocommerce/widgets/my-account.php:1142
#: modules/woocommerce/widgets/my-account.php:1522
#: modules/woocommerce/widgets/purchase-summary.php:1295
msgid "Buttons"
msgstr ""

#: modules/woocommerce/widgets/menu-cart.php:500
#: modules/woocommerce/widgets/menu-cart.php:1743
#: modules/woocommerce/widgets/product-add-to-cart.php:388
#: modules/woocommerce/widgets/products-base.php:539
msgid "View Cart"
msgstr "View Basket"

#: modules/woocommerce/tags/woocommerce-add-to-cart.php:20
#: modules/woocommerce/widgets/product-add-to-cart.php:21
msgid "Add To Cart"
msgstr "Add to Basket"

#: modules/woocommerce/widgets/cart.php:1341
#: modules/woocommerce/widgets/checkout.php:1466
#: modules/woocommerce/widgets/menu-cart.php:1479
#: modules/woocommerce/widgets/my-account.php:1397
#: modules/woocommerce/widgets/product-add-to-cart.php:649
#: modules/woocommerce/widgets/purchase-summary.php:1169
msgid "Variations"
msgstr ""

#: modules/woocommerce/widgets/product-add-to-cart.php:724
msgid "Select field"
msgstr ""

#: modules/woocommerce/widgets/checkout.php:280
#: modules/woocommerce/widgets/checkout.php:287
#: modules/woocommerce/widgets/checkout.php:304
#: modules/woocommerce/widgets/checkout.php:305
#: modules/woocommerce/widgets/checkout.php:1771
#: modules/woocommerce/widgets/product-additional-information.php:18
msgid "Additional Information"
msgstr ""

#: modules/woocommerce/tags/product-content.php:14
#: modules/woocommerce/widgets/product-content.php:17
msgid "Product Content"
msgstr ""

#: modules/woocommerce/widgets/product-data-tabs.php:19
msgid "Product Data Tabs"
msgstr ""

#: modules/video-playlist/widgets/video-playlist.php:343
#: modules/video-playlist/widgets/video-playlist.php:1299
#: modules/woocommerce/widgets/my-account.php:44
#: modules/woocommerce/widgets/my-account.php:326
#: modules/woocommerce/widgets/product-data-tabs.php:35
msgid "Tabs"
msgstr "Tabs"

#: modules/woocommerce/widgets/product-data-tabs.php:173
msgid "Panel"
msgstr ""

#: modules/woocommerce/widgets/product-additional-information.php:35
#: modules/woocommerce/widgets/product-data-tabs.php:201
#: modules/woocommerce/widgets/product-related.php:98
#: modules/woocommerce/widgets/product-related.php:106
#: modules/woocommerce/widgets/product-upsell.php:85
#: modules/woocommerce/widgets/product-upsell.php:93
msgid "Heading"
msgstr "Heading"

#: modules/woocommerce/widgets/product-images.php:20
msgid "Product Images"
msgstr ""

#: modules/woocommerce/widgets/product-meta.php:19
msgid "Product Meta"
msgstr ""

#: modules/woocommerce/widgets/product-meta.php:56
msgid "Table"
msgstr ""

#: modules/woocommerce/widgets/product-meta.php:371
#: modules/woocommerce/widgets/product-meta.php:398
msgid "N/A"
msgstr ""

#: modules/woocommerce/widgets/product-meta.php:283
#: modules/woocommerce/widgets/product-meta.php:293
#: modules/woocommerce/widgets/product-meta.php:399
msgid "Category"
msgstr ""

#: modules/woocommerce/widgets/product-rating.php:51
#: modules/woocommerce/widgets/products-base.php:235
msgid "Star Color"
msgstr ""

#: modules/woocommerce/widgets/product-rating.php:62
#: modules/woocommerce/widgets/products-base.php:246
msgid "Empty Star Color"
msgstr ""

#: modules/woocommerce/widgets/product-rating.php:92
#: modules/woocommerce/widgets/products-base.php:257
msgid "Star Size"
msgstr ""

#: modules/woocommerce/widgets/product-related.php:20
msgid "Product Related"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:61
#: modules/woocommerce/widgets/product-related.php:35
#: modules/woocommerce/widgets/products.php:183
msgid "Related Products"
msgstr ""

#: modules/woocommerce/widgets/product-related.php:42
msgid "Products Per Page"
msgstr ""

#: modules/woocommerce/widgets/product-short-description.php:18
msgid "Short Description"
msgstr ""

#. translators: %s: Page number.
#: core/utils.php:122
msgid "&nbsp;&ndash; Page %s"
msgstr ""

#: modules/dynamic-tags/pods/module.php:131
#: modules/dynamic-tags/pods/tags/pods-date-time.php:25
#: modules/dynamic-tags/pods/tags/pods-date.php:17
#: modules/dynamic-tags/pods/tags/pods-gallery.php:19
#: modules/dynamic-tags/pods/tags/pods-image.php:19
#: modules/dynamic-tags/pods/tags/pods-numeric.php:17
#: modules/dynamic-tags/pods/tags/pods-text.php:15
#: modules/dynamic-tags/pods/tags/pods-url.php:19
msgid "Pods"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:1227
#: modules/pricing/widgets/price-table.php:805
msgid "Below"
msgstr ""

#: modules/pricing/widgets/price-table.php:806
msgid "Beside"
msgstr ""

#. translators: %s: Post type label.
#. translators: %s: Taxonomy label.
#: modules/theme-builder/conditions/post-type-archive.php:38
#: modules/theme-builder/conditions/post-type-archive.php:43
#: modules/theme-builder/documents/archive.php:73
#: modules/theme-builder/documents/archive.php:85
msgid "%s Archive"
msgstr ""

#: modules/theme-builder/documents/theme-page-document.php:49
msgid "Elementor Canvas"
msgstr ""

#: modules/theme-builder/documents/theme-page-document.php:50
msgid "Elementor Full Width"
msgstr ""

#: modules/theme-builder/module.php:220
msgid "Select Post Type"
msgstr ""

#: modules/payments/classes/payment-button.php:93
msgid "An error occurred."
msgstr ""

#: modules/forms/classes/ajax-handler.php:41
msgid "This field is required."
msgstr ""

#. translators: %s: Elementor.
#: modules/forms/actions/discord.php:198 modules/forms/actions/slack.php:227
msgid "Powered by %s"
msgstr ""

#: assets/js/notes/notes-app.js:1140
msgid "Something went wrong."
msgstr "Something went wrong."

#: license/admin.php:609
msgid "Copy the license key from your account and paste it below."
msgstr ""

#. translators: %s: Elementor Pro version.
#: license/admin.php:537
msgid "Elementor Pro v%s"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:120
#: modules/animated-headline/widgets/animated-headline.php:179
msgid "Enter your headline"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:104
msgid "Enter your quote"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:239
#: modules/flip-box/widgets/flip-box.php:154
#: modules/flip-box/widgets/flip-box.php:280
#: modules/pricing/widgets/price-table.php:51
msgid "Enter your title"
msgstr "Enter your title"

#: modules/call-to-action/widgets/call-to-action.php:276
#: modules/flip-box/widgets/flip-box.php:169
#: modules/flip-box/widgets/flip-box.php:294
#: modules/pricing/widgets/price-table.php:63
msgid "Enter your description"
msgstr "Enter your description"

#: modules/flip-box/widgets/flip-box.php:726
#: modules/lottie/widgets/lottie.php:607 modules/lottie/widgets/lottie.php:642
#: modules/page-transitions/module.php:656
msgid "Opacity"
msgstr "Opacity"

#: modules/carousel/widgets/media-carousel.php:211
msgid "YouTube or Vimeo link"
msgstr ""

#: modules/forms/actions/webhook.php:37
msgid "https://your-webhook-url.com"
msgstr ""

#. translators: 1: upload_max_filesize, 2: php.ini
#: modules/forms/fields/upload.php:279
msgid "The uploaded file exceeds the %1$s directive in %2$s."
msgstr ""

#. translators: %d: The number of allowed files.
#: modules/forms/fields/upload.php:299
msgid "You can upload only %d file."
msgid_plural "You can upload up to %d files."
msgstr[0] ""
msgstr[1] ""

#: modules/theme-elements/widgets/post-info.php:113
msgctxt "Date Format"
msgid "March 6, 2018 (F j, Y)"
msgstr ""

#: modules/theme-builder/conditions/search.php:23
#: modules/theme-builder/documents/archive.php:93
#: modules/theme-builder/documents/search-results.php:23
#: modules/theme-builder/documents/search-results.php:27
#: modules/theme-builder/documents/search-results.php:50
#: modules/woocommerce/conditions/product-search.php:26
#: modules/woocommerce/documents/product-archive.php:107
msgid "Search Results"
msgstr ""

#: modules/theme-builder/conditions/singular.php:29
#: modules/woocommerce/widgets/product-meta.php:291
#: modules/woocommerce/widgets/product-meta.php:324
msgid "Singular"
msgstr ""

#: modules/theme-builder/conditions/singular.php:33
msgid "All Singular"
msgstr ""

#: modules/query-control/controls/group-control-related.php:61
#: modules/theme-builder/documents/archive.php:90
msgid "Recent Posts"
msgstr ""

#: modules/lottie/widgets/lottie.php:390
#: modules/theme-builder/documents/section.php:22
#: modules/video-playlist/widgets/video-playlist.php:82
#: modules/video-playlist/widgets/video-playlist.php:1213
#: modules/woocommerce/widgets/cart.php:1821
#: modules/woocommerce/widgets/cart.php:1941
#: modules/woocommerce/widgets/cart.php:2165
#: modules/woocommerce/widgets/checkout.php:1823
#: modules/woocommerce/widgets/checkout.php:2069
#: modules/woocommerce/widgets/checkout.php:2250
#: modules/woocommerce/widgets/checkout.php:2413
#: modules/woocommerce/widgets/checkout.php:2621
#: modules/woocommerce/widgets/checkout.php:3323
#: modules/woocommerce/widgets/checkout.php:3472
msgid "Section"
msgstr "Section"

#: modules/theme-builder/documents/section.php:54
msgid "Location Settings"
msgstr ""

#: modules/custom-code/custom-code-metabox.php:154
#: modules/custom-code/module.php:372
#: modules/dynamic-tags/tags/contact-url.php:152
#: modules/dynamic-tags/tags/contact-url.php:195
#: modules/theme-builder/documents/section.php:70
msgid "Location"
msgstr ""

#: modules/loop-builder/documents/loop.php:351
#: modules/loop-builder/documents/loop.php:354
#: modules/theme-builder/documents/section.php:84
#: modules/woocommerce/widgets/checkout.php:4060
#: assets/js/form-submission-admin.js:1593
msgid "Apply"
msgstr "Apply"

#: modules/theme-builder/documents/error-404.php:42
#: modules/theme-builder/documents/single-base.php:138
msgid "404"
msgstr ""

#. translators: 1: Widget name, 2: Template name.
#: modules/theme-builder/documents/single-base.php:175
msgid "You must include the %1$s Widget in your template (%2$s), in order for Elementor to work on this page."
msgstr ""

#: modules/theme-builder/documents/theme-document.php:139
msgid "Unsupported"
msgstr ""

#: modules/theme-builder/documents/theme-document.php:286
#: modules/woocommerce/widgets/purchase-summary.php:412
msgid "Preview Settings"
msgstr ""

#: modules/theme-builder/documents/theme-document.php:294
msgid "Preview Dynamic Content as"
msgstr ""

#: modules/theme-builder/documents/theme-document.php:325
msgid "Search Term"
msgstr ""

#: modules/theme-builder/documents/theme-document.php:337
#: modules/theme-builder/documents/theme-document.php:340
msgid "Apply & Preview"
msgstr ""

#: modules/theme-builder/documents/theme-page-document.php:45
msgid "Page Layout"
msgstr "Page Layout"

#: modules/forms/classes/form-record.php:206
msgid "Elementor"
msgstr "Elementor"

#: modules/lottie/widgets/lottie.php:489
msgid "Canvas"
msgstr ""

#: modules/theme-builder/documents/theme-page-document.php:59
msgid "Default Page Template from your theme"
msgstr "Default Page Template from your theme"

#: modules/theme-builder/documents/theme-page-document.php:72
msgid "No header, no footer, just Elementor"
msgstr "No header, no footer, just Elementor"

#: modules/theme-builder/documents/theme-page-document.php:85
msgid "This template includes the header, full-width content and footer"
msgstr "This template includes the header, full-width content and footer"

#: assets/js/editor.js:5707
#: assets/js/packages/editor-documents-extended/editor-documents-extended.js:308
#: assets/js/packages/editor-documents-extended/editor-documents-extended.strings.js:3
msgid "Display Conditions"
msgstr ""

#: modules/dynamic-tags/tags/user-info.php:77 modules/popup/form-action.php:40
#: modules/query-control/controls/group-control-query.php:315
#: modules/query-control/controls/group-control-query.php:332
#: modules/theme-elements/widgets/post-info.php:698
msgid "Choose"
msgstr ""

#: assets/js/app.js:3609 assets/js/custom-code.js:1114 assets/js/editor.js:5173
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions-rows.js:68
msgid "Add Condition"
msgstr ""

#: modules/theme-builder/views/panel-template.php:52
msgid "Where Do You Want to Display Your %s?"
msgstr ""

#: modules/theme-builder/module.php:190
msgid "Select a Location"
msgstr ""

#: modules/theme-builder/skins/post-comments-skin-classic.php:68
msgid "Gravatar"
msgstr ""

#: modules/theme-builder/skins/post-comments-skin-classic.php:253
msgid "Reply Button"
msgstr ""

#. translators: 1: Comment date, 2: Comment time.
#: modules/theme-builder/skins/post-comments-skin-classic.php:453
msgid "%1$s at %2$s"
msgstr ""

#: modules/theme-builder/skins/post-comments-skin-classic.php:461
msgid "Your comment is awaiting moderation."
msgstr ""

#: modules/theme-builder/views/comments-template.php:10
msgid "This post is password protected. Enter the password to view comments."
msgstr ""

#: modules/theme-builder/views/comments-template.php:50
#: modules/theme-elements/widgets/post-comments.php:106
msgid "Comments are closed."
msgstr ""

#: modules/theme-builder/views/panel-template.php:80
#: modules/theme-builder/views/panel-template.php:82
msgid "Preview Changes"
msgstr "Preview Changes"

#: modules/assets-manager/asset-types/fonts-manager.php:331
#: modules/theme-builder/views/panel-template.php:92 assets/js/app.js:2987
#: assets/js/app.js:3754
#: core/app/modules/site-editor/assets/js/molecules/site-template-thumbnail.js:13
#: core/app/modules/site-editor/assets/js/pages/import.js:49
msgid "Preview"
msgstr "Preview"

#: modules/theme-builder/widgets/archive-posts.php:26
msgid "Archive Posts"
msgstr ""

#: modules/theme-builder/widgets/archive-posts.php:77
#: modules/theme-builder/widgets/archive-posts.php:92
#: modules/woocommerce/widgets/archive-products-deprecated.php:102
#: modules/woocommerce/widgets/archive-products-deprecated.php:114
#: modules/woocommerce/widgets/archive-products.php:132
#: modules/woocommerce/widgets/archive-products.php:147
msgid "Nothing Found Message"
msgstr ""

#: modules/theme-builder/widgets/archive-posts.php:79
#: modules/woocommerce/widgets/archive-products-deprecated.php:104
#: modules/woocommerce/widgets/archive-products.php:134
msgid "It seems we can't find what you're looking for."
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-url.php:18
#: modules/dynamic-tags/pods/tags/pods-url.php:19
#: modules/dynamic-tags/toolset/tags/toolset-url.php:19
msgid "URL Field"
msgstr ""

#: modules/progress-tracker/widgets/progress-tracker.php:71
#: modules/theme-builder/widgets/post-content.php:23
msgid "Post Content"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:22
msgid "Post Info"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:99
msgid "Terms"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:128
msgid "Custom Date Format"
msgstr ""

#. translators: %s: Allowed data letters (see:
#. http://php.net/manual/en/function.date.php).
#. translators: %s: Allowed time letters (see:
#. http://php.net/manual/en/function.time.php).
#: modules/theme-elements/widgets/post-info.php:137
#: modules/theme-elements/widgets/post-info.php:174
msgid "Use the letters: %s"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:164
msgid "Custom Time Format"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:249
#: modules/theme-elements/widgets/post-info.php:251
#: modules/theme-elements/widgets/post-info.php:781
msgid "No Comments"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:262
#: modules/theme-elements/widgets/post-info.php:264
#: modules/theme-elements/widgets/post-info.php:782
msgid "One Comment"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:277
#: modules/theme-elements/widgets/post-info.php:783
msgid "%s Comments"
msgstr ""

#: base/base-carousel-trait.php:376 base/base-carousel-trait.php:538
#: base/base-carousel-trait.php:1212 base/base-carousel-trait.php:1275
#: base/base-carousel-trait.php:1364 base/base-carousel-trait.php:1372
#: modules/dynamic-tags/tags/contact-url.php:211
#: modules/hotspot/widgets/hotspot.php:600
#: modules/loop-builder/widgets/base.php:189
#: modules/loop-filter/widgets/taxonomy-filter.php:112
#: modules/loop-filter/widgets/taxonomy-filter.php:152
#: modules/loop-filter/widgets/taxonomy-filter.php:187
#: modules/loop-filter/widgets/taxonomy-filter.php:226
#: modules/mega-menu/widgets/mega-menu.php:321
#: modules/mega-menu/widgets/mega-menu.php:357
#: modules/mega-menu/widgets/mega-menu.php:548
#: modules/popup/display-settings/timing.php:262
#: modules/theme-elements/widgets/post-info.php:435
#: modules/video-playlist/widgets/video-playlist.php:651
#: modules/woocommerce/widgets/cart.php:155
#: modules/woocommerce/widgets/cart.php:239
#: modules/woocommerce/widgets/cart.php:312
#: modules/woocommerce/widgets/cart.php:359
#: modules/woocommerce/widgets/cart.php:415
#: modules/woocommerce/widgets/checkout.php:155
#: modules/woocommerce/widgets/checkout.php:322
#: modules/woocommerce/widgets/checkout.php:454
#: modules/woocommerce/widgets/checkout.php:534
#: modules/woocommerce/widgets/checkout.php:2935
#: modules/woocommerce/widgets/checkout.php:2969
#: modules/woocommerce/widgets/checkout.php:3221
#: modules/woocommerce/widgets/checkout.php:3261
#: modules/woocommerce/widgets/my-account.php:82
#: modules/woocommerce/widgets/my-account.php:193
#: modules/woocommerce/widgets/products.php:215
#: modules/woocommerce/widgets/purchase-summary.php:87
#: modules/woocommerce/widgets/purchase-summary.php:205
#: modules/woocommerce/widgets/purchase-summary.php:251
#: modules/woocommerce/widgets/purchase-summary.php:297
#: modules/woocommerce/widgets/purchase-summary.php:343
#: modules/woocommerce/widgets/purchase-summary.php:389
msgid "Start"
msgstr "Start"

#: base/base-carousel-trait.php:384 base/base-carousel-trait.php:546
#: base/base-carousel-trait.php:1220 base/base-carousel-trait.php:1283
#: base/base-carousel-trait.php:1364 base/base-carousel-trait.php:1372
#: modules/dynamic-tags/tags/contact-url.php:226
#: modules/hotspot/widgets/hotspot.php:608
#: modules/loop-builder/widgets/base.php:197
#: modules/loop-filter/widgets/taxonomy-filter.php:120
#: modules/loop-filter/widgets/taxonomy-filter.php:160
#: modules/loop-filter/widgets/taxonomy-filter.php:195
#: modules/loop-filter/widgets/taxonomy-filter.php:234
#: modules/mega-menu/widgets/mega-menu.php:329
#: modules/mega-menu/widgets/mega-menu.php:365
#: modules/mega-menu/widgets/mega-menu.php:556
#: modules/popup/display-settings/timing.php:280
#: modules/theme-elements/widgets/post-info.php:443
#: modules/video-playlist/widgets/video-playlist.php:655
#: modules/woocommerce/widgets/cart.php:163
#: modules/woocommerce/widgets/cart.php:247
#: modules/woocommerce/widgets/cart.php:320
#: modules/woocommerce/widgets/cart.php:367
#: modules/woocommerce/widgets/cart.php:423
#: modules/woocommerce/widgets/checkout.php:163
#: modules/woocommerce/widgets/checkout.php:330
#: modules/woocommerce/widgets/checkout.php:462
#: modules/woocommerce/widgets/checkout.php:542
#: modules/woocommerce/widgets/checkout.php:2943
#: modules/woocommerce/widgets/checkout.php:2977
#: modules/woocommerce/widgets/checkout.php:3229
#: modules/woocommerce/widgets/checkout.php:3269
#: modules/woocommerce/widgets/my-account.php:90
#: modules/woocommerce/widgets/my-account.php:201
#: modules/woocommerce/widgets/products.php:223
#: modules/woocommerce/widgets/purchase-summary.php:95
#: modules/woocommerce/widgets/purchase-summary.php:213
#: modules/woocommerce/widgets/purchase-summary.php:259
#: modules/woocommerce/widgets/purchase-summary.php:305
#: modules/woocommerce/widgets/purchase-summary.php:351
#: modules/woocommerce/widgets/purchase-summary.php:397
msgid "End"
msgstr "End"

#: base/base-carousel-trait.php:196
#: modules/woocommerce/tags/product-price.php:24
msgid "Both"
msgstr ""

#: modules/woocommerce/tags/product-price.php:25
msgid "Original"
msgstr ""

#: modules/woocommerce/settings/settings-woocommerce.php:46
#: modules/woocommerce/widgets/elements.php:19
msgid "WooCommerce Pages"
msgstr ""

#: modules/woocommerce/tags/product-sale.php:23
msgid "Sale!"
msgstr ""

#: modules/woocommerce/widgets/add-to-cart.php:77
msgid "Show Quantity"
msgstr ""

#: modules/payments/classes/payment-button.php:248
#: modules/payments/widgets/stripe-button.php:376
#: modules/woocommerce/tags/woocommerce-add-to-cart.php:58
#: modules/woocommerce/widgets/add-to-cart.php:88
#: modules/woocommerce/widgets/menu-cart.php:1538
#: modules/woocommerce/widgets/product-add-to-cart.php:445
#: modules/woocommerce/widgets/product-add-to-cart.php:453
msgid "Quantity"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:18
msgid "Woo - Single Elements"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:45
msgid "Data Tabs"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:51
msgid "Sharing"
msgstr ""

#: modules/woocommerce/widgets/product-images.php:53
#: modules/woocommerce/widgets/products-base.php:908
#: modules/woocommerce/widgets/products-base.php:916
#: modules/woocommerce/widgets/single-elements.php:52
msgid "Sale Flash"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:53
msgid "Additional Information Tab"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:54
msgid "Upsell"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:55
msgid "Stock Status"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:98
#: modules/woocommerce/widgets/single-elements.php:107
msgid "Admin Notice:"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:98
msgid "Please enable the Review Rating"
msgstr ""

#: modules/woocommerce/widgets/single-elements.php:107
msgid "No Rating Reviews"
msgstr ""

#. translators: %s: Search term.
#: core/utils.php:118
msgid "Search Results for: %s"
msgstr ""

#. translators: Category archive title. %s: Category name.
#: core/utils.php:129
msgid "Category: %s"
msgstr ""

#. translators: Tag archive title. %s: Tag name.
#: core/utils.php:135
msgid "Tag: %s"
msgstr ""

#. translators: Author archive title. %s: Author name.
#: core/utils.php:142
msgid "Author: %s"
msgstr ""

#: core/utils.php:145
msgctxt "yearly archives date format"
msgid "Y"
msgstr ""

#. translators: Yearly archive title. %s: Year.
#: core/utils.php:149
msgid "Year: %s"
msgstr ""

#: core/utils.php:152
msgctxt "monthly archives date format"
msgid "F Y"
msgstr ""

#. translators: Monthly archive title. %s: Month name and a year.
#: core/utils.php:156
msgid "Month: %s"
msgstr ""

#: core/utils.php:159
msgctxt "daily archives date format"
msgid "F j, Y"
msgstr ""

#. translators: Daily archive title. %s: Date.
#: core/utils.php:163
msgid "Day: %s"
msgstr ""

#: core/utils.php:167
msgctxt "post format archive title"
msgid "Asides"
msgstr ""

#: core/utils.php:169
msgctxt "post format archive title"
msgid "Galleries"
msgstr ""

#: core/utils.php:171
msgctxt "post format archive title"
msgid "Images"
msgstr ""

#: core/utils.php:173
msgctxt "post format archive title"
msgid "Videos"
msgstr ""

#: core/utils.php:175
msgctxt "post format archive title"
msgid "Quotes"
msgstr ""

#: core/utils.php:177
msgctxt "post format archive title"
msgid "Links"
msgstr ""

#: core/utils.php:179
msgctxt "post format archive title"
msgid "Statuses"
msgstr ""

#: core/utils.php:181
msgctxt "post format archive title"
msgid "Audio"
msgstr ""

#: core/utils.php:183
msgctxt "post format archive title"
msgid "Chats"
msgstr ""

#. translators: Post type archive title. %s: Post type name.
#: core/utils.php:190
msgid "Archives: %s"
msgstr ""

#. translators: Taxonomy term archive title. 1: Taxonomy singular name, 2:
#. Current taxonomy term.
#: core/utils.php:198
msgid "%1$s: %2$s"
msgstr ""

#: core/utils.php:201 modules/theme-builder/conditions/archive.php:31
#: modules/theme-builder/documents/archive.php:30
msgid "Archives"
msgstr ""

#: modules/dynamic-tags/acf/module.php:153
#: modules/dynamic-tags/acf/tags/acf-color.php:18
#: modules/dynamic-tags/acf/tags/acf-date-time.php:25
#: modules/dynamic-tags/acf/tags/acf-file.php:17
#: modules/dynamic-tags/acf/tags/acf-gallery.php:18
#: modules/dynamic-tags/acf/tags/acf-image.php:19
#: modules/dynamic-tags/acf/tags/acf-number.php:18
#: modules/dynamic-tags/acf/tags/acf-text.php:18
#: modules/dynamic-tags/acf/tags/acf-url.php:18
msgid "ACF"
msgstr ""

#: modules/dynamic-tags/acf/module.php:116
#: modules/dynamic-tags/pods/tags/pods-base.php:54
#: modules/dynamic-tags/pods/tags/pods-date-time.php:69
#: modules/dynamic-tags/pods/tags/pods-gallery.php:71
#: modules/dynamic-tags/pods/tags/pods-image.php:67
#: modules/dynamic-tags/pods/tags/pods-url.php:78
#: modules/dynamic-tags/tags/post-custom-field.php:48
#: modules/dynamic-tags/toolset/tags/toolset-base.php:29
#: modules/dynamic-tags/toolset/tags/toolset-gallery.php:73
#: modules/dynamic-tags/toolset/tags/toolset-image.php:71
#: modules/dynamic-tags/toolset/tags/toolset-url.php:74
msgid "Key"
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-date-time.php:81
#: modules/dynamic-tags/acf/tags/acf-image.php:88
#: modules/dynamic-tags/acf/tags/acf-url.php:98
#: modules/dynamic-tags/pods/tags/pods-date-time.php:79
#: modules/dynamic-tags/pods/tags/pods-image.php:76
#: modules/dynamic-tags/pods/tags/pods-url.php:87
#: modules/dynamic-tags/tags/post-featured-image.php:49
#: modules/dynamic-tags/toolset/tags/toolset-image.php:80
#: modules/dynamic-tags/toolset/tags/toolset-url.php:83
#: modules/query-control/controls/group-control-related.php:56
msgid "Fallback"
msgstr "Fallback"

#: modules/dynamic-tags/module.php:133
#: modules/theme-builder/classes/locations-manager.php:524
#: modules/theme-builder/documents/archive.php:26
#: modules/theme-builder/documents/archive.php:50
#: modules/theme-builder/documents/archive.php:100
#: modules/theme-builder/documents/search-results.php:55
#: modules/woocommerce/documents/product-archive.php:93
#: modules/woocommerce/documents/product-archive.php:103
#: modules/woocommerce/documents/product-archive.php:114
msgid "Archive"
msgstr ""

#: modules/dynamic-tags/module.php:136
#: modules/popup/display-settings/timing.php:253
#: modules/theme-builder/documents/header-footer-base.php:20
msgid "Site"
msgstr ""

#: modules/dynamic-tags/tags/archive-description.php:18
#: modules/woocommerce/widgets/archive-description.php:18
msgid "Archive Description"
msgstr ""

#: modules/dynamic-tags/tags/archive-meta.php:18
msgid "Archive Meta"
msgstr ""

#: modules/dynamic-tags/tags/archive-meta.php:59
#: modules/dynamic-tags/tags/author-meta.php:48
#: modules/dynamic-tags/tags/user-info.php:94
msgid "Meta Key"
msgstr ""

#: modules/dynamic-tags/tags/archive-title.php:19
#: modules/theme-builder/widgets/archive-title.php:20
msgid "Archive Title"
msgstr ""

#: modules/dynamic-tags/tags/archive-title.php:42
#: modules/dynamic-tags/tags/page-title.php:61
msgid "Include Context"
msgstr ""

#: modules/dynamic-tags/tags/archive-url.php:27
msgid "Archive URL"
msgstr ""

#: modules/dynamic-tags/tags/author-info.php:54
#: modules/dynamic-tags/tags/user-info.php:83
msgid "Bio"
msgstr ""

#: modules/dynamic-tags/tags/author-meta.php:18
msgid "Author Meta"
msgstr ""

#: modules/dynamic-tags/tags/author-name.php:18
msgid "Author Name"
msgstr ""

#: modules/dynamic-tags/tags/author-profile-picture.php:19
msgid "Author Profile Picture"
msgstr ""

#: modules/dynamic-tags/tags/author-url.php:27
msgid "Author URL"
msgstr ""

#: modules/dynamic-tags/tags/author-url.php:58
#: modules/theme-builder/conditions/author.php:25
#: modules/theme-builder/documents/archive.php:92
msgid "Author Archive"
msgstr ""

#: modules/dynamic-tags/tags/author-url.php:59
msgid "Author Website"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:19
msgid "Comments Number"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:37
msgid "No Comments Format"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:38
msgid "No Responses"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:45
msgid "One Comment Format"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:46
msgid "One Response"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:53
msgid "Many Comment Format"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:54
msgid "{number} Responses"
msgstr ""

#: modules/dynamic-tags/tags/comments-number.php:66
msgid "Comments Link"
msgstr ""

#: modules/dynamic-tags/tags/comments-url.php:18
msgid "Comments URL"
msgstr ""

#: modules/dynamic-tags/tags/current-date-time.php:18
msgid "Current Date Time"
msgstr ""

#: modules/dynamic-tags/tags/current-date-time.php:33
#: modules/theme-elements/widgets/post-info.php:108
msgid "Date Format"
msgstr ""

#: modules/dynamic-tags/tags/current-date-time.php:51
#: modules/theme-elements/widgets/post-info.php:146
msgid "Time Format"
msgstr ""

#: modules/dynamic-tags/pods/tags/pods-date.php:80
#: modules/dynamic-tags/tags/current-date-time.php:70
#: modules/dynamic-tags/tags/post-date.php:64
#: modules/dynamic-tags/tags/post-time.php:62
#: modules/dynamic-tags/toolset/tags/toolset-date.php:99
#: modules/theme-elements/widgets/post-info.php:237
msgid "Custom Format"
msgstr ""

#: modules/dynamic-tags/pods/tags/pods-date.php:82
#: modules/dynamic-tags/tags/current-date-time.php:72
#: modules/dynamic-tags/tags/post-date.php:66
#: modules/dynamic-tags/tags/post-time.php:64
#: modules/dynamic-tags/toolset/tags/toolset-date.php:101
msgid "Documentation on date and time formatting"
msgstr ""

#: modules/dynamic-tags/tags/post-custom-field.php:19
msgid "Post Custom Field"
msgstr ""

#: modules/dynamic-tags/tags/post-date.php:18
msgid "Post Date"
msgstr ""

#: modules/dynamic-tags/tags/post-date.php:36
#: modules/dynamic-tags/tags/post-time.php:36
msgid "Post Published"
msgstr ""

#: modules/dynamic-tags/tags/post-date.php:37
#: modules/dynamic-tags/tags/post-time.php:37
msgid "Post Modified"
msgstr ""

#: modules/dynamic-tags/pods/tags/pods-date.php:62
#: modules/dynamic-tags/tags/post-date.php:46
#: modules/dynamic-tags/tags/post-time.php:46
#: modules/dynamic-tags/toolset/tags/toolset-date.php:81
#: modules/woocommerce/tags/product-price.php:21
#: modules/woocommerce/tags/product-rating.php:21
msgid "Format"
msgstr ""

#: modules/dynamic-tags/pods/tags/pods-date.php:70
#: modules/dynamic-tags/tags/post-date.php:54
#: modules/dynamic-tags/toolset/tags/toolset-date.php:89
msgid "Human Readable"
msgstr ""

#. translators: %s: Human readable date/time.
#: modules/dynamic-tags/tags/post-date.php:80
msgid "%s ago"
msgstr ""

#: modules/dynamic-tags/tags/post-excerpt.php:19
#: modules/theme-builder/widgets/post-excerpt.php:23
msgid "Post Excerpt"
msgstr ""

#: modules/dynamic-tags/tags/post-featured-image.php:27
#: modules/theme-builder/widgets/post-featured-image.php:22
msgid "Featured Image"
msgstr "Featured Image"

#: modules/dynamic-tags/tags/post-id.php:17
msgid "Post ID"
msgstr ""

#: modules/dynamic-tags/tags/post-terms.php:19
msgid "Post Terms"
msgstr ""

#: modules/dynamic-tags/tags/post-time.php:18
msgid "Post Time"
msgstr ""

#: modules/dynamic-tags/tags/post-url.php:19
msgid "Post URL"
msgstr ""

#: modules/dynamic-tags/tags/site-logo.php:17
#: modules/theme-builder/widgets/site-logo.php:26
#: modules/theme-builder/widgets/site-logo.php:56
#: modules/theme-builder/widgets/site-logo.php:63
msgid "Site Logo"
msgstr ""

#: modules/dynamic-tags/tags/site-tagline.php:17
msgid "Site Tagline"
msgstr ""

#: modules/dynamic-tags/tags/site-title.php:17
#: modules/theme-builder/widgets/site-title.php:23
msgid "Site Title"
msgstr ""

#: modules/dynamic-tags/tags/site-url.php:18
#: modules/theme-builder/widgets/site-logo.php:88
msgid "Site URL"
msgstr ""

#: modules/pricing/widgets/price-table.php:151
msgid "Currency Format"
msgstr ""

#: modules/query-control/controls/group-control-posts.php:113
#: modules/query-control/controls/group-control-query.php:51
#: modules/woocommerce/traits/products-trait.php:56
msgid "Current Query"
msgstr ""

#: modules/role-manager/module.php:56
msgid "Access to edit content only"
msgstr ""

#: modules/motion-fx/controls-group.php:41
msgid "Scrolling Effects"
msgstr ""

#: modules/sticky/module.php:49
msgid "Sticky"
msgstr ""

#: modules/sticky/module.php:85
msgid "Sticky On"
msgstr ""

#: modules/motion-fx/controls-group.php:140
#: modules/popup/display-settings/timing.php:183 modules/sticky/module.php:78
msgid "Desktop"
msgstr "Desktop"

#: modules/custom-code/module.php:371
#: modules/theme-builder/classes/conditions-manager.php:62
#: assets/js/app.js:2875
#: core/app/modules/site-editor/assets/js/molecules/site-template-footer.js:10
msgid "Instances"
msgstr ""

#: modules/theme-builder/classes/conditions-manager.php:115
#: assets/js/app.js:3279 assets/js/custom-code.js:784
#: core/app/modules/site-editor/assets/js/pages/conditions/condition-conflicts.js:21
msgid "Elementor recognized that you have set this location for other templates: "
msgstr ""

#: modules/query-control/controls/group-control-query.php:65
#: modules/table-of-contents/widgets/table-of-contents.php:103
#: modules/theme-builder/classes/conditions-repeater.php:28
#: assets/js/app.js:3498 assets/js/custom-code.js:1003
#: core/app/modules/site-editor/assets/js/pages/conditions/condition-type.js:8
msgid "Include"
msgstr ""

#: modules/theme-builder/classes/conditions-repeater.php:38
#: modules/theme-builder/conditions/general.php:24
#: modules/woocommerce/widgets/cart.php:57
#: modules/woocommerce/widgets/checkout.php:63
#: modules/woocommerce/widgets/product-additional-information.php:28
msgid "General"
msgstr "General"

#: modules/theme-builder/classes/locations-manager.php:405
#: modules/theme-builder/documents/single-base.php:87
msgid "Content Area"
msgstr ""

#. translators: %s: Location name.
#: modules/theme-builder/classes/locations-manager.php:478
msgid "Location '%s' is not a core location."
msgstr ""

#: modules/gallery/widgets/gallery.php:72
#: modules/loop-builder/documents/loop.php:150
#: modules/theme-builder/classes/locations-manager.php:530
#: modules/theme-builder/documents/single-base.php:24
#: modules/theme-builder/documents/single-base.php:44
#: modules/theme-builder/documents/single-base.php:135
#: modules/theme-builder/documents/single.php:15
msgid "Single"
msgstr "Single"

#: modules/theme-builder/conditions/archive.php:35
msgid "All Archives"
msgstr ""

#: modules/theme-builder/conditions/date.php:23
#: modules/theme-builder/documents/archive.php:91
msgid "Date Archive"
msgstr ""

#: modules/theme-builder/conditions/front-page.php:23
msgid "Front Page"
msgstr ""

#: modules/theme-builder/conditions/general.php:28
msgid "Entire Site"
msgstr ""

#. translators: %s: Taxonomy label.
#: modules/theme-builder/conditions/in-taxonomy.php:38
msgid "In %s"
msgstr ""

#: modules/theme-builder/conditions/not-found404.php:23
#: modules/theme-builder/module.php:246
msgid "404 Page"
msgstr ""

#: modules/carousel/widgets/base.php:90
msgid "Set how many slides are scrolled per swipe."
msgstr "Set how many slides are scrolled per swipe."

#: modules/assets-manager/asset-types/fonts-manager.php:294
msgid "Enter Font Family"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1406
#: modules/gallery/widgets/gallery.php:663
#: modules/gallery/widgets/gallery.php:824
#: modules/gallery/widgets/gallery.php:1076
#: modules/hotspot/widgets/hotspot.php:557
#: modules/mega-menu/widgets/mega-menu.php:513
#: modules/mega-menu/widgets/mega-menu.php:624
#: modules/mega-menu/widgets/mega-menu.php:1527
#: modules/page-transitions/module.php:261
#: modules/page-transitions/module.php:417 modules/popup/document.php:383
msgid "Animation Duration"
msgstr "Animation Duration"

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:166
msgid "TypeKit partners with the world’s leading type foundries to bring thousands of beautiful fonts to designers every day."
msgstr ""

#: modules/assets-manager/asset-types/icons/font-awesome-pro.php:108
msgid "Kit ID"
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-fonts-menu-item.php:18
#: modules/assets-manager/asset-types/fonts-manager.php:564
msgctxt "Elementor Font"
msgid "Custom Fonts"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:110
msgctxt "Font type taxonomy general name"
msgid "Font Types"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:111
msgctxt "Font type singular name"
msgid "Font Type"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:112
msgid "Search Font Types"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:113
msgid "Popular Font Types"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:114
msgid "All Font Types"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:115
msgid "Edit Font Type"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:116
msgid "Update Font Type"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:117
msgid "Add New Font Type"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:118
msgid "New Font Type Name"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:119
msgid "Separate Font Types with commas"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:120
msgid "Add or remove Font Types"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:121
msgid "Choose from the most used Font Types"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:122
msgid "No Font Types found."
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:123
msgid "Font Types"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:143
#: modules/assets-manager/asset-types/fonts-manager.php:146
#: modules/assets-manager/asset-types/fonts-manager.php:152
msgid "Font updated."
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:144
#: modules/assets-manager/asset-types/icons-manager.php:107
#: modules/custom-code/module.php:175
msgid "Custom field updated."
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:145
#: modules/assets-manager/asset-types/icons-manager.php:108
#: modules/custom-code/module.php:176
msgid "Custom field deleted."
msgstr ""

#. translators: %s: Date and time of the revision.
#: modules/assets-manager/asset-types/fonts-manager.php:148
msgid "Font restored to revision from %s"
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:149
#: modules/assets-manager/asset-types/fonts-manager.php:150
msgid "Font saved."
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:151
msgid "Font submitted."
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:153
msgid "Font draft updated."
msgstr ""

#: modules/assets-manager/asset-types/fonts-manager.php:330
msgid "Font Family"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:190
msgid "Upload"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:244
msgid "Add item"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:245
msgid "Row"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:334
msgid "Are you sure?"
msgstr ""

#: modules/assets-manager/classes/assets-base.php:337
msgid "Close"
msgstr "Close"

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:83
#: modules/assets-manager/classes/font-base.php:20
msgid "Elementor Is Making the Web Beautiful!!!"
msgstr ""

#: modules/assets-manager/asset-types/admin-menu-items/custom-fonts-promotion-menu-item.php:38
#: modules/assets-manager/asset-types/admin-menu-items/custom-fonts-promotion-menu-item.php:42
#: modules/assets-manager/asset-types/fonts-manager.php:530
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:20
msgid "Custom Fonts"
msgstr "Custom Fonts"

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:40
msgid "Manage Your Font Files"
msgstr ""

#. translators: %s: Font file format.
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:115
msgid "%s File"
msgstr ""

#. translators: %s: Font file format.
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:117
msgid "Upload font .%s file"
msgstr ""

#. translators: %s: Font file format.
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:119
msgid "Select .%s file"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:139
msgid "Add Font Variation"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:141
#: modules/assets-manager/classes/assets-base.php:191
#: modules/assets-manager/classes/assets-base.php:335 assets/js/app.js:3585
#: assets/js/custom-code.js:1090 assets/js/notes/notes-app.js:445
#: assets/js/notes/notes-app.js:737
#: core/app/modules/site-editor/assets/js/pages/conditions/conditions-rows.js:34
msgid "Delete"
msgstr "Delete"

#. translators: %s: Font family.
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:398
msgid "Font %s was not found."
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:411
msgid "Italic"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:412
msgid "Oblique"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:419
msgid "Bold"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:434
msgid "Embedded OpenType, Used by IE6-IE9 Browsers"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:435
msgid "The Web Open Font Format 2, Used by Super Modern Browsers"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:436
msgid "The Web Open Font Format, Used by Modern Browsers"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:437
msgid "TrueType Fonts, Used for better supporting Safari, Android, iOS"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:438
msgid "SVG fonts allow SVG to be used as glyphs when displaying text, Used by Legacy iOS"
msgstr ""

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:159
msgid "Fonts Families Found in project. Please note that typekit takes a few minutes to sync once you publish or update a project."
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:31
msgid "Call to Action"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:179
#: modules/hotspot/widgets/hotspot.php:372
#: modules/hotspot/widgets/hotspot.php:726
msgid "Min Width"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1343
#: modules/gallery/widgets/gallery.php:790
#: modules/gallery/widgets/gallery.php:1031
msgid "Entrance"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1355
#: modules/gallery/widgets/gallery.php:1043
msgid "Reaction"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1366
#: modules/gallery/widgets/gallery.php:802
#: modules/gallery/widgets/gallery.php:1054
msgid "Exit"
msgstr ""

#: modules/page-transitions/module.php:240
msgid "Fade Out Right"
msgstr ""

#: modules/page-transitions/module.php:247
msgid "Slide Out Left"
msgstr ""

#: modules/page-transitions/module.php:246
msgid "Slide Out Up"
msgstr ""

#: modules/page-transitions/module.php:244
msgid "Slide Out Down"
msgstr ""

#: modules/page-transitions/module.php:238
msgid "Fade Out"
msgstr ""

#: modules/forms/actions/getresponse.php:106
msgid "Day Of Cycle"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:201
#: modules/hotspot/widgets/hotspot.php:745
#: modules/table-of-contents/widgets/table-of-contents.php:450
msgid "Min Height"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:942
#: modules/call-to-action/widgets/call-to-action.php:1007
msgid "Description Color"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:956
#: modules/call-to-action/widgets/call-to-action.php:1021
msgid "Button Color"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1314
msgid "Hover Effects"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1333
#: modules/call-to-action/widgets/call-to-action.php:1462
#: modules/gallery/widgets/gallery.php:642
#: modules/gallery/widgets/gallery.php:780
#: modules/gallery/widgets/gallery.php:1021
#: modules/mega-menu/widgets/mega-menu.php:614
#: modules/mega-menu/widgets/mega-menu.php:1074
#: modules/nav-menu/widgets/nav-menu.php:453
#: modules/posts/traits/button-widget-trait.php:348
#: modules/woocommerce/widgets/cart.php:1151
#: modules/woocommerce/widgets/cart.php:1722
#: modules/woocommerce/widgets/checkout.php:1332
#: modules/woocommerce/widgets/checkout.php:1707
#: modules/woocommerce/widgets/my-account.php:1258
#: modules/woocommerce/widgets/my-account.php:1638
#: modules/woocommerce/widgets/purchase-summary.php:1412
msgid "Hover Animation"
msgstr "Hover Animation"

#: modules/page-transitions/module.php:218
msgid "Slide In Right"
msgstr ""

#: modules/page-transitions/module.php:220
msgid "Slide In Left"
msgstr ""

#: modules/page-transitions/module.php:219
msgid "Slide In Up"
msgstr ""

#: modules/page-transitions/module.php:217
msgid "Slide In Down"
msgstr ""

#: modules/page-transitions/module.php:211
msgid "Fade In"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1434
#: modules/gallery/widgets/gallery.php:1099
#: modules/hotspot/widgets/hotspot.php:446
msgid "Sequenced Animation"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1508
#: modules/flip-box/widgets/flip-box.php:231
#: modules/flip-box/widgets/flip-box.php:387
#: modules/gallery/widgets/gallery.php:751
#: modules/slides/widgets/slides.php:200
msgid "Blend Mode"
msgstr ""

#: modules/gallery/widgets/gallery.php:73
msgid "Multiple"
msgstr ""

#: modules/motion-fx/controls-group.php:331
msgid "Blur"
msgstr ""

#: modules/forms/fields/acceptance.php:18 modules/forms/widgets/form.php:52
msgid "Acceptance"
msgstr ""

#: modules/forms/fields/acceptance.php:33
msgid "Acceptance Text"
msgstr ""

#: modules/forms/fields/acceptance.php:44
msgid "Checked by Default"
msgstr ""

#: modules/forms/fields/date.php:60
msgid "Min. Date"
msgstr ""

#: modules/forms/fields/date.php:75
msgid "Max. Date"
msgstr ""

#: modules/forms/fields/date.php:90 modules/forms/fields/time.php:42
msgid "Native HTML5"
msgstr ""

#: modules/forms/fields/number.php:53
msgid "Min. Value"
msgstr ""

#: modules/forms/fields/number.php:64
msgid "Max. Value"
msgstr ""

#. translators: %s: The value of max field.
#: modules/forms/fields/number.php:83
msgid "The field value must be less than or equal to %s."
msgstr ""

#: modules/forms/fields/tel.php:23
msgid "Only numbers and phone characters (#, -, *, etc) are accepted."
msgstr ""

#: modules/forms/fields/upload.php:24 modules/forms/widgets/form.php:56
msgid "File Upload"
msgstr ""

#: modules/forms/fields/upload.php:42
msgid "Max. File Size"
msgstr ""

#: modules/forms/fields/upload.php:48
msgid "If you need to increase max upload size please contact your hosting."
msgstr ""

#: modules/forms/fields/upload.php:55
msgid "Allowed File Types"
msgstr ""

#: modules/forms/fields/upload.php:60
msgid "Enter the allowed file types, separated by a comma (jpg, gif, pdf, etc)."
msgstr ""

#: modules/forms/fields/upload.php:67
msgid "Multiple Files"
msgstr ""

#: modules/forms/fields/upload.php:78
msgid "Max. Files"
msgstr ""

#: modules/forms/fields/upload.php:277
msgid "There is no error, the file uploaded with success."
msgstr ""

#. translators: %s: MAX_FILE_SIZE
#: modules/forms/fields/upload.php:281
msgid "The uploaded file exceeds the %s directive that was specified in the HTML form."
msgstr ""

#: modules/forms/fields/upload.php:282
msgid "The uploaded file was only partially uploaded."
msgstr ""

#: modules/forms/fields/upload.php:283
msgid "No file was uploaded."
msgstr ""

#: modules/forms/fields/upload.php:284
msgid "Missing a temporary folder."
msgstr ""

#: modules/forms/fields/upload.php:285
msgid "Failed to write file to disk."
msgstr ""

#. translators: %s: phpinfo()
#: modules/forms/fields/upload.php:287
msgid "A PHP extension stopped the file upload. PHP does not provide a way to ascertain which extension caused the file upload to stop; examining the list of loaded extensions with %s may help."
msgstr ""

#: modules/forms/fields/upload.php:330
msgid "This file type is not allowed."
msgstr ""

#: modules/forms/fields/upload.php:113 modules/forms/fields/upload.php:335
msgid "This file exceeds the maximum allowed size."
msgstr ""

#: modules/forms/fields/upload.php:488
msgid "There was an error while trying to upload your file."
msgstr ""

#: modules/forms/fields/upload.php:491
msgid "Upload directory is not writable or does not exist."
msgstr ""

#: modules/forms/widgets/form.php:186
msgid "Multiple Selection"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:109
msgid "Image Resolution"
msgstr ""

#: modules/forms/actions/activecampaign.php:76
#: modules/forms/actions/convertkit.php:71 modules/forms/actions/drip.php:74
#: modules/forms/actions/getresponse.php:71
#: modules/forms/actions/mailchimp.php:79
#: modules/forms/actions/mailerlite.php:74
msgid "Use this field to set a custom API Key for the current form"
msgstr ""

#: modules/forms/actions/activecampaign.php:33
#: modules/forms/actions/activecampaign.php:40
#: modules/forms/actions/activecampaign.php:289
msgid "ActiveCampaign"
msgstr ""

#: modules/forms/actions/activecampaign.php:88
msgid "Use this field to set a custom API URL for the current form"
msgstr ""

#: modules/forms/actions/activecampaign.php:135
#: modules/forms/actions/convertkit.php:108 modules/forms/actions/drip.php:132
#: modules/forms/actions/mailchimp.php:126
#: modules/woocommerce/widgets/product-meta.php:338
#: modules/woocommerce/widgets/product-meta.php:402
msgid "Tags"
msgstr ""

#: modules/forms/actions/activecampaign.php:137
#: modules/forms/actions/drip.php:134
msgid "Add as many tags as you want, comma separated."
msgstr ""

#: modules/forms/actions/activecampaign.php:86
#: modules/forms/actions/activecampaign.php:299
msgid "API URL"
msgstr ""

#: modules/forms/actions/activecampaign.php:313
#: modules/forms/actions/convertkit.php:262 modules/forms/actions/drip.php:301
#: modules/forms/actions/getresponse.php:310
#: modules/forms/actions/mailchimp.php:463
#: modules/forms/actions/mailerlite.php:261
msgid "Validate API Key"
msgstr ""

#: modules/forms/actions/convertkit.php:28
#: modules/forms/actions/convertkit.php:35
#: modules/forms/actions/convertkit.php:244
msgid "ConvertKit"
msgstr ""

#: modules/forms/actions/drip.php:28 modules/forms/actions/drip.php:35
#: modules/forms/actions/drip.php:283
msgid "Drip"
msgstr ""

#: modules/forms/actions/drip.php:81
msgid "Account"
msgstr ""

#: modules/forms/actions/drip.php:108
msgid "Send Additional Data to Drip"
msgstr ""

#: modules/forms/actions/drip.php:122
msgid "Send all form fields to drip as custom fields"
msgstr ""

#: modules/forms/actions/mailerlite.php:135
msgid "Integration requires an email field"
msgstr ""

#: modules/forms/actions/getresponse.php:28
#: modules/forms/actions/getresponse.php:35
#: modules/forms/actions/getresponse.php:292
msgid "GetResponse"
msgstr ""

#: modules/forms/actions/activecampaign.php:74
#: modules/forms/actions/convertkit.php:69 modules/forms/actions/drip.php:69
#: modules/forms/actions/getresponse.php:69
#: modules/forms/actions/mailchimp.php:74
#: modules/forms/actions/mailerlite.php:69
msgid "Custom API Key"
msgstr ""

#: modules/dynamic-tags/tags/post-custom-field.php:87
#: modules/forms/classes/activecampaign-handler.php:66
#: modules/forms/classes/convertkit-handler.php:76
#: modules/forms/classes/convertkit-handler.php:99
#: modules/forms/classes/drip-handler.php:63
#: modules/forms/classes/getresponse-handler.php:58
#: modules/forms/classes/mailerlite-handler.php:66
#: modules/theme-builder/documents/theme-section-document.php:29
#: modules/theme-builder/module.php:195
msgid "Select..."
msgstr ""

#: modules/forms/classes/ajax-handler.php:274
msgid "This message is not visible to site visitors."
msgstr ""

#. translators: %s: Integration label.
#: modules/forms/classes/integration-base.php:40
msgid "You can also set a different %s by choosing \"Custom\"."
msgstr ""

#: modules/forms/widgets/form.php:913
msgid "Form ID"
msgstr ""

#: modules/forms/widgets/form.php:767 modules/forms/widgets/form.php:919
#: modules/posts/traits/button-widget-trait.php:210
msgid "Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows `A-z 0-9` & underscore chars without spaces."
msgstr ""

#: modules/library/module.php:154
msgid "Want to learn more about Elementor library?"
msgstr ""

#: modules/posts/traits/button-widget-trait.php:43
msgid "Click here"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:22
msgid "Author Box"
msgstr ""

#: modules/dynamic-tags/tags/author-info.php:19
#: modules/theme-elements/widgets/author-box.php:44
msgid "Author Info"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:55
msgid "Current Author"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:64
#: modules/theme-elements/widgets/author-box.php:96
msgid "Profile Picture"
msgstr ""

#: modules/dynamic-tags/tags/user-info.php:79
#: modules/theme-elements/widgets/author-box.php:115
msgid "Display Name"
msgstr ""

#: modules/dynamic-tags/tags/author-info.php:56
#: modules/dynamic-tags/tags/user-info.php:85
#: modules/theme-elements/widgets/author-box.php:173
msgid "Website"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:174
msgid "Posts Archive"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:179
#: modules/theme-elements/widgets/author-box.php:226
msgid "Link for the Author Name and Image"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:186
#: modules/theme-elements/widgets/author-box.php:233
#: modules/theme-elements/widgets/author-box.php:534
msgid "Biography"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:203
#: modules/theme-elements/widgets/author-box.php:250
msgid "Archive Button"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:265
msgid "Archive Text"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:267
msgid "All Posts"
msgstr ""

#: modules/theme-elements/widgets/breadcrumbs.php:142
#: modules/woocommerce/widgets/breadcrumb.php:66
#: modules/woocommerce/widgets/cart.php:793
#: modules/woocommerce/widgets/cart.php:808
#: modules/woocommerce/widgets/cart.php:2121
#: modules/woocommerce/widgets/checkout.php:929
#: modules/woocommerce/widgets/checkout.php:946
#: modules/woocommerce/widgets/checkout.php:2864
#: modules/woocommerce/widgets/checkout.php:2881
#: modules/woocommerce/widgets/product-rating.php:73
msgid "Link Color"
msgstr "Link Colour"

#: modules/theme-elements/widgets/post-comments.php:20
msgid "Post Comments"
msgstr ""

#: modules/theme-elements/widgets/post-comments.php:56
msgid "Theme Comments"
msgstr ""

#: modules/theme-elements/widgets/post-comments.php:58
msgid "The Theme Comments skin uses the currently active theme comments design and layout to display the comment form and comments."
msgstr ""

#: modules/dynamic-tags/tags/internal-url.php:85
#: modules/dynamic-tags/tags/internal-url.php:102
#: modules/dynamic-tags/tags/internal-url.php:116
#: modules/dynamic-tags/tags/internal-url.php:130
#: modules/query-control/controls/group-control-posts.php:67
#: modules/query-control/controls/group-control-query.php:76
#: modules/query-control/controls/group-control-query.php:190
#: modules/query-control/controls/group-control-related.php:72
#: modules/query-control/module.php:92
#: modules/theme-elements/widgets/post-comments.php:79
#: modules/theme-elements/widgets/sitemap.php:92
#: modules/woocommerce/widgets/products-deprecated.php:177
msgid "Search & Select"
msgstr ""

#: modules/theme-elements/widgets/post-comments.php:109
msgid "Switch on comments from either the discussion box on the WordPress post edit screen or from the WordPress discussion settings."
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:103
msgid "Arrows Type"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:107
msgid "Double Angle"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:109
msgid "Chevron Circle"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:110
msgid "Caret"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:112
msgid "Long Arrow"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:113
msgid "Arrow Circle"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:114
msgid "Arrow Circle Negative"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:137
#: modules/theme-elements/widgets/post-navigation.php:437
msgid "Borders"
msgstr ""

#: modules/theme-elements/widgets/post-info.php:346
msgid "Choose Icon"
msgstr ""

#: modules/carousel/widgets/base.php:89
msgid "Slides to Scroll"
msgstr "Slides to Scroll"

#: modules/theme-elements/widgets/breadcrumbs.php:21
#: modules/theme-elements/widgets/breadcrumbs.php:40
#: modules/theme-elements/widgets/breadcrumbs.php:102
msgid "Breadcrumbs"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:21
#: modules/theme-elements/widgets/post-navigation.php:44
msgid "Post Navigation"
msgstr ""

#: modules/carousel/widgets/base.php:559 modules/forms/widgets/form.php:677
#: modules/forms/widgets/form.php:684 modules/forms/widgets/form.php:685
#: modules/loop-builder/widgets/base.php:282
#: modules/theme-elements/widgets/post-navigation.php:67
msgid "Previous"
msgstr ""

#: modules/carousel/widgets/base.php:563 modules/forms/widgets/form.php:662
#: modules/forms/widgets/form.php:669 modules/forms/widgets/form.php:670
#: modules/loop-builder/widgets/base.php:289
#: modules/theme-elements/widgets/post-navigation.php:79
#: modules/theme-elements/widgets/post-navigation.php:523
#: assets/js/editor.js:6112
msgid "Next"
msgstr ""

#: modules/dynamic-tags/tags/post-title.php:17
#: modules/theme-builder/widgets/post-title.php:16
#: modules/theme-elements/widgets/post-navigation.php:126
msgid "Post Title"
msgstr ""

#: modules/theme-elements/widgets/search-form.php:24
#: modules/theme-elements/widgets/search-form.php:46
msgid "Search Form"
msgstr ""

#: modules/theme-elements/widgets/search-form.php:59
msgid "Full Screen"
msgstr ""

#: modules/theme-elements/widgets/search-form.php:73
#: modules/theme-elements/widgets/search-form.php:115
#: modules/theme-elements/widgets/search-form.php:131
#: modules/theme-elements/widgets/search-form.php:795
#: modules/theme-elements/widgets/search-form.php:799
#: modules/theme-elements/widgets/search-form.php:804
#: modules/theme-elements/widgets/search-form.php:822
#: modules/theme-elements/widgets/search-form.php:825
#: modules/theme-elements/widgets/search-form.php:910
#: modules/theme-elements/widgets/search-form.php:914
#: modules/theme-elements/widgets/search-form.php:919
msgid "Search"
msgstr "Search"

#: modules/theme-elements/widgets/post-navigation.php:111
#: modules/theme-elements/widgets/post-navigation.php:341
#: modules/theme-elements/widgets/search-form.php:135
msgid "Arrow"
msgstr ""

#: modules/theme-elements/widgets/search-form.php:234
msgid "Input"
msgstr ""

#: modules/theme-elements/widgets/search-form.php:360
#: modules/woocommerce/widgets/cart.php:928
#: modules/woocommerce/widgets/checkout.php:1104
#: modules/woocommerce/widgets/my-account.php:1036
#: modules/woocommerce/widgets/product-add-to-cart.php:583
msgid "Focus"
msgstr ""

#. translators: 1: Bold text opening tag, 2: Bold text closing tag.
#: license/admin.php:339
msgid "%1$sYour license key doesn't match your current domain%2$s. This is most likely due to a change in the domain URL of your site (including HTTPS/SSL migration). Please deactivate the license and then reactivate it again."
msgstr ""

#: license/admin.php:60
msgid "Reactivate License"
msgstr ""

#: license/admin.php:53
msgid "License Mismatch"
msgstr ""

#. translators: 1: Bold text opening tag, 2: Bold text closing tag.
#: license/admin.php:56
msgid "%1$sYour license key doesn't match your current domain%2$s. This is most likely due to a change in the domain URL. Please deactivate the license and then reactivate it again."
msgstr ""

#. translators: %s: Days to expire.
#: license/admin.php:426
msgid "Your License Will Expire in %s."
msgstr ""

#: license/api.php:388
msgid "An error occurred. Please check your internet connection and try again. If the problem persists, contact our support."
msgstr ""

#: license/admin.php:589
msgid "Enter your license key here, to activate Elementor Pro, and get feature updates, premium support and unlimited access to the template library."
msgstr ""

#: license/admin.php:615 license/admin.php:634
msgid "Your License Key"
msgstr "Your Licence Key"

#: license/admin.php:617
msgid "Please enter your license key here"
msgstr "Please enter your licence key here"

#: license/admin.php:288
msgid "Mismatch"
msgstr ""

#: license/admin.php:690
msgid "Please activate your license to get feature updates, premium support and unlimited access to the template library."
msgstr "Please activate your licence to get feature updates, premium support and unlimited access to the template library."

#: license/admin.php:381
msgid "Welcome to Elementor Pro!"
msgstr ""

#: license/admin.php:42
msgid "Your License Is Inactive"
msgstr ""

#. translators: 1: Bold text opening tag, 2: Bold text closing tag.
#: license/admin.php:45 license/api.php:374
msgid "%1$sYour license key has been cancelled%2$s (most likely due to a refund request). Please consider acquiring a new license."
msgstr ""

#: modules/forms/actions/mailpoet3.php:28
msgid "MailPoet 3"
msgstr ""

#: modules/forms/classes/ajax-handler.php:44
msgid "Subscriber already exists."
msgstr ""

#: modules/carousel/widgets/media-carousel.php:483
#: modules/carousel/widgets/media-carousel.php:574
#: modules/gallery/widgets/gallery.php:479
#: modules/gallery/widgets/gallery.php:685
#: modules/gallery/widgets/gallery.php:711
#: modules/hotspot/widgets/hotspot.php:435 modules/popup/document.php:334
#: modules/popup/document.php:484
msgid "Overlay"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:501
#: modules/carousel/widgets/media-carousel.php:506
#: modules/dynamic-tags/tags/featured-image-data.php:89
#: modules/gallery/widgets/gallery.php:500
#: modules/gallery/widgets/gallery.php:517
#: modules/lottie/widgets/lottie.php:123 modules/lottie/widgets/lottie.php:129
#: modules/lottie/widgets/lottie.php:694
msgid "Caption"
msgstr "Caption"

#: modules/carousel/widgets/media-carousel.php:667
#: modules/video-playlist/widgets/video-playlist.php:585
#: modules/woocommerce/widgets/product-images.php:102
msgid "Thumbnails"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:684
msgid "Ratio"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:23
msgid "Testimonial Carousel"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:49
#: modules/carousel/widgets/testimonial-carousel.php:103
#: modules/woocommerce/widgets/menu-cart.php:110
msgid "Bubble"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:63
msgid "Image Inline"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:64
msgid "Image Stacked"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:65
msgid "Image Above"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:66
msgid "Image Left"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:67
msgid "Image Right"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:560
#: modules/carousel/widgets/testimonial-carousel.php:575
#: modules/carousel/widgets/testimonial-carousel.php:583
#: modules/carousel/widgets/testimonial-carousel.php:591
msgid "CEO"
msgstr ""

#: modules/carousel/widgets/base.php:57
msgid "Effect"
msgstr "Effect"

#: modules/carousel/widgets/base.php:62
msgid "Cube"
msgstr ""

#: modules/carousel/widgets/base.php:75
#: modules/carousel/widgets/media-carousel.php:734
msgid "Slides Per View"
msgstr ""

#: base/base-carousel-trait.php:733 base/base-carousel-trait.php:896
#: modules/carousel/widgets/base.php:177
msgid "Fraction"
msgstr ""

#: base/base-carousel-trait.php:734 modules/carousel/widgets/base.php:178
msgid "Progress"
msgstr ""

#: base/base-carousel-trait.php:164
#: modules/call-to-action/widgets/call-to-action.php:1562
#: modules/carousel/widgets/base.php:189 modules/lottie/widgets/lottie.php:668
#: modules/mega-menu/widgets/mega-menu.php:1082
#: modules/woocommerce/settings/settings-woocommerce.php:638
#: modules/woocommerce/widgets/cart.php:975
#: modules/woocommerce/widgets/cart.php:1134
#: modules/woocommerce/widgets/cart.php:1705
#: modules/woocommerce/widgets/checkout.php:1152
#: modules/woocommerce/widgets/checkout.php:1315
#: modules/woocommerce/widgets/checkout.php:1690
#: modules/woocommerce/widgets/my-account.php:1083
#: modules/woocommerce/widgets/my-account.php:1241
#: modules/woocommerce/widgets/my-account.php:1621
#: modules/woocommerce/widgets/product-add-to-cart.php:364
#: modules/woocommerce/widgets/product-add-to-cart.php:623
#: modules/woocommerce/widgets/purchase-summary.php:1395
msgid "Transition Duration"
msgstr "Transition Duration"

#: modules/carousel/widgets/base.php:250 modules/slides/widgets/slides.php:629
msgid "Pause on Interaction"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:31
msgid "Media Carousel"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:71
#: modules/dynamic-tags/tags/lightbox.php:22
#: modules/gallery/widgets/gallery.php:284
msgid "Lightbox"
msgstr "Lightbox"

#: modules/carousel/widgets/media-carousel.php:90
msgid "UI Color"
msgstr "UI Colour"

#: modules/carousel/widgets/media-carousel.php:101
msgid "UI Hover Color"
msgstr "UI Hover Colour"

#: modules/carousel/widgets/media-carousel.php:112
msgid "Video Width"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:175
#: modules/gallery/widgets/gallery.php:259 modules/lottie/widgets/lottie.php:60
#: modules/theme-builder/widgets/site-logo.php:90
msgid "Media File"
msgstr "Media File"

#: modules/carousel/widgets/media-carousel.php:176
#: modules/gallery/widgets/gallery.php:260
#: modules/lottie/widgets/lottie.php:175
#: modules/theme-builder/widgets/site-logo.php:89
#: modules/theme-elements/widgets/post-info.php:315
msgid "Custom URL"
msgstr "Custom URL"

#: modules/carousel/widgets/media-carousel.php:205
msgid "Video Link"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:210
msgid "Enter your video link"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:384
#: modules/nested-carousel/widgets/nested-carousel.php:28
msgid "Carousel"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:385
msgid "Slideshow"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:386
msgid "Coverflow"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:403
msgid "Image Fit"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:408
msgid "Contain"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:426
#: modules/video-playlist/widgets/video-playlist.php:498
#: modules/video-playlist/widgets/video-playlist.php:596
msgid "Play Icon"
msgstr "Play Icon"

#: modules/carousel/widgets/media-carousel.php:468
#: modules/mega-menu/widgets/mega-menu.php:856
#: modules/mega-menu/widgets/mega-menu.php:1026
#: modules/mega-menu/widgets/mega-menu.php:1127
#: modules/video-playlist/widgets/video-playlist.php:899
#: modules/video-playlist/widgets/video-playlist.php:1106
msgctxt "Text Shadow Control"
msgid "Shadow"
msgstr "Shadow"

#: modules/nav-menu/widgets/nav-menu.php:385
msgid "Hamburger"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:297
msgid "Mobile Dropdown"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:750
#: modules/nav-menu/widgets/nav-menu.php:334
msgid "Breakpoint"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:271
#: modules/nav-menu/widgets/nav-menu.php:348
msgid "Full Width"
msgstr "Full Width"

#: modules/nav-menu/widgets/nav-menu.php:350
msgid "Stretch the dropdown of the menu to full width."
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:380
#: modules/nav-menu/widgets/nav-menu.php:1189
msgid "Toggle Button"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:930
msgid "On desktop, this will affect the submenu. On mobile, this will affect the entire menu."
msgstr ""

#: modules/social/widgets/facebook-page.php:47
msgid "Paste the URL of the Facebook page."
msgstr ""

#: modules/blockquote/widgets/blockquote.php:33
#: modules/blockquote/widgets/blockquote.php:48
msgid "Blockquote"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:59
msgid "Quotation"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:60
msgid "Boxed"
msgstr "Boxed"

#: modules/blockquote/widgets/blockquote.php:61
msgid "Clean"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:117
#: modules/carousel/widgets/reviews.php:534
#: modules/carousel/widgets/reviews.php:669
#: modules/carousel/widgets/reviews.php:677
#: modules/carousel/widgets/reviews.php:685
#: modules/carousel/widgets/testimonial-carousel.php:548
#: modules/carousel/widgets/testimonial-carousel.php:574
#: modules/carousel/widgets/testimonial-carousel.php:582
#: modules/carousel/widgets/testimonial-carousel.php:590
#: modules/theme-elements/widgets/author-box.php:135
msgid "John Doe"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:125
msgid "Tweet Button"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:175
#: modules/blockquote/widgets/blockquote.php:896
#: modules/blockquote/widgets/blockquote.php:944
msgid "Tweet"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:401
#: modules/carousel/widgets/reviews.php:335
#: modules/share-buttons/widgets/share-buttons.php:463
msgid "Official"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:649
#: modules/call-to-action/widgets/call-to-action.php:393
#: modules/hotspot/widgets/hotspot.php:328
#: modules/hotspot/widgets/hotspot.php:888
#: modules/posts/skins/skin-classic.php:31
#: modules/table-of-contents/widgets/table-of-contents.php:363
#: modules/woocommerce/widgets/products-base.php:596
msgid "Box"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:780
msgid "Quote"
msgstr ""

#: modules/social/classes/facebook-sdk-manager.php:120
#: modules/social/classes/facebook-sdk-manager.php:176
msgid "Facebook SDK"
msgstr ""

#: modules/social/classes/facebook-sdk-manager.php:130
msgid "If you are using the Facebook Comments Widget, you can add moderating options through your application. Note that this option will not work on local sites and on domains that don't have public access."
msgstr ""

#: modules/social/classes/facebook-sdk-manager.php:134
msgid "App ID"
msgstr ""

#: modules/social/classes/facebook-sdk-manager.php:169
msgid "Facebook App ID is not valid"
msgstr ""

#: modules/social/widgets/facebook-button.php:21
msgid "Facebook Button"
msgstr ""

#: modules/social/widgets/facebook-button.php:49
msgid "Like"
msgstr ""

#: modules/social/widgets/facebook-button.php:50
msgid "Recommend"
msgstr ""

#: modules/social/widgets/facebook-button.php:62
msgid "Standard"
msgstr ""

#: modules/social/widgets/facebook-button.php:64
msgid "Button Count"
msgstr ""

#: modules/social/widgets/facebook-button.php:65
msgid "Box Count"
msgstr ""

#: modules/social/widgets/facebook-button.php:86
msgid "Color Scheme"
msgstr ""

#: modules/social/widgets/facebook-button.php:99
msgid "Share Button"
msgstr "Share Button"

#: modules/social/widgets/facebook-button.php:111
msgid "Faces"
msgstr ""

#: modules/social/widgets/facebook-button.php:175
#: modules/social/widgets/facebook-comments.php:122
#: modules/social/widgets/facebook-embed.php:182
#: modules/social/widgets/facebook-page.php:130
msgid "Please enter a valid URL"
msgstr ""

#: modules/social/widgets/facebook-comments.php:20
msgid "Facebook Comments"
msgstr ""

#: modules/social/widgets/facebook-comments.php:35
msgid "Comments Box"
msgstr ""

#: modules/query-control/controls/group-control-query.php:352
#: modules/social/widgets/facebook-comments.php:44
msgid "Comment Count"
msgstr ""

#: modules/social/widgets/facebook-comments.php:49
msgid "Minimum number of comments: 5"
msgstr ""

#: modules/social/widgets/facebook-comments.php:63
msgid "Social"
msgstr ""

#: modules/social/widgets/facebook-comments.php:64
msgid "Reverse Time"
msgstr ""

#: modules/social/widgets/facebook-embed.php:19
msgid "Facebook Embed"
msgstr ""

#: modules/social/widgets/facebook-embed.php:38
msgid "Embed"
msgstr ""

#: modules/dynamic-tags/module.php:130
#: modules/social/widgets/facebook-embed.php:51
msgid "Post"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:149
#: modules/dynamic-tags/tags/lightbox.php:44
#: modules/social/widgets/facebook-embed.php:52
msgid "Video"
msgstr "Video"

#: modules/social/widgets/facebook-embed.php:53
msgid "Comment"
msgstr ""

#: modules/social/widgets/facebook-embed.php:70
msgid "Hover over the date next to the post, and copy its link address."
msgstr ""

#: modules/social/widgets/facebook-embed.php:86
msgid "Hover over the date next to the video, and copy its link address."
msgstr ""

#: modules/social/widgets/facebook-embed.php:102
msgid "Hover over the date next to the comment, and copy its link address."
msgstr ""

#: modules/social/widgets/facebook-embed.php:109
msgid "Parent Comment"
msgstr ""

#: modules/social/widgets/facebook-embed.php:112
msgid "Set to include parent comment (if URL is a reply)."
msgstr ""

#: modules/social/widgets/facebook-embed.php:122
msgid "Full Post"
msgstr ""

#: modules/social/widgets/facebook-embed.php:125
msgid "Show the full text of the post"
msgstr ""

#: modules/social/widgets/facebook-embed.php:135
msgid "Allow Full Screen"
msgstr ""

#: modules/social/widgets/facebook-embed.php:159
#: modules/woocommerce/widgets/product-meta.php:275
msgid "Captions"
msgstr ""

#: modules/social/widgets/facebook-embed.php:162
msgid "Show captions if available (only on desktop)."
msgstr ""

#: modules/social/widgets/facebook-embed.php:176
msgid "Please set the embed type"
msgstr ""

#: modules/social/widgets/facebook-page.php:19
msgid "Facebook Page"
msgstr ""

#: modules/social/widgets/facebook-page.php:62
msgid "Timeline"
msgstr ""

#: modules/social/widgets/facebook-page.php:63
msgid "Events"
msgstr ""

#: modules/forms/widgets/form.php:1610
#: modules/payments/classes/payment-button.php:447
#: modules/social/widgets/facebook-page.php:64
#: modules/woocommerce/widgets/checkout.php:830
#: modules/woocommerce/widgets/menu-cart.php:2102
msgid "Messages"
msgstr ""

#: modules/social/widgets/facebook-page.php:72
msgid "Small Header"
msgstr ""

#: modules/social/widgets/facebook-page.php:90
msgid "Profile Photos"
msgstr ""

#: modules/social/widgets/facebook-page.php:99
msgid "Custom CTA Button"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:24
msgid "Animated Headline"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:39
#: modules/animated-headline/widgets/animated-headline.php:377
msgid "Headline"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:50
msgid "Highlighted"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:51
msgid "Rotating"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:90
msgctxt "Shapes"
msgid "Circle"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:91
msgctxt "Shapes"
msgid "Curly"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:92
msgctxt "Shapes"
msgid "Underline"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:93
msgctxt "Shapes"
msgid "Double"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:94
msgctxt "Shapes"
msgid "Double Underline"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:95
msgctxt "Shapes"
msgid "Underline Zigzag"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:96
msgctxt "Shapes"
msgid "Diagonal"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:97
msgctxt "Shapes"
msgid "Strikethrough"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:111
#: modules/payments/classes/payment-button.php:435
msgid "Before Text"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:119
msgid "This page is"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:129
msgid "Highlighted Text"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:137
msgid "Amazing"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:150
msgid "Rotating Text"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:152
msgid "Enter each word in a separate line"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:171
#: modules/payments/classes/payment-button.php:436
msgid "After Text"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:287
#: modules/table-of-contents/widgets/table-of-contents.php:85
#: modules/theme-builder/documents/theme-document.php:380
#: modules/theme-elements/widgets/author-box.php:150
#: modules/theme-elements/widgets/breadcrumbs.php:70
msgid "HTML Tag"
msgstr "HTML Tag"

#: modules/animated-headline/widgets/animated-headline.php:352
msgid "Bring to Front"
msgstr "Bring to Front"

#: modules/animated-headline/widgets/animated-headline.php:364
msgid "Rounded Edges"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:419
msgid "Animated Text"
msgstr ""

#: modules/forms/widgets/form.php:59
msgid "Hidden"
msgstr ""

#: modules/mega-menu/module.php:38 modules/mega-menu/widgets/mega-menu.php:35
#: modules/nav-menu/widgets/nav-menu.php:77
#: modules/nav-menu/widgets/nav-menu.php:1552
msgid "Menu"
msgstr "Menu"

#: base/base-carousel-trait.php:315 base/base-carousel-trait.php:477
#: base/base-carousel-trait.php:1198
#: modules/mega-menu/widgets/mega-menu.php:308
#: modules/nav-menu/widgets/nav-menu.php:116 modules/popup/document.php:272
#: modules/progress-tracker/widgets/progress-tracker.php:56
#: modules/video-playlist/widgets/video-playlist.php:52
#: modules/woocommerce/widgets/my-account.php:55
msgid "Horizontal"
msgstr "Horizontal"

#: base/base-carousel-trait.php:362 base/base-carousel-trait.php:524
#: base/base-carousel-trait.php:1260 modules/nav-menu/widgets/nav-menu.php:117
#: modules/popup/document.php:303
#: modules/video-playlist/widgets/video-playlist.php:53
#: modules/woocommerce/widgets/my-account.php:54
msgid "Vertical"
msgstr "Vertical"

#: modules/mega-menu/widgets/mega-menu.php:309
#: modules/nav-menu/widgets/nav-menu.php:118
#: modules/nav-menu/widgets/nav-menu.php:922
msgid "Dropdown"
msgstr ""

#: modules/theme-elements/widgets/search-form.php:174
#: modules/theme-elements/widgets/search-form.php:603 assets/js/app.js:4105
#: core/app/modules/site-editor/assets/js/part-actions/dialogs-and-buttons.js:65
msgid "Toggle"
msgstr "Toggle"

#: modules/loop-filter/widgets/taxonomy-filter.php:124
#: modules/loop-filter/widgets/taxonomy-filter.php:199
#: modules/mega-menu/widgets/mega-menu.php:333
#: modules/nav-menu/widgets/nav-menu.php:143
#: modules/woocommerce/widgets/my-account.php:94
msgid "Stretch"
msgstr "Stretch"

#: modules/gallery/widgets/gallery.php:374
#: modules/nav-menu/widgets/nav-menu.php:157
msgid "Pointer"
msgstr ""

#: modules/gallery/widgets/gallery.php:379
#: modules/nav-menu/widgets/nav-menu.php:162
#: modules/table-of-contents/widgets/table-of-contents.php:625
#: modules/table-of-contents/widgets/table-of-contents.php:658
#: modules/table-of-contents/widgets/table-of-contents.php:689
msgid "Underline"
msgstr ""

#: modules/gallery/widgets/gallery.php:380
#: modules/nav-menu/widgets/nav-menu.php:163
msgid "Overline"
msgstr ""

#: modules/gallery/widgets/gallery.php:381
#: modules/nav-menu/widgets/nav-menu.php:164
msgid "Double Line"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:272
msgid "Submenu Indicator"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:108
msgid "Chevron"
msgstr ""

#: modules/theme-elements/widgets/post-navigation.php:106
msgid "Angle"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:367
msgid "Aside"
msgstr "Aside"

#: modules/lottie/widgets/lottie.php:556
#: modules/page-transitions/module.php:618
msgid "Max Width"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:519
msgid "Toggle Align"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:557
msgid "Main Menu"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:244
#: modules/mega-menu/widgets/mega-menu.php:763
#: modules/mega-menu/widgets/mega-menu.php:1738
msgid "Menu Items"
msgstr ""

#: modules/gallery/widgets/gallery.php:1232
#: modules/gallery/widgets/gallery.php:1271
#: modules/nav-menu/widgets/nav-menu.php:651
#: modules/nav-menu/widgets/nav-menu.php:693
msgid "Pointer Color"
msgstr ""

#: modules/gallery/widgets/gallery.php:1293
#: modules/nav-menu/widgets/nav-menu.php:821
msgid "Pointer Width"
msgstr ""

#: modules/nav-menu/widgets/nav-menu.php:849
#: modules/nav-menu/widgets/nav-menu.php:1097
#: modules/posts/skins/skin-cards.php:417
msgid "Horizontal Padding"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:631
#: modules/nav-menu/widgets/nav-menu.php:866
#: modules/nav-menu/widgets/nav-menu.php:1111
#: modules/posts/skins/skin-cards.php:437
msgid "Vertical Padding"
msgstr ""

#: modules/global-widget/widgets/global-widget.php:123
msgid "Global"
msgstr "Global"

#: assets/js/editor.js:2549
msgid "Linked to Global"
msgstr ""

#: assets/js/editor.js:268 assets/js/loop.cfa59b67362d5bf08739.bundle.js:107
#: assets/js/preloaded-elements-handlers.js:542 assets/js/preview.js:109
msgid "Save %s"
msgstr ""

#: core/admin/admin.php:176
msgid "Rollback Pro Version"
msgstr ""

#: core/admin/admin.php:184
msgid "Warning: Please backup your database before making the rollback."
msgstr "Warning: Please backup your database before making the rollback."

#: core/admin/admin.php:228
msgid "Rollback to Previous Version"
msgstr "Rollback to Previous Version"

#: modules/query-control/controls/group-control-query.php:158
#: modules/query-control/module.php:78
#: modules/table-of-contents/widgets/table-of-contents.php:145
#: modules/theme-builder/classes/conditions-repeater.php:29
#: modules/theme-elements/widgets/sitemap.php:78
#: modules/woocommerce/widgets/products-deprecated.php:163
#: assets/js/app.js:3501 assets/js/custom-code.js:1006
#: core/app/modules/site-editor/assets/js/pages/conditions/condition-type.js:12
msgid "Exclude"
msgstr ""

#: modules/query-control/controls/group-control-query.php:173
#: modules/query-control/module.php:82
#: modules/theme-elements/widgets/post-comments.php:68
#: modules/theme-elements/widgets/sitemap.php:82
#: modules/woocommerce/traits/products-trait.php:80
#: modules/woocommerce/widgets/products-deprecated.php:167
msgid "Current Post"
msgstr ""

#: modules/posts/skins/skin-cards.php:29
#: modules/theme-builder/skins/posts-archive-skin-cards.php:24
msgid "Cards"
msgstr ""

#: modules/posts/skins/skin-cards.php:78
msgid "Show Image"
msgstr ""

#: modules/forms/widgets/form.php:361 modules/posts/skins/skin-cards.php:116
#: modules/posts/skins/skin-cards.php:301
msgid "Badge"
msgstr ""

#: modules/posts/skins/skin-cards.php:263
#: modules/posts/skins/skin-cards.php:329
#: modules/theme-elements/widgets/post-info.php:211
msgid "Avatar"
msgstr ""

#: modules/posts/skins/skin-cards.php:313
msgid "Badge Taxonomy"
msgstr ""

#: modules/posts/skins/skin-cards.php:349
msgid "Card"
msgstr ""

#: modules/posts/skins/skin-cards.php:455
msgid "Box Shadow"
msgstr ""

#: modules/posts/skins/skin-cards.php:465
msgid "Hover Effect"
msgstr ""

#: modules/posts/skins/skin-cards.php:483
msgid "Meta Border Color"
msgstr ""

#: modules/forms/widgets/login.php:147
msgid "Redirect After Login"
msgstr ""

#: modules/forms/widgets/login.php:163 modules/forms/widgets/login.php:192
msgid "Note: Because of security reasons, you can ONLY use your current domain here."
msgstr ""

#: modules/forms/classes/integration-base.php:65
msgid "Field Mapping"
msgstr ""

#: modules/forms/actions/email.php:209
msgid "Send As"
msgstr ""

#: modules/forms/actions/email.php:214 modules/forms/widgets/form.php:58
#: modules/forms/widgets/form.php:247
msgid "HTML"
msgstr "HTML"

#: modules/forms/actions/email.php:215
#: modules/woocommerce/widgets/menu-cart.php:111
msgid "Plain"
msgstr ""

#: modules/forms/actions/webhook.php:54
msgid "Advanced Data"
msgstr ""

#: modules/forms/classes/form-record.php:205
#: modules/notes/notifications/views/email.php:92
msgid "Powered by"
msgstr ""

#: modules/forms/widgets/form.php:441
msgid "Please make sure the ID is unique and not used elsewhere in this form. This field allows `A-z 0-9` & underscore chars without spaces."
msgstr ""

#: core/editor/template.php:14 license/admin.php:49 license/admin.php:203
msgid "Activate License"
msgstr ""

#: modules/forms/actions/email.php:39
msgid "To"
msgstr ""

#: modules/forms/actions/email.php:147
msgid "Cc"
msgstr ""

#: modules/forms/actions/email.php:164
msgid "Bcc"
msgstr ""

#: modules/forms/actions/email2.php:17
msgid "Email 2"
msgstr ""

#: modules/forms/actions/mailchimp.php:33
#: modules/forms/actions/mailchimp.php:40
#: modules/forms/actions/mailchimp.php:445
msgid "MailChimp"
msgstr ""

#: modules/forms/actions/activecampaign.php:60
#: modules/forms/actions/activecampaign.php:293
#: modules/forms/actions/convertkit.php:55
#: modules/forms/actions/convertkit.php:248 modules/forms/actions/drip.php:55
#: modules/forms/actions/drip.php:287 modules/forms/actions/getresponse.php:55
#: modules/forms/actions/getresponse.php:296
#: modules/forms/actions/mailchimp.php:60
#: modules/forms/actions/mailchimp.php:449
#: modules/forms/actions/mailerlite.php:55
#: modules/forms/actions/mailerlite.php:247
msgid "API Key"
msgstr ""

#: modules/forms/actions/mailchimp.php:111
msgid "Groups"
msgstr ""

#: modules/forms/actions/mailchimp.php:139
msgid "Double Opt-In"
msgstr ""

#: modules/forms/actions/mailpoet.php:26
msgid "MailPoet"
msgstr ""

#: modules/dynamic-tags/tags/user-info.php:81
#: modules/forms/actions/mailpoet.php:109
#: modules/forms/actions/mailpoet3.php:114
#: modules/woocommerce/widgets/checkout.php:3662
#: modules/woocommerce/widgets/checkout.php:3718
msgid "First Name"
msgstr ""

#: modules/dynamic-tags/tags/user-info.php:82
#: modules/forms/actions/mailpoet.php:114
#: modules/forms/actions/mailpoet3.php:119
#: modules/woocommerce/widgets/checkout.php:3666
#: modules/woocommerce/widgets/checkout.php:3722
msgid "Last Name"
msgstr ""

#: modules/countdown/widgets/countdown.php:265
#: modules/forms/actions/redirect.php:20 modules/forms/actions/redirect.php:27
msgid "Redirect"
msgstr ""

#: modules/forms/actions/webhook.php:18 modules/forms/actions/webhook.php:25
msgid "Webhook"
msgstr ""

#: modules/forms/widgets/form.php:780
msgid "Actions After Submit"
msgstr ""

#: modules/forms/widgets/form.php:809
msgid "Add Action"
msgstr ""

#: modules/forms/widgets/form.php:816
msgid "Add actions that will be performed after a visitor submits the form (e.g. send an email notification). Choosing an action will add its setting below."
msgstr ""

#: modules/carousel/widgets/base.php:150 modules/forms/widgets/form.php:905
#: modules/forms/widgets/login.php:140
#: modules/payments/classes/payment-button.php:310
#: modules/table-of-contents/widgets/table-of-contents.php:216
#: modules/theme-elements/widgets/sitemap.php:70
#: modules/video-playlist/widgets/video-playlist.php:518
#: modules/woocommerce/widgets/cart.php:448
#: modules/woocommerce/widgets/menu-cart.php:675
#: modules/woocommerce/widgets/my-account.php:236
msgid "Additional Options"
msgstr "Additional Options"

#: modules/forms/widgets/login.php:24
#: modules/woocommerce/widgets/checkout.php:3995
msgid "Login"
msgstr ""

#: modules/forms/widgets/login.php:465
#: modules/woocommerce/widgets/checkout.php:1053
#: modules/woocommerce/widgets/my-account.php:987
msgid "Fields"
msgstr ""

#: modules/forms/widgets/login.php:259
msgid "Username Label"
msgstr ""

#: modules/forms/widgets/login.php:264 modules/forms/widgets/login.php:288
msgid "Username or Email Address"
msgstr ""

#: modules/forms/widgets/login.php:286
msgid "Username Placeholder"
msgstr ""

#: modules/forms/widgets/login.php:301
msgid "Password Label"
msgstr ""

#: modules/forms/widgets/form.php:57 modules/forms/widgets/login.php:306
#: modules/forms/widgets/login.php:330
#: modules/woocommerce/widgets/checkout.php:3984
msgid "Password"
msgstr ""

#: modules/forms/widgets/login.php:328
msgid "Password Placeholder"
msgstr ""

#: modules/forms/widgets/login.php:87
msgid "Log In"
msgstr ""

#: modules/forms/widgets/login.php:205 modules/forms/widgets/login.php:929
#: modules/forms/widgets/login.php:1000
#: modules/woocommerce/widgets/checkout.php:4010
msgid "Lost your password?"
msgstr ""

#: modules/forms/widgets/login.php:217 modules/forms/widgets/login.php:939
#: modules/forms/widgets/login.php:1011
msgid "Register"
msgstr ""

#: modules/forms/widgets/login.php:229 modules/forms/widgets/login.php:907
#: modules/forms/widgets/login.php:982
msgid "Remember Me"
msgstr ""

#: modules/forms/widgets/login.php:240 modules/forms/widgets/login.php:710
msgid "Logged in Message"
msgstr ""

#: modules/forms/widgets/login.php:375
msgid "Links Color"
msgstr ""

#: modules/forms/widgets/login.php:389
msgid "Links Hover Color"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:79
#: modules/share-buttons/widgets/share-buttons.php:94
#: modules/share-buttons/widgets/share-buttons.php:307
msgid "Share Buttons"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:107
msgid "Network"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:55
#: modules/blockquote/widgets/blockquote.php:155
#: modules/call-to-action/widgets/call-to-action.php:53
#: modules/carousel/widgets/media-carousel.php:380
#: modules/carousel/widgets/testimonial-carousel.php:44
#: modules/share-buttons/widgets/share-buttons.php:183
#: modules/theme-elements/widgets/post-comments.php:53
#: modules/theme-elements/widgets/search-form.php:53
msgid "Skin"
msgstr "Skin"

#: modules/posts/skins/skin-cards.php:470
#: modules/share-buttons/widgets/share-buttons.php:186
msgid "Gradient"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:187
#: modules/theme-elements/widgets/search-form.php:58
msgid "Minimal"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:189
msgid "Boxed Icon"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:190
msgid "Flat"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:204
msgid "Rounded"
msgstr "Rounded"

#: modules/share-buttons/widgets/share-buttons.php:250
#: modules/woocommerce/widgets/cart.php:167
#: modules/woocommerce/widgets/cart.php:251
#: modules/woocommerce/widgets/cart.php:371
#: modules/woocommerce/widgets/cart.php:427
#: modules/woocommerce/widgets/checkout.php:546
#: modules/woocommerce/widgets/checkout.php:2981
#: modules/woocommerce/widgets/checkout.php:3273
#: modules/woocommerce/widgets/menu-cart.php:534
#: modules/woocommerce/widgets/menu-cart.php:591
msgid "Justify"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:204
#: modules/share-buttons/widgets/share-buttons.php:273
#: modules/social/widgets/facebook-button.php:120
#: modules/social/widgets/facebook-comments.php:73
msgid "Target URL"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:207
#: modules/share-buttons/widgets/share-buttons.php:276
#: modules/social/widgets/facebook-button.php:123
#: modules/social/widgets/facebook-comments.php:76
#: assets/js/form-submission-admin.js:2230
msgid "Current Page"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:349
msgid "Button Size"
msgstr ""

#: modules/share-buttons/widgets/share-buttons.php:402
msgid "Button Height"
msgstr ""

#: modules/slides/widgets/slides.php:85
msgctxt "Background Control"
msgid "Image"
msgstr "Image"

#: modules/slides/widgets/slides.php:96
msgctxt "Background Control"
msgid "Size"
msgstr "Size"

#: modules/carousel/widgets/base.php:212 modules/slides/widgets/slides.php:643
msgid "Autoplay Speed"
msgstr "Autoplay Speed"

#: core/admin/admin.php:240
msgid "View Elementor Pro Changelog"
msgstr ""

#: core/admin/admin.php:240
msgid "Changelog"
msgstr ""

#: modules/flip-box/widgets/flip-box.php:39
#: modules/flip-box/widgets/flip-box.php:583
msgid "Front"
msgstr ""

#: modules/flip-box/widgets/flip-box.php:266
#: modules/flip-box/widgets/flip-box.php:1070 assets/js/app.js:2790
#: core/app/modules/site-editor/assets/js/molecules/back-button.js:10
msgid "Back"
msgstr ""

#: modules/flip-box/widgets/flip-box.php:27
msgid "Flip Box"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:130
#: modules/call-to-action/widgets/call-to-action.php:551
#: modules/flip-box/widgets/flip-box.php:50
msgid "Graphic Element"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:94
#: modules/call-to-action/widgets/call-to-action.php:153
#: modules/flip-box/widgets/flip-box.php:73
#: modules/video-playlist/widgets/video-playlist.php:469
msgid "Choose Image"
msgstr "Choose Image"

#: modules/call-to-action/widgets/call-to-action.php:203
#: modules/flip-box/widgets/flip-box.php:121
#: modules/woocommerce/widgets/add-to-cart.php:116
#: modules/woocommerce/widgets/menu-cart.php:1669
#: modules/woocommerce/widgets/product-add-to-cart.php:184
#: modules/woocommerce/widgets/product-meta.php:57
#: modules/woocommerce/widgets/product-price.php:131
msgid "Stacked"
msgstr "Stacked"

#: modules/call-to-action/widgets/call-to-action.php:204
#: modules/flip-box/widgets/flip-box.php:122
#: modules/gallery/widgets/gallery.php:382
#: modules/nav-menu/widgets/nav-menu.php:165
#: modules/share-buttons/widgets/share-buttons.php:188
msgid "Framed"
msgstr "Framed"

#: modules/animated-headline/widgets/animated-headline.php:86
#: modules/animated-headline/widgets/animated-headline.php:309
#: modules/call-to-action/widgets/call-to-action.php:216
#: modules/flip-box/widgets/flip-box.php:134 modules/forms/widgets/form.php:858
#: modules/share-buttons/widgets/share-buttons.php:200
msgid "Shape"
msgstr "Shape"

#: modules/call-to-action/widgets/call-to-action.php:219
#: modules/flip-box/widgets/flip-box.php:137
#: modules/page-transitions/module.php:350
#: modules/share-buttons/widgets/share-buttons.php:205
#: modules/theme-elements/widgets/sitemap.php:496
msgid "Circle"
msgstr "Circle"

#: modules/call-to-action/widgets/call-to-action.php:220
#: modules/flip-box/widgets/flip-box.php:138
#: modules/share-buttons/widgets/share-buttons.php:203
#: modules/theme-elements/widgets/sitemap.php:500
msgid "Square"
msgstr "Square"

#: modules/call-to-action/widgets/call-to-action.php:238
#: modules/flip-box/widgets/flip-box.php:153
#: modules/flip-box/widgets/flip-box.php:279
msgid "This is the heading"
msgstr "This is the heading"

#: modules/call-to-action/widgets/call-to-action.php:336
#: modules/flip-box/widgets/flip-box.php:334
msgid "Whole Box"
msgstr ""

#: base/base-carousel-trait.php:72 modules/flip-box/widgets/flip-box.php:422
#: modules/gallery/widgets/gallery.php:63
#: modules/loop-filter/widgets/taxonomy-filter.php:259
#: modules/lottie/widgets/lottie.php:206
#: modules/theme-builder/views/panel-template.php:88
msgid "Settings"
msgstr "Settings"

#: modules/flip-box/widgets/flip-box.php:511
msgid "Flip Effect"
msgstr ""

#: modules/page-transitions/module.php:216
msgid "Zoom In"
msgstr ""

#: modules/page-transitions/module.php:243
msgid "Zoom Out"
msgstr ""

#: modules/flip-box/widgets/flip-box.php:529
msgid "Flip Direction"
msgstr ""

#: modules/flip-box/widgets/flip-box.php:565
msgid "3D Depth"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:663
#: modules/flip-box/widgets/flip-box.php:816
#: modules/forms/widgets/form.php:1795 modules/forms/widgets/form.php:1830
#: modules/forms/widgets/form.php:1865
#: modules/share-buttons/widgets/share-buttons.php:491
#: modules/share-buttons/widgets/share-buttons.php:527
msgid "Primary Color"
msgstr "Primary Colour"

#: modules/call-to-action/widgets/call-to-action.php:681
#: modules/flip-box/widgets/flip-box.php:834
#: modules/forms/widgets/form.php:1809 modules/forms/widgets/form.php:1844
#: modules/forms/widgets/form.php:1879
#: modules/share-buttons/widgets/share-buttons.php:503
#: modules/share-buttons/widgets/share-buttons.php:538
msgid "Secondary Color"
msgstr "Secondary Colour"

#: modules/call-to-action/widgets/call-to-action.php:700
#: modules/carousel/widgets/media-carousel.php:622
#: modules/flip-box/widgets/flip-box.php:853
#: modules/forms/widgets/form.php:1716
#: modules/share-buttons/widgets/share-buttons.php:367
#: modules/theme-elements/widgets/search-form.php:242
#: modules/theme-elements/widgets/search-form.php:562
#: modules/theme-elements/widgets/search-form.php:682
#: modules/woocommerce/widgets/menu-cart.php:1148
#: modules/woocommerce/widgets/menu-cart.php:1231
msgid "Icon Size"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:721
#: modules/flip-box/widgets/flip-box.php:875
msgid "Icon Padding"
msgstr ""

#: modules/flip-box/widgets/flip-box.php:896
msgid "Icon Rotate"
msgstr ""

#: modules/gallery/widgets/gallery.php:166
#: modules/loop-builder/widgets/loop-grid.php:79
#: modules/posts/skins/skin-base.php:82 modules/posts/widgets/portfolio.php:121
msgid "Masonry"
msgstr ""

#: modules/posts/skins/skin-base.php:205
#: modules/posts/widgets/portfolio.php:102
#: modules/query-control/controls/group-control-query.php:374
msgid "Posts Per Page"
msgstr ""

#: base/base-carousel-trait.php:714 base/base-carousel-trait.php:727
#: base/base-carousel-trait.php:749 modules/carousel/widgets/base.php:171
#: modules/carousel/widgets/base.php:443 modules/posts/skins/skin-base.php:1176
#: modules/posts/widgets/posts-base.php:158
#: modules/posts/widgets/posts-base.php:165
#: modules/posts/widgets/posts-base.php:472
#: modules/posts/widgets/posts-base.php:617
#: modules/slides/widgets/slides.php:1209
#: modules/woocommerce/widgets/products-base.php:734
#: modules/woocommerce/widgets/products.php:88
msgid "Pagination"
msgstr ""

#: modules/posts/skins/skin-base.php:1146
#: modules/posts/skins/skin-content-base.php:282
#: modules/social/widgets/facebook-page.php:34
#: modules/woocommerce/widgets/elements.php:63
#: assets/js/form-submission-admin.js:4859
msgid "Page"
msgstr "Page"

#: modules/posts/widgets/posts-base.php:850
#: modules/posts/widgets/posts-base.php:852
#: modules/table-of-contents/widgets/table-of-contents.php:172
msgid "Numbers"
msgstr ""

#: modules/posts/widgets/posts-base.php:851
#: modules/posts/widgets/posts-base.php:852
msgid "Previous/Next"
msgstr ""

#: modules/posts/widgets/posts-base.php:176
msgid "Page Limit"
msgstr ""

#: modules/posts/widgets/posts-base.php:191
msgid "Shorten"
msgstr ""

#: modules/posts/widgets/posts-base.php:206
#: modules/theme-elements/widgets/post-navigation.php:62
msgid "Previous Label"
msgstr ""

#: modules/posts/widgets/posts-base.php:210
msgid "&laquo; Previous"
msgstr ""

#: modules/posts/widgets/posts-base.php:223
#: modules/theme-elements/widgets/post-navigation.php:77
msgid "Next Label"
msgstr ""

#: modules/posts/widgets/posts-base.php:224
msgid "Next &raquo;"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:898
#: modules/posts/widgets/posts-base.php:498
msgid "Colors"
msgstr "Colours"

#: modules/slides/widgets/slides.php:101
msgctxt "Background Control"
msgid "Contain"
msgstr "Contain"

#: modules/slides/widgets/slides.php:122
msgid "Ken Burns Effect"
msgstr ""

#: modules/slides/widgets/slides.php:140
msgid "Zoom Direction"
msgstr ""

#: modules/slides/widgets/slides.php:144
msgid "In"
msgstr ""

#: modules/slides/widgets/slides.php:145
msgid "Out"
msgstr ""

#: modules/forms/widgets/form.php:1057 modules/posts/skins/skin-base.php:418
#: modules/posts/widgets/portfolio.php:281
#: modules/share-buttons/widgets/share-buttons.php:315
#: modules/woocommerce/widgets/categories.php:191
#: modules/woocommerce/widgets/checkout.php:971
#: modules/woocommerce/widgets/my-account.php:905
#: modules/woocommerce/widgets/products-base.php:48
msgid "Columns Gap"
msgstr "Columns Gap"

#: modules/forms/widgets/form.php:1079 modules/forms/widgets/login.php:353
#: modules/posts/skins/skin-base.php:439
#: modules/posts/skins/skin-content-base.php:146
#: modules/posts/widgets/portfolio.php:299
#: modules/pricing/widgets/price-list.php:454
#: modules/share-buttons/widgets/share-buttons.php:332
#: modules/theme-builder/skins/post-comments-skin-classic.php:89
#: modules/woocommerce/widgets/cart.php:861
#: modules/woocommerce/widgets/cart.php:1241
#: modules/woocommerce/widgets/cart.php:1526
#: modules/woocommerce/widgets/categories.php:212
#: modules/woocommerce/widgets/checkout.php:989
#: modules/woocommerce/widgets/checkout.php:1420
#: modules/woocommerce/widgets/my-account.php:923
#: modules/woocommerce/widgets/my-account.php:1315
#: modules/woocommerce/widgets/products-base.php:75
#: modules/woocommerce/widgets/purchase-summary.php:1088
msgid "Rows Gap"
msgstr ""

#: modules/forms/classes/honeypot-handler.php:17
msgid "Honeypot"
msgstr ""

#: modules/forms/classes/honeypot-handler.php:60
msgid "Invalid Form."
msgstr ""

#: modules/forms/widgets/form.php:551
msgid "Required Mark"
msgstr ""

#: modules/forms/widgets/form.php:1151
msgid "Mark Color"
msgstr ""

#: modules/posts/skins/skin-base.php:657
#: modules/theme-builder/skins/post-comments-skin-classic.php:162
#: modules/woocommerce/widgets/single-elements.php:50
msgid "Meta"
msgstr ""

#: modules/pricing/widgets/price-table.php:27
msgid "Price Table"
msgstr ""

#: modules/carousel/widgets/reviews.php:70
#: modules/pricing/widgets/price-table.php:42
#: modules/pricing/widgets/price-table.php:380
#: modules/table-of-contents/widgets/table-of-contents.php:479
#: modules/theme-builder/classes/locations-manager.php:511
#: modules/theme-builder/documents/header.php:24
msgid "Header"
msgstr ""

#: modules/pricing/widgets/price-table.php:98
#: modules/pricing/widgets/price-table.php:535
msgid "Currency Symbol"
msgstr ""

#: modules/pricing/widgets/price-table.php:128
msgid "Custom Symbol"
msgstr ""

#: modules/pricing/widgets/price-table.php:174
#: modules/pricing/widgets/price-table.php:677
msgid "Original Price"
msgstr ""

#: modules/pricing/widgets/price-table.php:189
#: modules/pricing/widgets/price-table.php:758
msgid "Period"
msgstr ""

#: modules/payments/classes/payment-button.php:222
#: modules/pricing/widgets/price-table.php:194
msgid "Monthly"
msgstr ""

#: modules/pricing/widgets/price-table.php:203
#: modules/pricing/widgets/price-table.php:820
#: modules/usage/features-reporter.php:13
msgid "Features"
msgstr ""

#: modules/pricing/widgets/price-table.php:217
#: modules/theme-elements/widgets/sitemap.php:428
msgid "List Item"
msgstr "List Item"

#: modules/pricing/widgets/price-table.php:239
#: modules/table-of-contents/widgets/table-of-contents.php:523
#: modules/woocommerce/widgets/menu-cart.php:752
#: modules/woocommerce/widgets/menu-cart.php:811
msgid "Icon Color"
msgstr ""

#: modules/pricing/widgets/price-table.php:255
msgid "List Item #1"
msgstr "List Item #1"

#: modules/pricing/widgets/price-table.php:259
msgid "List Item #2"
msgstr "List Item #2"

#: modules/pricing/widgets/price-table.php:263
msgid "List Item #3"
msgstr "List Item #3"

#: modules/pricing/widgets/price-table.php:276
#: modules/pricing/widgets/price-table.php:1034
#: modules/theme-builder/classes/locations-manager.php:517
#: modules/theme-builder/documents/footer.php:23
msgid "Footer"
msgstr ""

#: modules/pricing/widgets/price-table.php:310
#: modules/pricing/widgets/price-table.php:1245
#: assets/js/form-submission-admin.js:5212
msgid "Additional Info"
msgstr ""

#: modules/pricing/widgets/price-table.php:312
msgid "This is text element"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:352
#: modules/call-to-action/widgets/call-to-action.php:1236
#: modules/pricing/widgets/price-table.php:325
#: modules/pricing/widgets/price-table.php:1312
msgid "Ribbon"
msgstr ""

#: modules/pricing/widgets/price-table.php:344
msgid "Popular"
msgstr "Popular"

#: modules/pricing/widgets/price-table.php:446
msgid "Sub Title"
msgstr ""

#: modules/pricing/widgets/price-table.php:620
msgid "Fractional Part"
msgstr ""

#: base/base-carousel-trait.php:694 base/base-carousel-trait.php:1326
#: modules/call-to-action/widgets/call-to-action.php:68
#: modules/call-to-action/widgets/call-to-action.php:370
#: modules/carousel/widgets/base.php:454
#: modules/hotspot/widgets/hotspot.php:339
#: modules/hotspot/widgets/hotspot.php:490
#: modules/mega-menu/widgets/mega-menu.php:1215 modules/popup/document.php:263
#: modules/popup/document.php:525 modules/pricing/widgets/price-table.php:357
#: modules/pricing/widgets/price-table.php:567
#: modules/pricing/widgets/price-table.php:801
#: modules/slides/widgets/slides.php:1221
#: modules/video-playlist/widgets/video-playlist.php:48
#: modules/woocommerce/widgets/menu-cart.php:446
#: modules/woocommerce/widgets/products-base.php:1016
msgid "Position"
msgstr "Position"

#: modules/animated-headline/widgets/animated-headline.php:334
#: modules/blockquote/widgets/blockquote.php:555
#: modules/blockquote/widgets/blockquote.php:601
#: modules/call-to-action/widgets/call-to-action.php:498
#: modules/call-to-action/widgets/call-to-action.php:586
#: modules/carousel/widgets/base.php:125
#: modules/flip-box/widgets/flip-box.php:702
#: modules/hotspot/widgets/hotspot.php:897
#: modules/loop-builder/documents/loop.php:385
#: modules/lottie/widgets/lottie.php:521
#: modules/mega-menu/widgets/mega-menu.php:945
#: modules/nav-menu/widgets/nav-menu.php:756
#: modules/page-transitions/module.php:580 modules/popup/document.php:166
#: modules/pricing/widgets/price-table.php:904
#: modules/pricing/widgets/price-table.php:994
#: modules/progress-tracker/widgets/progress-tracker.php:223
#: modules/progress-tracker/widgets/progress-tracker.php:310
#: modules/progress-tracker/widgets/progress-tracker.php:379
#: modules/progress-tracker/widgets/progress-tracker.php:466
#: modules/theme-elements/widgets/post-info.php:514
#: modules/theme-elements/widgets/search-form.php:583
#: modules/video-playlist/widgets/video-playlist.php:1275
#: modules/woocommerce/settings/settings-woocommerce.php:675
#: modules/woocommerce/widgets/cart.php:608
#: modules/woocommerce/widgets/cart.php:1178
#: modules/woocommerce/widgets/checkout.php:607
#: modules/woocommerce/widgets/checkout.php:1357
#: modules/woocommerce/widgets/menu-cart.php:1075
#: modules/woocommerce/widgets/menu-cart.php:1389
#: modules/woocommerce/widgets/my-account.php:480
#: modules/woocommerce/widgets/my-account.php:571
#: modules/woocommerce/widgets/my-account.php:624
#: modules/woocommerce/widgets/my-account.php:1486
#: modules/woocommerce/widgets/my-account.php:1665
#: modules/woocommerce/widgets/product-add-to-cart.php:657
#: modules/woocommerce/widgets/product-meta.php:151
#: modules/woocommerce/widgets/products-base.php:986
#: modules/woocommerce/widgets/purchase-summary.php:485
#: modules/woocommerce/widgets/purchase-summary.php:817
#: modules/woocommerce/widgets/purchase-summary.php:1042
#: modules/woocommerce/widgets/purchase-summary.php:1258
#: modules/woocommerce/widgets/purchase-summary.php:1439
msgid "Width"
msgstr "Width"

#: modules/mega-menu/widgets/mega-menu.php:909
#: modules/nav-menu/widgets/nav-menu.php:720
#: modules/nav-menu/widgets/nav-menu.php:1128
#: modules/pricing/widgets/price-table.php:921
#: modules/theme-elements/widgets/post-info.php:454
#: modules/woocommerce/widgets/menu-cart.php:1566
#: modules/woocommerce/widgets/product-meta.php:89
msgid "Divider"
msgstr "Divider"

#: modules/blockquote/widgets/blockquote.php:274
#: modules/blockquote/widgets/blockquote.php:317
#: modules/blockquote/widgets/blockquote.php:568
#: modules/blockquote/widgets/blockquote.php:614
#: modules/blockquote/widgets/blockquote.php:823
#: modules/carousel/widgets/reviews.php:90
#: modules/carousel/widgets/reviews.php:290
#: modules/carousel/widgets/testimonial-carousel.php:230
#: modules/carousel/widgets/testimonial-carousel.php:399
#: modules/gallery/widgets/gallery.php:1327
#: modules/pricing/widgets/price-table.php:1008
#: modules/theme-elements/widgets/author-box.php:374
#: modules/theme-elements/widgets/author-box.php:516
#: modules/theme-elements/widgets/author-box.php:568
#: modules/theme-elements/widgets/post-navigation.php:414
msgid "Gap"
msgstr "Gap"

#: modules/popup/document.php:782 modules/posts/skins/skin-cards.php:225
#: modules/pricing/widgets/price-table.php:1288
#: modules/woocommerce/widgets/cart.php:661
#: modules/woocommerce/widgets/cart.php:1914
#: modules/woocommerce/widgets/cart.php:2033
#: modules/woocommerce/widgets/cart.php:2257
#: modules/woocommerce/widgets/checkout.php:662
#: modules/woocommerce/widgets/checkout.php:1915
#: modules/woocommerce/widgets/checkout.php:2161
#: modules/woocommerce/widgets/checkout.php:2342
#: modules/woocommerce/widgets/checkout.php:2505
#: modules/woocommerce/widgets/checkout.php:2713
#: modules/woocommerce/widgets/checkout.php:3415
#: modules/woocommerce/widgets/checkout.php:3564
msgid "Margin"
msgstr "Margin"

#: modules/call-to-action/widgets/call-to-action.php:1275
#: modules/nav-menu/widgets/nav-menu.php:1169
#: modules/pricing/widgets/price-table.php:1340
#: modules/woocommerce/widgets/menu-cart.php:274
#: modules/woocommerce/widgets/menu-cart.php:1011
#: modules/woocommerce/widgets/products-base.php:1044
msgid "Distance"
msgstr ""

#: modules/woocommerce/documents/product-post.php:61
#: modules/woocommerce/documents/product.php:145
#: modules/woocommerce/tags/traits/tag-product-id.php:11
#: modules/woocommerce/tags/woocommerce-add-to-cart.php:35
#: modules/woocommerce/wc-templates/cart/mini-cart.php:32
#: modules/woocommerce/widgets/add-to-cart.php:54
#: modules/woocommerce/widgets/add-to-cart.php:61
#: modules/woocommerce/widgets/elements.php:79
msgid "Product"
msgstr ""

#: modules/assets-manager/asset-types/fonts/typekit-fonts.php:170
msgid "Project ID"
msgstr ""

#: modules/woocommerce/widgets/add-to-cart.php:139
#: modules/woocommerce/widgets/add-to-cart.php:140
msgid "Add to Cart"
msgstr ""

#: modules/woocommerce/widgets/add-to-cart.php:262
#: modules/woocommerce/widgets/add-to-cart.php:271
#: modules/woocommerce/widgets/elements.php:113
msgid "Please set a valid product"
msgstr ""

#: modules/woocommerce/widgets/categories.php:54
msgid "Categories Count"
msgstr ""

#: modules/lottie/widgets/lottie.php:56
#: modules/query-control/controls/group-control-posts.php:62
#: modules/query-control/controls/group-control-query.php:47
#: modules/theme-elements/widgets/author-box.php:51
#: modules/theme-elements/widgets/post-comments.php:65
#: modules/theme-elements/widgets/sitemap.php:204
#: modules/theme-elements/widgets/sitemap.php:217
#: modules/woocommerce/widgets/categories.php:73
msgid "Source"
msgstr ""

#: modules/woocommerce/widgets/categories.php:76
msgid "Show All"
msgstr ""

#: modules/woocommerce/widgets/categories.php:78
msgid "By Parent"
msgstr ""

#: modules/theme-elements/widgets/sitemap.php:341
#: modules/woocommerce/tags/product-terms.php:25
#: modules/woocommerce/widgets/categories.php:95
#: modules/woocommerce/widgets/product-meta.php:305
#: modules/woocommerce/widgets/product-meta.php:400
msgid "Categories"
msgstr ""

#: modules/woocommerce/widgets/categories.php:107
msgid "Only Top Level"
msgstr ""

#: modules/woocommerce/widgets/categories.php:111
msgid "Parent"
msgstr ""

#: modules/theme-elements/widgets/sitemap.php:288
#: modules/woocommerce/widgets/categories.php:124
#: modules/woocommerce/widgets/menu-cart.php:121
msgid "Hide Empty"
msgstr ""

#: modules/woocommerce/widgets/categories.php:140
msgid "Slug"
msgstr ""

#: modules/popup/display-settings/timing.php:91
#: modules/woocommerce/widgets/categories.php:142
#: modules/woocommerce/widgets/categories.php:334
msgid "Count"
msgstr ""

#: modules/woocommerce/widgets/elements.php:56
#: modules/woocommerce/widgets/single-elements.php:34
#: modules/woocommerce/widgets/single-elements.php:41
msgid "Element"
msgstr ""

#: modules/woocommerce/widgets/elements.php:67
msgid "Cart Page"
msgstr ""

#: modules/woocommerce/widgets/elements.php:68
msgid "Single Product Page"
msgstr ""

#: modules/woocommerce/widgets/elements.php:69
msgid "Checkout Page"
msgstr ""

#: modules/woocommerce/widgets/elements.php:70
msgid "Order Tracking Form"
msgstr ""

#: license/admin.php:237
#: modules/woocommerce/settings/settings-woocommerce.php:96
#: modules/woocommerce/widgets/elements.php:71
#: modules/woocommerce/widgets/my-account.php:26
msgid "My Account"
msgstr ""

#: modules/woocommerce/widgets/elements.php:146
msgid "Your cart is currently empty."
msgstr ""

#: modules/woocommerce/widgets/products-deprecated.php:39
msgid "Woo - Products-"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:60
msgctxt "Posts Query Control"
msgid "Manual Selection"
msgstr ""

#: modules/posts/skins/skin-base.php:683
msgid "Separator Color"
msgstr ""

#: modules/slides/widgets/slides.php:100
msgctxt "Background Control"
msgid "Cover"
msgstr "Cover"

#: modules/slides/widgets/slides.php:102
msgctxt "Background Control"
msgid "Auto"
msgstr "Auto"

#: modules/woocommerce/widgets/products-deprecated.php:118
msgid "Filter By"
msgstr ""

#: modules/woocommerce/traits/products-trait.php:59
#: modules/woocommerce/widgets/products-deprecated.php:123
msgid "Featured"
msgstr ""

#: modules/pricing/widgets/price-table.php:163
#: modules/woocommerce/tags/product-price.php:26
#: modules/woocommerce/traits/products-trait.php:58
#: modules/woocommerce/widgets/products-deprecated.php:124
msgid "Sale"
msgstr ""

#: base/base-carousel-trait.php:58 base/base-carousel-trait.php:81
#: base/base-carousel-trait.php:115 base/base-carousel-trait.php:133
#: base/base-carousel-trait.php:148
#: modules/blockquote/widgets/blockquote.php:127
#: modules/call-to-action/widgets/call-to-action.php:1436
#: modules/code-highlight/widgets/code-highlight.php:176
#: modules/code-highlight/widgets/code-highlight.php:201
#: modules/flip-box/widgets/flip-box.php:567
#: modules/forms/widgets/login.php:151 modules/forms/widgets/login.php:180
#: modules/hotspot/widgets/hotspot.php:171
#: modules/hotspot/widgets/hotspot.php:319
#: modules/hotspot/widgets/hotspot.php:397
#: modules/hotspot/widgets/hotspot.php:449
#: modules/loop-builder/widgets/loop-grid.php:82
#: modules/loop-builder/widgets/loop-grid.php:98
#: modules/loop-builder/widgets/loop-grid.php:118
#: modules/loop-builder/widgets/loop-grid.php:264
#: modules/mega-menu/widgets/mega-menu.php:912
#: modules/motion-fx/controls-group.php:44
#: modules/motion-fx/controls-group.php:178
#: modules/nav-menu/widgets/nav-menu.php:723
#: modules/payments/classes/payment-button.php:238
#: modules/payments/classes/payment-button.php:336
#: modules/posts/skins/skin-base.php:85 modules/posts/widgets/portfolio.php:124
#: modules/posts/widgets/portfolio.php:166
#: modules/posts/widgets/portfolio.php:230
#: modules/posts/widgets/posts-base.php:289
#: modules/pricing/widgets/price-table.php:165
#: modules/scroll-snap/module.php:60
#: modules/theme-elements/widgets/post-info.php:457
#: modules/woocommerce/widgets/product-meta.php:92
msgid "On"
msgstr "On"

#: modules/loop-builder/documents/loop.php:324
#: modules/loop-builder/widgets/base.php:164
#: modules/posts/widgets/portfolio.php:198 modules/posts/widgets/posts.php:95
#: modules/woocommerce/widgets/categories.php:65
#: modules/woocommerce/widgets/products-deprecated.php:94
#: modules/woocommerce/widgets/products.php:50
msgid "Query"
msgstr ""

#: modules/forms/widgets/form.php:396 modules/popup/document.php:685
#: modules/theme-builder/widgets/archive-posts.php:70
#: modules/woocommerce/widgets/archive-products-deprecated.php:95
#: modules/woocommerce/widgets/archive-products.php:125
#: modules/woocommerce/widgets/checkout.php:204
#: modules/woocommerce/widgets/checkout.php:377
#: modules/woocommerce/widgets/checkout.php:3080
#: modules/woocommerce/widgets/products-deprecated.php:110
msgid "Advanced"
msgstr "Advanced"

#: modules/gallery/widgets/gallery.php:137
#: modules/query-control/controls/group-control-query.php:344
#: modules/social/widgets/facebook-comments.php:59
#: modules/theme-elements/widgets/sitemap.php:241
#: modules/theme-elements/widgets/sitemap.php:259
#: modules/woocommerce/widgets/categories.php:135
#: modules/woocommerce/widgets/product-related.php:58
#: modules/woocommerce/widgets/product-upsell.php:45
#: modules/woocommerce/widgets/products-deprecated.php:132
msgid "Order By"
msgstr ""

#: modules/query-control/controls/group-control-query.php:350
#: modules/theme-elements/widgets/sitemap.php:247
#: modules/woocommerce/traits/products-trait.php:75
#: modules/woocommerce/widgets/product-related.php:68
#: modules/woocommerce/widgets/product-upsell.php:55
#: modules/woocommerce/widgets/products-deprecated.php:142
msgid "Menu Order"
msgstr ""

#: modules/gallery/widgets/gallery.php:140
#: modules/query-control/controls/group-control-query.php:353
#: modules/theme-elements/widgets/sitemap.php:248
#: modules/woocommerce/traits/products-trait.php:74
#: modules/woocommerce/widgets/product-related.php:67
#: modules/woocommerce/widgets/product-upsell.php:54
#: modules/woocommerce/widgets/products-deprecated.php:141
msgid "Random"
msgstr "Random"

#: modules/query-control/controls/group-control-query.php:361
#: modules/theme-elements/widgets/sitemap.php:275
#: modules/woocommerce/widgets/categories.php:150
#: modules/woocommerce/widgets/product-related.php:76
#: modules/woocommerce/widgets/product-upsell.php:63
#: modules/woocommerce/widgets/products-deprecated.php:150
msgid "Order"
msgstr ""

#: modules/query-control/controls/group-control-query.php:365
#: modules/theme-elements/widgets/sitemap.php:279
#: modules/woocommerce/widgets/categories.php:154
#: modules/woocommerce/widgets/product-related.php:80
#: modules/woocommerce/widgets/product-upsell.php:67
#: modules/woocommerce/widgets/products-deprecated.php:154
msgid "ASC"
msgstr ""

#: license/admin.php:112
msgid "Please enter your license key."
msgstr ""

#: modules/query-control/controls/group-control-query.php:366
#: modules/theme-elements/widgets/sitemap.php:280
#: modules/woocommerce/widgets/categories.php:155
#: modules/woocommerce/widgets/product-related.php:81
#: modules/woocommerce/widgets/product-upsell.php:68
#: modules/woocommerce/widgets/products-deprecated.php:155
msgid "DESC"
msgstr ""

#. Plugin Name of the plugin
#: core/connect/apps/activate.php:65 core/connect/apps/activate.php:71
#: core/connect/apps/activate.php:79 core/connect/apps/activate.php:87
#: core/connect/apps/activate.php:95 core/upgrade/manager.php:21
#: license/admin.php:112 license/admin.php:120 license/admin.php:127
#: modules/compatibility-tag/compatibility-tag-component.php:29 plugin.php:519
msgid "Elementor Pro"
msgstr ""

#: modules/hotspot/widgets/hotspot.php:262
#: modules/hotspot/widgets/hotspot.php:299
#: modules/query-control/controls/group-control-query.php:271
#: modules/sticky/module.php:102 modules/woocommerce/widgets/cart.php:94
#: modules/woocommerce/widgets/checkout.php:101
msgid "Offset"
msgstr ""

#: modules/gallery/widgets/gallery.php:339
#: modules/gallery/widgets/gallery.php:1114
#: modules/posts/widgets/portfolio.php:219
#: modules/posts/widgets/portfolio.php:385
msgid "Filter Bar"
msgstr ""

#: modules/dynamic-tags/tags/internal-url.php:78
#: modules/dynamic-tags/tags/post-terms.php:61
#: modules/loop-filter/widgets/taxonomy-filter.php:69
#: modules/posts/widgets/portfolio.php:237
#: modules/theme-elements/widgets/post-info.php:183
#: modules/theme-elements/widgets/post-navigation.php:179
#: modules/theme-elements/widgets/sitemap.php:196
#: modules/woocommerce/tags/product-terms.php:47
msgid "Taxonomy"
msgstr ""

#: license/api.php:371
msgid "Your license is missing. Please check your key again."
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:386
#: modules/posts/widgets/portfolio.php:254
#: modules/woocommerce/widgets/cart.php:1314
#: modules/woocommerce/widgets/checkout.php:398
#: modules/woocommerce/widgets/checkout.php:1439
#: modules/woocommerce/widgets/my-account.php:1370
#: modules/woocommerce/widgets/purchase-summary.php:767
#: modules/woocommerce/widgets/purchase-summary.php:992
#: modules/woocommerce/widgets/purchase-summary.php:1142
msgid "Items"
msgstr ""

#: modules/posts/widgets/portfolio.php:267
msgid "Item Gap"
msgstr ""

#: modules/posts/widgets/portfolio.php:332
msgid "Item Overlay"
msgstr ""

#: license/api.php:97 license/api.php:310 license/api.php:347
msgid "An error occurred, please try again"
msgstr ""

#: modules/carousel/widgets/base.php:510
#: modules/posts/widgets/portfolio.php:410
#: modules/slides/widgets/slides.php:1278
#: modules/video-playlist/widgets/video-playlist.php:1372
msgid "Active Color"
msgstr "Active Colour"

#: license/admin.php:152 license/admin.php:561
msgid "License"
msgstr ""

#: license/admin.php:196 license/admin.php:571
msgid "License Settings"
msgstr ""

#: modules/loop-filter/widgets/taxonomy-filter.php:394
#: modules/mega-menu/widgets/mega-menu.php:768
msgid "Space between Items"
msgstr ""

#: modules/gallery/widgets/gallery.php:361
#: modules/posts/widgets/portfolio.php:583
#: modules/query-control/controls/group-control-query.php:290
#: modules/theme-builder/classes/conditions-repeater.php:47
#: modules/theme-builder/classes/conditions-repeater.php:63
#: modules/theme-builder/conditions/taxonomy.php:58
#: modules/theme-elements/widgets/sitemap.php:313 assets/js/app.js:3388
#: assets/js/custom-code.js:893 assets/js/editor.js:4568
#: assets/js/form-submission-admin.js:4658
#: core/app/modules/site-editor/assets/js/pages/conditions/condition-sub-id.js:45
msgid "All"
msgstr "All"

#: modules/query-control/controls/group-control-query.php:280
msgid "Use this setting to skip over posts (e.g. '2' to skip over 2 posts)."
msgstr ""

#: modules/pricing/widgets/price-table.php:91
#: modules/pricing/widgets/price-table.php:479
msgid "Pricing"
msgstr ""

#: modules/pricing/widgets/price-list.php:25
msgid "Price List"
msgstr ""

#: core/connect/apps/activate.php:14 license/admin.php:619
msgid "Activate"
msgstr ""

#: modules/forms/actions/activecampaign.php:98
#: modules/forms/actions/getresponse.php:81
#: modules/forms/actions/mailpoet.php:45 modules/forms/actions/mailpoet3.php:45
#: modules/pricing/widgets/price-list.php:40
#: modules/pricing/widgets/price-list.php:183
#: modules/table-of-contents/widgets/table-of-contents.php:551
#: modules/theme-elements/widgets/post-info.php:401
#: modules/theme-elements/widgets/sitemap.php:354
msgid "List"
msgstr "List"

#: modules/pricing/widgets/price-list.php:110
msgid "List Items"
msgstr ""

#: license/admin.php:638
msgid "Deactivate"
msgstr ""

#: modules/payments/classes/payment-button.php:159
#: modules/pricing/widgets/price-list.php:50
#: modules/pricing/widgets/price-list.php:232
#: modules/pricing/widgets/price-table.php:139
#: modules/woocommerce/traits/products-trait.php:71
#: modules/woocommerce/wc-templates/cart/mini-cart.php:47
#: modules/woocommerce/widgets/product-price.php:36
#: modules/woocommerce/widgets/product-related.php:64
#: modules/woocommerce/widgets/product-upsell.php:51
#: modules/woocommerce/widgets/products-base.php:298
#: modules/woocommerce/widgets/products-deprecated.php:138
#: modules/woocommerce/widgets/single-elements.php:48
msgid "Price"
msgstr ""

#: license/admin.php:295 modules/notes/user/personal-data.php:101
msgid "Status"
msgstr "Status"

#: license/admin.php:287
msgid "Expired"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:270
#: modules/call-to-action/widgets/call-to-action.php:857
#: modules/carousel/widgets/media-carousel.php:507
#: modules/dynamic-tags/tags/contact-url.php:180
#: modules/dynamic-tags/tags/featured-image-data.php:90
#: modules/flip-box/widgets/flip-box.php:166
#: modules/flip-box/widgets/flip-box.php:291
#: modules/flip-box/widgets/flip-box.php:1028
#: modules/flip-box/widgets/flip-box.php:1228
#: modules/forms/actions/discord.php:97 modules/forms/actions/slack.php:113
#: modules/gallery/widgets/gallery.php:502
#: modules/gallery/widgets/gallery.php:511
#: modules/gallery/widgets/gallery.php:519
#: modules/gallery/widgets/gallery.php:981
#: modules/pricing/widgets/price-list.php:74
#: modules/pricing/widgets/price-list.php:266
#: modules/pricing/widgets/price-table.php:61
#: modules/slides/widgets/slides.php:256 modules/slides/widgets/slides.php:917
#: modules/woocommerce/widgets/cart.php:2083
#: modules/woocommerce/widgets/categories.php:141
#: modules/woocommerce/widgets/checkout.php:1956
#: modules/woocommerce/widgets/checkout.php:2743
msgid "Description"
msgstr "Description"

#: modules/animated-headline/widgets/animated-headline.php:249
#: modules/blockquote/widgets/blockquote.php:221
#: modules/call-to-action/widgets/call-to-action.php:320
#: modules/carousel/widgets/media-carousel.php:171
#: modules/carousel/widgets/reviews.php:640
#: modules/dynamic-tags/tags/comments-number.php:61
#: modules/dynamic-tags/tags/post-terms.php:80
#: modules/flip-box/widgets/flip-box.php:319
#: modules/gallery/widgets/gallery.php:254
#: modules/hotspot/widgets/hotspot.php:90 modules/lottie/widgets/lottie.php:169
#: modules/lottie/widgets/lottie.php:184
#: modules/mega-menu/widgets/mega-menu.php:179
#: modules/posts/traits/button-widget-trait.php:87
#: modules/pricing/widgets/price-list.php:98
#: modules/pricing/widgets/price-table.php:295
#: modules/share-buttons/widgets/share-buttons.php:287
#: modules/slides/widgets/slides.php:280
#: modules/social/widgets/facebook-button.php:153
#: modules/social/widgets/facebook-comments.php:103
#: modules/social/widgets/facebook-page.php:43
#: modules/theme-elements/widgets/author-box.php:169
#: modules/theme-elements/widgets/author-box.php:220
#: modules/theme-elements/widgets/post-info.php:303
#: modules/video-playlist/widgets/video-playlist.php:90
#: modules/video-playlist/widgets/video-playlist.php:106
#: modules/woocommerce/widgets/cart.php:2110
#: modules/woocommerce/widgets/checkout.php:2010
#: modules/woocommerce/widgets/checkout.php:3605
#: modules/woocommerce/widgets/product-meta.php:245
msgid "Link"
msgstr "Link"

#: license/admin.php:297 modules/forms/widgets/form.php:1823
#: modules/gallery/widgets/gallery.php:1250
#: modules/loop-filter/widgets/taxonomy-filter.php:516
#: modules/mega-menu/widgets/mega-menu.php:647
#: modules/mega-menu/widgets/mega-menu.php:1105
#: modules/mega-menu/widgets/mega-menu.php:1342
#: modules/mega-menu/widgets/mega-menu.php:1549
#: modules/mega-menu/widgets/mega-menu.php:1801
#: modules/nav-menu/widgets/nav-menu.php:466
#: modules/nav-menu/widgets/nav-menu.php:674
#: modules/nav-menu/widgets/nav-menu.php:1014
#: modules/posts/widgets/posts-base.php:549
#: modules/table-of-contents/widgets/table-of-contents.php:671
#: modules/usage/integrations-reporter.php:28
#: modules/usage/integrations-reporter.php:42
#: modules/video-playlist/widgets/video-playlist.php:998
#: modules/woocommerce/widgets/my-account.php:417
#: modules/woocommerce/widgets/product-data-tabs.php:96
#: modules/woocommerce/widgets/products-base.php:873
msgid "Active"
msgstr ""

#: modules/pricing/widgets/price-list.php:115
msgid "First item on the list"
msgstr ""

#: modules/pricing/widgets/price-list.php:121
msgid "Second item on the list"
msgstr ""

#: modules/pricing/widgets/price-list.php:127
msgid "Third item on the list"
msgstr ""

#: modules/carousel/widgets/reviews.php:109
#: modules/dynamic-tags/tags/post-terms.php:71
#: modules/pricing/widgets/price-list.php:300
#: modules/video-playlist/widgets/video-playlist.php:932
#: modules/video-playlist/widgets/video-playlist.php:1135
#: modules/woocommerce/tags/product-terms.php:57
msgid "Separator"
msgstr "Separator"

#: modules/carousel/widgets/reviews.php:408
#: modules/mega-menu/widgets/mega-menu.php:929
#: modules/nav-menu/widgets/nav-menu.php:740
#: modules/pricing/widgets/price-list.php:312
#: modules/pricing/widgets/price-table.php:934
#: modules/theme-elements/widgets/post-info.php:471
#: modules/woocommerce/settings/settings-woocommerce.php:661
#: modules/woocommerce/widgets/base-widget.php:107
#: modules/woocommerce/widgets/menu-cart.php:54
#: modules/woocommerce/widgets/menu-cart.php:57
#: modules/woocommerce/widgets/menu-cart.php:60
#: modules/woocommerce/widgets/menu-cart.php:1059
#: modules/woocommerce/widgets/menu-cart.php:1366
#: modules/woocommerce/widgets/menu-cart.php:1578
#: modules/woocommerce/widgets/product-meta.php:107
msgid "Solid"
msgstr "Solid"

#: license/admin.php:290 license/api.php:269 license/api.php:305
#: license/api.php:342
msgid "HTTP Error"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:931
#: modules/nav-menu/widgets/nav-menu.php:742
#: modules/pricing/widgets/price-list.php:313
#: modules/pricing/widgets/price-table.php:936
#: modules/theme-elements/widgets/post-info.php:473
#: modules/woocommerce/settings/settings-woocommerce.php:663
#: modules/woocommerce/widgets/base-widget.php:109
#: modules/woocommerce/widgets/menu-cart.php:1061
#: modules/woocommerce/widgets/menu-cart.php:1368
#: modules/woocommerce/widgets/menu-cart.php:1580
#: modules/woocommerce/widgets/product-meta.php:109
msgid "Dotted"
msgstr "Dotted"

#: modules/countdown/widgets/countdown.php:25
#: modules/countdown/widgets/countdown.php:40
msgid "Countdown"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:932
#: modules/nav-menu/widgets/nav-menu.php:743
#: modules/pricing/widgets/price-list.php:314
#: modules/pricing/widgets/price-table.php:937
#: modules/theme-elements/widgets/post-info.php:474
#: modules/woocommerce/settings/settings-woocommerce.php:664
#: modules/woocommerce/widgets/base-widget.php:110
#: modules/woocommerce/widgets/menu-cart.php:1062
#: modules/woocommerce/widgets/menu-cart.php:1369
#: modules/woocommerce/widgets/menu-cart.php:1581
#: modules/woocommerce/widgets/product-meta.php:110
msgid "Dashed"
msgstr "Dashed"

#: modules/countdown/widgets/countdown.php:50
#: modules/countdown/widgets/countdown.php:60
msgid "Due Date"
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:930
#: modules/nav-menu/widgets/nav-menu.php:741
#: modules/pricing/widgets/price-list.php:315
#: modules/pricing/widgets/price-table.php:935
#: modules/theme-elements/widgets/post-info.php:472
#: modules/woocommerce/settings/settings-woocommerce.php:662
#: modules/woocommerce/widgets/base-widget.php:108
#: modules/woocommerce/widgets/menu-cart.php:1060
#: modules/woocommerce/widgets/menu-cart.php:1367
#: modules/woocommerce/widgets/menu-cart.php:1579
#: modules/woocommerce/widgets/product-meta.php:108
msgid "Double"
msgstr "Double"

#. translators: %s: Time zone.
#: modules/countdown/widgets/countdown.php:64
msgid "Date set according to your timezone: %s."
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:64
#: modules/pricing/widgets/price-list.php:329
#: modules/pricing/widgets/price-table.php:970
#: modules/theme-elements/widgets/post-info.php:490
#: modules/video-playlist/widgets/video-playlist.php:961
#: modules/video-playlist/widgets/video-playlist.php:1164
#: modules/woocommerce/widgets/cart.php:1429
#: modules/woocommerce/widgets/cart.php:1461
#: modules/woocommerce/widgets/cart.php:1591
#: modules/woocommerce/widgets/checkout.php:1514
#: modules/woocommerce/widgets/checkout.php:1572
#: modules/woocommerce/widgets/menu-cart.php:1604
#: modules/woocommerce/widgets/product-meta.php:126
msgid "Weight"
msgstr "Weight"

#: modules/blockquote/widgets/blockquote.php:136
#: modules/call-to-action/widgets/call-to-action.php:199
#: modules/countdown/widgets/countdown.php:109
#: modules/flip-box/widgets/flip-box.php:117
#: modules/posts/traits/button-widget-trait.php:190
#: modules/share-buttons/widgets/share-buttons.php:152
#: modules/woocommerce/widgets/product-meta.php:52
#: assets/js/form-submission-admin.js:4759
msgid "View"
msgstr "View"

#: modules/countdown/widgets/countdown.php:112
msgid "Block"
msgstr ""

#: modules/countdown/widgets/countdown.php:113
#: modules/forms/widgets/form.php:367 modules/forms/widgets/form.php:569
#: modules/theme-elements/widgets/post-info.php:77
#: modules/woocommerce/widgets/add-to-cart.php:115
#: modules/woocommerce/widgets/menu-cart.php:1668
#: modules/woocommerce/widgets/product-add-to-cart.php:183
#: modules/woocommerce/widgets/product-meta.php:58
msgid "Inline"
msgstr "Inline"

#: modules/countdown/widgets/countdown.php:123
#: modules/countdown/widgets/countdown.php:190
#: modules/countdown/widgets/countdown.php:192
#: modules/countdown/widgets/countdown.php:193
#: modules/countdown/widgets/countdown.php:602
msgid "Days"
msgstr ""

#: modules/pricing/widgets/price-list.php:478
#: modules/theme-elements/widgets/author-box.php:334
msgid "Vertical Align"
msgstr ""

#: base/base-carousel-trait.php:250 base/base-carousel-trait.php:1186
#: modules/carousel/widgets/base.php:161
#: modules/carousel/widgets/reviews.php:112
#: modules/countdown/widgets/countdown.php:125
#: modules/countdown/widgets/countdown.php:136
#: modules/countdown/widgets/countdown.php:147
#: modules/countdown/widgets/countdown.php:158
#: modules/countdown/widgets/countdown.php:169
#: modules/forms/widgets/form.php:540 modules/forms/widgets/form.php:553
#: modules/forms/widgets/login.php:50 modules/forms/widgets/login.php:209
#: modules/forms/widgets/login.php:221 modules/forms/widgets/login.php:233
#: modules/forms/widgets/login.php:244
#: modules/loop-filter/widgets/taxonomy-filter.php:281
#: modules/loop-filter/widgets/taxonomy-filter.php:292
#: modules/loop-filter/widgets/taxonomy-filter.php:322
#: modules/popup/display-settings/timing.php:109 modules/popup/document.php:337
#: modules/popup/document.php:352 modules/posts/skins/skin-base.php:218
#: modules/posts/skins/skin-base.php:256 modules/posts/skins/skin-base.php:296
#: modules/posts/skins/skin-cards.php:303
#: modules/posts/skins/skin-cards.php:331
#: modules/posts/widgets/portfolio.php:227
#: modules/pricing/widgets/price-table.php:332
#: modules/progress-tracker/widgets/progress-tracker.php:133
#: modules/share-buttons/widgets/share-buttons.php:171
#: modules/theme-builder/skins/post-comments-skin-classic.php:72
#: modules/theme-elements/widgets/author-box.php:67
#: modules/theme-elements/widgets/author-box.php:118
#: modules/theme-elements/widgets/author-box.php:189
#: modules/theme-elements/widgets/author-box.php:206
#: modules/theme-elements/widgets/post-navigation.php:53
#: modules/theme-elements/widgets/post-navigation.php:94
#: modules/theme-elements/widgets/post-navigation.php:128
#: modules/theme-elements/widgets/post-navigation.php:139
#: modules/theme-elements/widgets/sitemap.php:112
#: modules/video-playlist/widgets/video-playlist.php:242
#: modules/video-playlist/widgets/video-playlist.php:380
#: modules/video-playlist/widgets/video-playlist.php:462
#: modules/video-playlist/widgets/video-playlist.php:535
#: modules/video-playlist/widgets/video-playlist.php:546
#: modules/video-playlist/widgets/video-playlist.php:576
#: modules/video-playlist/widgets/video-playlist.php:587
#: modules/woocommerce/tags/product-stock.php:45
#: modules/woocommerce/widgets/add-to-cart.php:80
#: modules/woocommerce/widgets/cart.php:199
#: modules/woocommerce/widgets/checkout.php:289
#: modules/woocommerce/widgets/checkout.php:3178
#: modules/woocommerce/widgets/menu-cart.php:138
#: modules/woocommerce/widgets/menu-cart.php:310
#: modules/woocommerce/widgets/menu-cart.php:399
#: modules/woocommerce/widgets/menu-cart.php:474
#: modules/woocommerce/widgets/menu-cart.php:502
#: modules/woocommerce/widgets/menu-cart.php:559
#: modules/woocommerce/widgets/product-add-to-cart.php:455
#: modules/woocommerce/widgets/product-additional-information.php:37
#: modules/woocommerce/widgets/product-images.php:55
#: modules/woocommerce/widgets/product-related.php:109
#: modules/woocommerce/widgets/product-upsell.php:96
#: modules/woocommerce/widgets/products-base.php:760
#: modules/woocommerce/widgets/products-base.php:919
#: modules/woocommerce/widgets/products.php:174
#: modules/woocommerce/widgets/purchase-summary.php:55
msgid "Show"
msgstr "Show"

#: modules/call-to-action/widgets/call-to-action.php:461
#: modules/flip-box/widgets/flip-box.php:546
#: modules/flip-box/widgets/flip-box.php:641
#: modules/flip-box/widgets/flip-box.php:1129
#: modules/gallery/widgets/gallery.php:893
#: modules/hotspot/widgets/hotspot.php:287
#: modules/hotspot/widgets/hotspot.php:355
#: modules/hotspot/widgets/hotspot.php:508
#: modules/lottie/widgets/lottie.php:255
#: modules/motion-fx/controls-group.php:114
#: modules/motion-fx/controls-group.php:232
#: modules/motion-fx/controls-group.php:275
#: modules/motion-fx/controls-group.php:322
#: modules/motion-fx/controls-group.php:368
#: modules/motion-fx/controls-group.php:411
#: modules/motion-fx/controls-group.php:457 modules/popup/document.php:245
#: modules/popup/document.php:317 modules/pricing/widgets/price-list.php:490
#: modules/pricing/widgets/price-table.php:598
#: modules/pricing/widgets/price-table.php:658
#: modules/pricing/widgets/price-table.php:735
#: modules/scroll-snap/module.php:79 modules/slides/widgets/slides.php:382
#: modules/slides/widgets/slides.php:819 modules/sticky/module.php:54
#: modules/woocommerce/widgets/menu-cart.php:422
#: modules/woocommerce/widgets/menu-cart.php:454
#: modules/woocommerce/widgets/menu-cart.php:638
msgid "Bottom"
msgstr "Bottom"

#: base/base-carousel-trait.php:249 base/base-carousel-trait.php:1185
#: modules/carousel/widgets/base.php:160
#: modules/carousel/widgets/reviews.php:111
#: modules/countdown/widgets/countdown.php:126
#: modules/countdown/widgets/countdown.php:137
#: modules/countdown/widgets/countdown.php:148
#: modules/countdown/widgets/countdown.php:159
#: modules/countdown/widgets/countdown.php:170
#: modules/countdown/widgets/countdown.php:266
#: modules/forms/widgets/form.php:541 modules/forms/widgets/form.php:554
#: modules/forms/widgets/login.php:49 modules/forms/widgets/login.php:208
#: modules/forms/widgets/login.php:220 modules/forms/widgets/login.php:232
#: modules/forms/widgets/login.php:243
#: modules/loop-filter/widgets/taxonomy-filter.php:280
#: modules/loop-filter/widgets/taxonomy-filter.php:291
#: modules/loop-filter/widgets/taxonomy-filter.php:321
#: modules/popup/display-settings/timing.php:110 modules/popup/document.php:336
#: modules/popup/document.php:351 modules/posts/skins/skin-base.php:219
#: modules/posts/skins/skin-base.php:257 modules/posts/skins/skin-base.php:297
#: modules/posts/skins/skin-cards.php:304
#: modules/posts/skins/skin-cards.php:332
#: modules/progress-tracker/widgets/progress-tracker.php:134
#: modules/share-buttons/widgets/share-buttons.php:172
#: modules/theme-builder/skins/post-comments-skin-classic.php:71
#: modules/theme-elements/widgets/author-box.php:68
#: modules/theme-elements/widgets/author-box.php:119
#: modules/theme-elements/widgets/author-box.php:190
#: modules/theme-elements/widgets/author-box.php:207
#: modules/theme-elements/widgets/post-navigation.php:54
#: modules/theme-elements/widgets/post-navigation.php:95
#: modules/theme-elements/widgets/post-navigation.php:129
#: modules/theme-elements/widgets/post-navigation.php:140
#: modules/theme-elements/widgets/sitemap.php:111
#: modules/video-playlist/widgets/video-playlist.php:243
#: modules/video-playlist/widgets/video-playlist.php:381
#: modules/video-playlist/widgets/video-playlist.php:461
#: modules/video-playlist/widgets/video-playlist.php:536
#: modules/video-playlist/widgets/video-playlist.php:547
#: modules/video-playlist/widgets/video-playlist.php:577
#: modules/video-playlist/widgets/video-playlist.php:588
#: modules/woocommerce/tags/product-stock.php:46
#: modules/woocommerce/widgets/add-to-cart.php:79
#: modules/woocommerce/widgets/cart.php:200
#: modules/woocommerce/widgets/checkout.php:290
#: modules/woocommerce/widgets/checkout.php:3179
#: modules/woocommerce/widgets/menu-cart.php:139
#: modules/woocommerce/widgets/menu-cart.php:311
#: modules/woocommerce/widgets/menu-cart.php:400
#: modules/woocommerce/widgets/menu-cart.php:475
#: modules/woocommerce/widgets/menu-cart.php:503
#: modules/woocommerce/widgets/menu-cart.php:560
#: modules/woocommerce/widgets/product-add-to-cart.php:456
#: modules/woocommerce/widgets/product-additional-information.php:38
#: modules/woocommerce/widgets/product-images.php:56
#: modules/woocommerce/widgets/product-related.php:108
#: modules/woocommerce/widgets/product-upsell.php:95
#: modules/woocommerce/widgets/products-base.php:759
#: modules/woocommerce/widgets/products-base.php:918
#: modules/woocommerce/widgets/products.php:175
#: modules/woocommerce/widgets/purchase-summary.php:56
msgid "Hide"
msgstr "Hide"

#: modules/carousel/widgets/base.php:33 modules/carousel/widgets/base.php:45
#: modules/carousel/widgets/base.php:285
#: modules/nested-carousel/widgets/nested-carousel.php:84
#: modules/nested-carousel/widgets/nested-carousel.php:173
#: modules/slides/widgets/slides.php:26 modules/slides/widgets/slides.php:55
#: modules/slides/widgets/slides.php:482 modules/slides/widgets/slides.php:730
msgid "Slides"
msgstr ""

#: modules/countdown/widgets/countdown.php:77
#: modules/countdown/widgets/countdown.php:80
#: modules/countdown/widgets/countdown.php:134
#: modules/countdown/widgets/countdown.php:208
#: modules/countdown/widgets/countdown.php:210
#: modules/countdown/widgets/countdown.php:211
#: modules/countdown/widgets/countdown.php:603
msgid "Hours"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1451
#: modules/flip-box/widgets/flip-box.php:180
#: modules/flip-box/widgets/flip-box.php:346
#: modules/gallery/widgets/gallery.php:383
#: modules/gallery/widgets/gallery.php:484
#: modules/nav-menu/widgets/nav-menu.php:166
#: modules/page-transitions/module.php:189 modules/slides/widgets/slides.php:66
#: modules/video-playlist/widgets/video-playlist.php:710
#: modules/video-playlist/widgets/video-playlist.php:804
#: modules/video-playlist/widgets/video-playlist.php:1013
#: modules/video-playlist/widgets/video-playlist.php:1221
msgid "Background"
msgstr "Background"

#: modules/countdown/widgets/countdown.php:93
#: modules/countdown/widgets/countdown.php:96
#: modules/countdown/widgets/countdown.php:145
#: modules/countdown/widgets/countdown.php:226
#: modules/countdown/widgets/countdown.php:228
#: modules/countdown/widgets/countdown.php:229
#: modules/countdown/widgets/countdown.php:604
msgid "Minutes"
msgstr ""

#: modules/countdown/widgets/countdown.php:156
#: modules/countdown/widgets/countdown.php:244
#: modules/countdown/widgets/countdown.php:246
#: modules/countdown/widgets/countdown.php:247
#: modules/countdown/widgets/countdown.php:605
msgid "Seconds"
msgstr ""

#: modules/countdown/widgets/countdown.php:167
msgid "Show Label"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:57
#: modules/carousel/widgets/media-carousel.php:407
msgid "Cover"
msgstr ""

#: modules/countdown/widgets/countdown.php:179
#: modules/forms/widgets/login.php:251
#: modules/share-buttons/widgets/share-buttons.php:121
msgid "Custom Label"
msgstr ""

#: modules/carousel/widgets/media-carousel.php:409
#: modules/woocommerce/widgets/add-to-cart.php:117
#: modules/woocommerce/widgets/product-add-to-cart.php:185
msgid "Auto"
msgstr ""

#: modules/dynamic-tags/tags/post-excerpt.php:41
#: modules/gallery/widgets/gallery.php:295
#: modules/loop-builder/widgets/loop-grid.php:197
#: modules/payments/classes/payment-button.php:353
#: modules/posts/skins/skin-base.php:280 modules/posts/skins/skin-base.php:323
#: modules/posts/skins/skin-base.php:345 modules/posts/skins/skin-cards.php:80
#: modules/woocommerce/widgets/cart.php:80
#: modules/woocommerce/widgets/cart.php:457
#: modules/woocommerce/widgets/cart.php:484
#: modules/woocommerce/widgets/checkout.php:86
#: modules/woocommerce/widgets/menu-cart.php:123
#: modules/woocommerce/widgets/menu-cart.php:693
#: modules/woocommerce/widgets/menu-cart.php:706
#: modules/woocommerce/widgets/my-account.php:245
#: modules/woocommerce/widgets/products-base.php:526
msgid "Yes"
msgstr "Yes"

#: modules/flip-box/widgets/flip-box.php:204
#: modules/flip-box/widgets/flip-box.php:360
#: modules/slides/widgets/slides.php:162
msgid "Background Overlay"
msgstr "Background Overlay"

#: modules/dynamic-tags/tags/post-excerpt.php:42
#: modules/gallery/widgets/gallery.php:296
#: modules/loop-builder/widgets/loop-grid.php:196
#: modules/payments/classes/payment-button.php:352
#: modules/posts/skins/skin-base.php:281 modules/posts/skins/skin-base.php:324
#: modules/posts/skins/skin-base.php:346 modules/posts/skins/skin-cards.php:81
#: modules/woocommerce/widgets/cart.php:81
#: modules/woocommerce/widgets/cart.php:458
#: modules/woocommerce/widgets/cart.php:485
#: modules/woocommerce/widgets/checkout.php:87
#: modules/woocommerce/widgets/menu-cart.php:124
#: modules/woocommerce/widgets/menu-cart.php:694
#: modules/woocommerce/widgets/menu-cart.php:707
#: modules/woocommerce/widgets/my-account.php:246
#: modules/woocommerce/widgets/products-base.php:527
msgid "No"
msgstr "No"

#: modules/call-to-action/widgets/call-to-action.php:1489
#: modules/call-to-action/widgets/call-to-action.php:1543
#: modules/theme-elements/widgets/search-form.php:263
msgid "Overlay Color"
msgstr ""

#: modules/countdown/widgets/countdown.php:312
msgid "Boxes"
msgstr ""

#: modules/slides/widgets/slides.php:245
msgid "Slide Heading"
msgstr ""

#: modules/countdown/widgets/countdown.php:320
msgid "Container Width"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:424
#: modules/blockquote/widgets/blockquote.php:465
#: modules/blockquote/widgets/blockquote.php:681
#: modules/blockquote/widgets/blockquote.php:732
#: modules/call-to-action/widgets/call-to-action.php:914
#: modules/call-to-action/widgets/call-to-action.php:979
#: modules/call-to-action/widgets/call-to-action.php:1106
#: modules/call-to-action/widgets/call-to-action.php:1148
#: modules/call-to-action/widgets/call-to-action.php:1248
#: modules/carousel/widgets/base.php:328
#: modules/carousel/widgets/media-carousel.php:586
#: modules/carousel/widgets/reviews.php:79
#: modules/carousel/widgets/testimonial-carousel.php:114
#: modules/countdown/widgets/countdown.php:352
#: modules/forms/widgets/form.php:1267 modules/forms/widgets/form.php:1370
#: modules/forms/widgets/form.php:1423 modules/forms/widgets/form.php:1481
#: modules/forms/widgets/form.php:1532 modules/forms/widgets/form.php:1968
#: modules/forms/widgets/login.php:498
#: modules/mega-menu/widgets/mega-menu.php:871
#: modules/mega-menu/widgets/mega-menu.php:1041
#: modules/mega-menu/widgets/mega-menu.php:1142
#: modules/mega-menu/widgets/mega-menu.php:1434
#: modules/mega-menu/widgets/mega-menu.php:1491
#: modules/mega-menu/widgets/mega-menu.php:1570
#: modules/mega-menu/widgets/mega-menu.php:1669
#: modules/mega-menu/widgets/mega-menu.php:1778
#: modules/mega-menu/widgets/mega-menu.php:1822
#: modules/nav-menu/widgets/nav-menu.php:960
#: modules/nav-menu/widgets/nav-menu.php:997
#: modules/nav-menu/widgets/nav-menu.php:1033
#: modules/nav-menu/widgets/nav-menu.php:1222
#: modules/nav-menu/widgets/nav-menu.php:1254
#: modules/nested-carousel/widgets/nested-carousel.php:210
#: modules/popup/document.php:625 modules/popup/document.php:656
#: modules/posts/skins/skin-cards.php:153
#: modules/posts/skins/skin-cards.php:357
#: modules/posts/skins/skin-classic.php:122
#: modules/posts/skins/skin-classic.php:160
#: modules/posts/widgets/portfolio.php:340
#: modules/pricing/widgets/price-table.php:389
#: modules/pricing/widgets/price-table.php:488
#: modules/pricing/widgets/price-table.php:829
#: modules/pricing/widgets/price-table.php:1043
#: modules/pricing/widgets/price-table.php:1324
#: modules/progress-tracker/widgets/progress-tracker.php:406
#: modules/table-of-contents/widgets/table-of-contents.php:371
#: modules/table-of-contents/widgets/table-of-contents.php:487
#: modules/theme-builder/skins/post-comments-skin-classic.php:110
#: modules/theme-builder/skins/post-comments-skin-classic.php:293
#: modules/theme-builder/skins/post-comments-skin-classic.php:361
#: modules/theme-elements/widgets/author-box.php:620
#: modules/theme-elements/widgets/author-box.php:665
#: modules/theme-elements/widgets/search-form.php:315
#: modules/theme-elements/widgets/search-form.php:381
#: modules/theme-elements/widgets/search-form.php:511
#: modules/theme-elements/widgets/search-form.php:546
#: modules/theme-elements/widgets/search-form.php:634
#: modules/theme-elements/widgets/search-form.php:666
#: modules/video-playlist/widgets/video-playlist.php:1339
#: modules/woocommerce/widgets/cart.php:576
#: modules/woocommerce/widgets/cart.php:1828
#: modules/woocommerce/widgets/cart.php:1948
#: modules/woocommerce/widgets/cart.php:2172
#: modules/woocommerce/widgets/checkout.php:575
#: modules/woocommerce/widgets/checkout.php:1830
#: modules/woocommerce/widgets/checkout.php:2076
#: modules/woocommerce/widgets/checkout.php:2257
#: modules/woocommerce/widgets/checkout.php:2420
#: modules/woocommerce/widgets/checkout.php:2628
#: modules/woocommerce/widgets/checkout.php:3330
#: modules/woocommerce/widgets/checkout.php:3479
#: modules/woocommerce/widgets/menu-cart.php:763
#: modules/woocommerce/widgets/menu-cart.php:822
#: modules/woocommerce/widgets/menu-cart.php:997
#: modules/woocommerce/widgets/menu-cart.php:1044
#: modules/woocommerce/widgets/menu-cart.php:1801
#: modules/woocommerce/widgets/menu-cart.php:1838
#: modules/woocommerce/widgets/menu-cart.php:1980
#: modules/woocommerce/widgets/menu-cart.php:2017
#: modules/woocommerce/widgets/my-account.php:593
#: modules/woocommerce/widgets/product-add-to-cart.php:301
#: modules/woocommerce/widgets/product-add-to-cart.php:342
#: modules/woocommerce/widgets/product-add-to-cart.php:560
#: modules/woocommerce/widgets/product-add-to-cart.php:601
#: modules/woocommerce/widgets/product-add-to-cart.php:744
#: modules/woocommerce/widgets/product-data-tabs.php:71
#: modules/woocommerce/widgets/product-data-tabs.php:114
#: modules/woocommerce/widgets/products-base.php:400
#: modules/woocommerce/widgets/products-base.php:453
#: modules/woocommerce/widgets/products-base.php:668
#: modules/woocommerce/widgets/products-base.php:706
#: modules/woocommerce/widgets/products-base.php:831
#: modules/woocommerce/widgets/products-base.php:861
#: modules/woocommerce/widgets/products-base.php:891
#: modules/woocommerce/widgets/products-base.php:946
#: modules/woocommerce/widgets/purchase-summary.php:454
msgid "Background Color"
msgstr "Background Colour"

#: modules/call-to-action/widgets/call-to-action.php:307
#: modules/flip-box/widgets/flip-box.php:306
#: modules/posts/widgets/posts-base.php:365
#: modules/pricing/widgets/price-table.php:283
#: modules/slides/widgets/slides.php:268
msgid "Button Text"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:58
#: modules/blockquote/widgets/blockquote.php:524
#: modules/carousel/widgets/testimonial-carousel.php:162
#: modules/carousel/widgets/testimonial-carousel.php:429
#: modules/theme-builder/skins/post-comments-skin-classic.php:307
#: modules/theme-elements/widgets/author-box.php:398
#: modules/woocommerce/widgets/products-base.php:757
msgid "Border"
msgstr "Border"

#: modules/call-to-action/widgets/call-to-action.php:312
#: modules/flip-box/widgets/flip-box.php:308 modules/library/module.php:154
#: modules/pricing/widgets/price-table.php:285
#: modules/slides/widgets/slides.php:270 modules/slides/widgets/slides.php:490
#: modules/slides/widgets/slides.php:496 modules/slides/widgets/slides.php:502
msgid "Click Here"
msgstr ""

#: base/base-carousel-trait.php:647
#: modules/blockquote/widgets/blockquote.php:364
#: modules/blockquote/widgets/blockquote.php:700
#: modules/blockquote/widgets/blockquote.php:751
#: modules/call-to-action/widgets/call-to-action.php:621
#: modules/call-to-action/widgets/call-to-action.php:767
#: modules/call-to-action/widgets/call-to-action.php:1195
#: modules/carousel/widgets/base.php:351
#: modules/carousel/widgets/reviews.php:309
#: modules/carousel/widgets/testimonial-carousel.php:150
#: modules/carousel/widgets/testimonial-carousel.php:478
#: modules/countdown/widgets/countdown.php:375
#: modules/flip-box/widgets/flip-box.php:492
#: modules/flip-box/widgets/flip-box.php:762
#: modules/flip-box/widgets/flip-box.php:940
#: modules/flip-box/widgets/flip-box.php:1460
#: modules/forms/widgets/form.php:1309 modules/forms/widgets/form.php:1583
#: modules/forms/widgets/form.php:2009 modules/forms/widgets/login.php:540
#: modules/forms/widgets/login.php:623 modules/gallery/widgets/gallery.php:578
#: modules/gallery/widgets/gallery.php:618
#: modules/hotspot/widgets/hotspot.php:801
#: modules/hotspot/widgets/hotspot.php:942
#: modules/loop-filter/widgets/taxonomy-filter.php:569
#: modules/mega-menu/widgets/mega-menu.php:1179
#: modules/mega-menu/widgets/mega-menu.php:1610
#: modules/mega-menu/widgets/mega-menu.php:1694
#: modules/mega-menu/widgets/mega-menu.php:1888
#: modules/nav-menu/widgets/nav-menu.php:901
#: modules/nav-menu/widgets/nav-menu.php:1072
#: modules/nav-menu/widgets/nav-menu.php:1306
#: modules/nested-carousel/widgets/nested-carousel.php:235
#: modules/popup/document.php:448 modules/posts/skins/skin-base.php:499
#: modules/posts/skins/skin-cards.php:184
#: modules/posts/skins/skin-cards.php:399
#: modules/posts/skins/skin-classic.php:51
#: modules/posts/traits/button-widget-trait.php:371
#: modules/posts/widgets/portfolio.php:318
#: modules/pricing/widgets/price-list.php:408
#: modules/pricing/widgets/price-table.php:1160
#: modules/progress-tracker/widgets/progress-tracker.php:341
#: modules/progress-tracker/widgets/progress-tracker.php:497
#: modules/slides/widgets/slides.php:1017
#: modules/table-of-contents/widgets/table-of-contents.php:426
#: modules/theme-builder/skins/post-comments-skin-classic.php:148
#: modules/theme-builder/skins/post-comments-skin-classic.php:317
#: modules/theme-elements/widgets/author-box.php:447
#: modules/theme-elements/widgets/author-box.php:712
#: modules/theme-elements/widgets/search-form.php:442
#: modules/theme-elements/widgets/search-form.php:715
#: modules/woocommerce/settings/settings-woocommerce.php:528
#: modules/woocommerce/settings/settings-woocommerce.php:700
#: modules/woocommerce/widgets/cart.php:637
#: modules/woocommerce/widgets/cart.php:1005
#: modules/woocommerce/widgets/cart.php:1207
#: modules/woocommerce/widgets/cart.php:1745
#: modules/woocommerce/widgets/cart.php:1890
#: modules/woocommerce/widgets/cart.php:2009
#: modules/woocommerce/widgets/cart.php:2233
#: modules/woocommerce/widgets/categories.php:276
#: modules/woocommerce/widgets/checkout.php:636
#: modules/woocommerce/widgets/checkout.php:1182
#: modules/woocommerce/widgets/checkout.php:1386
#: modules/woocommerce/widgets/checkout.php:1730
#: modules/woocommerce/widgets/checkout.php:1891
#: modules/woocommerce/widgets/checkout.php:2137
#: modules/woocommerce/widgets/checkout.php:2318
#: modules/woocommerce/widgets/checkout.php:2481
#: modules/woocommerce/widgets/checkout.php:2689
#: modules/woocommerce/widgets/checkout.php:3391
#: modules/woocommerce/widgets/checkout.php:3540
#: modules/woocommerce/widgets/menu-cart.php:877
#: modules/woocommerce/widgets/menu-cart.php:1104
#: modules/woocommerce/widgets/menu-cart.php:1723
#: modules/woocommerce/widgets/menu-cart.php:1879
#: modules/woocommerce/widgets/menu-cart.php:2058
#: modules/woocommerce/widgets/my-account.php:509
#: modules/woocommerce/widgets/my-account.php:653
#: modules/woocommerce/widgets/my-account.php:1113
#: modules/woocommerce/widgets/my-account.php:1281
#: modules/woocommerce/widgets/my-account.php:1694
#: modules/woocommerce/widgets/product-add-to-cart.php:258
#: modules/woocommerce/widgets/product-add-to-cart.php:505
#: modules/woocommerce/widgets/product-add-to-cart.php:774
#: modules/woocommerce/widgets/product-data-tabs.php:159
#: modules/woocommerce/widgets/product-data-tabs.php:247
#: modules/woocommerce/widgets/product-images.php:77
#: modules/woocommerce/widgets/product-images.php:119
#: modules/woocommerce/widgets/products-base.php:145
#: modules/woocommerce/widgets/products-base.php:488
#: modules/woocommerce/widgets/products-base.php:616
#: modules/woocommerce/widgets/products-base.php:971
#: modules/woocommerce/widgets/purchase-summary.php:514
#: modules/woocommerce/widgets/purchase-summary.php:1468
msgid "Border Radius"
msgstr "Border Radius"

#: modules/blockquote/widgets/blockquote.php:231
#: modules/call-to-action/widgets/call-to-action.php:325
#: modules/carousel/widgets/media-carousel.php:191
#: modules/carousel/widgets/reviews.php:645
#: modules/flip-box/widgets/flip-box.php:324
#: modules/forms/actions/redirect.php:39 modules/forms/widgets/login.php:162
#: modules/forms/widgets/login.php:191 modules/hotspot/widgets/hotspot.php:95
#: modules/posts/traits/button-widget-trait.php:92
#: modules/pricing/widgets/price-table.php:297
#: modules/share-buttons/widgets/share-buttons.php:293
#: modules/slides/widgets/slides.php:282
#: modules/social/widgets/facebook-button.php:154
#: modules/social/widgets/facebook-comments.php:104
#: modules/theme-elements/widgets/author-box.php:222
#: modules/theme-elements/widgets/author-box.php:252
msgid "https://your-link.com"
msgstr "https://your-link.com"

#: modules/carousel/widgets/base.php:291
#: modules/countdown/widgets/countdown.php:387
#: modules/gallery/widgets/gallery.php:1315
#: modules/nav-menu/widgets/nav-menu.php:883
#: modules/posts/widgets/portfolio.php:435
#: modules/posts/widgets/posts-base.php:571
#: modules/theme-elements/widgets/post-info.php:409
#: modules/woocommerce/widgets/menu-cart.php:1690
#: modules/woocommerce/widgets/product-add-to-cart.php:684
#: modules/woocommerce/widgets/product-meta.php:67
#: modules/woocommerce/widgets/product-rating.php:114
#: modules/woocommerce/widgets/purchase-summary.php:691
#: modules/woocommerce/widgets/purchase-summary.php:863
msgid "Space Between"
msgstr "Space Between"

#: modules/call-to-action/widgets/call-to-action.php:333
#: modules/flip-box/widgets/flip-box.php:331
#: modules/slides/widgets/slides.php:292
msgid "Apply Link On"
msgstr ""

#: base/base-carousel-trait.php:682
#: modules/blockquote/widgets/blockquote.php:660
#: modules/call-to-action/widgets/call-to-action.php:473
#: modules/call-to-action/widgets/call-to-action.php:1221
#: modules/carousel/widgets/base.php:379
#: modules/carousel/widgets/testimonial-carousel.php:126
#: modules/countdown/widgets/countdown.php:411
#: modules/countdown/widgets/countdown.php:566
#: modules/flip-box/widgets/flip-box.php:591
#: modules/flip-box/widgets/flip-box.php:1078
#: modules/forms/widgets/form.php:1749 modules/gallery/widgets/gallery.php:911
#: modules/hotspot/widgets/hotspot.php:778
#: modules/hotspot/widgets/hotspot.php:916
#: modules/loop-filter/widgets/taxonomy-filter.php:582
#: modules/mega-menu/widgets/mega-menu.php:1192
#: modules/mega-menu/widgets/mega-menu.php:1623
#: modules/mega-menu/widgets/mega-menu.php:1718
#: modules/nested-carousel/widgets/nested-carousel.php:254
#: modules/popup/document.php:795 modules/posts/skins/skin-classic.php:69
#: modules/posts/traits/button-widget-trait.php:393
#: modules/pricing/widgets/price-table.php:403
#: modules/pricing/widgets/price-table.php:499
#: modules/pricing/widgets/price-table.php:841
#: modules/pricing/widgets/price-table.php:1054
#: modules/progress-tracker/widgets/progress-tracker.php:523
#: modules/slides/widgets/slides.php:770
#: modules/table-of-contents/widgets/table-of-contents.php:438
#: modules/theme-elements/widgets/author-box.php:734
#: modules/theme-elements/widgets/sitemap.php:381
#: modules/video-playlist/widgets/video-playlist.php:1426
#: modules/woocommerce/settings/settings-woocommerce.php:710
#: modules/woocommerce/widgets/cart.php:649
#: modules/woocommerce/widgets/cart.php:1017
#: modules/woocommerce/widgets/cart.php:1219
#: modules/woocommerce/widgets/cart.php:1757
#: modules/woocommerce/widgets/cart.php:1902
#: modules/woocommerce/widgets/cart.php:2021
#: modules/woocommerce/widgets/cart.php:2245
#: modules/woocommerce/widgets/checkout.php:648
#: modules/woocommerce/widgets/checkout.php:1194
#: modules/woocommerce/widgets/checkout.php:1398
#: modules/woocommerce/widgets/checkout.php:1742
#: modules/woocommerce/widgets/checkout.php:1903
#: modules/woocommerce/widgets/checkout.php:2149
#: modules/woocommerce/widgets/checkout.php:2330
#: modules/woocommerce/widgets/checkout.php:2493
#: modules/woocommerce/widgets/checkout.php:2701
#: modules/woocommerce/widgets/checkout.php:3403
#: modules/woocommerce/widgets/checkout.php:3552
#: modules/woocommerce/widgets/menu-cart.php:960
#: modules/woocommerce/widgets/menu-cart.php:1124
#: modules/woocommerce/widgets/menu-cart.php:1905
#: modules/woocommerce/widgets/menu-cart.php:2084
#: modules/woocommerce/widgets/my-account.php:521
#: modules/woocommerce/widgets/my-account.php:665
#: modules/woocommerce/widgets/my-account.php:1125
#: modules/woocommerce/widgets/my-account.php:1293
#: modules/woocommerce/widgets/my-account.php:1706
#: modules/woocommerce/widgets/product-add-to-cart.php:270
#: modules/woocommerce/widgets/product-add-to-cart.php:520
#: modules/woocommerce/widgets/products-base.php:634
#: modules/woocommerce/widgets/products-base.php:785
#: modules/woocommerce/widgets/purchase-summary.php:526
#: modules/woocommerce/widgets/purchase-summary.php:1480
msgid "Padding"
msgstr "Padding"

#: modules/slides/widgets/slides.php:295
msgid "Whole Slide"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:98
#: modules/blockquote/widgets/blockquote.php:244
#: modules/call-to-action/widgets/call-to-action.php:123
#: modules/call-to-action/widgets/call-to-action.php:785
#: modules/call-to-action/widgets/call-to-action.php:1323
#: modules/carousel/widgets/testimonial-carousel.php:222
#: modules/carousel/widgets/testimonial-carousel.php:524
#: modules/countdown/widgets/countdown.php:425
#: modules/dynamic-tags/tags/internal-url.php:77
#: modules/flip-box/widgets/flip-box.php:45
#: modules/flip-box/widgets/flip-box.php:272 modules/forms/widgets/form.php:80
#: modules/gallery/widgets/gallery.php:846
#: modules/hotspot/widgets/hotspot.php:70
#: modules/mega-menu/widgets/mega-menu.php:1656
#: modules/notes/user/personal-data.php:105
#: modules/posts/skins/skin-base.php:579
#: modules/progress-tracker/widgets/progress-tracker.php:542
#: modules/slides/widgets/slides.php:236
#: modules/theme-builder/skins/post-comments-skin-classic.php:61
#: modules/theme-builder/skins/post-comments-skin-classic.php:218
#: modules/theme-builder/widgets/post-excerpt.php:42
#: modules/video-playlist/widgets/video-playlist.php:1395
#: modules/woocommerce/widgets/checkout.php:182
#: modules/woocommerce/widgets/checkout.php:349
#: modules/woocommerce/widgets/checkout.php:3058
#: modules/woocommerce/widgets/products.php:64
msgid "Content"
msgstr "Content"

#: modules/call-to-action/widgets/call-to-action.php:337
#: modules/flip-box/widgets/flip-box.php:335
#: modules/slides/widgets/slides.php:296
msgid "Button Only"
msgstr ""

#: modules/countdown/widgets/countdown.php:433
msgid "Digits"
msgstr ""

#: base/base-carousel-trait.php:849 base/base-carousel-trait.php:952
#: modules/blockquote/widgets/blockquote.php:209
#: modules/blockquote/widgets/blockquote.php:402
#: modules/carousel/widgets/reviews.php:336
#: modules/dynamic-tags/pods/tags/pods-date.php:71
#: modules/dynamic-tags/tags/current-date-time.php:42
#: modules/dynamic-tags/tags/post-date.php:55
#: modules/dynamic-tags/tags/post-time.php:53
#: modules/dynamic-tags/toolset/tags/toolset-date.php:90
#: modules/forms/widgets/form.php:934 modules/lottie/widgets/lottie.php:130
#: modules/popup/display-settings/timing.php:153
#: modules/popup/display-settings/timing.php:218 modules/popup/document.php:197
#: modules/pricing/widgets/price-table.php:119
#: modules/query-control/controls/group-control-query.php:296
#: modules/share-buttons/widgets/share-buttons.php:277
#: modules/share-buttons/widgets/share-buttons.php:464
#: modules/slides/widgets/slides.php:323
#: modules/social/widgets/facebook-button.php:124
#: modules/social/widgets/facebook-comments.php:77
#: modules/theme-elements/widgets/author-box.php:56
#: modules/theme-elements/widgets/post-comments.php:69
#: modules/theme-elements/widgets/post-info.php:100
#: modules/theme-elements/widgets/post-info.php:117
#: modules/theme-elements/widgets/post-info.php:154
#: modules/theme-elements/widgets/post-info.php:288
#: modules/theme-elements/widgets/post-info.php:334
#: modules/woocommerce/widgets/menu-cart.php:61
#: assets/js/form-submission-admin.js:1763
msgid "Custom"
msgstr "Custom"

#: base/base-carousel-trait.php:801 base/base-carousel-trait.php:824
#: base/base-carousel-trait.php:931 base/base-carousel-trait.php:1038
#: base/base-carousel-trait.php:1061 base/base-carousel-trait.php:1123
#: modules/animated-headline/widgets/animated-headline.php:320
#: modules/blockquote/widgets/blockquote.php:398
#: modules/blockquote/widgets/blockquote.php:544
#: modules/blockquote/widgets/blockquote.php:590
#: modules/blockquote/widgets/blockquote.php:791
#: modules/carousel/widgets/base.php:431 modules/carousel/widgets/base.php:495
#: modules/carousel/widgets/media-carousel.php:79
#: modules/carousel/widgets/media-carousel.php:434
#: modules/carousel/widgets/reviews.php:123
#: modules/carousel/widgets/reviews.php:181
#: modules/carousel/widgets/reviews.php:212
#: modules/carousel/widgets/reviews.php:240
#: modules/carousel/widgets/reviews.php:331
#: modules/carousel/widgets/reviews.php:344
#: modules/carousel/widgets/reviews.php:461
#: modules/countdown/widgets/countdown.php:441
#: modules/countdown/widgets/countdown.php:472
#: modules/forms/actions/discord.php:129 modules/forms/actions/slack.php:145
#: modules/forms/widgets/form.php:1207 modules/forms/widgets/form.php:1951
#: modules/forms/widgets/form.php:2059 modules/gallery/widgets/gallery.php:938
#: modules/gallery/widgets/gallery.php:993
#: modules/hotspot/widgets/hotspot.php:678
#: modules/hotspot/widgets/hotspot.php:928
#: modules/mega-menu/widgets/mega-menu.php:841
#: modules/mega-menu/widgets/mega-menu.php:987
#: modules/mega-menu/widgets/mega-menu.php:1011
#: modules/mega-menu/widgets/mega-menu.php:1112
#: modules/mega-menu/widgets/mega-menu.php:1313
#: modules/mega-menu/widgets/mega-menu.php:1330
#: modules/mega-menu/widgets/mega-menu.php:1347
#: modules/mega-menu/widgets/mega-menu.php:1417
#: modules/mega-menu/widgets/mega-menu.php:1474
#: modules/mega-menu/widgets/mega-menu.php:1553
#: modules/nav-menu/widgets/nav-menu.php:798
#: modules/nav-menu/widgets/nav-menu.php:1210
#: modules/nav-menu/widgets/nav-menu.php:1242
#: modules/page-transitions/module.php:506 modules/popup/document.php:613
#: modules/popup/document.php:645 modules/posts/skins/skin-base.php:598
#: modules/posts/skins/skin-base.php:669 modules/posts/skins/skin-base.php:743
#: modules/posts/skins/skin-base.php:803
#: modules/posts/widgets/portfolio.php:354
#: modules/posts/widgets/portfolio.php:396
#: modules/posts/widgets/posts-base.php:110
#: modules/posts/widgets/posts-base.php:516
#: modules/posts/widgets/posts-base.php:536
#: modules/posts/widgets/posts-base.php:556
#: modules/pricing/widgets/price-list.php:199
#: modules/pricing/widgets/price-list.php:241
#: modules/pricing/widgets/price-list.php:275
#: modules/pricing/widgets/price-list.php:352
#: modules/pricing/widgets/price-table.php:424
#: modules/pricing/widgets/price-table.php:455
#: modules/pricing/widgets/price-table.php:511
#: modules/pricing/widgets/price-table.php:690
#: modules/pricing/widgets/price-table.php:770
#: modules/pricing/widgets/price-table.php:853
#: modules/pricing/widgets/price-table.php:952
#: modules/pricing/widgets/price-table.php:1257
#: modules/progress-tracker/widgets/progress-tracker.php:209
#: modules/progress-tracker/widgets/progress-tracker.php:326
#: modules/progress-tracker/widgets/progress-tracker.php:365
#: modules/progress-tracker/widgets/progress-tracker.php:482
#: modules/progress-tracker/widgets/progress-tracker.php:562
#: modules/share-buttons/widgets/share-buttons.php:460
#: modules/slides/widgets/slides.php:73 modules/slides/widgets/slides.php:180
#: modules/slides/widgets/slides.php:1263
#: modules/table-of-contents/widgets/table-of-contents.php:713
#: modules/theme-builder/widgets/archive-posts.php:102
#: modules/theme-elements/widgets/author-box.php:491
#: modules/theme-elements/widgets/author-box.php:543
#: modules/theme-elements/widgets/author-box.php:651
#: modules/theme-elements/widgets/breadcrumbs.php:163
#: modules/theme-elements/widgets/post-info.php:572
#: modules/theme-elements/widgets/post-info.php:600
#: modules/theme-elements/widgets/post-navigation.php:215
#: modules/theme-elements/widgets/post-navigation.php:239
#: modules/theme-elements/widgets/post-navigation.php:289
#: modules/theme-elements/widgets/post-navigation.php:312
#: modules/theme-elements/widgets/post-navigation.php:361
#: modules/theme-elements/widgets/post-navigation.php:381
#: modules/theme-elements/widgets/post-navigation.php:448
#: modules/theme-elements/widgets/search-form.php:623
#: modules/theme-elements/widgets/search-form.php:654
#: modules/theme-elements/widgets/sitemap.php:402
#: modules/theme-elements/widgets/sitemap.php:437
#: modules/theme-elements/widgets/sitemap.php:472
#: modules/video-playlist/widgets/video-playlist.php:721
#: modules/video-playlist/widgets/video-playlist.php:752
#: modules/video-playlist/widgets/video-playlist.php:818
#: modules/video-playlist/widgets/video-playlist.php:852
#: modules/video-playlist/widgets/video-playlist.php:882
#: modules/video-playlist/widgets/video-playlist.php:982
#: modules/video-playlist/widgets/video-playlist.php:1024
#: modules/video-playlist/widgets/video-playlist.php:1059
#: modules/video-playlist/widgets/video-playlist.php:1090
#: modules/video-playlist/widgets/video-playlist.php:1185
#: modules/video-playlist/widgets/video-playlist.php:1232
#: modules/video-playlist/widgets/video-playlist.php:1287
#: modules/video-playlist/widgets/video-playlist.php:1361
#: modules/video-playlist/widgets/video-playlist.php:1404
#: modules/video-playlist/widgets/video-playlist.php:1456
#: modules/video-playlist/widgets/video-playlist.php:1479
#: modules/woocommerce/settings/settings-woocommerce.php:231
#: modules/woocommerce/settings/settings-woocommerce.php:248
#: modules/woocommerce/settings/settings-woocommerce.php:302
#: modules/woocommerce/settings/settings-woocommerce.php:319
#: modules/woocommerce/settings/settings-woocommerce.php:451
#: modules/woocommerce/settings/settings-woocommerce.php:484
#: modules/woocommerce/settings/settings-woocommerce.php:688
#: modules/woocommerce/widgets/archive-products-deprecated.php:124
#: modules/woocommerce/widgets/archive-products.php:157
#: modules/woocommerce/widgets/cart.php:623
#: modules/woocommerce/widgets/cart.php:691
#: modules/woocommerce/widgets/cart.php:744
#: modules/woocommerce/widgets/cart.php:832
#: modules/woocommerce/widgets/cart.php:899
#: modules/woocommerce/widgets/cart.php:933
#: modules/woocommerce/widgets/cart.php:1193
#: modules/woocommerce/widgets/cart.php:1268
#: modules/woocommerce/widgets/cart.php:1321
#: modules/woocommerce/widgets/cart.php:1348
#: modules/woocommerce/widgets/cart.php:1379
#: modules/woocommerce/widgets/cart.php:1394
#: modules/woocommerce/widgets/cart.php:1418
#: modules/woocommerce/widgets/cart.php:1450
#: modules/woocommerce/widgets/cart.php:1486
#: modules/woocommerce/widgets/cart.php:1501
#: modules/woocommerce/widgets/cart.php:1553
#: modules/woocommerce/widgets/cart.php:1580
#: modules/woocommerce/widgets/cart.php:1876
#: modules/woocommerce/widgets/cart.php:1995
#: modules/woocommerce/widgets/cart.php:2054
#: modules/woocommerce/widgets/cart.php:2090
#: modules/woocommerce/widgets/cart.php:2136
#: modules/woocommerce/widgets/cart.php:2219
#: modules/woocommerce/widgets/categories.php:309
#: modules/woocommerce/widgets/categories.php:343
#: modules/woocommerce/widgets/checkout.php:622
#: modules/woocommerce/widgets/checkout.php:692
#: modules/woocommerce/widgets/checkout.php:746
#: modules/woocommerce/widgets/checkout.php:792
#: modules/woocommerce/widgets/checkout.php:838
#: modules/woocommerce/widgets/checkout.php:866
#: modules/woocommerce/widgets/checkout.php:894
#: modules/woocommerce/widgets/checkout.php:1015
#: modules/woocommerce/widgets/checkout.php:1074
#: modules/woocommerce/widgets/checkout.php:1110
#: modules/woocommerce/widgets/checkout.php:1372
#: modules/woocommerce/widgets/checkout.php:1446
#: modules/woocommerce/widgets/checkout.php:1473
#: modules/woocommerce/widgets/checkout.php:1503
#: modules/woocommerce/widgets/checkout.php:1534
#: modules/woocommerce/widgets/checkout.php:1561
#: modules/woocommerce/widgets/checkout.php:1877
#: modules/woocommerce/widgets/checkout.php:1936
#: modules/woocommerce/widgets/checkout.php:1963
#: modules/woocommerce/widgets/checkout.php:1990
#: modules/woocommerce/widgets/checkout.php:2023
#: modules/woocommerce/widgets/checkout.php:2040
#: modules/woocommerce/widgets/checkout.php:2123
#: modules/woocommerce/widgets/checkout.php:2182
#: modules/woocommerce/widgets/checkout.php:2217
#: modules/woocommerce/widgets/checkout.php:2304
#: modules/woocommerce/widgets/checkout.php:2364
#: modules/woocommerce/widgets/checkout.php:2467
#: modules/woocommerce/widgets/checkout.php:2526
#: modules/woocommerce/widgets/checkout.php:2561
#: modules/woocommerce/widgets/checkout.php:2588
#: modules/woocommerce/widgets/checkout.php:2675
#: modules/woocommerce/widgets/checkout.php:2750
#: modules/woocommerce/widgets/checkout.php:2777
#: modules/woocommerce/widgets/checkout.php:2804
#: modules/woocommerce/widgets/checkout.php:2831
#: modules/woocommerce/widgets/checkout.php:3377
#: modules/woocommerce/widgets/checkout.php:3437
#: modules/woocommerce/widgets/checkout.php:3526
#: modules/woocommerce/widgets/checkout.php:3585
#: modules/woocommerce/widgets/checkout.php:3618
#: modules/woocommerce/widgets/checkout.php:3635
#: modules/woocommerce/widgets/menu-cart.php:1090
#: modules/woocommerce/widgets/menu-cart.php:1175
#: modules/woocommerce/widgets/menu-cart.php:1201
#: modules/woocommerce/widgets/menu-cart.php:1265
#: modules/woocommerce/widgets/menu-cart.php:1291
#: modules/woocommerce/widgets/menu-cart.php:1318
#: modules/woocommerce/widgets/menu-cart.php:1401
#: modules/woocommerce/widgets/menu-cart.php:1446
#: modules/woocommerce/widgets/menu-cart.php:1462
#: modules/woocommerce/widgets/menu-cart.php:1487
#: modules/woocommerce/widgets/menu-cart.php:1515
#: modules/woocommerce/widgets/menu-cart.php:1546
#: modules/woocommerce/widgets/menu-cart.php:1593
#: modules/woocommerce/widgets/my-account.php:362
#: modules/woocommerce/widgets/my-account.php:393
#: modules/woocommerce/widgets/my-account.php:438
#: modules/woocommerce/widgets/my-account.php:495
#: modules/woocommerce/widgets/my-account.php:560
#: modules/woocommerce/widgets/my-account.php:639
#: modules/woocommerce/widgets/my-account.php:698
#: modules/woocommerce/widgets/my-account.php:751
#: modules/woocommerce/widgets/my-account.php:778
#: modules/woocommerce/widgets/my-account.php:805
#: modules/woocommerce/widgets/my-account.php:833
#: modules/woocommerce/widgets/my-account.php:865
#: modules/woocommerce/widgets/my-account.php:880
#: modules/woocommerce/widgets/my-account.php:949
#: modules/woocommerce/widgets/my-account.php:1007
#: modules/woocommerce/widgets/my-account.php:1041
#: modules/woocommerce/widgets/my-account.php:1342
#: modules/woocommerce/widgets/my-account.php:1377
#: modules/woocommerce/widgets/my-account.php:1404
#: modules/woocommerce/widgets/my-account.php:1435
#: modules/woocommerce/widgets/my-account.php:1450
#: modules/woocommerce/widgets/my-account.php:1507
#: modules/woocommerce/widgets/my-account.php:1680
#: modules/woocommerce/widgets/product-add-to-cart.php:397
#: modules/woocommerce/widgets/product-add-to-cart.php:705
#: modules/woocommerce/widgets/product-add-to-cart.php:733
#: modules/woocommerce/widgets/product-additional-information.php:49
#: modules/woocommerce/widgets/product-additional-information.php:74
#: modules/woocommerce/widgets/product-meta.php:199
#: modules/woocommerce/widgets/product-meta.php:234
#: modules/woocommerce/widgets/product-meta.php:262
#: modules/woocommerce/widgets/product-price.php:78
#: modules/woocommerce/widgets/product-price.php:112
#: modules/woocommerce/widgets/product-related.php:119
#: modules/woocommerce/widgets/product-upsell.php:106
#: modules/woocommerce/widgets/products-base.php:178
#: modules/woocommerce/widgets/products-base.php:307
#: modules/woocommerce/widgets/products-base.php:343
#: modules/woocommerce/widgets/products-base.php:548
#: modules/woocommerce/widgets/products-base.php:820
#: modules/woocommerce/widgets/products-base.php:850
#: modules/woocommerce/widgets/products-base.php:880
#: modules/woocommerce/widgets/products.php:280
#: modules/woocommerce/widgets/purchase-summary.php:500
#: modules/woocommerce/widgets/purchase-summary.php:574
#: modules/woocommerce/widgets/purchase-summary.php:609
#: modules/woocommerce/widgets/purchase-summary.php:662
#: modules/woocommerce/widgets/purchase-summary.php:718
#: modules/woocommerce/widgets/purchase-summary.php:774
#: modules/woocommerce/widgets/purchase-summary.php:839
#: modules/woocommerce/widgets/purchase-summary.php:890
#: modules/woocommerce/widgets/purchase-summary.php:943
#: modules/woocommerce/widgets/purchase-summary.php:999
#: modules/woocommerce/widgets/purchase-summary.php:1064
#: modules/woocommerce/widgets/purchase-summary.php:1114
#: modules/woocommerce/widgets/purchase-summary.php:1149
#: modules/woocommerce/widgets/purchase-summary.php:1176
#: modules/woocommerce/widgets/purchase-summary.php:1207
#: modules/woocommerce/widgets/purchase-summary.php:1222
#: modules/woocommerce/widgets/purchase-summary.php:1280
#: modules/woocommerce/widgets/purchase-summary.php:1454
msgid "Color"
msgstr "Colour"

#: modules/slides/widgets/slides.php:325
msgid "Set custom style that will only affect this specific slide."
msgstr ""

#: modules/blockquote/widgets/blockquote.php:173
#: modules/countdown/widgets/countdown.php:463
#: modules/forms/widgets/form.php:96 modules/forms/widgets/form.php:538
#: modules/forms/widgets/form.php:1102 modules/forms/widgets/login.php:46
#: modules/forms/widgets/login.php:405 modules/hotspot/widgets/hotspot.php:77
#: modules/share-buttons/widgets/share-buttons.php:169
#: modules/theme-elements/widgets/post-navigation.php:51
#: modules/theme-elements/widgets/post-navigation.php:195
#: modules/woocommerce/widgets/checkout.php:188
#: modules/woocommerce/widgets/checkout.php:355
#: modules/woocommerce/widgets/checkout.php:3064
#: modules/woocommerce/widgets/product-add-to-cart.php:696
msgid "Label"
msgstr ""

#: modules/popup/document.php:570 modules/slides/widgets/slides.php:332
#: modules/slides/widgets/slides.php:782
msgid "Horizontal Position"
msgstr ""

#: modules/countdown/widgets/countdown.php:600
msgid "Months"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:449
#: modules/flip-box/widgets/flip-box.php:629
#: modules/flip-box/widgets/flip-box.php:1117
#: modules/gallery/widgets/gallery.php:881 modules/popup/document.php:538
#: modules/pricing/widgets/price-table.php:586
#: modules/pricing/widgets/price-table.php:646
#: modules/pricing/widgets/price-table.php:723
#: modules/slides/widgets/slides.php:370 modules/slides/widgets/slides.php:806
#: modules/woocommerce/widgets/menu-cart.php:630
msgid "Vertical Position"
msgstr ""

#: modules/countdown/widgets/countdown.php:601
msgid "Weeks"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:457
#: modules/flip-box/widgets/flip-box.php:637
#: modules/flip-box/widgets/flip-box.php:1125
#: modules/gallery/widgets/gallery.php:889
#: modules/pricing/widgets/price-table.php:594
#: modules/pricing/widgets/price-table.php:654
#: modules/pricing/widgets/price-table.php:731
#: modules/slides/widgets/slides.php:378 modules/slides/widgets/slides.php:815
#: modules/theme-elements/widgets/author-box.php:342
#: modules/woocommerce/widgets/menu-cart.php:418
msgid "Middle"
msgstr "Middle"

#: modules/custom-css/module.php:101 assets/js/editor.js:656
msgid "Custom CSS"
msgstr "Custom CSS"

#: modules/slides/widgets/slides.php:408 modules/slides/widgets/slides.php:830
#: modules/woocommerce/widgets/product-related.php:150
#: modules/woocommerce/widgets/product-upsell.php:137
msgid "Text Align"
msgstr "Text Align"

#: modules/custom-css/module.php:109
msgid "Add your own custom CSS"
msgstr ""

#: modules/slides/widgets/slides.php:441
msgid "Content Color"
msgstr ""

#: modules/slides/widgets/slides.php:488
msgid "Slide 1 Heading"
msgstr ""

#: modules/forms/classes/form-record.php:20
msgid "No Label"
msgstr ""

#: modules/slides/widgets/slides.php:494
msgid "Slide 2 Heading"
msgstr ""

#: modules/slides/widgets/slides.php:500
msgid "Slide 3 Heading"
msgstr ""

#: base/base-carousel-trait.php:1008
#: modules/call-to-action/widgets/call-to-action.php:401
#: modules/call-to-action/widgets/call-to-action.php:524
#: modules/carousel/widgets/base.php:104
#: modules/carousel/widgets/media-carousel.php:647
#: modules/code-highlight/widgets/code-highlight.php:229
#: modules/flip-box/widgets/flip-box.php:469
#: modules/forms/widgets/form.php:1985
#: modules/mega-menu/widgets/mega-menu.php:964
#: modules/nav-menu/widgets/nav-menu.php:775 modules/popup/document.php:191
#: modules/progress-tracker/widgets/progress-tracker.php:418
#: modules/slides/widgets/slides.php:513
#: modules/social/widgets/facebook-page.php:108
#: modules/theme-elements/widgets/post-info.php:543
#: modules/video-playlist/widgets/video-playlist.php:424
#: modules/video-playlist/widgets/video-playlist.php:666
#: modules/woocommerce/widgets/product-meta.php:170
#: modules/woocommerce/widgets/products-base.php:1001
msgid "Height"
msgstr "Height"

#: modules/forms/widgets/form.php:137
msgid "Required"
msgstr ""

#: modules/slides/widgets/slides.php:581
msgid "Slider Options"
msgstr ""

#: base/base-carousel-trait.php:234 base/base-carousel-trait.php:585
#: modules/carousel/widgets/base.php:394 modules/slides/widgets/slides.php:589
#: modules/slides/widgets/slides.php:1133
msgid "Navigation"
msgstr "Navigation"

#: modules/slides/widgets/slides.php:593
msgid "Arrows and Dots"
msgstr "Arrows and Dots"

#: license/admin.php:311 assets/js/form-submission-admin.js:2589
msgid "Unknown"
msgstr ""

#: base/base-carousel-trait.php:247 modules/carousel/widgets/base.php:158
#: modules/carousel/widgets/base.php:402 modules/slides/widgets/slides.php:594
#: modules/slides/widgets/slides.php:1144
#: modules/theme-elements/widgets/post-navigation.php:92
msgid "Arrows"
msgstr "Arrows"

#: modules/forms/actions/email.php:195
#: modules/forms/classes/form-record.php:164 modules/forms/fields/date.php:25
#: modules/forms/widgets/form.php:54 modules/posts/skins/skin-base.php:372
#: modules/query-control/controls/group-control-query.php:286
#: modules/query-control/controls/group-control-query.php:348
#: modules/theme-elements/widgets/post-info.php:96
#: modules/theme-elements/widgets/sitemap.php:245
#: modules/woocommerce/traits/products-trait.php:69
#: modules/woocommerce/widgets/product-related.php:62
#: modules/woocommerce/widgets/product-upsell.php:49
#: modules/woocommerce/widgets/products-deprecated.php:136
msgid "Date"
msgstr ""

#: base/base-carousel-trait.php:732 base/base-carousel-trait.php:761
#: modules/carousel/widgets/base.php:176 modules/slides/widgets/slides.php:595
msgid "Dots"
msgstr "Dots"

#: modules/forms/actions/email.php:196
#: modules/forms/classes/form-record.php:171 modules/forms/fields/time.php:27
#: modules/forms/widgets/form.php:55 modules/posts/skins/skin-base.php:373
#: modules/social/widgets/facebook-comments.php:65
#: modules/theme-elements/widgets/post-info.php:97
msgid "Time"
msgstr ""

#: modules/carousel/widgets/base.php:236 modules/slides/widgets/slides.php:615
msgid "Pause on Hover"
msgstr "Pause on Hover"

#: modules/forms/actions/email.php:197
#: modules/forms/classes/form-record.php:178
msgid "Page URL"
msgstr ""

#: base/base-carousel-trait.php:77 modules/carousel/widgets/base.php:200
#: modules/slides/widgets/slides.php:605
#: modules/social/widgets/facebook-embed.php:147
#: modules/video-playlist/widgets/video-playlist.php:525
msgid "Autoplay"
msgstr "Autoplay"

#: modules/forms/actions/email.php:198
#: modules/forms/classes/form-record.php:192
#: modules/forms/submissions/export/csv-export.php:92
#: modules/forms/submissions/personal-data.php:59
msgid "User Agent"
msgstr ""

#: modules/forms/actions/email.php:199
#: modules/forms/classes/form-record.php:199
msgid "Remote IP"
msgstr ""

#: modules/animated-headline/widgets/animated-headline.php:188
#: modules/carousel/widgets/base.php:226 modules/slides/widgets/slides.php:660
msgid "Infinite Loop"
msgstr "Infinite Loop"

#: modules/slides/widgets/slides.php:670
msgid "Transition"
msgstr ""

#. translators: %s: Site title.
#: modules/forms/actions/email.php:56 modules/forms/actions/email.php:254
msgid "New message from \"%s\""
msgstr ""

#: modules/carousel/widgets/base.php:60 modules/slides/widgets/slides.php:674
msgid "Slide"
msgstr "Slide"

#: modules/forms/classes/recaptcha-handler.php:47
msgid "To use reCAPTCHA, you need to add the API Key and complete the setup process in Dashboard > Elementor > Settings > Integrations > reCAPTCHA."
msgstr ""

#: modules/carousel/widgets/base.php:61 modules/slides/widgets/slides.php:675
msgid "Fade"
msgstr "Fade"

#: modules/forms/classes/recaptcha-handler.php:52
#: modules/forms/classes/recaptcha-handler.php:271
msgid "reCAPTCHA"
msgstr ""

#: modules/slides/widgets/slides.php:695
msgid "Content Animation"
msgstr ""

#: modules/forms/classes/recaptcha-handler.php:63
#: modules/forms/classes/recaptcha-v3-handler.php:68
msgid "Site Key"
msgstr ""

#: modules/motion-fx/controls-group.php:205
#: modules/popup/display-settings/triggers.php:54
#: modules/slides/widgets/slides.php:700
msgid "Down"
msgstr ""

#: modules/forms/classes/recaptcha-handler.php:69
#: modules/forms/classes/recaptcha-v3-handler.php:74
msgid "Secret Key"
msgstr ""

#: modules/motion-fx/controls-group.php:204
#: modules/popup/display-settings/triggers.php:55
#: modules/slides/widgets/slides.php:701
msgid "Up"
msgstr ""

#: modules/forms/classes/recaptcha-handler.php:134
msgid "The Captcha field cannot be blank. Please enter a value."
msgstr ""

#: modules/slides/widgets/slides.php:704
msgid "Zoom"
msgstr "Zoom"

#: modules/forms/classes/recaptcha-handler.php:140
msgid "The secret parameter is missing."
msgstr ""

#: modules/mega-menu/widgets/mega-menu.php:266
#: modules/slides/widgets/slides.php:738
msgid "Content Width"
msgstr "Content Width"

#: modules/forms/classes/recaptcha-handler.php:141
msgid "The secret parameter is invalid or malformed."
msgstr ""

#: base/base-carousel-trait.php:1151
#: modules/call-to-action/widgets/call-to-action.php:743
#: modules/call-to-action/widgets/call-to-action.php:1174
#: modules/carousel/widgets/base.php:339
#: modules/carousel/widgets/testimonial-carousel.php:189
#: modules/carousel/widgets/testimonial-carousel.php:455
#: modules/flip-box/widgets/flip-box.php:916
#: modules/flip-box/widgets/flip-box.php:1436
#: modules/forms/widgets/form.php:1295 modules/forms/widgets/login.php:526
#: modules/gallery/widgets/gallery.php:558
#: modules/mega-menu/widgets/mega-menu.php:887
#: modules/mega-menu/widgets/mega-menu.php:1057
#: modules/mega-menu/widgets/mega-menu.php:1158
#: modules/mega-menu/widgets/mega-menu.php:1453
#: modules/mega-menu/widgets/mega-menu.php:1510
#: modules/mega-menu/widgets/mega-menu.php:1589
#: modules/mega-menu/widgets/mega-menu.php:1685
#: modules/mega-menu/widgets/mega-menu.php:1876
#: modules/nav-menu/widgets/nav-menu.php:1146
#: modules/nav-menu/widgets/nav-menu.php:1286
#: modules/nested-carousel/widgets/nested-carousel.php:226
#: modules/posts/skins/skin-cards.php:379
#: modules/posts/skins/skin-classic.php:39
#: modules/share-buttons/widgets/share-buttons.php:434
#: modules/slides/widgets/slides.php:997
#: modules/table-of-contents/widgets/table-of-contents.php:406
#: modules/theme-builder/skins/post-comments-skin-classic.php:135
#: modules/theme-elements/widgets/author-box.php:424
#: modules/theme-elements/widgets/author-box.php:688
#: modules/theme-elements/widgets/search-form.php:428
#: modules/theme-elements/widgets/search-form.php:694
#: modules/video-playlist/widgets/video-playlist.php:1307
#: modules/woocommerce/widgets/cart.php:1861
#: modules/woocommerce/widgets/cart.php:1980
#: modules/woocommerce/widgets/cart.php:2204
#: modules/woocommerce/widgets/checkout.php:1862
#: modules/woocommerce/widgets/checkout.php:2108
#: modules/woocommerce/widgets/checkout.php:2289
#: modules/woocommerce/widgets/checkout.php:2452
#: modules/woocommerce/widgets/checkout.php:2660
#: modules/woocommerce/widgets/checkout.php:3362
#: modules/woocommerce/widgets/checkout.php:3511
#: modules/woocommerce/widgets/menu-cart.php:856
#: modules/woocommerce/widgets/product-data-tabs.php:235
#: modules/woocommerce/widgets/products-base.php:604
msgid "Border Width"
msgstr "Border Width"

#: modules/forms/classes/recaptcha-handler.php:142
msgid "The response parameter is missing."
msgstr ""

#: modules/slides/widgets/slides.php:1156
msgid "Arrows Position"
msgstr ""

#: modules/forms/classes/recaptcha-handler.php:143
msgid "The response parameter is invalid or malformed."
msgstr ""

#: base/base-carousel-trait.php:698 modules/carousel/widgets/base.php:459
#: modules/popup/document.php:528 modules/slides/widgets/slides.php:1160
#: modules/slides/widgets/slides.php:1226
msgid "Inside"
msgstr "Inside"

#. translators: %d: Response code.
#: modules/forms/classes/recaptcha-handler.php:163
msgid "Can not connect to the reCAPTCHA server (%d)."
msgstr ""

#: base/base-carousel-trait.php:699 modules/carousel/widgets/base.php:458
#: modules/popup/document.php:529 modules/slides/widgets/slides.php:1161
#: modules/slides/widgets/slides.php:1225
msgid "Outside"
msgstr "Outside"

#: modules/forms/widgets/form.php:1010
msgid "Invalid Form"
msgstr ""

#: modules/slides/widgets/slides.php:1173
msgid "Arrows Size"
msgstr ""

#: assets/js/form-submission-admin.js:5219
msgid "Form:"
msgstr ""

#: modules/slides/widgets/slides.php:1194
msgid "Arrows Color"
msgstr ""

#: modules/dynamic-tags/acf/tags/acf-number.php:18
#: modules/dynamic-tags/acf/tags/acf-text.php:18
#: modules/dynamic-tags/pods/tags/pods-numeric.php:17
#: modules/dynamic-tags/pods/tags/pods-text.php:15
#: modules/dynamic-tags/tags/author-info.php:50
#: modules/dynamic-tags/tags/user-info.php:74
#: modules/dynamic-tags/toolset/tags/toolset-text.php:15
#: modules/forms/widgets/form.php:1234 modules/woocommerce/widgets/cart.php:880
msgid "Field"
msgstr ""

#: modules/forms/actions/convertkit.php:81 modules/forms/widgets/form.php:29
#: modules/forms/widgets/form.php:1049 modules/forms/widgets/login.php:345
#: assets/js/form-submission-admin.js:4855
msgid "Form"
msgstr "Form"

#: modules/call-to-action/widgets/call-to-action.php:1056
#: modules/flip-box/widgets/flip-box.php:1306
#: modules/forms/classes/form-base.php:31 modules/forms/widgets/form.php:524
#: modules/forms/widgets/login.php:60 modules/forms/widgets/login.php:97
#: modules/posts/traits/button-widget-trait.php:32
#: modules/pricing/widgets/price-table.php:1082
#: modules/slides/widgets/slides.php:43
msgid "Extra Small"
msgstr "Extra Small"

#: modules/call-to-action/widgets/call-to-action.php:1057
#: modules/flip-box/widgets/flip-box.php:1307
#: modules/forms/classes/form-base.php:32 modules/forms/widgets/form.php:525
#: modules/forms/widgets/login.php:61 modules/forms/widgets/login.php:98
#: modules/posts/traits/button-widget-trait.php:33
#: modules/pricing/widgets/price-table.php:1083
#: modules/slides/widgets/slides.php:44
#: modules/social/widgets/facebook-button.php:77
msgid "Small"
msgstr "Small"

#: modules/call-to-action/widgets/call-to-action.php:1058
#: modules/flip-box/widgets/flip-box.php:1308
#: modules/forms/classes/form-base.php:33 modules/forms/widgets/form.php:526
#: modules/forms/widgets/login.php:62 modules/forms/widgets/login.php:99
#: modules/posts/traits/button-widget-trait.php:34
#: modules/pricing/widgets/price-table.php:1084
#: modules/slides/widgets/slides.php:45
#: modules/woocommerce/widgets/menu-cart.php:53
#: modules/woocommerce/widgets/menu-cart.php:56
#: modules/woocommerce/widgets/menu-cart.php:59
msgid "Medium"
msgstr "Medium"

#: modules/call-to-action/widgets/call-to-action.php:1059
#: modules/flip-box/widgets/flip-box.php:1309
#: modules/forms/classes/form-base.php:34 modules/forms/widgets/form.php:527
#: modules/forms/widgets/login.php:63 modules/forms/widgets/login.php:100
#: modules/posts/traits/button-widget-trait.php:35
#: modules/pricing/widgets/price-table.php:1085
#: modules/slides/widgets/slides.php:46
#: modules/social/widgets/facebook-button.php:78
msgid "Large"
msgstr "Large"

#: modules/woocommerce/widgets/products-deprecated.php:83
msgid "Products Count"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:1060
#: modules/flip-box/widgets/flip-box.php:1310
#: modules/forms/classes/form-base.php:35 modules/forms/widgets/form.php:528
#: modules/forms/widgets/login.php:64 modules/forms/widgets/login.php:101
#: modules/posts/traits/button-widget-trait.php:36
#: modules/pricing/widgets/price-table.php:1086
#: modules/slides/widgets/slides.php:47
msgid "Extra Large"
msgstr "Extra Large"

#: modules/carousel/widgets/media-carousel.php:141
#: modules/countdown/widgets/countdown.php:47
#: modules/dynamic-tags/tags/contact-url.php:34
#: modules/dynamic-tags/tags/internal-url.php:74
#: modules/dynamic-tags/tags/lightbox.php:40
#: modules/dynamic-tags/tags/post-date.php:33
#: modules/dynamic-tags/tags/post-time.php:33
#: modules/dynamic-tags/tags/request-parameter.php:61
#: modules/forms/widgets/form.php:86 modules/forms/widgets/form.php:838
#: modules/gallery/widgets/gallery.php:69
#: modules/page-transitions/module.php:302
#: modules/posts/traits/button-widget-trait.php:55
#: modules/social/widgets/facebook-button.php:45
#: modules/social/widgets/facebook-embed.php:47
#: modules/theme-elements/widgets/post-info.php:91
#: modules/theme-elements/widgets/search-form.php:95
#: modules/theme-elements/widgets/sitemap.php:191
#: modules/video-playlist/widgets/video-playlist.php:75
msgid "Type"
msgstr "Type"

#: modules/carousel/widgets/media-carousel.php:488
#: modules/carousel/widgets/reviews.php:165 modules/forms/widgets/form.php:44
#: modules/forms/widgets/login.php:82 modules/gallery/widgets/gallery.php:384
#: modules/nav-menu/widgets/nav-menu.php:167
#: modules/posts/traits/button-widget-trait.php:44
#: modules/pricing/widgets/price-table.php:212
#: modules/theme-elements/widgets/author-box.php:474
#: modules/theme-elements/widgets/post-info.php:640
#: modules/theme-elements/widgets/search-form.php:100
#: modules/theme-elements/widgets/search-form.php:113
#: modules/woocommerce/tags/product-sale.php:21
#: modules/woocommerce/widgets/cart.php:138
#: modules/woocommerce/widgets/cart.php:219
#: modules/woocommerce/widgets/cart.php:342
#: modules/woocommerce/widgets/cart.php:398
#: modules/woocommerce/widgets/product-meta.php:217
msgid "Text"
msgstr "Text"

#: modules/woocommerce/traits/products-trait.php:72
#: modules/woocommerce/widgets/product-related.php:65
#: modules/woocommerce/widgets/product-upsell.php:52
#: modules/woocommerce/widgets/products-deprecated.php:139
msgid "Popularity"
msgstr ""

#: modules/dynamic-tags/tags/contact-url.php:39 modules/forms/fields/tel.php:17
#: modules/forms/widgets/form.php:48
msgid "Tel"
msgstr ""

#: modules/carousel/widgets/reviews.php:380
#: modules/carousel/widgets/reviews.php:556
#: modules/woocommerce/traits/products-trait.php:73
#: modules/woocommerce/widgets/product-related.php:66
#: modules/woocommerce/widgets/product-upsell.php:53
#: modules/woocommerce/widgets/products-base.php:226
#: modules/woocommerce/widgets/products-deprecated.php:140
#: modules/woocommerce/widgets/single-elements.php:47
msgid "Rating"
msgstr "Rating"

#: modules/dynamic-tags/tags/author-info.php:55
#: modules/dynamic-tags/tags/contact-url.php:38
#: modules/dynamic-tags/tags/contact-url.php:56
#: modules/dynamic-tags/tags/user-info.php:84
#: modules/forms/actions/email.php:21 modules/forms/actions/mailpoet.php:119
#: modules/forms/actions/mailpoet3.php:124
#: modules/forms/classes/getresponse-handler.php:82
#: modules/forms/widgets/form.php:45 modules/forms/widgets/form.php:502
#: modules/forms/widgets/form.php:503
#: modules/woocommerce/widgets/checkout.php:3980
#: modules/woocommerce/widgets/purchase-summary.php:144
msgid "Email"
msgstr "Email"

#: modules/forms/widgets/form.php:46
msgid "Textarea"
msgstr "Textarea"

#: modules/dynamic-tags/acf/tags/acf-number.php:18
#: modules/dynamic-tags/tags/contact-url.php:90
#: modules/forms/fields/number.php:20 modules/forms/widgets/form.php:53
#: modules/woocommerce/widgets/purchase-summary.php:120
msgid "Number"
msgstr "Number"

#: modules/dynamic-tags/tags/contact-url.php:37
#: modules/forms/widgets/form.php:50
#: modules/library/wp-widgets/elementor-library.php:109
#: modules/theme-builder/documents/section.php:60
#: modules/theme-builder/module.php:229
#: modules/woocommerce/widgets/elements.php:66
#: modules/woocommerce/widgets/single-elements.php:44
msgid "Select"
msgstr "Select"

#: modules/dynamic-tags/tags/author-url.php:54
#: modules/forms/widgets/form.php:47 modules/gallery/widgets/gallery.php:269
#: modules/popup/display-settings/timing.php:120
#: modules/social/widgets/facebook-embed.php:61
#: modules/social/widgets/facebook-embed.php:77
#: modules/social/widgets/facebook-embed.php:93
#: modules/video-playlist/widgets/video-playlist.php:171
msgid "URL"
msgstr "URL"

#: modules/forms/widgets/form.php:51
#: modules/woocommerce/widgets/checkout.php:1983
#: modules/woocommerce/widgets/checkout.php:2210
#: modules/woocommerce/widgets/checkout.php:2797
msgid "Checkbox"
msgstr "Checkbox"

#: modules/forms/widgets/form.php:49
msgid "Radio"
msgstr "Radio"

#: modules/forms/widgets/form.php:108
#: modules/theme-elements/widgets/search-form.php:70
#: modules/woocommerce/widgets/checkout.php:196
#: modules/woocommerce/widgets/checkout.php:366
#: modules/woocommerce/widgets/checkout.php:3072
msgid "Placeholder"
msgstr "Placeholder"

#: modules/dynamic-tags/acf/module.php:85 modules/forms/widgets/form.php:163
msgid "Options"
msgstr "Options"

#: modules/forms/widgets/form.php:225
msgid "Inline List"
msgstr "Inline List"

#: modules/forms/widgets/form.php:266 modules/forms/widgets/form.php:600
msgid "Column Width"
msgstr "Column Width"

#: base/base-carousel-trait.php:25 base/base-carousel-trait.php:42
#: modules/call-to-action/widgets/call-to-action.php:202
#: modules/carousel/widgets/base.php:76 modules/carousel/widgets/base.php:91
#: modules/carousel/widgets/media-carousel.php:735
#: modules/carousel/widgets/testimonial-carousel.php:48
#: modules/dynamic-tags/pods/tags/pods-date.php:65
#: modules/dynamic-tags/tags/current-date-time.php:36
#: modules/dynamic-tags/tags/current-date-time.php:54
#: modules/dynamic-tags/tags/post-date.php:49
#: modules/dynamic-tags/tags/post-time.php:49
#: modules/dynamic-tags/toolset/tags/toolset-date.php:84
#: modules/flip-box/widgets/flip-box.php:120 modules/forms/widgets/form.php:269
#: modules/forms/widgets/form.php:603 modules/gallery/widgets/gallery.php:139
#: modules/gallery/widgets/gallery.php:294
#: modules/lottie/widgets/lottie.php:369
#: modules/mega-menu/widgets/mega-menu.php:583
#: modules/mega-menu/widgets/mega-menu.php:661
#: modules/motion-fx/controls-group.php:163
#: modules/nav-menu/widgets/nav-menu.php:416
#: modules/nav-menu/widgets/nav-menu.php:483
#: modules/posts/traits/button-widget-trait.php:59
#: modules/theme-builder/documents/theme-page-document.php:48
#: modules/theme-elements/widgets/breadcrumbs.php:73
#: modules/theme-elements/widgets/post-info.php:73
#: modules/theme-elements/widgets/post-info.php:333
msgid "Default"
msgstr "Default"

#: modules/forms/widgets/form.php:203 modules/forms/widgets/form.php:304
#: modules/woocommerce/widgets/products.php:73
msgid "Rows"
msgstr "Rows"

#: base/base-carousel-trait.php:607 base/base-carousel-trait.php:771
#: modules/blockquote/widgets/blockquote.php:346
#: modules/blockquote/widgets/blockquote.php:802
#: modules/call-to-action/widgets/call-to-action.php:1052
#: modules/carousel/widgets/base.php:411 modules/carousel/widgets/base.php:473
#: modules/carousel/widgets/media-carousel.php:446
#: modules/carousel/widgets/reviews.php:137
#: modules/carousel/widgets/reviews.php:272
#: modules/carousel/widgets/reviews.php:359
#: modules/carousel/widgets/reviews.php:423
#: modules/carousel/widgets/testimonial-carousel.php:363
#: modules/flip-box/widgets/flip-box.php:1302
#: modules/forms/widgets/form.php:320 modules/forms/widgets/form.php:590
#: modules/forms/widgets/login.php:94 modules/hotspot/widgets/hotspot.php:692
#: modules/mega-menu/widgets/mega-menu.php:1252
#: modules/mega-menu/widgets/mega-menu.php:1376
#: modules/nav-menu/widgets/nav-menu.php:1269
#: modules/page-transitions/module.php:521 modules/popup/document.php:671
#: modules/posts/skins/skin-cards.php:204
#: modules/posts/skins/skin-cards.php:276
#: modules/posts/traits/button-widget-trait.php:132
#: modules/pricing/widgets/price-table.php:547
#: modules/pricing/widgets/price-table.php:629
#: modules/pricing/widgets/price-table.php:1078
#: modules/progress-tracker/widgets/progress-tracker.php:180
#: modules/slides/widgets/slides.php:976 modules/slides/widgets/slides.php:1240
#: modules/social/widgets/facebook-button.php:73
#: modules/table-of-contents/widgets/table-of-contents.php:727
#: modules/theme-elements/widgets/post-info.php:222
#: modules/theme-elements/widgets/post-info.php:616
#: modules/theme-elements/widgets/post-navigation.php:396
#: modules/theme-elements/widgets/post-navigation.php:461
#: modules/theme-elements/widgets/search-form.php:151
#: modules/theme-elements/widgets/search-form.php:215
#: modules/video-playlist/widgets/video-playlist.php:914
#: modules/video-playlist/widgets/video-playlist.php:1116
#: modules/woocommerce/widgets/menu-cart.php:920
msgid "Size"
msgstr "Size"

#: base/base-carousel-trait.php:792 base/base-carousel-trait.php:1029
#: base/base-carousel-trait.php:1110
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:410
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:418
#: modules/blockquote/widgets/blockquote.php:414
#: modules/blockquote/widgets/blockquote.php:537
#: modules/blockquote/widgets/blockquote.php:674
#: modules/call-to-action/widgets/call-to-action.php:907
#: modules/call-to-action/widgets/call-to-action.php:1088
#: modules/call-to-action/widgets/call-to-action.php:1482
#: modules/call-to-action/widgets/call-to-action.php:1511
#: modules/flip-box/widgets/flip-box.php:234
#: modules/flip-box/widgets/flip-box.php:390
#: modules/flip-box/widgets/flip-box.php:1336
#: modules/forms/widgets/form.php:324 modules/forms/widgets/form.php:1355
#: modules/forms/widgets/login.php:565 modules/gallery/widgets/gallery.php:540
#: modules/gallery/widgets/gallery.php:698
#: modules/gallery/widgets/gallery.php:755
#: modules/gallery/widgets/gallery.php:1156
#: modules/loop-filter/widgets/taxonomy-filter.php:420
#: modules/lottie/widgets/lottie.php:600
#: modules/mega-menu/widgets/mega-menu.php:569
#: modules/mega-menu/widgets/mega-menu.php:834
#: modules/mega-menu/widgets/mega-menu.php:1308
#: modules/mega-menu/widgets/mega-menu.php:1413
#: modules/mega-menu/widgets/mega-menu.php:1757
#: modules/nav-menu/widgets/nav-menu.php:399
#: modules/nav-menu/widgets/nav-menu.php:582
#: modules/nav-menu/widgets/nav-menu.php:941
#: modules/nav-menu/widgets/nav-menu.php:1203 modules/popup/document.php:606
#: modules/posts/skins/skin-base.php:540
#: modules/posts/skins/skin-classic.php:107
#: modules/posts/traits/button-widget-trait.php:252
#: modules/posts/widgets/posts-base.php:509
#: modules/pricing/widgets/price-table.php:1099
#: modules/scroll-snap/module.php:120
#: modules/share-buttons/widgets/share-buttons.php:484
#: modules/slides/widgets/slides.php:203 modules/slides/widgets/slides.php:1037
#: modules/table-of-contents/widgets/table-of-contents.php:604
#: modules/theme-builder/skins/post-comments-skin-classic.php:263
#: modules/theme-elements/widgets/author-box.php:598
#: modules/theme-elements/widgets/breadcrumbs.php:135
#: modules/theme-elements/widgets/post-navigation.php:208
#: modules/theme-elements/widgets/post-navigation.php:282
#: modules/theme-elements/widgets/post-navigation.php:354
#: modules/theme-elements/widgets/search-form.php:290
#: modules/theme-elements/widgets/search-form.php:493
#: modules/theme-elements/widgets/search-form.php:616
#: modules/video-playlist/widgets/video-playlist.php:789
#: modules/video-playlist/widgets/video-playlist.php:1449
#: modules/woocommerce/settings/settings-woocommerce.php:225
#: modules/woocommerce/settings/settings-woocommerce.php:296
#: modules/woocommerce/settings/settings-woocommerce.php:564
#: modules/woocommerce/widgets/cart.php:788
#: modules/woocommerce/widgets/cart.php:894
#: modules/woocommerce/widgets/cart.php:1057
#: modules/woocommerce/widgets/cart.php:1374
#: modules/woocommerce/widgets/cart.php:1481
#: modules/woocommerce/widgets/cart.php:1628
#: modules/woocommerce/widgets/cart.php:2116
#: modules/woocommerce/widgets/checkout.php:923
#: modules/woocommerce/widgets/checkout.php:1068
#: modules/woocommerce/widgets/checkout.php:1235
#: modules/woocommerce/widgets/checkout.php:1610
#: modules/woocommerce/widgets/checkout.php:2017
#: modules/woocommerce/widgets/checkout.php:2858
#: modules/woocommerce/widgets/checkout.php:3612
#: modules/woocommerce/widgets/menu-cart.php:733
#: modules/woocommerce/widgets/menu-cart.php:1165
#: modules/woocommerce/widgets/menu-cart.php:1255
#: modules/woocommerce/widgets/menu-cart.php:1441
#: modules/woocommerce/widgets/menu-cart.php:1777
#: modules/woocommerce/widgets/menu-cart.php:1956
#: modules/woocommerce/widgets/my-account.php:341
#: modules/woocommerce/widgets/my-account.php:860
#: modules/woocommerce/widgets/my-account.php:1002
#: modules/woocommerce/widgets/my-account.php:1164
#: modules/woocommerce/widgets/my-account.php:1430
#: modules/woocommerce/widgets/my-account.php:1544
#: modules/woocommerce/widgets/product-add-to-cart.php:283
#: modules/woocommerce/widgets/product-add-to-cart.php:542
#: modules/woocommerce/widgets/product-data-tabs.php:53
#: modules/woocommerce/widgets/products-base.php:381
#: modules/woocommerce/widgets/products-base.php:653
#: modules/woocommerce/widgets/products-base.php:813
#: modules/woocommerce/widgets/purchase-summary.php:1202
#: modules/woocommerce/widgets/purchase-summary.php:1318
msgid "Normal"
msgstr "Normal"

#: modules/forms/widgets/form.php:325
msgid "Compact"
msgstr "Compact"

#: modules/animated-headline/widgets/animated-headline.php:46
#: modules/assets-manager/asset-types/fonts/custom-fonts.php:73
#: modules/forms/widgets/form.php:341
#: modules/mega-menu/widgets/mega-menu.php:926
#: modules/nav-menu/widgets/nav-menu.php:737
#: modules/page-transitions/module.php:495
#: modules/pricing/widgets/price-list.php:309
#: modules/pricing/widgets/price-table.php:931
#: modules/slides/widgets/slides.php:316
#: modules/theme-builder/documents/header-footer-base.php:36
#: modules/theme-builder/widgets/post-content.php:47
#: modules/theme-builder/widgets/post-excerpt.php:63
#: modules/theme-elements/widgets/post-info.php:468
#: modules/theme-elements/widgets/sitemap.php:487
#: modules/video-playlist/widgets/video-playlist.php:941
#: modules/video-playlist/widgets/video-playlist.php:1144
#: modules/woocommerce/widgets/archive-description.php:40
#: modules/woocommerce/widgets/breadcrumb.php:38
#: modules/woocommerce/widgets/menu-cart.php:1574
#: modules/woocommerce/widgets/product-images.php:36
#: modules/woocommerce/widgets/product-meta.php:35
#: modules/woocommerce/widgets/product-meta.php:104
#: modules/woocommerce/widgets/product-rating.php:34
#: modules/woocommerce/widgets/product-short-description.php:34
#: modules/woocommerce/widgets/product-stock.php:34
msgid "Style"
msgstr "Style"

#: modules/forms/widgets/form.php:345
#: modules/social/widgets/facebook-button.php:90
#: modules/woocommerce/widgets/menu-cart.php:52
#: modules/woocommerce/widgets/menu-cart.php:55
#: modules/woocommerce/widgets/menu-cart.php:58
msgid "Light"
msgstr "Light"

#: modules/forms/widgets/form.php:346
#: modules/social/widgets/facebook-button.php:91
msgid "Dark"
msgstr "Dark"

#: modules/forms/widgets/form.php:384 modules/popup/document.php:807
msgid "CSS Classes"
msgstr "CSS Classes"

#: modules/forms/widgets/form.php:387 modules/popup/document.php:809
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Add your custom class WITHOUT the dot. e.g: my-class"

#: modules/forms/actions/drip.php:119 modules/forms/widgets/form.php:468
#: modules/forms/widgets/login.php:39
msgid "Form Fields"
msgstr "Form Fields"

#: modules/forms/widgets/form.php:475 modules/forms/widgets/form.php:478
msgid "Form Name"
msgstr "Form Name"

#: modules/forms/widgets/form.php:477
msgid "New Form"
msgstr "New Form"

#: modules/assets-manager/asset-types/icons/templates.php:13
#: modules/carousel/widgets/reviews.php:173
#: modules/carousel/widgets/reviews.php:532
#: modules/carousel/widgets/testimonial-carousel.php:285
#: modules/carousel/widgets/testimonial-carousel.php:546
#: modules/forms/classes/getresponse-handler.php:88
#: modules/forms/widgets/form.php:491 modules/forms/widgets/form.php:492
#: modules/theme-elements/widgets/author-box.php:133
#: modules/theme-elements/widgets/author-box.php:482
#: modules/theme-elements/widgets/sitemap.php:264
#: modules/video-playlist/widgets/video-playlist.php:356
#: modules/video-playlist/widgets/video-playlist.php:370
#: modules/woocommerce/widgets/categories.php:139
msgid "Name"
msgstr "Name"

#: modules/countdown/widgets/countdown.php:279
#: modules/countdown/widgets/countdown.php:504
#: modules/dynamic-tags/tags/contact-url.php:79
#: modules/forms/actions/email.php:79 modules/forms/widgets/form.php:509
#: modules/forms/widgets/form.php:510
#: modules/woocommerce/widgets/checkout.php:496
#: modules/woocommerce/widgets/checkout.php:2554
#: modules/woocommerce/widgets/checkout.php:2770
#: modules/woocommerce/widgets/purchase-summary.php:67
msgid "Message"
msgstr "Message"

#: modules/forms/widgets/form.php:521 modules/forms/widgets/login.php:57
msgid "Input Size"
msgstr "Input Size"

#: modules/woocommerce/widgets/checkout.php:1008
#: modules/woocommerce/widgets/my-account.php:942
msgid "Labels"
msgstr "Labels"

#: modules/forms/widgets/form.php:565
msgid "Label Position"
msgstr "Label Position"

#: modules/call-to-action/widgets/call-to-action.php:76
#: modules/forms/widgets/form.php:568
#: modules/mega-menu/widgets/mega-menu.php:1219
#: modules/theme-elements/widgets/author-box.php:285
msgid "Above"
msgstr "Above"

#: modules/forms/widgets/form.php:692
msgid "Submit Button"
msgstr "Submit Button"

#: modules/forms/widgets/form.php:702 modules/forms/widgets/form.php:703
msgid "Send"
msgstr "Send"

#: modules/animated-headline/widgets/animated-headline.php:261
#: modules/blockquote/widgets/blockquote.php:71
#: modules/call-to-action/widgets/call-to-action.php:423
#: modules/carousel/widgets/testimonial-carousel.php:77
#: modules/countdown/widgets/countdown.php:515
#: modules/flip-box/widgets/flip-box.php:603
#: modules/flip-box/widgets/flip-box.php:1090
#: modules/forms/widgets/form.php:625 modules/forms/widgets/login.php:110
#: modules/gallery/widgets/gallery.php:855
#: modules/gallery/widgets/gallery.php:1125
#: modules/hotspot/widgets/hotspot.php:859
#: modules/loop-builder/widgets/base.php:185
#: modules/lottie/widgets/lottie.php:99 modules/lottie/widgets/lottie.php:705
#: modules/mega-menu/widgets/mega-menu.php:544
#: modules/nav-menu/widgets/nav-menu.php:127
#: modules/posts/skins/skin-base.php:461
#: modules/posts/traits/button-widget-trait.php:103
#: modules/posts/widgets/posts-base.php:240
#: modules/posts/widgets/posts-base.php:399
#: modules/pricing/widgets/price-table.php:879
#: modules/progress-tracker/widgets/progress-tracker.php:244
#: modules/share-buttons/widgets/share-buttons.php:234
#: modules/theme-builder/widgets/post-content.php:55
#: modules/theme-builder/widgets/post-excerpt.php:71
#: modules/theme-elements/widgets/author-box.php:301
#: modules/theme-elements/widgets/breadcrumbs.php:47
#: modules/theme-elements/widgets/post-info.php:431
#: modules/theme-elements/widgets/search-form.php:186
#: modules/woocommerce/widgets/archive-description.php:57
#: modules/woocommerce/widgets/breadcrumb.php:85
#: modules/woocommerce/widgets/cart.php:151
#: modules/woocommerce/widgets/cart.php:235
#: modules/woocommerce/widgets/cart.php:308
#: modules/woocommerce/widgets/cart.php:355
#: modules/woocommerce/widgets/cart.php:411
#: modules/woocommerce/widgets/categories.php:233
#: modules/woocommerce/widgets/checkout.php:151
#: modules/woocommerce/widgets/checkout.php:318
#: modules/woocommerce/widgets/checkout.php:450
#: modules/woocommerce/widgets/checkout.php:530
#: modules/woocommerce/widgets/checkout.php:2931
#: modules/woocommerce/widgets/checkout.php:2965
#: modules/woocommerce/widgets/checkout.php:3217
#: modules/woocommerce/widgets/checkout.php:3257
#: modules/woocommerce/widgets/menu-cart.php:149
#: modules/woocommerce/widgets/menu-cart.php:518
#: modules/woocommerce/widgets/menu-cart.php:575
#: modules/woocommerce/widgets/menu-cart.php:614
#: modules/woocommerce/widgets/menu-cart.php:1337
#: modules/woocommerce/widgets/menu-cart.php:2132
#: modules/woocommerce/widgets/my-account.php:189
#: modules/woocommerce/widgets/product-add-to-cart.php:214
#: modules/woocommerce/widgets/product-price.php:53
#: modules/woocommerce/widgets/product-rating.php:142
#: modules/woocommerce/widgets/product-short-description.php:51
#: modules/woocommerce/widgets/products-base.php:102
#: modules/woocommerce/widgets/products.php:211
#: modules/woocommerce/widgets/purchase-summary.php:83
#: modules/woocommerce/widgets/purchase-summary.php:201
#: modules/woocommerce/widgets/purchase-summary.php:247
#: modules/woocommerce/widgets/purchase-summary.php:293
#: modules/woocommerce/widgets/purchase-summary.php:339
#: modules/woocommerce/widgets/purchase-summary.php:385
msgid "Alignment"
msgstr "Alignment"

#: base/base-carousel-trait.php:179 base/base-carousel-trait.php:197
#: modules/animated-headline/widgets/animated-headline.php:265
#: modules/blockquote/widgets/blockquote.php:75
#: modules/call-to-action/widgets/call-to-action.php:72
#: modules/call-to-action/widgets/call-to-action.php:374
#: modules/call-to-action/widgets/call-to-action.php:427
#: modules/carousel/widgets/testimonial-carousel.php:82
#: modules/countdown/widgets/countdown.php:519
#: modules/flip-box/widgets/flip-box.php:534
#: modules/flip-box/widgets/flip-box.php:607
#: modules/flip-box/widgets/flip-box.php:1094
#: modules/forms/widgets/form.php:629 modules/forms/widgets/login.php:114
#: modules/gallery/widgets/gallery.php:859
#: modules/gallery/widgets/gallery.php:1129
#: modules/hotspot/widgets/hotspot.php:247
#: modules/hotspot/widgets/hotspot.php:343
#: modules/hotspot/widgets/hotspot.php:496
#: modules/hotspot/widgets/hotspot.php:863
#: modules/lottie/widgets/lottie.php:103 modules/lottie/widgets/lottie.php:709
#: modules/mega-menu/widgets/mega-menu.php:56
#: modules/motion-fx/controls-group.php:80
#: modules/nav-menu/widgets/nav-menu.php:131
#: modules/nav-menu/widgets/nav-menu.php:524 modules/popup/document.php:278
#: modules/posts/skins/skin-base.php:71 modules/posts/skins/skin-base.php:465
#: modules/posts/skins/skin-cards.php:132
#: modules/posts/traits/button-widget-trait.php:107
#: modules/posts/widgets/posts-base.php:244
#: modules/posts/widgets/posts-base.php:403
#: modules/pricing/widgets/price-table.php:361
#: modules/pricing/widgets/price-table.php:883
#: modules/progress-tracker/widgets/progress-tracker.php:112
#: modules/progress-tracker/widgets/progress-tracker.php:147
#: modules/progress-tracker/widgets/progress-tracker.php:248
#: modules/share-buttons/widgets/share-buttons.php:238
#: modules/slides/widgets/slides.php:336 modules/slides/widgets/slides.php:412
#: modules/slides/widgets/slides.php:703 modules/slides/widgets/slides.php:787
#: modules/slides/widgets/slides.php:834
#: modules/theme-builder/widgets/post-content.php:59
#: modules/theme-builder/widgets/post-excerpt.php:75
#: modules/theme-elements/widgets/author-box.php:281
#: modules/theme-elements/widgets/author-box.php:305
#: modules/theme-elements/widgets/breadcrumbs.php:51
#: modules/theme-elements/widgets/search-form.php:191
#: modules/woocommerce/widgets/archive-description.php:61
#: modules/woocommerce/widgets/breadcrumb.php:89
#: modules/woocommerce/widgets/categories.php:237
#: modules/woocommerce/widgets/menu-cart.php:153
#: modules/woocommerce/widgets/menu-cart.php:217
#: modules/woocommerce/widgets/menu-cart.php:245
#: modules/woocommerce/widgets/menu-cart.php:364
#: modules/woocommerce/widgets/menu-cart.php:522
#: modules/woocommerce/widgets/menu-cart.php:579
#: modules/woocommerce/widgets/menu-cart.php:1341
#: modules/woocommerce/widgets/menu-cart.php:2136
#: modules/woocommerce/widgets/product-add-to-cart.php:218
#: modules/woocommerce/widgets/product-price.php:57
#: modules/woocommerce/widgets/product-rating.php:146
#: modules/woocommerce/widgets/product-related.php:154
#: modules/woocommerce/widgets/product-short-description.php:55
#: modules/woocommerce/widgets/product-upsell.php:141
#: modules/woocommerce/widgets/products-base.php:106
#: modules/woocommerce/widgets/products-base.php:1020
msgid "Left"
msgstr "Left"

#: base/base-carousel-trait.php:380 base/base-carousel-trait.php:542
#: base/base-carousel-trait.php:1216 base/base-carousel-trait.php:1279
#: base/base-carousel-trait.php:1368
#: modules/animated-headline/widgets/animated-headline.php:269
#: modules/blockquote/widgets/blockquote.php:79
#: modules/call-to-action/widgets/call-to-action.php:431
#: modules/carousel/widgets/testimonial-carousel.php:86
#: modules/countdown/widgets/countdown.php:523
#: modules/flip-box/widgets/flip-box.php:611
#: modules/flip-box/widgets/flip-box.php:1098
#: modules/forms/widgets/form.php:633 modules/forms/widgets/login.php:118
#: modules/gallery/widgets/gallery.php:863
#: modules/gallery/widgets/gallery.php:1133
#: modules/hotspot/widgets/hotspot.php:604
#: modules/hotspot/widgets/hotspot.php:867
#: modules/loop-builder/widgets/base.php:193
#: modules/loop-filter/widgets/taxonomy-filter.php:116
#: modules/loop-filter/widgets/taxonomy-filter.php:156
#: modules/loop-filter/widgets/taxonomy-filter.php:191
#: modules/loop-filter/widgets/taxonomy-filter.php:230
#: modules/lottie/widgets/lottie.php:107 modules/lottie/widgets/lottie.php:713
#: modules/mega-menu/widgets/mega-menu.php:60
#: modules/mega-menu/widgets/mega-menu.php:325
#: modules/mega-menu/widgets/mega-menu.php:361
#: modules/mega-menu/widgets/mega-menu.php:552
#: modules/motion-fx/controls-group.php:84
#: modules/motion-fx/controls-group.php:110
#: modules/nav-menu/widgets/nav-menu.php:135
#: modules/nav-menu/widgets/nav-menu.php:368
#: modules/nav-menu/widgets/nav-menu.php:528 modules/popup/document.php:244
#: modules/popup/document.php:282 modules/popup/document.php:313
#: modules/posts/skins/skin-base.php:469
#: modules/posts/traits/button-widget-trait.php:111
#: modules/posts/widgets/posts-base.php:248
#: modules/posts/widgets/posts-base.php:407
#: modules/pricing/widgets/price-list.php:486
#: modules/pricing/widgets/price-table.php:887
#: modules/progress-tracker/widgets/progress-tracker.php:252
#: modules/scroll-snap/module.php:78
#: modules/share-buttons/widgets/share-buttons.php:242
#: modules/slides/widgets/slides.php:340 modules/slides/widgets/slides.php:416
#: modules/slides/widgets/slides.php:791 modules/slides/widgets/slides.php:838
#: modules/theme-builder/widgets/post-content.php:63
#: modules/theme-builder/widgets/post-excerpt.php:79
#: modules/theme-elements/widgets/author-box.php:309
#: modules/theme-elements/widgets/breadcrumbs.php:55
#: modules/theme-elements/widgets/post-info.php:439
#: modules/theme-elements/widgets/search-form.php:195
#: modules/woocommerce/widgets/archive-description.php:65
#: modules/woocommerce/widgets/breadcrumb.php:93
#: modules/woocommerce/widgets/cart.php:159
#: modules/woocommerce/widgets/cart.php:243
#: modules/woocommerce/widgets/cart.php:316
#: modules/woocommerce/widgets/cart.php:363
#: modules/woocommerce/widgets/cart.php:419
#: modules/woocommerce/widgets/categories.php:241
#: modules/woocommerce/widgets/checkout.php:159
#: modules/woocommerce/widgets/checkout.php:326
#: modules/woocommerce/widgets/checkout.php:458
#: modules/woocommerce/widgets/checkout.php:538
#: modules/woocommerce/widgets/checkout.php:2939
#: modules/woocommerce/widgets/checkout.php:2973
#: modules/woocommerce/widgets/checkout.php:3225
#: modules/woocommerce/widgets/checkout.php:3265
#: modules/woocommerce/widgets/menu-cart.php:157
#: modules/woocommerce/widgets/menu-cart.php:249
#: modules/woocommerce/widgets/menu-cart.php:526
#: modules/woocommerce/widgets/menu-cart.php:583
#: modules/woocommerce/widgets/menu-cart.php:1345
#: modules/woocommerce/widgets/menu-cart.php:2140
#: modules/woocommerce/widgets/my-account.php:86
#: modules/woocommerce/widgets/my-account.php:197
#: modules/woocommerce/widgets/product-add-to-cart.php:222
#: modules/woocommerce/widgets/product-price.php:61
#: modules/woocommerce/widgets/product-rating.php:150
#: modules/woocommerce/widgets/product-related.php:158
#: modules/woocommerce/widgets/product-short-description.php:59
#: modules/woocommerce/widgets/product-upsell.php:145
#: modules/woocommerce/widgets/products-base.php:110
#: modules/woocommerce/widgets/products.php:219
#: modules/woocommerce/widgets/purchase-summary.php:91
#: modules/woocommerce/widgets/purchase-summary.php:209
#: modules/woocommerce/widgets/purchase-summary.php:255
#: modules/woocommerce/widgets/purchase-summary.php:301
#: modules/woocommerce/widgets/purchase-summary.php:347
#: modules/woocommerce/widgets/purchase-summary.php:393
msgid "Center"
msgstr "Centre"

#: base/base-carousel-trait.php:180 base/base-carousel-trait.php:198
#: modules/animated-headline/widgets/animated-headline.php:273
#: modules/blockquote/widgets/blockquote.php:83
#: modules/call-to-action/widgets/call-to-action.php:80
#: modules/call-to-action/widgets/call-to-action.php:378
#: modules/call-to-action/widgets/call-to-action.php:435
#: modules/carousel/widgets/testimonial-carousel.php:90
#: modules/countdown/widgets/countdown.php:527
#: modules/flip-box/widgets/flip-box.php:538
#: modules/flip-box/widgets/flip-box.php:615
#: modules/flip-box/widgets/flip-box.php:1102
#: modules/forms/widgets/form.php:637 modules/forms/widgets/login.php:122
#: modules/gallery/widgets/gallery.php:867
#: modules/gallery/widgets/gallery.php:1137
#: modules/hotspot/widgets/hotspot.php:251
#: modules/hotspot/widgets/hotspot.php:351
#: modules/hotspot/widgets/hotspot.php:504
#: modules/hotspot/widgets/hotspot.php:871
#: modules/lottie/widgets/lottie.php:111 modules/lottie/widgets/lottie.php:717
#: modules/mega-menu/widgets/mega-menu.php:64
#: modules/motion-fx/controls-group.php:88
#: modules/nav-menu/widgets/nav-menu.php:139
#: modules/nav-menu/widgets/nav-menu.php:532 modules/popup/document.php:286
#: modules/posts/skins/skin-base.php:72 modules/posts/skins/skin-base.php:473
#: modules/posts/skins/skin-cards.php:136
#: modules/posts/traits/button-widget-trait.php:115
#: modules/posts/widgets/posts-base.php:252
#: modules/posts/widgets/posts-base.php:411
#: modules/pricing/widgets/price-table.php:365
#: modules/pricing/widgets/price-table.php:891
#: modules/progress-tracker/widgets/progress-tracker.php:116
#: modules/progress-tracker/widgets/progress-tracker.php:151
#: modules/progress-tracker/widgets/progress-tracker.php:256
#: modules/share-buttons/widgets/share-buttons.php:246
#: modules/slides/widgets/slides.php:344 modules/slides/widgets/slides.php:420
#: modules/slides/widgets/slides.php:702 modules/slides/widgets/slides.php:795
#: modules/slides/widgets/slides.php:842
#: modules/theme-builder/widgets/post-content.php:67
#: modules/theme-builder/widgets/post-excerpt.php:83
#: modules/theme-elements/widgets/author-box.php:289
#: modules/theme-elements/widgets/author-box.php:313
#: modules/theme-elements/widgets/breadcrumbs.php:59
#: modules/theme-elements/widgets/search-form.php:199
#: modules/woocommerce/widgets/archive-description.php:69
#: modules/woocommerce/widgets/breadcrumb.php:97
#: modules/woocommerce/widgets/categories.php:245
#: modules/woocommerce/widgets/menu-cart.php:161
#: modules/woocommerce/widgets/menu-cart.php:221
#: modules/woocommerce/widgets/menu-cart.php:253
#: modules/woocommerce/widgets/menu-cart.php:368
#: modules/woocommerce/widgets/menu-cart.php:530
#: modules/woocommerce/widgets/menu-cart.php:587
#: modules/woocommerce/widgets/menu-cart.php:1349
#: modules/woocommerce/widgets/menu-cart.php:2144
#: modules/woocommerce/widgets/product-add-to-cart.php:226
#: modules/woocommerce/widgets/product-price.php:65
#: modules/woocommerce/widgets/product-rating.php:154
#: modules/woocommerce/widgets/product-related.php:162
#: modules/woocommerce/widgets/product-short-description.php:63
#: modules/woocommerce/widgets/product-upsell.php:149
#: modules/woocommerce/widgets/products-base.php:114
#: modules/woocommerce/widgets/products-base.php:1024
msgid "Right"
msgstr "Right"

#: modules/forms/widgets/form.php:641 modules/forms/widgets/login.php:126
#: modules/gallery/widgets/gallery.php:165
#: modules/hotspot/widgets/hotspot.php:875
#: modules/loop-builder/widgets/base.php:201
#: modules/posts/traits/button-widget-trait.php:119
#: modules/posts/widgets/posts-base.php:415
#: modules/theme-builder/widgets/post-content.php:71
#: modules/theme-builder/widgets/post-excerpt.php:87
#: modules/woocommerce/widgets/archive-description.php:73
#: modules/woocommerce/widgets/menu-cart.php:2148
#: modules/woocommerce/widgets/product-add-to-cart.php:230
#: modules/woocommerce/widgets/product-rating.php:158
#: modules/woocommerce/widgets/product-short-description.php:67
msgid "Justified"
msgstr "Justified"

#: base/base-carousel-trait.php:271 base/base-carousel-trait.php:433
#: modules/call-to-action/widgets/call-to-action.php:142
#: modules/call-to-action/widgets/call-to-action.php:183
#: modules/carousel/widgets/media-carousel.php:489
#: modules/carousel/widgets/media-carousel.php:519
#: modules/carousel/widgets/reviews.php:323
#: modules/carousel/widgets/reviews.php:388
#: modules/carousel/widgets/reviews.php:570
#: modules/flip-box/widgets/flip-box.php:62
#: modules/flip-box/widgets/flip-box.php:101
#: modules/flip-box/widgets/flip-box.php:784 modules/forms/fields/step.php:93
#: modules/forms/widgets/form.php:713 modules/hotspot/widgets/hotspot.php:102
#: modules/mega-menu/widgets/mega-menu.php:205
#: modules/mega-menu/widgets/mega-menu.php:394
#: modules/mega-menu/widgets/mega-menu.php:575
#: modules/mega-menu/widgets/mega-menu.php:653
#: modules/mega-menu/widgets/mega-menu.php:1205
#: modules/nav-menu/widgets/nav-menu.php:408
#: modules/nav-menu/widgets/nav-menu.php:475
#: modules/page-transitions/module.php:307
#: modules/page-transitions/module.php:316
#: modules/posts/traits/button-widget-trait.php:144
#: modules/pricing/widgets/price-table.php:229
#: modules/table-of-contents/widgets/table-of-contents.php:183
#: modules/table-of-contents/widgets/table-of-contents.php:243
#: modules/theme-elements/widgets/post-info.php:329
#: modules/theme-elements/widgets/post-info.php:592
#: modules/theme-elements/widgets/search-form.php:99
#: modules/theme-elements/widgets/search-form.php:126
#: modules/video-playlist/widgets/video-playlist.php:874
#: modules/video-playlist/widgets/video-playlist.php:1082
#: modules/woocommerce/settings/settings-woocommerce.php:477
#: modules/woocommerce/widgets/menu-cart.php:49
#: modules/woocommerce/widgets/menu-cart.php:912
msgid "Icon"
msgstr "Icon"

#: modules/forms/widgets/form.php:723 modules/hotspot/widgets/hotspot.php:112
#: modules/posts/traits/button-widget-trait.php:157
#: modules/woocommerce/widgets/menu-cart.php:360
#: modules/woocommerce/widgets/menu-cart.php:410
msgid "Icon Position"
msgstr "Icon Position"

#: modules/forms/widgets/form.php:727
#: modules/mega-menu/widgets/mega-menu.php:1231
#: modules/posts/traits/button-widget-trait.php:161
#: modules/pricing/widgets/price-table.php:572
#: modules/query-control/controls/group-control-query.php:310
#: modules/theme-elements/widgets/post-info.php:197
msgid "Before"
msgstr "Before"

#: modules/forms/widgets/form.php:728
#: modules/mega-menu/widgets/mega-menu.php:1223
#: modules/posts/traits/button-widget-trait.php:162
#: modules/pricing/widgets/price-table.php:576
#: modules/query-control/controls/group-control-query.php:327
msgid "After"
msgstr "After"

#: modules/forms/widgets/form.php:739 modules/hotspot/widgets/hotspot.php:142
#: modules/posts/traits/button-widget-trait.php:171
msgid "Icon Spacing"
msgstr "Icon Spacing"

#: modules/forms/actions/email.php:47 modules/forms/actions/email.php:153
#: modules/forms/actions/email.php:170
msgid "Separate emails with commas"
msgstr ""

#: modules/forms/actions/email.php:103
msgid "From Email"
msgstr ""

#: modules/forms/actions/email.php:119
msgid "From Name"
msgstr ""

#: modules/forms/actions/email.php:135
msgid "Reply-To"
msgstr ""

#: modules/forms/actions/email.php:181 modules/posts/skins/skin-base.php:365
#: modules/theme-elements/widgets/post-info.php:61
msgid "Meta Data"
msgstr ""

#: modules/forms/actions/email.php:200
msgid "Credit"
msgstr ""

#: modules/forms/actions/redirect.php:37
msgid "Redirect To"
msgstr ""

#: modules/forms/actions/discord.php:36 modules/forms/actions/slack.php:37
#: modules/forms/actions/webhook.php:35
msgid "Webhook URL"
msgstr ""

#: modules/forms/actions/webhook.php:43
msgid "Enter the integration URL (like Zapier) that will receive the form's submitted data."
msgstr ""

#: modules/forms/widgets/form.php:943
#: modules/payments/classes/payment-button.php:360
#: modules/posts/widgets/posts-base.php:434
msgid "Custom Messages"
msgstr ""

#: modules/forms/widgets/form.php:956
msgid "Success Message"
msgstr ""

#: modules/payments/classes/payment-button.php:373
msgid "Error Message"
msgstr ""

#: base/base-carousel-trait.php:862 base/base-carousel-trait.php:965
#: modules/call-to-action/widgets/call-to-action.php:565
#: modules/call-to-action/widgets/call-to-action.php:642
#: modules/call-to-action/widgets/call-to-action.php:841
#: modules/call-to-action/widgets/call-to-action.php:882
#: modules/carousel/widgets/reviews.php:442
#: modules/flip-box/widgets/flip-box.php:681
#: modules/flip-box/widgets/flip-box.php:795
#: modules/flip-box/widgets/flip-box.php:968
#: modules/flip-box/widgets/flip-box.php:1169
#: modules/flip-box/widgets/flip-box.php:1239
#: modules/forms/widgets/form.php:1111 modules/forms/widgets/form.php:1186
#: modules/forms/widgets/form.php:1695 modules/forms/widgets/login.php:416
#: modules/gallery/widgets/gallery.php:239
#: modules/gallery/widgets/gallery.php:966
#: modules/lottie/widgets/lottie.php:757
#: modules/mega-menu/widgets/mega-menu.php:1281
#: modules/posts/skins/skin-base.php:514 modules/posts/skins/skin-base.php:637
#: modules/posts/skins/skin-base.php:711 modules/posts/skins/skin-base.php:771
#: modules/posts/skins/skin-base.php:835
#: modules/posts/widgets/portfolio.php:457
#: modules/posts/widgets/posts-base.php:138
#: modules/posts/widgets/posts-base.php:596
#: modules/pricing/widgets/price-list.php:369
#: modules/pricing/widgets/price-list.php:420
#: modules/slides/widgets/slides.php:874 modules/slides/widgets/slides.php:925
#: modules/theme-builder/skins/post-comments-skin-classic.php:170
#: modules/theme-elements/widgets/post-navigation.php:482
#: modules/woocommerce/widgets/cart.php:718
#: modules/woocommerce/widgets/cart.php:763
#: modules/woocommerce/widgets/cart.php:1295
#: modules/woocommerce/widgets/categories.php:288
#: modules/woocommerce/widgets/checkout.php:719
#: modules/woocommerce/widgets/checkout.php:765
#: modules/woocommerce/widgets/checkout.php:811
#: modules/woocommerce/widgets/checkout.php:1034
#: modules/woocommerce/widgets/menu-cart.php:938
#: modules/woocommerce/widgets/menu-cart.php:1622
#: modules/woocommerce/widgets/my-account.php:66
#: modules/woocommerce/widgets/my-account.php:533
#: modules/woocommerce/widgets/my-account.php:725
#: modules/woocommerce/widgets/my-account.php:968
#: modules/woocommerce/widgets/product-add-to-cart.php:419
#: modules/woocommerce/widgets/product-add-to-cart.php:467
#: modules/woocommerce/widgets/product-add-to-cart.php:672
#: modules/woocommerce/widgets/product-images.php:90
#: modules/woocommerce/widgets/product-images.php:131
#: modules/woocommerce/widgets/product-price.php:141
#: modules/woocommerce/widgets/product-related.php:178
#: modules/woocommerce/widgets/product-upsell.php:165
#: modules/woocommerce/widgets/products-base.php:157
#: modules/woocommerce/widgets/products-base.php:206
#: modules/woocommerce/widgets/products-base.php:279
#: modules/woocommerce/widgets/products-base.php:512
#: modules/woocommerce/widgets/products-base.php:570
#: modules/woocommerce/widgets/products-base.php:745
#: modules/woocommerce/widgets/products.php:305
#: modules/woocommerce/widgets/purchase-summary.php:538
#: modules/woocommerce/widgets/purchase-summary.php:636
#: modules/woocommerce/widgets/purchase-summary.php:748
#: modules/woocommerce/widgets/purchase-summary.php:917
#: modules/woocommerce/widgets/purchase-summary.php:973
msgid "Spacing"
msgstr "Spacing"

#: modules/animated-headline/widgets/animated-headline.php:385
#: modules/animated-headline/widgets/animated-headline.php:427
#: modules/animated-headline/widgets/animated-headline.php:489
#: modules/blockquote/widgets/blockquote.php:252
#: modules/blockquote/widgets/blockquote.php:295
#: modules/blockquote/widgets/blockquote.php:441
#: modules/blockquote/widgets/blockquote.php:484
#: modules/call-to-action/widgets/call-to-action.php:1095
#: modules/call-to-action/widgets/call-to-action.php:1137
#: modules/call-to-action/widgets/call-to-action.php:1262
#: modules/carousel/widgets/media-carousel.php:597
#: modules/carousel/widgets/testimonial-carousel.php:252
#: modules/carousel/widgets/testimonial-carousel.php:294
#: modules/carousel/widgets/testimonial-carousel.php:328
#: modules/countdown/widgets/countdown.php:540
#: modules/flip-box/widgets/flip-box.php:990
#: modules/flip-box/widgets/flip-box.php:1039
#: modules/flip-box/widgets/flip-box.php:1190
#: modules/flip-box/widgets/flip-box.php:1261
#: modules/flip-box/widgets/flip-box.php:1346
#: modules/flip-box/widgets/flip-box.php:1395
#: modules/forms/widgets/form.php:1137 modules/forms/widgets/form.php:1242
#: modules/forms/widgets/form.php:1385 modules/forms/widgets/form.php:1437
#: modules/forms/widgets/form.php:1494 modules/forms/widgets/form.php:1544
#: modules/forms/widgets/login.php:438 modules/forms/widgets/login.php:473
#: modules/forms/widgets/login.php:572 modules/forms/widgets/login.php:656
#: modules/forms/widgets/login.php:718 modules/gallery/widgets/gallery.php:1163
#: modules/gallery/widgets/gallery.php:1196
#: modules/gallery/widgets/gallery.php:1217
#: modules/gallery/widgets/gallery.php:1257
#: modules/hotspot/widgets/hotspot.php:837
#: modules/loop-filter/widgets/taxonomy-filter.php:425
#: modules/loop-filter/widgets/taxonomy-filter.php:473
#: modules/loop-filter/widgets/taxonomy-filter.php:521
#: modules/lottie/widgets/lottie.php:731
#: modules/mega-menu/widgets/mega-menu.php:1762
#: modules/mega-menu/widgets/mega-menu.php:1806
#: modules/nav-menu/widgets/nav-menu.php:589
#: modules/nav-menu/widgets/nav-menu.php:613
#: modules/nav-menu/widgets/nav-menu.php:633
#: modules/nav-menu/widgets/nav-menu.php:681
#: modules/nav-menu/widgets/nav-menu.php:948
#: modules/nav-menu/widgets/nav-menu.php:982
#: modules/nav-menu/widgets/nav-menu.php:1021
#: modules/posts/skins/skin-cards.php:170
#: modules/posts/traits/button-widget-trait.php:260
#: modules/posts/traits/button-widget-trait.php:304
#: modules/pricing/widgets/price-table.php:1109
#: modules/pricing/widgets/price-table.php:1196
#: modules/pricing/widgets/price-table.php:1358
#: modules/slides/widgets/slides.php:892 modules/slides/widgets/slides.php:943
#: modules/slides/widgets/slides.php:1044
#: modules/slides/widgets/slides.php:1090
#: modules/table-of-contents/widgets/table-of-contents.php:498
#: modules/table-of-contents/widgets/table-of-contents.php:611
#: modules/table-of-contents/widgets/table-of-contents.php:644
#: modules/table-of-contents/widgets/table-of-contents.php:678
#: modules/theme-builder/skins/post-comments-skin-classic.php:191
#: modules/theme-builder/skins/post-comments-skin-classic.php:226
#: modules/theme-builder/skins/post-comments-skin-classic.php:270
#: modules/theme-builder/skins/post-comments-skin-classic.php:350
#: modules/theme-builder/widgets/post-content.php:84
#: modules/theme-builder/widgets/post-excerpt.php:100
#: modules/theme-elements/widgets/author-box.php:605
#: modules/theme-elements/widgets/breadcrumbs.php:121
#: modules/theme-elements/widgets/post-info.php:666
#: modules/theme-elements/widgets/search-form.php:297
#: modules/theme-elements/widgets/search-form.php:367
#: modules/theme-elements/widgets/search-form.php:500
#: modules/theme-elements/widgets/search-form.php:534
#: modules/woocommerce/settings/settings-woocommerce.php:570
#: modules/woocommerce/settings/settings-woocommerce.php:601
#: modules/woocommerce/widgets/archive-description.php:86
#: modules/woocommerce/widgets/breadcrumb.php:55
#: modules/woocommerce/widgets/cart.php:1062
#: modules/woocommerce/widgets/cart.php:1093
#: modules/woocommerce/widgets/cart.php:1633
#: modules/woocommerce/widgets/cart.php:1664
#: modules/woocommerce/widgets/checkout.php:1241
#: modules/woocommerce/widgets/checkout.php:1274
#: modules/woocommerce/widgets/checkout.php:1616
#: modules/woocommerce/widgets/checkout.php:1649
#: modules/woocommerce/widgets/menu-cart.php:738
#: modules/woocommerce/widgets/menu-cart.php:797
#: modules/woocommerce/widgets/menu-cart.php:983
#: modules/woocommerce/widgets/menu-cart.php:1787
#: modules/woocommerce/widgets/menu-cart.php:1824
#: modules/woocommerce/widgets/menu-cart.php:1966
#: modules/woocommerce/widgets/menu-cart.php:2003
#: modules/woocommerce/widgets/my-account.php:1169
#: modules/woocommerce/widgets/my-account.php:1200
#: modules/woocommerce/widgets/my-account.php:1549
#: modules/woocommerce/widgets/my-account.php:1580
#: modules/woocommerce/widgets/product-add-to-cart.php:290
#: modules/woocommerce/widgets/product-add-to-cart.php:331
#: modules/woocommerce/widgets/product-add-to-cart.php:549
#: modules/woocommerce/widgets/product-add-to-cart.php:590
#: modules/woocommerce/widgets/product-data-tabs.php:60
#: modules/woocommerce/widgets/product-data-tabs.php:103
#: modules/woocommerce/widgets/product-data-tabs.php:181
#: modules/woocommerce/widgets/product-data-tabs.php:209
#: modules/woocommerce/widgets/product-short-description.php:80
#: modules/woocommerce/widgets/product-stock.php:51
#: modules/woocommerce/widgets/products-base.php:388
#: modules/woocommerce/widgets/products-base.php:442
#: modules/woocommerce/widgets/products-base.php:932
#: modules/woocommerce/widgets/purchase-summary.php:1323
#: modules/woocommerce/widgets/purchase-summary.php:1354
msgid "Text Color"
msgstr "Text Colour"

#: base/base-carousel-trait.php:1148
#: modules/call-to-action/widgets/call-to-action.php:1117
#: modules/call-to-action/widgets/call-to-action.php:1159
#: modules/carousel/widgets/base.php:368
#: modules/carousel/widgets/testimonial-carousel.php:173
#: modules/carousel/widgets/testimonial-carousel.php:440
#: modules/flip-box/widgets/flip-box.php:1372
#: modules/flip-box/widgets/flip-box.php:1421
#: modules/forms/widgets/form.php:1281 modules/forms/widgets/form.php:1399
#: modules/forms/widgets/form.php:1449 modules/forms/widgets/form.php:1508
#: modules/forms/widgets/form.php:1556 modules/forms/widgets/login.php:512
#: modules/forms/widgets/login.php:682 modules/gallery/widgets/gallery.php:547
#: modules/gallery/widgets/gallery.php:607
#: modules/mega-menu/widgets/mega-menu.php:884
#: modules/mega-menu/widgets/mega-menu.php:1054
#: modules/mega-menu/widgets/mega-menu.php:1155
#: modules/mega-menu/widgets/mega-menu.php:1450
#: modules/mega-menu/widgets/mega-menu.php:1507
#: modules/mega-menu/widgets/mega-menu.php:1586
#: modules/mega-menu/widgets/mega-menu.php:1682
#: modules/mega-menu/widgets/mega-menu.php:1870
#: modules/nested-carousel/widgets/nested-carousel.php:223
#: modules/posts/skins/skin-cards.php:368
#: modules/posts/skins/skin-classic.php:133
#: modules/posts/skins/skin-classic.php:171
#: modules/posts/traits/button-widget-trait.php:333
#: modules/pricing/widgets/price-table.php:1222
#: modules/slides/widgets/slides.php:1070
#: modules/slides/widgets/slides.php:1116
#: modules/table-of-contents/widgets/table-of-contents.php:383
#: modules/theme-builder/skins/post-comments-skin-classic.php:123
#: modules/theme-builder/skins/post-comments-skin-classic.php:372
#: modules/theme-elements/widgets/author-box.php:409
#: modules/theme-elements/widgets/search-form.php:330
#: modules/theme-elements/widgets/search-form.php:396
#: modules/video-playlist/widgets/video-playlist.php:1328
#: modules/woocommerce/settings/settings-woocommerce.php:626
#: modules/woocommerce/widgets/cart.php:961
#: modules/woocommerce/widgets/cart.php:1120
#: modules/woocommerce/widgets/cart.php:1691
#: modules/woocommerce/widgets/checkout.php:1138
#: modules/woocommerce/widgets/checkout.php:1301
#: modules/woocommerce/widgets/checkout.php:1676
#: modules/woocommerce/widgets/menu-cart.php:774
#: modules/woocommerce/widgets/menu-cart.php:833
#: modules/woocommerce/widgets/menu-cart.php:1849
#: modules/woocommerce/widgets/menu-cart.php:2028
#: modules/woocommerce/widgets/my-account.php:404
#: modules/woocommerce/widgets/my-account.php:449
#: modules/woocommerce/widgets/my-account.php:1069
#: modules/woocommerce/widgets/my-account.php:1227
#: modules/woocommerce/widgets/my-account.php:1607
#: modules/woocommerce/widgets/product-add-to-cart.php:312
#: modules/woocommerce/widgets/product-add-to-cart.php:353
#: modules/woocommerce/widgets/product-add-to-cart.php:571
#: modules/woocommerce/widgets/product-add-to-cart.php:612
#: modules/woocommerce/widgets/product-add-to-cart.php:755
#: modules/woocommerce/widgets/product-data-tabs.php:83
#: modules/woocommerce/widgets/product-data-tabs.php:127
#: modules/woocommerce/widgets/products-base.php:411
#: modules/woocommerce/widgets/products-base.php:464
#: modules/woocommerce/widgets/products-base.php:679
#: modules/woocommerce/widgets/products-base.php:717
#: modules/woocommerce/widgets/products-base.php:770
#: modules/woocommerce/widgets/purchase-summary.php:1381
msgid "Border Color"
msgstr "Border Colour"

#: modules/blockquote/widgets/blockquote.php:338
#: modules/call-to-action/widgets/call-to-action.php:1041
#: modules/flip-box/widgets/flip-box.php:1291
#: modules/forms/widgets/login.php:75 modules/forms/widgets/login.php:555
#: modules/posts/widgets/posts-base.php:51
#: modules/posts/widgets/posts-base.php:354
#: modules/pricing/widgets/price-table.php:1066
#: modules/slides/widgets/slides.php:968
#: modules/social/widgets/facebook-button.php:36
#: modules/social/widgets/facebook-button.php:63
#: modules/theme-elements/widgets/search-form.php:83
#: modules/theme-elements/widgets/search-form.php:466
#: modules/woocommerce/module.php:755 modules/woocommerce/module.php:765
#: modules/woocommerce/settings/settings-woocommerce.php:541
#: modules/woocommerce/widgets/checkout.php:1212
#: modules/woocommerce/widgets/product-add-to-cart.php:197
#: modules/woocommerce/widgets/products-base.php:370
msgid "Button"
msgstr "Button"

#: modules/woocommerce/widgets/cart.php:675
#: modules/woocommerce/widgets/checkout.php:676
#: modules/woocommerce/widgets/my-account.php:681
#: modules/woocommerce/widgets/purchase-summary.php:558
msgid "Typography"
msgstr "Typography"

#: modules/forms/widgets/form.php:1596 modules/forms/widgets/login.php:635
#: modules/pricing/widgets/price-table.php:1172
#: modules/share-buttons/widgets/share-buttons.php:563
#: modules/theme-builder/skins/post-comments-skin-classic.php:329
#: modules/woocommerce/widgets/products-base.php:500
msgid "Text Padding"
msgstr ""

#: base/base-carousel-trait.php:815 base/base-carousel-trait.php:1052
#: base/base-carousel-trait.php:1113
#: modules/blockquote/widgets/blockquote.php:455
#: modules/blockquote/widgets/blockquote.php:583
#: modules/blockquote/widgets/blockquote.php:725
#: modules/call-to-action/widgets/call-to-action.php:972
#: modules/call-to-action/widgets/call-to-action.php:1130
#: modules/call-to-action/widgets/call-to-action.php:1536
#: modules/flip-box/widgets/flip-box.php:1385
#: modules/forms/widgets/form.php:1466 modules/forms/widgets/login.php:649
#: modules/gallery/widgets/gallery.php:600
#: modules/gallery/widgets/gallery.php:722
#: modules/gallery/widgets/gallery.php:1189
#: modules/hotspot/widgets/hotspot.php:525
#: modules/loop-filter/widgets/taxonomy-filter.php:468
#: modules/lottie/widgets/lottie.php:635
#: modules/mega-menu/widgets/mega-menu.php:457
#: modules/mega-menu/widgets/mega-menu.php:608
#: modules/mega-menu/widgets/mega-menu.php:1004
#: modules/mega-menu/widgets/mega-menu.php:1325
#: modules/mega-menu/widgets/mega-menu.php:1470
#: modules/nav-menu/widgets/nav-menu.php:444
#: modules/nav-menu/widgets/nav-menu.php:606
#: modules/nav-menu/widgets/nav-menu.php:975
#: modules/nav-menu/widgets/nav-menu.php:1235 modules/popup/document.php:638
#: modules/posts/skins/skin-base.php:556
#: modules/posts/skins/skin-classic.php:145
#: modules/posts/traits/button-widget-trait.php:296
#: modules/posts/widgets/posts-base.php:529
#: modules/pricing/widgets/price-table.php:1186
#: modules/share-buttons/widgets/share-buttons.php:520
#: modules/slides/widgets/slides.php:1083
#: modules/table-of-contents/widgets/table-of-contents.php:637
#: modules/theme-builder/skins/post-comments-skin-classic.php:343
#: modules/theme-elements/widgets/author-box.php:644
#: modules/theme-elements/widgets/breadcrumbs.php:156
#: modules/theme-elements/widgets/post-navigation.php:232
#: modules/theme-elements/widgets/post-navigation.php:305
#: modules/theme-elements/widgets/post-navigation.php:374
#: modules/theme-elements/widgets/search-form.php:527
#: modules/theme-elements/widgets/search-form.php:647
#: modules/video-playlist/widgets/video-playlist.php:1472
#: modules/woocommerce/settings/settings-woocommerce.php:242
#: modules/woocommerce/settings/settings-woocommerce.php:313
#: modules/woocommerce/settings/settings-woocommerce.php:595
#: modules/woocommerce/widgets/cart.php:803
#: modules/woocommerce/widgets/cart.php:1088
#: modules/woocommerce/widgets/cart.php:1389
#: modules/woocommerce/widgets/cart.php:1496
#: modules/woocommerce/widgets/cart.php:1659
#: modules/woocommerce/widgets/cart.php:2131
#: modules/woocommerce/widgets/checkout.php:940
#: modules/woocommerce/widgets/checkout.php:1268
#: modules/woocommerce/widgets/checkout.php:1643
#: modules/woocommerce/widgets/checkout.php:2034
#: modules/woocommerce/widgets/checkout.php:2875
#: modules/woocommerce/widgets/checkout.php:3629
#: modules/woocommerce/widgets/menu-cart.php:792
#: modules/woocommerce/widgets/menu-cart.php:1191
#: modules/woocommerce/widgets/menu-cart.php:1281
#: modules/woocommerce/widgets/menu-cart.php:1457
#: modules/woocommerce/widgets/menu-cart.php:1814
#: modules/woocommerce/widgets/menu-cart.php:1993
#: modules/woocommerce/widgets/my-account.php:372
#: modules/woocommerce/widgets/my-account.php:875
#: modules/woocommerce/widgets/my-account.php:1195
#: modules/woocommerce/widgets/my-account.php:1445
#: modules/woocommerce/widgets/my-account.php:1575
#: modules/woocommerce/widgets/product-add-to-cart.php:324
#: modules/woocommerce/widgets/products-base.php:435
#: modules/woocommerce/widgets/products-base.php:691
#: modules/woocommerce/widgets/products-base.php:843
#: modules/woocommerce/widgets/purchase-summary.php:1217
#: modules/woocommerce/widgets/purchase-summary.php:1349
msgid "Hover"
msgstr "Hover"

#: modules/animated-headline/widgets/animated-headline.php:62
#: modules/call-to-action/widgets/call-to-action.php:1393
#: modules/carousel/widgets/media-carousel.php:546
#: modules/forms/widgets/form.php:1571 modules/forms/widgets/login.php:696
#: modules/gallery/widgets/gallery.php:393
#: modules/gallery/widgets/gallery.php:413
#: modules/gallery/widgets/gallery.php:433
#: modules/gallery/widgets/gallery.php:459
#: modules/hotspot/widgets/hotspot.php:430
#: modules/hotspot/widgets/hotspot.php:537
#: modules/lottie/widgets/lottie.php:388
#: modules/mega-menu/widgets/mega-menu.php:483
#: modules/nav-menu/widgets/nav-menu.php:179
#: modules/nav-menu/widgets/nav-menu.php:200
#: modules/nav-menu/widgets/nav-menu.php:221
#: modules/nav-menu/widgets/nav-menu.php:248
#: modules/page-transitions/module.php:306
#: modules/page-transitions/module.php:346
#: modules/page-transitions/module.php:373
#: modules/pricing/widgets/price-table.php:1233
#: modules/theme-builder/skins/post-comments-skin-classic.php:386
#: modules/theme-elements/widgets/author-box.php:676
msgid "Animation"
msgstr ""

#: modules/global-widget/documents/widget.php:34
msgid "Global Widget"
msgstr ""

#: assets/js/5c03292ae33aceaec8d9.bundle.js:86 assets/js/editor.js:6720
#: assets/js/notes/notes-app.js:143
msgid "Save"
msgstr ""

#: modules/global-widget/views/panel-template.php:20 assets/js/editor.js:3481
msgid "Unlink"
msgstr ""

#: assets/js/app.js:3776 assets/js/app.js:3955 assets/js/app.js:4017
#: assets/js/editor.js:3482 assets/js/form-submission-admin.js:5185
#: assets/js/notes/notes-app.js:154 assets/js/notes/notes-app.js:321
#: assets/js/notes/notes-app.js:434 assets/js/notes/notes-app.js:1076
#: core/app/modules/site-editor/assets/js/pages/import.js:79
#: core/app/modules/site-editor/assets/js/part-actions/dialog-delete.js:28
#: core/app/modules/site-editor/assets/js/part-actions/dialog-rename.js:37
msgid "Cancel"
msgstr "Cancel"

#: assets/js/editor.js:2616 assets/js/editor.js:3474
msgid "Unlink Widget"
msgstr ""

#: assets/js/editor.js:3475
msgid "This will make the widget stop being global. It'll be reverted into being just a regular widget."
msgstr ""

#: assets/js/editor.js:3295
msgid "Save your widget as a global widget"
msgstr ""

#: assets/js/editor.js:3296
msgid "You'll be able to add this global widget to multiple areas on your site, and edit it from one single place."
msgstr ""

#: modules/global-widget/views/panel-template.php:11
msgid "Edit this global widget to simultaneously update every place you used it, or unlink it so it gets back to being regular widget."
msgstr ""

#: modules/global-widget/views/panel-template.php:15
msgid "Edit global widget"
msgstr ""

#: modules/assets-manager/asset-types/fonts/custom-fonts.php:140
#: modules/assets-manager/classes/assets-base.php:336
#: modules/global-widget/views/panel-template.php:16
#: modules/theme-builder/skins/post-comments-skin-classic.php:457
#: assets/js/app.js:2917 assets/js/app.js:3757
#: assets/js/form-submission-admin.js:5185 assets/js/notes/notes-app.js:722
#: core/app/modules/site-editor/assets/js/molecules/site-template-header.js:10
#: core/app/modules/site-editor/assets/js/pages/import.js:52
msgid "Edit"
msgstr "Edit"

#: modules/global-widget/views/panel-template.php:19
msgid "Unlink from global"
msgstr ""

#: modules/global-widget/views/panel-template.php:31
msgid "Save Your First Global Widget"
msgstr ""

#: modules/global-widget/views/panel-template.php:32
msgid "Save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr ""

#: modules/dynamic-tags/tags/shortcode.php:18
#: modules/dynamic-tags/tags/shortcode.php:39
#: modules/forms/widgets/form.php:454 modules/library/classes/shortcode.php:20
msgid "Shortcode"
msgstr "Shortcode"

#: modules/library/wp-widgets/elementor-library.php:137
#: modules/theme-builder/documents/single-base.php:177
#: modules/woocommerce/widgets/cart.php:554
#: modules/woocommerce/widgets/my-account.php:312 assets/js/editor.js:3685
msgid "Edit Template"
msgstr "Edit Template"

#: modules/library/module.php:153
msgid "You Haven’t Saved Templates Yet."
msgstr ""

#: modules/library/widgets/template.php:20
#: modules/library/widgets/template.php:54 assets/js/editor.js:202
#: assets/js/loop.cfa59b67362d5bf08739.bundle.js:41
#: assets/js/loop.cfa59b67362d5bf08739.bundle.js:267
#: assets/js/preloaded-elements-handlers.js:476
#: assets/js/preloaded-elements-handlers.js:3298 assets/js/preview.js:43
msgid "Template"
msgstr "Template"

#: modules/library/widgets/template.php:65
#: modules/library/wp-widgets/elementor-library.php:107
msgid "Choose Template"
msgstr "Choose Template"

#: modules/library/wp-widgets/elementor-library.php:19
msgid "Elementor Library"
msgstr ""

#: modules/library/wp-widgets/elementor-library.php:21
msgid "Embed your saved elements."
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:233
#: modules/call-to-action/widgets/call-to-action.php:359
#: modules/call-to-action/widgets/call-to-action.php:809
#: modules/carousel/widgets/media-carousel.php:505
#: modules/carousel/widgets/reviews.php:203
#: modules/carousel/widgets/reviews.php:544
#: modules/carousel/widgets/testimonial-carousel.php:319
#: modules/carousel/widgets/testimonial-carousel.php:558
#: modules/dynamic-tags/tags/contact-url.php:164
#: modules/dynamic-tags/tags/featured-image-data.php:87
#: modules/flip-box/widgets/flip-box.php:151
#: modules/flip-box/widgets/flip-box.php:277
#: modules/flip-box/widgets/flip-box.php:957
#: modules/flip-box/widgets/flip-box.php:1158
#: modules/forms/actions/discord.php:83 modules/forms/actions/slack.php:99
#: modules/gallery/widgets/gallery.php:97
#: modules/gallery/widgets/gallery.php:494
#: modules/gallery/widgets/gallery.php:499
#: modules/gallery/widgets/gallery.php:516
#: modules/gallery/widgets/gallery.php:926
#: modules/library/wp-widgets/elementor-library.php:102
#: modules/lottie/widgets/lottie.php:128
#: modules/mega-menu/widgets/mega-menu.php:166
#: modules/nested-carousel/widgets/nested-carousel.php:93
#: modules/posts/skins/skin-base.php:216 modules/posts/skins/skin-base.php:587
#: modules/pricing/widgets/price-list.php:61
#: modules/pricing/widgets/price-list.php:191
#: modules/pricing/widgets/price-table.php:49
#: modules/pricing/widgets/price-table.php:342
#: modules/pricing/widgets/price-table.php:415
#: modules/query-control/controls/group-control-query.php:349
#: modules/slides/widgets/slides.php:243 modules/slides/widgets/slides.php:866
#: modules/table-of-contents/widgets/table-of-contents.php:72
#: modules/theme-builder/skins/post-comments-skin-classic.php:35
#: modules/theme-builder/skins/post-comments-skin-classic.php:42
#: modules/theme-elements/widgets/post-navigation.php:269
#: modules/theme-elements/widgets/sitemap.php:230
#: modules/theme-elements/widgets/sitemap.php:246
#: modules/theme-elements/widgets/sitemap.php:393
#: modules/video-playlist/widgets/video-playlist.php:196
#: modules/video-playlist/widgets/video-playlist.php:201
#: modules/video-playlist/widgets/video-playlist.php:1352
#: modules/woocommerce/traits/products-trait.php:70
#: modules/woocommerce/widgets/cart.php:2047
#: modules/woocommerce/widgets/categories.php:300
#: modules/woocommerce/widgets/checkout.php:2175
#: modules/woocommerce/widgets/checkout.php:2357
#: modules/woocommerce/widgets/checkout.php:2519
#: modules/woocommerce/widgets/product-related.php:63
#: modules/woocommerce/widgets/product-upsell.php:50
#: modules/woocommerce/widgets/products-base.php:169
#: modules/woocommerce/widgets/products-deprecated.php:137
#: modules/woocommerce/widgets/products.php:145
#: modules/woocommerce/widgets/products.php:172
#: modules/woocommerce/widgets/products.php:249
#: modules/woocommerce/widgets/purchase-summary.php:189
#: modules/woocommerce/widgets/purchase-summary.php:235
#: modules/woocommerce/widgets/purchase-summary.php:281
#: modules/woocommerce/widgets/purchase-summary.php:327
#: modules/woocommerce/widgets/purchase-summary.php:373
#: modules/woocommerce/widgets/single-elements.php:46
msgid "Title"
msgstr "Title"

#: modules/query-control/controls/group-control-posts.php:112
#: modules/query-control/controls/group-control-query.php:50
#: modules/query-control/controls/group-control-query.php:174
#: modules/query-control/controls/group-control-related.php:60
#: modules/query-control/module.php:83
#: modules/theme-elements/widgets/sitemap.php:83
#: modules/woocommerce/traits/products-trait.php:81
#: modules/woocommerce/widgets/categories.php:77
#: modules/woocommerce/widgets/products-deprecated.php:168
msgid "Manual Selection"
msgstr ""

#: modules/loop-builder/documents/loop.php:338
#: modules/loop-builder/module.php:162
#: modules/loop-builder/skins/skin-loop-post.php:17
#: modules/posts/widgets/posts.php:23
msgid "Posts"
msgstr ""

#: modules/posts/skins/skin-base.php:66
msgid "Image Position"
msgstr "Image Position"

#: modules/call-to-action/widgets/call-to-action.php:453
#: modules/flip-box/widgets/flip-box.php:542
#: modules/flip-box/widgets/flip-box.php:633
#: modules/flip-box/widgets/flip-box.php:1121
#: modules/gallery/widgets/gallery.php:885
#: modules/hotspot/widgets/hotspot.php:283
#: modules/hotspot/widgets/hotspot.php:347
#: modules/hotspot/widgets/hotspot.php:500
#: modules/lottie/widgets/lottie.php:256
#: modules/motion-fx/controls-group.php:106
#: modules/motion-fx/controls-group.php:233
#: modules/motion-fx/controls-group.php:276
#: modules/motion-fx/controls-group.php:323
#: modules/motion-fx/controls-group.php:369
#: modules/motion-fx/controls-group.php:412
#: modules/motion-fx/controls-group.php:458 modules/popup/document.php:243
#: modules/popup/document.php:309 modules/posts/skins/skin-base.php:70
#: modules/pricing/widgets/price-list.php:482
#: modules/pricing/widgets/price-table.php:590
#: modules/pricing/widgets/price-table.php:650
#: modules/pricing/widgets/price-table.php:727
#: modules/scroll-snap/module.php:77 modules/slides/widgets/slides.php:374
#: modules/slides/widgets/slides.php:811 modules/sticky/module.php:53
#: modules/theme-elements/widgets/author-box.php:338
#: modules/woocommerce/widgets/menu-cart.php:414
#: modules/woocommerce/widgets/menu-cart.php:450
#: modules/woocommerce/widgets/menu-cart.php:634
msgid "Top"
msgstr "Top"

#: base/base-carousel-trait.php:195 base/base-carousel-trait.php:731
#: base/base-carousel-trait.php:848 base/base-carousel-trait.php:951
#: modules/blockquote/widgets/blockquote.php:208
#: modules/call-to-action/widgets/call-to-action.php:134
#: modules/call-to-action/widgets/call-to-action.php:1337
#: modules/call-to-action/widgets/call-to-action.php:1339
#: modules/carousel/widgets/base.php:175
#: modules/carousel/widgets/media-carousel.php:174
#: modules/carousel/widgets/media-carousel.php:487
#: modules/dynamic-tags/tags/comments-number.php:65
#: modules/dynamic-tags/tags/current-date-time.php:37
#: modules/dynamic-tags/tags/current-date-time.php:55
#: modules/flip-box/widgets/flip-box.php:54
#: modules/gallery/widgets/gallery.php:258
#: modules/gallery/widgets/gallery.php:378
#: modules/gallery/widgets/gallery.php:498
#: modules/gallery/widgets/gallery.php:515
#: modules/gallery/widgets/gallery.php:784
#: modules/gallery/widgets/gallery.php:786
#: modules/gallery/widgets/gallery.php:1025
#: modules/gallery/widgets/gallery.php:1027
#: modules/hotspot/widgets/hotspot.php:436
#: modules/hotspot/widgets/hotspot.php:527
#: modules/lottie/widgets/lottie.php:127 modules/lottie/widgets/lottie.php:174
#: modules/lottie/widgets/lottie.php:220
#: modules/mega-menu/widgets/mega-menu.php:487
#: modules/mega-menu/widgets/mega-menu.php:723
#: modules/nav-menu/widgets/nav-menu.php:161
#: modules/nav-menu/widgets/nav-menu.php:329
#: modules/nav-menu/widgets/nav-menu.php:384
#: modules/page-transitions/module.php:210
#: modules/page-transitions/module.php:237
#: modules/page-transitions/module.php:305
#: modules/page-transitions/module.php:377
#: modules/payments/classes/payment-button.php:279
#: modules/payments/module.php:225 modules/payments/module.php:228
#: modules/payments/widgets/stripe-button.php:402
#: modules/payments/widgets/stripe-button.php:415
#: modules/posts/skins/skin-base.php:73 modules/posts/skins/skin-cards.php:469
#: modules/posts/widgets/posts-base.php:849
#: modules/pricing/widgets/price-list.php:316
#: modules/pricing/widgets/price-table.php:101
#: modules/progress-tracker/widgets/progress-tracker.php:291
#: modules/progress-tracker/widgets/progress-tracker.php:447
#: modules/query-control/controls/group-control-related.php:59
#: modules/slides/widgets/slides.php:596 modules/slides/widgets/slides.php:699
#: modules/sticky/module.php:52
#: modules/theme-builder/classes/conditions-manager.php:79
#: modules/theme-builder/widgets/site-logo.php:87
#: modules/theme-elements/widgets/author-box.php:172
#: modules/theme-elements/widgets/post-info.php:332
#: modules/theme-elements/widgets/sitemap.php:504
#: modules/theme-elements/widgets/sitemap.php:630
#: modules/video-playlist/widgets/video-playlist.php:945
#: modules/video-playlist/widgets/video-playlist.php:1148
#: modules/video-playlist/widgets/video-playlist.php:1259
#: modules/woocommerce/settings/settings-woocommerce.php:660
#: modules/woocommerce/widgets/base-widget.php:106
#: modules/woocommerce/widgets/menu-cart.php:109
#: modules/woocommerce/widgets/menu-cart.php:1058
#: modules/woocommerce/widgets/menu-cart.php:1365
#: modules/woocommerce/widgets/menu-cart.php:1577
#: modules/woocommerce/widgets/products-deprecated.php:122
msgid "None"
msgstr "None"

#: modules/posts/skins/skin-base.php:111
#: modules/posts/skins/skin-content-base.php:69
msgid "Image Ratio"
msgstr ""

#: modules/posts/skins/skin-base.php:143
#: modules/posts/skins/skin-content-base.php:100
msgid "Image Width"
msgstr ""

#: modules/gallery/widgets/gallery.php:176
#: modules/loop-builder/widgets/loop-grid.php:44
#: modules/posts/skins/skin-base.php:182 modules/posts/widgets/portfolio.php:78
#: modules/share-buttons/widgets/share-buttons.php:215
#: modules/theme-elements/widgets/sitemap.php:123
#: modules/woocommerce/skins/skin-classic.php:38
#: modules/woocommerce/widgets/base-widget.php:41
msgid "Columns"
msgstr "Columns"

#: modules/call-to-action/widgets/call-to-action.php:248
#: modules/flip-box/widgets/flip-box.php:429
#: modules/posts/skins/skin-base.php:228
#: modules/posts/widgets/portfolio.php:173
#: modules/pricing/widgets/price-list.php:140
#: modules/pricing/widgets/price-table.php:73
#: modules/slides/widgets/slides.php:539
#: modules/theme-elements/widgets/sitemap.php:145
msgid "Title HTML Tag"
msgstr "Title HTML Tag"

#: modules/posts/skins/skin-base.php:254 modules/posts/skins/skin-base.php:731
#: modules/woocommerce/widgets/single-elements.php:49
msgid "Excerpt"
msgstr "Excerpt"

#: modules/dynamic-tags/tags/post-excerpt.php:31
#: modules/posts/skins/skin-base.php:265
msgid "Excerpt Length"
msgstr ""

#: modules/carousel/widgets/reviews.php:790
#: modules/posts/skins/skin-base.php:294 modules/posts/skins/skin-base.php:791
msgid "Read More"
msgstr ""

#: modules/posts/skins/skin-base.php:306
msgid "Read More Text"
msgstr ""

#: modules/posts/skins/skin-base.php:311
msgid "Read More »"
msgstr ""

#: modules/blockquote/widgets/blockquote.php:112
#: modules/blockquote/widgets/blockquote.php:287
#: modules/dynamic-tags/module.php:145
#: modules/dynamic-tags/tags/internal-url.php:80
#: modules/posts/skins/skin-base.php:371
#: modules/query-control/controls/group-control-posts.php:81
#: modules/query-control/controls/group-control-query.php:98
#: modules/query-control/controls/group-control-query.php:135
#: modules/query-control/controls/group-control-query.php:176
#: modules/query-control/controls/group-control-query.php:234
#: modules/theme-elements/widgets/post-info.php:95
msgid "Author"
msgstr ""

#: modules/dynamic-tags/module.php:148 modules/posts/skins/skin-base.php:374
#: modules/theme-builder/skins/post-comments-skin-classic.php:81
#: modules/theme-elements/widgets/post-comments.php:39
#: modules/theme-elements/widgets/post-info.php:98
#: modules/theme-elements/widgets/post-info.php:275
msgid "Comments"
msgstr "Comments"

#: modules/posts/skins/skin-base.php:384
msgid "Separator Between"
msgstr ""

#: modules/carousel/widgets/testimonial-carousel.php:59
#: modules/gallery/widgets/gallery.php:161
#: modules/loop-builder/documents/loop.php:188
#: modules/loop-builder/widgets/base.php:112
#: modules/loop-builder/widgets/loop-carousel.php:73
#: modules/loop-builder/widgets/loop-grid.php:324
#: modules/loop-filter/widgets/taxonomy-filter.php:49
#: modules/mega-menu/widgets/mega-menu.php:157
#: modules/nav-menu/widgets/nav-menu.php:67
#: modules/nav-menu/widgets/nav-menu.php:112 modules/popup/document.php:158
#: modules/posts/skins/skin-base.php:410 modules/posts/widgets/portfolio.php:70
#: modules/posts/widgets/posts-base.php:839
#: modules/social/widgets/facebook-button.php:58
#: modules/social/widgets/facebook-page.php:54
#: modules/theme-elements/widgets/author-box.php:277
#: modules/theme-elements/widgets/post-info.php:68
#: modules/video-playlist/widgets/video-playlist.php:638
#: modules/woocommerce/widgets/add-to-cart.php:104
#: modules/woocommerce/widgets/add-to-cart.php:112
#: modules/woocommerce/widgets/cart.php:64
#: modules/woocommerce/widgets/categories.php:44
#: modules/woocommerce/widgets/checkout.php:70
#: modules/woocommerce/widgets/menu-cart.php:1665
#: modules/woocommerce/widgets/my-account.php:51
#: modules/woocommerce/widgets/product-add-to-cart.php:172
#: modules/woocommerce/widgets/product-add-to-cart.php:180
#: modules/woocommerce/widgets/products-deprecated.php:75
msgid "Layout"
msgstr "Layout"

#: modules/call-to-action/widgets/call-to-action.php:46
#: modules/call-to-action/widgets/call-to-action.php:138
#: modules/call-to-action/widgets/call-to-action.php:486
#: modules/carousel/widgets/media-carousel.php:145
#: modules/carousel/widgets/media-carousel.php:160
#: modules/carousel/widgets/reviews.php:264
#: modules/carousel/widgets/reviews.php:518
#: modules/carousel/widgets/testimonial-carousel.php:355
#: modules/carousel/widgets/testimonial-carousel.php:535
#: modules/dynamic-tags/tags/lightbox.php:48
#: modules/dynamic-tags/tags/lightbox.php:58
#: modules/flip-box/widgets/flip-box.php:58
#: modules/flip-box/widgets/flip-box.php:670
#: modules/gallery/widgets/gallery.php:530
#: modules/page-transitions/module.php:308
#: modules/page-transitions/module.php:332
#: modules/posts/skins/skin-base.php:488
#: modules/pricing/widgets/price-list.php:86
#: modules/pricing/widgets/price-list.php:391
#: modules/theme-elements/widgets/author-box.php:326
#: modules/woocommerce/widgets/categories.php:259
#: modules/woocommerce/widgets/products-base.php:128
msgid "Image"
msgstr "Image"

#: modules/call-to-action/widgets/call-to-action.php:928
#: modules/call-to-action/widgets/call-to-action.php:993
msgid "Title Color"
msgstr ""

#: modules/call-to-action/widgets/call-to-action.php:56
#: modules/posts/skins/skin-classic.php:24
#: modules/theme-builder/skins/posts-archive-skin-classic.php:23
#: modules/theme-elements/widgets/search-form.php:57
#: modules/woocommerce/skins/skin-classic.php:25
msgid "Classic"
msgstr ""

#: modules/posts/widgets/portfolio.php:35
msgid "Portfolio"
msgstr ""

#: modules/theme-elements/widgets/author-box.php:356
msgid "Image Size"
msgstr "Image Size"

#: modules/posts/widgets/portfolio.php:136
msgid "Item Ratio"
msgstr ""

#: modules/posts/widgets/portfolio.php:162
msgid "Show Title"
msgstr ""

#: base/base-carousel-trait.php:57 base/base-carousel-trait.php:82
#: base/base-carousel-trait.php:116 base/base-carousel-trait.php:134
#: base/base-carousel-trait.php:149
#: modules/blockquote/widgets/blockquote.php:128
#: modules/call-to-action/widgets/call-to-action.php:1437
#: modules/code-highlight/widgets/code-highlight.php:177
#: modules/code-highlight/widgets/code-highlight.php:202
#: modules/flip-box/widgets/flip-box.php:568
#: modules/forms/widgets/login.php:150 modules/forms/widgets/login.php:179
#: modules/hotspot/widgets/hotspot.php:170
#: modules/hotspot/widgets/hotspot.php:318
#: modules/hotspot/widgets/hotspot.php:396
#: modules/hotspot/widgets/hotspot.php:448
#: modules/loop-builder/widgets/loop-grid.php:81
#: modules/loop-builder/widgets/loop-grid.php:97
#: modules/loop-builder/widgets/loop-grid.php:117
#: modules/loop-builder/widgets/loop-grid.php:263
#: modules/mega-menu/widgets/mega-menu.php:911
#: modules/motion-fx/controls-group.php:43
#: modules/motion-fx/controls-group.php:177
#: modules/nav-menu/widgets/nav-menu.php:722
#: modules/payments/classes/payment-button.php:237
#: modules/payments/classes/payment-button.php:335
#: modules/posts/skins/skin-base.php:84 modules/posts/widgets/portfolio.php:123
#: modules/posts/widgets/portfolio.php:165
#: modules/posts/widgets/portfolio.php:229
#: modules/posts/widgets/posts-base.php:290
#: modules/pricing/widgets/price-table.php:166
#: modules/scroll-snap/module.php:61
#: modules/theme-elements/widgets/post-info.php:456
#: modules/woocommerce/widgets/product-meta.php:91
msgid "Off"
msgstr "Off"