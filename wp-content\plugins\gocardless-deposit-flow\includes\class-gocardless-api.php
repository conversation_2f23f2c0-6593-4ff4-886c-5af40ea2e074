<?php
/**
 * GoCardless API integration for Deposit Flow
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use GoCardlessPro\Client;
use GoCardlessPro\Environment;

class GCDF_GoCardless_API {

    /**
     * GoCardless client instance
     */
    private $client;

    /**
     * Plugin settings
     */
    private $settings;

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = gcdf()->get_settings();
        $this->init_client();
    }

    /**
     * Initialize GoCardless client
     */
    private function init_client() {
        if (empty($this->settings['gocardless_access_token'])) {
            return;
        }

        try {
            $environment = ($this->settings['gocardless_environment'] === 'live') 
                ? Environment::LIVE 
                : Environment::SANDBOX;

            $this->client = new Client([
                'access_token' => $this->settings['gocardless_access_token'],
                'environment' => $environment
            ]);

            gcdf()->log('GoCardless client initialized successfully');
        } catch (Exception $e) {
            gcdf()->log('Failed to initialize GoCardless client: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * Test connection to GoCardless API
     */
    public function test_connection() {
        if (!$this->client) {
            throw new Exception(__('GoCardless client not initialized', 'gocardless-deposit-flow'));
        }

        try {
            // Try to list creditors to test the connection
            $creditors = $this->client->creditors()->list([
                'limit' => 1
            ]);

            gcdf()->log('GoCardless connection test successful');
            return true;
        } catch (Exception $e) {
            gcdf()->log('GoCardless connection test failed: ' . $e->getMessage(), 'error');
            throw new Exception(__('Connection failed: ', 'gocardless-deposit-flow') . $e->getMessage());
        }
    }

    /**
     * Create a redirect flow for deposit payment
     */
    public function create_redirect_flow($post_id, $user_id, $deposit_id) {
        if (!$this->client) {
            throw new Exception(__('GoCardless client not initialized', 'gocardless-deposit-flow'));
        }

        try {
            $user = get_userdata($user_id);
            $post = get_post($post_id);
            $amount = gcdf()->auction_integration->get_post_deposit_amount($post_id);
            $currency = gcdf()->get_currency();

            // Convert amount to pence/cents
            $amount_in_minor_unit = $this->convert_to_minor_unit($amount, $currency);

            // Generate session token
            $session_token = wp_generate_uuid4();

            // Create redirect flow
            $redirect_flow = $this->client->redirectFlows()->create([
                'params' => [
                    'description' => sprintf(
                        __('Deposit for %s', 'gocardless-deposit-flow'),
                        $post->post_title
                    ),
                    'session_token' => $session_token,
                    'success_redirect_url' => $this->get_success_url($deposit_id),
                    'prefilled_customer' => [
                        'given_name' => $user->first_name ?: $user->display_name,
                        'family_name' => $user->last_name ?: '',
                        'email' => $user->user_email,
                    ],
                    'links' => [
                        'creditor' => $this->get_creditor_id()
                    ],
                    'scheme' => $this->get_scheme_for_currency($currency)
                ]
            ]);

            // Update deposit record with redirect flow ID
            gcdf()->get_database()->update_deposit($deposit_id, [
                'redirect_flow_id' => $redirect_flow->id,
                'status' => 'processing',
                'metadata' => json_encode([
                    'session_token' => $session_token,
                    'amount_minor_unit' => $amount_in_minor_unit,
                    'currency' => $currency
                ])
            ]);

            gcdf()->log("Created redirect flow {$redirect_flow->id} for deposit {$deposit_id}");

            return [
                'redirect_flow_id' => $redirect_flow->id,
                'redirect_url' => $redirect_flow->redirect_url,
                'session_token' => $session_token
            ];

        } catch (Exception $e) {
            gcdf()->log('Failed to create redirect flow: ' . $e->getMessage(), 'error');
            throw new Exception(__('Failed to create payment flow: ', 'gocardless-deposit-flow') . $e->getMessage());
        }
    }

    /**
     * Complete redirect flow and create payment
     */
    public function complete_redirect_flow($redirect_flow_id, $session_token) {
        if (!$this->client) {
            throw new Exception(__('GoCardless client not initialized', 'gocardless-deposit-flow'));
        }

        try {
            // Complete the redirect flow
            $redirect_flow = $this->client->redirectFlows()->complete(
                $redirect_flow_id,
                ['params' => ['session_token' => $session_token]]
            );

            // Get deposit record
            $deposit = gcdf()->get_database()->get_deposit_by_redirect_flow_id($redirect_flow_id);
            if (!$deposit) {
                throw new Exception(__('Deposit record not found', 'gocardless-deposit-flow'));
            }

            $metadata = json_decode($deposit->metadata, true);
            $amount_minor_unit = $metadata['amount_minor_unit'] ?? $this->convert_to_minor_unit($deposit->amount, $deposit->currency);

            // Create payment
            $payment = $this->client->payments()->create([
                'params' => [
                    'amount' => $amount_minor_unit,
                    'currency' => $deposit->currency,
                    'description' => sprintf(
                        __('Deposit for auction item #%d', 'gocardless-deposit-flow'),
                        $deposit->post_id
                    ),
                    'metadata' => [
                        'deposit_id' => $deposit->id,
                        'post_id' => $deposit->post_id,
                        'user_id' => $deposit->user_id
                    ],
                    'links' => [
                        'mandate' => $redirect_flow->links->mandate
                    ]
                ]
            ]);

            // Update deposit record
            gcdf()->get_database()->update_deposit($deposit->id, [
                'payment_id' => $payment->id,
                'mandate_id' => $redirect_flow->links->mandate,
                'gocardless_status' => $payment->status,
                'status' => $this->map_gocardless_status($payment->status)
            ]);

            gcdf()->log("Created payment {$payment->id} for deposit {$deposit->id}");

            return [
                'payment_id' => $payment->id,
                'status' => $payment->status,
                'deposit' => $deposit
            ];

        } catch (Exception $e) {
            gcdf()->log('Failed to complete redirect flow: ' . $e->getMessage(), 'error');
            throw new Exception(__('Failed to complete payment: ', 'gocardless-deposit-flow') . $e->getMessage());
        }
    }

    /**
     * Get payment status
     */
    public function get_payment_status($payment_id) {
        if (!$this->client) {
            throw new Exception(__('GoCardless client not initialized', 'gocardless-deposit-flow'));
        }

        try {
            $payment = $this->client->payments()->get($payment_id);
            return [
                'id' => $payment->id,
                'status' => $payment->status,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'created_at' => $payment->created_at
            ];
        } catch (Exception $e) {
            gcdf()->log('Failed to get payment status: ' . $e->getMessage(), 'error');
            throw new Exception(__('Failed to get payment status: ', 'gocardless-deposit-flow') . $e->getMessage());
        }
    }

    /**
     * Cancel payment
     */
    public function cancel_payment($payment_id) {
        if (!$this->client) {
            throw new Exception(__('GoCardless client not initialized', 'gocardless-deposit-flow'));
        }

        try {
            $payment = $this->client->payments()->cancel($payment_id);
            gcdf()->log("Cancelled payment {$payment_id}");
            return $payment;
        } catch (Exception $e) {
            gcdf()->log('Failed to cancel payment: ' . $e->getMessage(), 'error');
            throw new Exception(__('Failed to cancel payment: ', 'gocardless-deposit-flow') . $e->getMessage());
        }
    }

    /**
     * Convert amount to minor unit (pence/cents)
     */
    private function convert_to_minor_unit($amount, $currency) {
        // Most currencies use 2 decimal places
        $multiplier = 100;
        
        // Some currencies use different multipliers
        $zero_decimal_currencies = ['JPY', 'KRW', 'VND'];
        if (in_array($currency, $zero_decimal_currencies)) {
            $multiplier = 1;
        }

        return intval($amount * $multiplier);
    }

    /**
     * Get creditor ID
     */
    private function get_creditor_id() {
        try {
            $creditors = $this->client->creditors()->list(['limit' => 1]);
            if (!empty($creditors->records)) {
                return $creditors->records[0]->id;
            }
        } catch (Exception $e) {
            gcdf()->log('Failed to get creditor ID: ' . $e->getMessage(), 'error');
        }
        
        throw new Exception(__('No creditor found. Please check your GoCardless account setup.', 'gocardless-deposit-flow'));
    }

    /**
     * Get scheme for currency
     */
    private function get_scheme_for_currency($currency) {
        $schemes = [
            'GBP' => 'bacs',
            'EUR' => 'sepa_core',
            'USD' => 'ach',
            'CAD' => 'pad',
            'AUD' => 'becs',
            'NZD' => 'becs_nz',
            'SEK' => 'autogiro',
            'DKK' => 'betalingsservice'
        ];

        return $schemes[$currency] ?? 'bacs';
    }

    /**
     * Map GoCardless status to plugin status
     */
    private function map_gocardless_status($gocardless_status) {
        $status_map = [
            'pending_customer_approval' => 'pending',
            'pending_submission' => 'processing',
            'submitted' => 'processing',
            'confirmed' => 'completed',
            'paid_out' => 'completed',
            'cancelled' => 'cancelled',
            'customer_approval_denied' => 'failed',
            'failed' => 'failed',
            'charged_back' => 'refunded'
        ];

        return $status_map[$gocardless_status] ?? 'pending';
    }

    /**
     * Get success redirect URL
     */
    private function get_success_url($deposit_id) {
        $settings = gcdf()->get_settings();
        
        if (!empty($settings['success_redirect_url'])) {
            return add_query_arg(['deposit_id' => $deposit_id], $settings['success_redirect_url']);
        }

        // Default success URL
        return add_query_arg([
            'gcdf_action' => 'success',
            'deposit_id' => $deposit_id
        ], home_url());
    }

    /**
     * Get cancel redirect URL
     */
    private function get_cancel_url($deposit_id) {
        $settings = gcdf()->get_settings();
        
        if (!empty($settings['cancel_redirect_url'])) {
            return add_query_arg(['deposit_id' => $deposit_id], $settings['cancel_redirect_url']);
        }

        // Default cancel URL
        return add_query_arg([
            'gcdf_action' => 'cancel',
            'deposit_id' => $deposit_id
        ], wp_get_referer() ?: home_url());
    }

    /**
     * Verify webhook signature
     */
    public function verify_webhook_signature($payload, $signature, $webhook_secret) {
        $expected_signature = hash_hmac('sha256', $payload, $webhook_secret);
        return hash_equals($expected_signature, $signature);
    }

    /**
     * Process webhook event
     */
    public function process_webhook_event($event) {
        try {
            switch ($event['resource_type']) {
                case 'payments':
                    $this->process_payment_webhook($event);
                    break;
                case 'mandates':
                    $this->process_mandate_webhook($event);
                    break;
                default:
                    gcdf()->log("Unhandled webhook event type: {$event['resource_type']}");
            }
        } catch (Exception $e) {
            gcdf()->log('Failed to process webhook: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Process payment webhook
     */
    private function process_payment_webhook($event) {
        $payment_id = $event['links']['payment'];
        $deposit = gcdf()->get_database()->get_deposit_by_payment_id($payment_id);
        
        if (!$deposit) {
            gcdf()->log("No deposit found for payment {$payment_id}");
            return;
        }

        $new_status = $this->map_gocardless_status($event['details']['cause']);
        
                    gcdf()->get_database()->update_deposit($deposit->id, [
            'gocardless_status' => $event['details']['cause'],
            'status' => $new_status
        ]);

        // If payment is confirmed, complete the auction
        if ($new_status === 'completed') {
            do_action('gcdf_deposit_completed', $deposit);
        }

        gcdf()->log("Updated deposit {$deposit->id} status to {$new_status} via webhook");
    }

    /**
     * Process mandate webhook
     */
    private function process_mandate_webhook($event) {
        // Handle mandate events if needed
        gcdf()->log("Received mandate webhook: {$event['action']}");
    }
}
