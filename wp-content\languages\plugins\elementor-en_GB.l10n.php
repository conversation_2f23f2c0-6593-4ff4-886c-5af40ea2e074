<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2024-12-16 16:53:45+0000','plural-forms'=>'nplurals=2; plural=n != 1;','project-id-version'=>'Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)','language'=>'en_GB','messages'=>['Invalid file name.'=>'Invalid file name.','Learn more about this page.'=>'Learn more about this page.','Space between Items'=>'Space between Items','Multiple'=>'Multiple','One'=>'One','Max Items Expanded'=>'Max Items Expanded','All collapsed'=>'All collapsed','First expanded'=>'First expanded','Default State'=>'Default State','Interactions'=>'Interactions','Collapse'=>'Collapse','Expand'=>'Expand','Item Position'=>'Item Position','Choose Video File'=>'Choose Video File','%1$s of %2$s'=>'%1$s of %2$s','Box'=>'Box','Popular Add-ons, New Possibilities.'=>'Popular add-ons, new possibilities.','Gradient'=>'Gradient','Note: Scroll tabs if they don’t fit into their parent container.'=>'Note: Scroll tabs if they don’t fit into their parent container.','Horizontal Scroll'=>'Horizontal Scroll','Item #3'=>'Item #3','Item #2'=>'Item #2','Item #1'=>'Item #1','Item Title'=>'Item Title','item #%s'=>'item #%s','You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities.'=>'You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities.','Object Position'=>'Object Position','Fit to Size'=>'Fit to Size','You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities.'=>'You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities.','Go to slide'=>'Go to slide','This is the last slide'=>'This is the last slide','This is the first slide'=>'This is the first slide','Next slide'=>'Next slide','Previous slide'=>'Previous slide','Show/hide Element'=>'Show/hide Element','Show/hide inner elements'=>'Show/hide inner elements','Resize navigator'=>'Resize navigator','Resize structure'=>'Resize structure','Panels'=>'Panels','Clear gallery'=>'Clear gallery','Allow Comments'=>'Allow Comments','Order'=>'Order','Get Elementor Pro'=>'Get Elementor Pro','AI'=>'AI','Dismiss this notice.'=>'Dismiss this notice.','Activate All'=>'Activate All','Accent Color'=>'Accent Colour','Tab #3'=>'Tab #3','Shadow'=>'Shadow','Titles'=>'Titles','Preload'=>'Preload','Tab #%s'=>'Tab #%s','Tab #%d'=>'Tab #%d','Metadata'=>'Metadata','Align Title'=>'Align Title','Google Fonts'=>'Google Fonts','%1$s (%2$s %3$dpx)'=>'%1$s (%2$s %3$dpx)','Distance from content'=>'Distance from content','Gap between tabs'=>'Gap between tabs','Nested Elements'=>'Nested Elements','Lazy Load Background Images'=>'Lazy Load Background Images','Activate'=>'Activate','(link)'=>'(link)','Typography Control(Black)'=>'(Black)','Typography Control(Extra Bold)'=>'(Extra Bold)','Typography Control(Bold)'=>'(Bold)','Typography Control(Semi Bold)'=>'(Semi Bold)','Typography Control(Medium)'=>'(Medium)','Typography Control(Normal)'=>'(Normal)','Typography Control(Light)'=>'(Light)','Typography Control(Extra Light)'=>'(Extra Light)','Typography Control(Thin)'=>'(Thin)','Requires'=>'Requires','Remove Kit'=>'Remove Kit','Remove the most recent Kit'=>'Remove the most recent Kit','Remove all the content and site settings that came with "%1$s" on %2$s.%3$s Your original site settings will be restored.'=>'Remove all the content and site settings that came with "%1$s" on %2$s.%3$s Your original site settings will be restored.','imported kit'=>'imported kit','Remove all the content and site settings that came with "%1$s" on %2$s %3$s and revert to the site setting that came with "%4$s" on %5$s.'=>'Remove all the content and site settings that came with "%1$s" on %2$s %3$s and revert to the site setting that came with "%4$s" on %5$s.','Item #%d'=>'Item #%d','Next Arrow Icon'=>'Next Arrow Icon','Previous Arrow Icon'=>'Previous Arrow Icon','Horizontal Position'=>'Horizontal Position','Vertical Position'=>'Vertical Position','Dismiss Icon'=>'Dismiss Icon','Don’t add links to elements nested in this container - it will break the layout.'=>'Don’t add links to elements nested in this container - it will break the layout.','Any time you can change the settings in %1$sUser Preferences%2$s'=>'Any time you can change the settings in %1$sUser Preferences%2$s','Now you can choose where you want to go on the site from the following options'=>'Now you can choose where you want to go on the site from the following options','Performance'=>'Performance','Try it out'=>'Try it out','With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed.'=>'With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed.','Improve your site’s performance score.'=>'Improve your site’s performance score.','WP Dashboard'=>'WP Dashboard','All Posts'=>'All Posts','This Post'=>'This Post','Exit to'=>'Exit to','Sets the default space inside the container (Default is 10px)'=>'Sets the default space inside the container (Default is 10px)','Container Padding'=>'Container Padding','Unleash the full power of Elementor\'s features and web creation tools.'=>'Unleash the full power of Elementor\'s features and web creation tools.','Notes'=>'Notes','Upgrade Now'=>'Upgrade Now','Upgrade'=>'Upgrade','Pagination'=>'Pagination','Hue'=>'Hue','Exclusion'=>'Exclusion','Difference'=>'Difference','Reorder'=>'Reorder','Container'=>'Container','System Colors'=>'System Colours','Copies all of the selected sections and columns and pastes them in a container beneath the original.'=>'Copies all of the selected sections and columns and pastes them in a container below the original.','Convert'=>'Convert','Convert to container'=>'Convert to container','Lazyload'=>'Lazyload','System Fonts'=>'System Fonts','Import Export'=>'Import Export','Elementor Event Tracker'=>'Elementor Event Tracker','Play Video about'=>'Play Video about','To achieve full height Container use %s.'=>'To achieve full height Container use %s.','Add New Container'=>'Add New Container','Bold'=>'Bold','Show me how'=>'Show me how','Add New Page Template'=>'Add New Page Template','Skew Y'=>'Skew Y','Skew X'=>'Skew X','Skew'=>'Skew','Scale Y'=>'Scale Y','Scale X'=>'Scale X','Offset Y'=>'Offset Y','Offset X'=>'Offset X','Perspective'=>'Perspective','Rotate Y'=>'Rotate Y','Rotate X'=>'Rotate X','Transform'=>'Transform','Log'=>'Log','By clicking Sign Up, you agree to Elementor\'s %1$s and %2$s'=>'By clicking Sign Up, you agree to Elementor\'s %1$s and %2$s','Stroke Color'=>'Stroke Colour','X Anchor Point'=>'X Anchor Point','Flip Vertical'=>'Flip Vertical','Flip Horizontal'=>'Flip Horizontal','Keep Proportions'=>'Keep Proportions','3D Rotate'=>'3D Rotate','Custom Code'=>'Custom Code','Text Stroke'=>'Text Stroke','Privacy Policy'=>'Privacy Policy','Terms of Service'=>'Terms of Service','Ongoing Experiments'=>'Ongoing Experiments','Stable Features'=>'Stable Features','Connect & Activate'=>'Connect & Activate','Elements Usage'=>'Elements Usage','Set your Google Maps API Key in Elementor\'s %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s'=>'Set your Google Maps API Key in Elementor\'s %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s','Y Anchor Point'=>'Y Anchor Point','Invalid title'=>'Invalid title','The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts.'=>'The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts.','Inline Font Icons'=>'Inline Font Icons','Elementor Experiments'=>'Elementor Experiments','Not allowed to rollback versions'=>'Not allowed to rollback versions','The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings.'=>'The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings.','Saturation'=>'Saturation','Luminosity'=>'Luminosity','You can enable it from the %1$sElementor settings page%2$s.'=>'You can enable it from the %1$sElementor settings page%2$s.','Widescreen breakpoint settings will apply from the selected value and up.'=>'Widescreen breakpoint settings will apply from the selected value and up.','Multiply'=>'Multiply','Screen'=>'Screen','Overlay'=>'Overlay','Darken'=>'Darken','Lighten'=>'Lighten','Color Dodge'=>'Colour Dodge','In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s.'=>'In order for Theme Style to affect all relevant Elementor elements, please disable Default Colous and Fonts from the %1$sSettings Page%2$s.','Meet Page Transitions'=>'Meet Page Transitions','Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load.'=>'Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load.','Select one or go ahead and %1$screate one%2$s now.'=>'Select one or go ahead and %1$screate one%2$s now.','Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up'=>'Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up','Hide On %s'=>'Hide On %s','Templates Help You %1$sWork Efficiently%2$s'=>'Templates Help You %1$sWork Efficiently%2$s','Page Transitions'=>'Page Transitions','Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen.'=>'Get pixel-perfect design for every screen size. You can now add up to 6 customisable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and wide-screen.','Additional Custom Breakpoints'=>'Additional Custom Breakpoints','Please note! We couldn\'t deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue'=>'Please note! We couldn\'t deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue','%1$sClick here%2$s to troubleshoot'=>'%1$sClick here%2$s to troubleshoot','Or view %1$sTrashed Items%1$s'=>'Or view %1$sTrashed Items%1$s','Learn more about %1$sWordPress revisions%2$s'=>'Learn more about %1$sWordPress revisions%2$s','The %1$s argument is deprecated since version %2$s!'=>'The %1$s argument is deprecated since version %2$s!','The %1$s argument is deprecated since version %2$s! Use %3$s instead.'=>'The %1$s argument is deprecated since version %2$s! Use %3$s instead.','%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s'=>'%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s','Categories'=>'Categories','Features'=>'Features','Favorites'=>'Favourites','Header'=>'Header','Sections'=>'Sections','There\'s already an active kit.'=>'There\'s already an active kit.','An error occurred while trying to create a kit.'=>'An error occurred while trying to create a kit.','New kit have been created successfully'=>'New kit has been created successfully','Styles set in Elementor are saved in CSS files in the uploads folder and in the site’s database. Recreate those files and settings, according to the most recent settings.'=>'Styles set in Elementor are saved in CSS files in the uploads folder and in the site’s database. Recreate those files and settings, according to the most recent settings.','Color Sampler'=>'Colour Sampler','It seems like your site doesn\'t have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again.'=>'It seems like your site doesn\'t have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again.','Recreate Kit'=>'Recreate Kit','Regenerate Files & Data'=>'Regenerate Files & Data','Regenerate CSS & Data'=>'Regenerate CSS & Data','Submissions'=>'Submissions','Default device view'=>'Default device view','Kit not found'=>'Kit not found','Kit not exists.'=>'Kit does not exist.','Kit Library'=>'Kit Library','Important:'=>'Important:','Compatible'=>'Compatible','Incompatible'=>'Incompatible','Compatibility not specified'=>'Compatibility not specified','Compatibility unknown'=>'Compatibility unknown','API Key'=>'API Key','Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps\' %1$sUsing API Keys%2$s page.'=>'Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps\' %1$sUsing API Keys%2$s page.','Google Maps Embed API'=>'Google Maps Embed API','Managing a multi-user site?'=>'Managing a multi-user site?','With Elementor Pro, you can control user access and make sure no one messes up your design.'=>'With Elementor Pro, you can control user access and make sure no one messes up your design.','Blocking'=>'Blocking','Swap'=>'Swap','Optional'=>'Optional','Flower'=>'Flower','Sketch'=>'Sketch','Triangle'=>'Triangle','Blob'=>'Blob','Hexagon'=>'Hexagon','Mask'=>'Mask','Fit'=>'Fit','Scale'=>'Scale','Database update process is running in the background. Taking a while?'=>'Database update process is running in the background. Taking a while?','Widescreen'=>'Widescreen','%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down'=>'%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down','Desktop <br> Settings added for the base device will apply to all breakpoints unless edited'=>'Desktop <br> Settings added for the base device will apply to all breakpoints unless edited','Top Center'=>'Top Centre','Center Right'=>'Centre Right','Center Left'=>'Centre Left','Center Center'=>'Centre Centre','Need More Shapes?'=>'Need More Shapes?','FAQ Schema'=>'FAQ Schema','Font-display property defines how font files are loaded and displayed by the browser.'=>'Font-display property defines how font files are loaded and displayed by the browser.','Google Fonts Load'=>'Google Fonts Load','Manage Breakpoints'=>'Manage Breakpoints','Round'=>'Round','Repeat-x'=>'Repeat-x','Repeat-y'=>'Repeat-y','No-repeat'=>'No-repeat','Repeat'=>'Repeat','Y Position'=>'Y Position','X Position'=>'X Position','Bottom Right'=>'Bottom Right','Bottom Left'=>'Bottom Left','Bottom Center'=>'Bottom Centre','Top Right'=>'Top Right','Top Left'=>'Top Left','Love using Elementor?'=>'Love using Elementor?','Template Kits'=>'Template Kits','Import / Export Kit'=>'Import / Export Kit','Apply the design and settings of another site to this one.'=>'Apply the design and settings of another site to this one.','Start Import'=>'Start Import','Import a Template Kit'=>'Import a Template Kit','Bundle your whole site - or just some of its elements - to be used for another website.'=>'Bundle your whole site - or just some of its elements - to be used for another website.','Start Export'=>'Start Export','Export a Template Kit'=>'Export a Template Kit','Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s'=>'Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s','Failed to import %1$s %2$s'=>'Failed to import %1$s %2$s','Menu item skipped due to missing menu slug'=>'Menu item skipped due to missing menu slug','Menu item skipped due to invalid menu slug: %s'=>'Menu item skipped due to invalid menu slug: %s','Fetching attachments is not enabled'=>'Fetching attachments is not enabled','Invalid file type'=>'Invalid file type','Could not create temporary file.'=>'Could not create temporary file.','Request failed due to an error: %1$s (%2$s)'=>'Request failed due to an error: %1$s (%2$s)','Remote server returned the following unexpected result: %1$s (%2$s)'=>'Remote server returned the following unexpected result: %1$s (%2$s)','Remote server did not respond'=>'Remote server did not respond','Zero size file downloaded'=>'Zero size file downloaded','Downloaded file has incorrect size'=>'Downloaded file has incorrect size','Remote file is too large, limit is %s'=>'Remote file is too large, limit is %s','Sorry, this file type is not permitted for security reasons.'=>'Sorry, this file type is not permitted for security reasons.','Path'=>'Path','Stroke'=>'Stroke','Wave'=>'Wave','Arc'=>'Arc','Oval'=>'Oval','Spiral'=>'Spiral','The file does not exist, please try again.'=>'The file does not exist, please try again.','Failed to import author %s. Their posts will be attributed to the current user.'=>'Failed to import author %s. Their posts will be attributed to the current user.','Failed to create new user for %s. Their posts will be attributed to the current user.'=>'Failed to create new user for %s. Their posts will be attributed to the current user.','The uploaded file could not be moved'=>'The uploaded file could not be moved','There was an error when reading this WXR file'=>'There was an error when reading this WXR file','This does not appear to be a WXR file, missing/invalid WXR version number'=>'This does not appear to be a WXR file, missing/invalid WXR version number','Failed to import %1$s: Invalid post type %2$s'=>'Failed to import %1$s: Invalid post type %2$s','Mobile and Tablet options cannot be deleted.'=>'Mobile and Tablet options cannot be deleted.','Active Breakpoints'=>'Active Breakpoints','Starting Point'=>'Starting Point','Word Spacing'=>'Word Spacing','Show Path'=>'Show Path','LTR'=>'LTR','RTL'=>'RTL','Text Direction'=>'Text Direction','SVG'=>'SVG','Path Type'=>'Path Type','Add Your Curvy Text Here'=>'Add Your Curvy Text Here','Text Path'=>'Text Path','Learn more'=>'Learn more','Get a sneak peek at our in progress development versions, and help us improve Elementor to perfection. Developer Edition releases contain experimental functionality for testing purposes.'=>'Get a sneak peek at our in progress development versions, and help us improve Elementor to perfection. Developer Edition releases contain experimental functionality for testing purposes.','Development'=>'Development','Elementor Developer Edition'=>'Elementor Developer Edition','Inactive by default'=>'Inactive by default','Active by default'=>'Active by default','Status: %s'=>'Status: %s','Experiments'=>'Experiments','No available experiments'=>'No available experiments','Stable'=>'Stable','Release Candidate'=>'Release Candidate','Beta'=>'Beta','Alpha'=>'Alpha','The current version of Elementor doesn\'t have any experimental features . if you\'re feeling curious make sure to come back in future versions.'=>'The current version of Elementor doesn\'t have any experimental features. If you\'re feeling curious, make sure to come back in future versions.','Install & Activate'=>'Install and activate','To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time.'=>'To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time.','No landing pages found'=>'No landing pages found.','Unknown'=>'Unknown','Tested up to %s version'=>'Tested up to %s version','Plugin'=>'Plugin','Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s.'=>'Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s.','Custom Columns Gap'=>'Custom columns gap','Compatibility Alert'=>'Compatibility alert','Landing Page'=>'Landing page','Landing Pages'=>'Landing pages','Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow.'=>'Adds a new Elementor content type that allows you to create beautiful landing pages instantly in a streamlined workflow.','Build Effective Landing Pages for your business\' marketing campaigns.'=>'Build effective landing pages for your business\' marketing campaigns.','Add New Landing Page'=>'Add new landing page','Edit Landing Page'=>'Edit landing page','New Landing Page'=>'New landing page','All Landing Pages'=>'All landing pages','View Landing Page'=>'View landing page','Search Landing Pages'=>'Search landing pages','No landing pages found in trash'=>'No landing pages found in bin','Keep my settings'=>'Keep my settings','By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone.'=>'By removing this template you will delete your entire site settings. If this template is deleted, all associated settings: global colours and fonts, theme style, layout, background, and Lightbox settings will be removed from your existing site. This action cannot be undone.','Are you sure you want to delete your Site Settings?'=>'Are you sure you want to delete your site settings?','The Global value you are trying to use is not available.'=>'The Global value you are trying to use is not available.','The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!'=>'The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!','Choose SVG'=>'Choose SVG','Breakpoint'=>'Breakpoint','Global Colors'=>'Global Colours','Additional Settings'=>'Additional Settings','Apply Link On'=>'Apply Link On','Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us.'=>'Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us.','Breakpoints'=>'Breakpoints','Changes will be reflected in the preview only after the page reloads.'=>'Changes will be reflected in the preview only after the page reloads.','Choose description'=>'Choose description','Choose name'=>'Choose name','Default Page Layout'=>'Default Page Layout','Design System'=>'Design System','Download'=>'Download','Fallback Font Family'=>'Fallback Font Family','Fill'=>'Fill','Items'=>'Items','Layout Settings'=>'Layout Settings','Mobile Browser Background'=>'Mobile Browser Background','Object Fit'=>'Object Fit','Rows Gap'=>'Rows Gap','Site Description'=>'Site Description','Site Favicon'=>'Site Favicon','Site Identity'=>'Site Identity','Site Logo'=>'Site Logo','Site Name'=>'Site Name','Site Settings'=>'Site Settings','Suggested favicon dimensions: 512 × 512 pixels.'=>'Suggested favicon dimensions: 512 × 512 pixels.','Theme'=>'Theme','User Preferences'=>'User Preferences','Heads up, Please backup before upgrade!'=>'Heads up, please back up before upgrade!','The `theme-color` meta tag will only be available in supported browsers and devices.'=>'The `theme-color` meta tag will only be available in supported browsers and devices.','Error'=>'Error','The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment'=>'The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment','Suggested image dimensions: %1$s × %2$s pixels.'=>'Suggested image dimensions: %1$s × %2$s pixels.','Reset Data'=>'Reset Data','Click the media icon to upload file'=>'Click the media icon to upload file','Enable Unfiltered File Uploads'=>'Enable Unfiltered File Uploads','Get introduced to Elementor by watching our "Getting Started" video series. It will guide you through the steps needed to create your website. Then click to create your first page.'=>'Get introduced to Elementor by watching our "Getting Started" video series. It will guide you through the steps needed to create your website. Then click to create your first page.','Watch the Full Guide'=>'Watch the Full Guide','If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode.'=>'If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode.','Download image'=>'Download image','Pin it'=>'Pin it','Share on Twitter'=>'Share on Twitter','Share on Facebook'=>'Share on Facebook','Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma.'=>'Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma.','Get more dynamic capabilities by incorporating dozens of Elementor\'s native dynamic tags.'=>'Get more dynamic capabilities by incorporating dozens of Elementor\'s native dynamic tags.','You’re missing out!'=>'You’re missing out!','We highly recommend backing up your database before performing this upgrade.'=>'We highly recommend backing up your database before performing this upgrade.','The upgrade process includes a database update'=>'The upgrade process includes a database update','Attributes lets you add custom HTML attributes to any element.'=>'Attributes lets you add custom HTML attributes to any element.','Attributes'=>'Attributes','Click here to run it now'=>'Click here to run it now','Alt'=>'Alt','Share'=>'Share','Fullscreen'=>'Fullscreen','Back'=>'Back','Focus'=>'Focus','Field'=>'Field','Label'=>'Label','Body'=>'Body','Buttons'=>'Buttons','Draft'=>'Draft','Kit'=>'Kit','%s Widget'=>'%s Widget','Custom Attributes'=>'Custom Attributes','Default Kit'=>'Default Kit','Dynamic Tags'=>'Dynamic Tags','Elementor Dynamic Content'=>'Elementor Dynamic Content','Form Fields'=>'Form Fields','Meet Our Attributes'=>'Meet Our Attributes','Navigation Icons Size'=>'Navigation Icons Size','Paragraph Spacing'=>'Paragraph Spacing','Toolbar Icons Size'=>'Toolbar Icons Size','Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better.'=>'Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better.','Already connected.'=>'Already connected.','Connected as %s'=>'Connected as %s','%s Video Player'=>'%s Video Player','Auto'=>'Auto','Background Position'=>'Background Position','Background Size'=>'Background Size','Connecting to the Library failed. Please try reloading the page and try again'=>'Connecting to the Library failed. Please try reloading the page and try again','Contain'=>'Contain','Cover'=>'Cover','Get Started'=>'Get Started','Pause on Interaction'=>'Pause on Interaction','Preferences'=>'Preferences','Usage Data Sharing'=>'Usage Data Sharing','Auto detect'=>'Auto detect','Custom Icons'=>'Custom Icons','Transition'=>'Transition','Duration'=>'Duration','Clear Log'=>'Clear Log','Next'=>'Next','Previous'=>'Previous','Amount'=>'Amount','Line'=>'Line','Out'=>'Out','In'=>'In','This cover image will replace the background video in case that the video could not be loaded.'=>'This cover image will replace the background video in case that the video could not be loaded.','YouTube/Vimeo link, or link to video file (mp4 is recommended).'=>'YouTube/Vimeo link, or link to video file (MP4 is recommended).','Reinstall'=>'Reinstall','Post'=>'Post','Add Element'=>'Add Element','Basic Gallery'=>'Basic Gallery','Ken Burns Effect'=>'Ken Burns Effect','Play On Mobile'=>'Play On Mobile','File Path: %s'=>'File Path: %s','Deprecated'=>'Deprecated','Hurray! The upgrade process to Font Awesome 5 was completed successfully.'=>'Hurray! The upgrade process to Font Awesome 5 was completed successfully.','Upgrade To Font Awesome 5'=>'Upgrade To Font Awesome 5','This action is not reversible and cannot be undone by rolling back to previous versions.'=>'This action is not reversible and cannot be undone by rolling back to previous versions.','Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome.'=>'Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome.','By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon.'=>'By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon.','Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility.'=>'Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility.','Upload'=>'Upload','All Icons'=>'All Icons','As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email'=>'As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email','Choose Video'=>'Choose Video','Document not found.'=>'Document not found.','Font Awesome Upgrade'=>'Font Awesome Upgrade','Get Beta Updates'=>'Get Beta Updates','Icon Library'=>'Icon Library','Load Font Awesome 4 Support'=>'Load Font Awesome 4 Support','Need Help'=>'Need Help','Play Once'=>'Play Once','Sign Up'=>'Sign Up','Some of your theme files are missing.'=>'Some of your theme files are missing.','Upload SVG'=>'Upload SVG','We recommend you only enable this feature if you understand the security risks involved.'=>'We recommend you only enable this feature if you understand the security risks involved.','Your Email'=>'Your Email','Your site\'s .htaccess file appears to be missing.'=>'Your site\'s .htaccess file appears to be missing.','Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library.'=>'Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library.','Font Awesome - Brands'=>'Font Awesome - Brands','Font Awesome - Solid'=>'Font Awesome - Solid','Font Awesome - Regular'=>'Font Awesome - Regular','Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts.'=>'Elementor will try to sanitise the unfiltered files, removing potential malicious code and scripts.','Template not exist.'=>'Template does not exist.','Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk.'=>'Please note! Allowing uploads of any files (SVG and JSON included) is a potential security risk.','This file is not allowed for security reasons.'=>'This file is not allowed for security reasons.','Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor.'=>'Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor.','Hidden'=>'Hidden','Overflow'=>'Overflow','Get Help'=>'Get Help','Custom positioning is not considered best practice for responsive web design and should not be used too frequently.'=>'Custom positioning is not considered best practice for responsive web design and should not be used too frequently.','Please note!'=>'Please note!','Space Evenly'=>'Space Evenly','Space Around'=>'Space Around','Vertical Orientation'=>'Vertical Orientation','Offset'=>'Offset','Horizontal Orientation'=>'Horizontal Orientation','Fixed'=>'Fixed','Absolute'=>'Absolute','Vertical Align'=>'Vertical Align','Custom Width'=>'Custom Width','Motion Effects'=>'Motion Effects','Super Admin'=>'Super Admin','Having problems loading Elementor? Please enable Safe Mode to troubleshoot.'=>'Having problems loading Elementor? Please enable Safe Mode to troubleshoot.','The issue was probably caused by one of your plugins or theme.'=>'The issue was probably caused by one of your plugins or theme.','Note: The ID link ONLY accepts these chars: %s'=>'Note: The ID link ONLY accepts these chars: %s','Choose File'=>'Choose File','External URL'=>'External URL','Order By'=>'Order By','Read More Text'=>'Read More Text','Note: This widget only affects themes that use `%s` in archive pages.'=>'Note: This widget only affects themes that use `%s` in archive pages.','Continue reading'=>'Continue reading','Read More'=>'Read More','Location'=>'Location','Continue reading %s'=>'Continue reading %s','(more&hellip;)'=>'(more&hellip;)','Template LibraryFilter by category'=>'Filter by category','Template LibraryAll Categories'=>'All Categories','Template LibraryCategory'=>'Category','Template LibraryCategories'=>'Categories','Template LibraryTemplates'=>'Templates','Get Popup Builder'=>'Get Popup Builder','Popups'=>'Popups','Theme Builder'=>'Theme Builder','Enable Safe Mode'=>'Enable Safe Mode','Can\'t Edit?'=>'Can\'t Edit?','Still experiencing issues?'=>'Still experiencing issues?','Editor successfully loaded?'=>'Editor successfully loaded?','Disable Safe Mode'=>'Disable Safe Mode','Safe Mode ON'=>'Safe Mode ON','Cannot enable Safe Mode'=>'Cannot enable Safe Mode','Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin.'=>'Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin.','Safe Mode'=>'Safe Mode','Elementor Data Updater'=>'Elementor Data Updater','Every %d minutes'=>'Every %d minutes','The database update process is now complete. Thank you for updating to the latest version!'=>'The database update process is now complete. Thank you for updating to the latest version!','Your site database needs to be updated to the latest version.'=>'Your site database needs to be updated to the latest version.','The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today.'=>'The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today.','Not Supported'=>'Not Supported','Users'=>'Users','Plugins'=>'Plugins','Any Video'=>'Any Video','Current Video Channel'=>'Current Video Channel','Unmarked Color'=>'Unmarked Colour','Stars'=>'Stars','Outline'=>'Outline','Unmarked Style'=>'Unmarked Style','Rating Scale'=>'Rating Scale','Rating'=>'Rating','Star Rating'=>'Star Rating','Custom Caption'=>'Custom Caption','Attachment Caption'=>'Attachment Caption','Quit'=>'Quit','Keyboard Shortcuts'=>'Keyboard Shortcuts','Show / Hide Panel'=>'Show / Hide Panel','Go To'=>'Go To','Redo'=>'Redo','Undo'=>'Undo','Type to find anything in Elementor'=>'Type to find anything in Elementor','Finder'=>'Finder','Customizer'=>'Customiser','Menus'=>'Menus','Dashboard'=>'Dashboard','Homepage'=>'Home page','Create'=>'Create','Disconnect'=>'Disconnect','Connect'=>'Connect','Future'=>'Future','Disconnected successfully.'=>'Disconnected successfully.','Connected successfully.'=>'Connected successfully.','Poster'=>'Poster','Lazy Load'=>'Lazy Load','Enter your URL'=>'Enter your URL','Welcome to Elementor'=>'Welcome to Elementor','Create Your First Post'=>'Create Your First Post','Create Your First Page'=>'Create Your First Page','Getting Started'=>'Getting Started','Inner Section'=>'Inner Section','Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget.'=>'Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget.','Easy Navigation is Here!'=>'Easy Navigation is Here!','Empty'=>'Empty','Navigator'=>'Navigator','Skip'=>'Skip','Paste URL or type'=>'Paste URL or type','Filter ControlHue'=>'Hue','Debug Bar'=>'Debug Bar','Hide Notification'=>'Hide Notification','Happy To Help'=>'Happy To Help','Congrats!'=>'Congrats!','Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo.'=>'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo.','Zoom'=>'Zoom','Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed.'=>'Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed.','Single'=>'Single','Logo'=>'Logo','Modest Branding'=>'Modest Branding','Video Info'=>'Video Info','URL'=>'URL','Self Hosted'=>'Self Hosted','Dailymotion'=>'Dailymotion','Source'=>'Source','Button ID'=>'Button ID','Artwork'=>'Artwork','WooCommerce'=>'WooCommerce','Site'=>'Site','Pro'=>'Pro','Blend Mode'=>'Blend Mode','Copy'=>'Copy','Drag widget here'=>'Drag widget here','Filter ControlSaturation'=>'Saturation','Filter ControlContrast'=>'Contrast','Filter ControlBrightness'=>'Brightness','Filter ControlBlur'=>'Blur','Specify an end time (in seconds)'=>'Specify an end time (in seconds)','End Time'=>'End Time','Specify a start time (in seconds)'=>'Specify a start time (in seconds)','Start Time'=>'Start Time','Wait! Don\'t deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work.'=>'Wait! Don\'t deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work.','I have Elementor Pro'=>'I have Elementor Pro','Elementor Debugger'=>'Elementor Debugger','Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces.'=>'Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces.','Back to WordPress Editor'=>'Back to WordPress Editor','Elementor %s'=>'Elementor %s','Add New %s'=>'Add New %s','Opacity'=>'Opacity','Max Width'=>'Max Width','New Template'=>'New Template','Position'=>'Position','Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!'=>'Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!','Knowledge Base'=>'Knowledge Base','This template includes the header, full-width content and footer'=>'This template includes the header, full-width content and footer','No header, no footer, just Elementor'=>'No header, no footer, just Elementor','Page Layout'=>'Page Layout','Default Page Template from your theme.'=>'Default Page Template from your theme.','Play Video'=>'Play Video','Inline'=>'Inline','Separator'=>'Separator','Add New'=>'Add New','Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow.'=>'Add templates and reuse them across your website. Easily export and import them to any other project, for an optimised workflow.','Create Your First %s'=>'Create Your First %s','All'=>'All','Template LibraryMy Templates'=>'My Templates','Create Template'=>'Create Template','Enter template name (optional)'=>'Enter template name (optional)','Name your template'=>'Name your template','Select the type of template you want to work on'=>'Select the type of template you want to work on','Choose Template Type'=>'Choose Template Type','Use templates to create the different pieces of your site, and reuse them with one click whenever needed.'=>'Use templates to create the different pieces of your site, and reuse them with one click whenever needed.','Custom Fonts'=>'Custom Fonts','More actions'=>'More actions','Search Templates:'=>'Search Templates:','Pages'=>'Pages','This tag has no settings.'=>'This tag has no settings.','%s Settings'=>'%s Settings','Want to give access only to content?'=>'Want to give access only to content?','No access to editor'=>'No access to editor','Role Excluded'=>'Role Excluded','Manage What Your Users Can Edit In Elementor'=>'Manage What Your Users Can Edit In Elementor','Role Manager'=>'Role Manager','Fallback'=>'Fallback','Featured Image'=>'Featured Image','Body Style'=>'Body Style','Document'=>'Document','Action not found.'=>'Action not found.','Token Expired.'=>'Token Expired.','Set how many slides are scrolled per swipe.'=>'Set how many slides are scrolled per swipe.','Note: Attachment Fixed works only on desktop.'=>'Note: Attachment Fixed works only on desktop.','Create New Post'=>'Create New Post','Google (Early Access)'=>'Google (Early Access)','Current Version'=>'Current Version','Privacy Mode'=>'Privacy Mode','Enter your shortcode'=>'Enter your shortcode','Enter your image caption'=>'Enter your image caption','Enter your code'=>'Enter your code','Add Your Heading Text Here'=>'Add Your Heading Text Here','Enter your description'=>'Enter your description','This is an Alert'=>'This is an Alert','Active Icon'=>'Active Icon','Last edited on %1$s by %2$s'=>'Last edited on %1$s by %2$s','Draft saved on %1$s by %2$s'=>'Draft saved on %1$s by %2$s','revision date formatM j, H:i'=>'M j, H:i','Hurray! Your %s is live.'=>'Hurray! Your %s is live.','Published'=>'Published','No Results Found'=>'No Results Found','Select File'=>'Select File','or'=>'or','Drag & drop your .JSON or .zip template file'=>'Drag & drop your .JSON or .zip template file','Import Template to Your Library'=>'Import Template to Your Library','Click here'=>'Click here','Want to learn more about the Elementor library?'=>'Want to learn more about the Elementor library?','Favorite'=>'Favourite','Creation Date'=>'Creation Date','Created By'=>'Created By','Search'=>'Search','My Favorites'=>'My Favourites','Popular'=>'Popular','Trend'=>'Trend','New'=>'New','Import Template'=>'Import Template','Remove'=>'Remove','Duplicate'=>'Duplicate','Drag & Drop'=>'Drag & Drop','Hide Panel'=>'Hide Panel','Save as Template'=>'Save as Template','Save Draft'=>'Save Draft','Save Options'=>'Save Options','Publish'=>'Publish','Preview Changes'=>'Preview Changes','Search Widget:'=>'Search Widget:','Back to default'=>'Back to default','Typography ControlLine Through'=>'Line Through','Typography ControlOverline'=>'Overline','Typography ControlUnderline'=>'Underline','Typography ControlDecoration'=>'Decoration','Unlinked values'=>'Unlinked values','Blog'=>'Blog','(opens in a new window)'=>'(opens in a new window)','News & Updates'=>'News & Updates','Dashboard Overview Widget Recently DateM jS'=>'M jS','Recently Edited'=>'Recently Edited','Create New Page'=>'Create New Page','Elementor Overview'=>'Elementor Overview','Excerpt'=>'Excerpt','When you turn on privacy mode, YouTube/Vimeo won\'t store information about visitors on your website unless they play the video.'=>'When you turn on privacy mode, YouTube/Vimeo won\'t store information about visitors on your website unless they play the video.','Access denied.'=>'Access denied.','Disable Default Fonts'=>'Disable Default Fonts','End'=>'End','Start'=>'Start','The preview could not be loaded'=>'The preview could not be loaded','We’re sorry, but something went wrong. Click on \'Learn more\' and follow each of the steps to quickly solve it.'=>'We\'re sorry, but something went wrong. Click on “Learn more” and follow each of the steps to quickly solve it.','Update Notification'=>'Update Notification','https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash'=>'https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash','https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash'=>'https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash','Once you start working, you\'ll be able to redo / undo any action you make in the editor.'=>'Once you start working, you\'ll be able to redo / undo any action you make in the editor.','No History Yet'=>'No History Yet','Switch to Revisions tab for older versions'=>'Switch to Revisions tab for older versions','Revisions'=>'Revisions','Actions'=>'Actions','History'=>'History','UI Hover Color'=>'UI Hover Colour','UI Color'=>'UI Colour','Mute'=>'Mute','Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library.'=>'Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library.','Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file.'=>'Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file.','Image Lightbox'=>'Image Lightbox','Please Note: We do not recommend updating to a beta version on production sites.'=>'Please Note: We do not recommend updating to a beta version on production sites.','Beta Tester'=>'Beta Tester','Become a Beta Tester'=>'Become a Beta Tester','Warning: Please backup your database before making the rollback.'=>'Warning: Please backup your database before making the rollback.','Rollback Version'=>'Rollback Version','Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared.'=>'Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared.','Version Control'=>'Version Control','Switch Editor Loader Method'=>'Switch Editor Loader Method','Integrations'=>'Integrations','Rollback to Previous Version'=>'Rollback to Previous Version','Animation Delay'=>'Animation Delay','Z-Index'=>'Z-Index','Widgets Space'=>'Widgets Space','Add nofollow'=>'Add nofollow','Open in new window'=>'Open in new window','Link Options'=>'Link Options','Box Shadow ControlOutline'=>'Outline','Edit %s'=>'Edit %s','Transition Duration'=>'Transition Duration','Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it.'=>'Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it.','Sets the default space between widgets (Default: 20px)'=>'Sets the default space between widgets (Default: 20px)','Internal Embedding'=>'Internal Embedding','External File'=>'External File','CSS Print Method'=>'CSS Print Method','For troubleshooting server configuration conflicts.'=>'For troubleshooting server configuration conflicts.','Enable'=>'Enable','Disable'=>'Disable','Status'=>'Status','Space'=>'Space','Drop Cap'=>'Drop Cap','Elementor lets you hide the page title. This works for themes that have "h1.entry-title" selector. If your theme\'s selector is different, please enter it above.'=>'Elementor lets you hide the page title. This works for themes that have "h1.entry-title" selector. If your theme\'s selector is different, please enter it above.','Page Title Selector'=>'Page Title Selector','Select'=>'Select','Template'=>'Template','Hide Title'=>'Hide Title','Maintenance Mode ON'=>'Maintenance Mode ON','Choose Template'=>'Choose Template','To enable maintenance mode you have to set a template for the maintenance mode page.'=>'To enable maintenance mode you have to set a template for the maintenance mode page.','Edit Template'=>'Edit Template','Roles'=>'Roles','Logged In'=>'Logged In','Who Can Access'=>'Who Can Access','Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed.'=>'Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed.','Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days.'=>'Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days.','Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code).'=>'Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code).','Maintenance'=>'Maintenance','Coming Soon'=>'Coming Soon','Disabled'=>'Disabled','Choose Mode'=>'Choose Mode','Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched.'=>'Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched.','Maintenance Mode'=>'Maintenance Mode','Paste Style'=>'Paste Style','Bring to Front'=>'Bring to Front','Lightbox'=>'Lightbox','Space Between'=>'Space Between','List'=>'List','ShapesBook'=>'Book','ShapesSplit'=>'Split','ShapesArrow'=>'Arrow','ShapesWaves Pattern'=>'Waves Pattern','ShapesWaves Brush'=>'Waves Brush','ShapesWaves'=>'Waves','ShapesCurve Asymmetrical'=>'Curve Asymmetrical','ShapesCurve'=>'Curve','ShapesFan Opacity'=>'Fan Opacity','ShapesTilt Opacity'=>'Tilt Opacity','ShapesTilt'=>'Tilt','ShapesTriangle Asymmetrical'=>'Triangle Asymmetrical','ShapesTriangle'=>'Triangle','ShapesPyramids'=>'Pyramids','ShapesZigzag'=>'Zigzag','ShapesClouds'=>'Clouds','ShapesDrops'=>'Drops','ShapesMountains'=>'Mountains','Invert'=>'Invert','Flip'=>'Flip','Shape Divider'=>'Shape Divider','Navigation Width'=>'Navigation Width','Hover'=>'Hover','Add your custom id WITHOUT the Pound key. e.g: my-id'=>'Add your custom id WITHOUT the Pound key. e.g: my-id','CSS ID'=>'CSS ID','Background ControlType'=>'Type','Background ControlLocation'=>'Location','Checking this box will disable Elementor\'s Default Fonts, and make Elementor inherit the fonts from your theme.'=>'Checking this box will disable Elementor\'s Default Fonts, and make Elementor inherit the fonts from your theme.','Checking this box will disable Elementor\'s Default Colors, and make Elementor inherit the colors from your theme.'=>'Checking this box will disable Elementor\'s Default Colours, and make Elementor inherit the colours from your theme.','Video Tutorials'=>'Video Tutorials','View Elementor Video Tutorials'=>'View Elementor Video Tutorials','Docs & FAQs'=>'Docs & FAQs','View Elementor Documentation'=>'View Elementor Documentation','Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to \'HTTPS\').'=>'Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to \'HTTPS\').','Update Site Address (URL)'=>'Update Site Address (URL)','Replace URL'=>'Replace URL','%1$s ago (%2$s)'=>'%1$s ago (%2$s)','revision date formatM j @ H:i'=>'M j @ H:i','Autosave'=>'Autosave','Revision'=>'Revision','It looks like the post revision feature is unavailable in your website.'=>'It looks like the post revision feature is unavailable in your website.','Revision history lets you save your previous versions of your work, and restore them any time.'=>'Revision history lets you save your previous versions of your work, and restore them any time.','By'=>'By','No Revisions Saved Yet'=>'No Revisions Saved Yet','Start designing your page and you will be able to see the entire revision history here.'=>'Start designing your page and you will be able to see the entire revision history here.','Thousand Separator'=>'Thousand Separator','Custom CSS lets you add CSS code to any widget, and see it render live right in the editor.'=>'Custom CSS lets you add CSS code to any widget, and see it render live right in the editor.','Meet Our Custom CSS'=>'Meet Our Custom CSS','Custom CSS'=>'Custom CSS','With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place.'=>'With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place.','Meet Our Global Widget'=>'Meet Our Global Widget','Go Pro'=>'Go Pro','Get more with Elementor Pro'=>'Get more with Elementor Pro','Off'=>'Off','On'=>'On','Extra Large'=>'Extra Large','Extra Small'=>'Extra Small','Improve Elementor'=>'Improve Elementor','Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one.'=>'Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one.','Skin'=>'Skin','%s are disabled'=>'%s are disabled','Update changes to page'=>'Update changes to page','No thanks'=>'No thanks','Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width.'=>'Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width.','Stretched Section Fit To'=>'Stretched Section Fit To','Stretch the section to the full width of the page using JS.'=>'Stretch the section to the full width of the page using JS.','Stretch Section'=>'Stretch Section','Sets the default width of the content area (Default: 1140px)'=>'Sets the default width of the content area (Default: 1140px)','Learn more.'=>'Learn more.','Reverse Columns'=>'Reverse Columns','Mobile'=>'Mobile','Link values together'=>'Link values together','Shortcode'=>'Shortcode','Remote'=>'Remote','Import Now'=>'Import Now','Import Templates'=>'Import Templates','Export Template'=>'Export Template','(no title)'=>'(no title)','Template LibraryType'=>'Type','Template LibraryTemplate'=>'Template','Local'=>'Local','Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button.'=>'Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button.','Sync Library'=>'Sync Library','Tools'=>'Tools','Page'=>'Page','Enter Template Name'=>'Enter Template Name','Export'=>'Export','Stay tuned! More awesome templates coming real soon.'=>'Stay tuned! More awesome templates coming real soon.','Back to Library'=>'Back to Library','Insert'=>'Insert','Library'=>'Library','Close'=>'Close','Add Template'=>'Add Template','Saved Templates'=>'Saved Templates','Template Library'=>'Template Library','Learn More'=>'Learn More','Global Fonts'=>'Global Fonts','Hover Animation'=>'Hover Animation','Fast'=>'Fast','Slow'=>'Slow','Disable Default Colors'=>'Disable Default Colours','Entrance Animation'=>'Entrance Animation','Box Shadow ControlInset'=>'Inset','Vertical'=>'Vertical','Horizontal'=>'Horizontal','Spread'=>'Spread','Blur'=>'Blur','Aside'=>'Aside','Testimonial'=>'Testimonial','Official Color'=>'Official Colour','Rounded'=>'Rounded','Social Icons'=>'Social Icons','My Skill'=>'My Skill','Custom Colors'=>'Custom Colours','Username'=>'Username','Play Counts'=>'Play Counts','Comments'=>'Comments','Share Button'=>'Share Button','Download Button'=>'Download Button','Like Button'=>'Like Button','Buy Button'=>'Buy Button','Visual Player'=>'Visual Player','SoundCloud'=>'SoundCloud','Background Overlay'=>'Background Overlay','Extended'=>'Extended','If you have a moment, please share why you are deactivating Elementor:'=>'If you have a moment, please share why you are deactivating Elementor:','Quick Feedback'=>'Quick Feedback','Please share the reason'=>'Please share the reason','Other'=>'Other','It\'s a temporary deactivation'=>'It\'s a temporary deactivation','I couldn\'t get the plugin to work'=>'I couldn\'t get the plugin to work','Please share which plugin'=>'Please share which plugin','I found a better plugin'=>'I found a better plugin','I no longer need the plugin'=>'I no longer need the plugin','Update Now'=>'Update Now','View Elementor version %s details'=>'View Elementor version %s details','There is a new version of Elementor Page Builder available. <a href="%1$s" class="thickbox open-plugin-details-modal" aria-label="%2$s">View version %3$s details</a> or <a href="%4$s" class="update-link" aria-label="%5$s">update now</a>.'=>'There is a new version of Elementor Page Builder available. <a href="%1$s" class="thickbox open-plugin-details-modal" aria-label="%2$s">View version %3$s details</a> or <a href="%4$s" class="update-link" aria-label="%5$s">update now</a>.','Custom URL'=>'Custom URL','Background'=>'Background','Wider'=>'Wider','General'=>'General','Paste'=>'Paste','Intro Byline'=>'Intro Byline','Spacing'=>'Spacing','Outside'=>'Outside','Inside'=>'Inside','Direction'=>'Direction','Additional Options'=>'Additional Options','Arrows and Dots'=>'Arrows and Dots','Left Border Width'=>'Left Border Width','Animation Duration'=>'Animation Duration','Image Stretch'=>'Image Stretch','Image Carousel'=>'Image Carousel','Animation Speed'=>'Animation Speed','You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio.'=>'You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio.','Controls Color'=>'Controls Colour','Intro Portrait'=>'Intro Portrait','Intro Title'=>'Intro Title','Loop'=>'Loop','Video Options'=>'Video Options','Vimeo'=>'Vimeo','Video'=>'Video','The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing.'=>'The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing.','Fade'=>'Fade','Effect'=>'Effect','Image Size ControlFull'=>'Full','Edit gallery'=>'Edit gallery','Vertical Alignment'=>'Vertical Alignment','Infinite Loop'=>'Infinite Loop','Dots'=>'Dots','Title HTML Tag'=>'Title HTML Tag','This is the heading'=>'This is the heading','Add your custom class WITHOUT the dot. e.g: my-class'=>'Add your custom class WITHOUT the dot. e.g: my-class','The list of fonts used if the chosen font is not available.'=>'The list of fonts used if the chosen font is not available.','Form'=>'Form','Elements'=>'Elements','Settings'=>'Settings','Typography ControlCapitalize'=>'Capitalise','Typography ControlOblique'=>'Oblique','Typography ControlItalic'=>'Italic','Typography ControlLowercase'=>'Lowercase','Typography ControlUppercase'=>'Uppercase','Active Color'=>'Active Colour','Background Color'=>'Background Colour','Chat Background Color'=>'Chat background colour','Border Color'=>'Border Colour','Center'=>'Centre','Elementor.com'=>'Elementor.com','Play Icon'=>'Play Icon','Image Overlay'=>'Image Overlay','Suggested Videos'=>'Suggested Videos','Aspect Ratio'=>'Aspect Ratio','YouTube'=>'YouTube','Toggle Content'=>'Toggle Content','Toggle Title'=>'Toggle Title','Toggle #2'=>'Toggle #2','Toggle #1'=>'Toggle #1','Toggle Items'=>'Toggle Items','Toggle'=>'Toggle','Text Editor'=>'Text Editor','Tab Content'=>'Tab Content','Tab Title'=>'Tab Title','Tab #2'=>'Tab #2','Tab #1'=>'Tab #1','Tabs Items'=>'Tabs Items','Tabs'=>'Tabs','Pause on Hover'=>'Pause on Hover','Autoplay Speed'=>'Autoplay Speed','Arrows'=>'Arrows','Slide'=>'Slide','Choose Sidebar'=>'Choose Sidebar','No sidebars were found'=>'No sidebars were found','Sidebar'=>'Sidebar','Title Style'=>'Title Style','Web Designer'=>'Web Designer','e.g. Web Designer'=>'e.g. Web Designer','Inner Text'=>'Inner Text','Display Percentage'=>'Display Percentage','Percentage'=>'Percentage','Progress Bar'=>'Progress Bar','For Example: About'=>'For Example: About','The ID of Menu Anchor.'=>'The ID of Menu Anchor.','This ID will be the CSS ID you will have to use in your own page, Without #.'=>'This ID will be the CSS ID you will have to use in your own page, Without #.','Menu Anchor'=>'Menu Anchor','Image Position'=>'Image Position','Image Spacing'=>'Image Spacing','Image Size'=>'Image Size','Image Box'=>'Image Box','Icon Hover'=>'Icon Hover','Border Width'=>'Border Width','Rotate'=>'Rotate','Secondary Color'=>'Secondary Colour','Primary Color'=>'Primary Colour','Square'=>'Square','Circle'=>'Circle','Shape'=>'Shape','Framed'=>'Framed','Stacked'=>'Stacked','List Item'=>'List Item','List Item #3'=>'List Item #3','List Item #2'=>'List Item #2','List Item #1'=>'List Item #1','Icon List'=>'Icon List','Icon Spacing'=>'Icon Spacing','Icon Box'=>'Icon Box','HTML Code'=>'HTML Code','HTML'=>'HTML','HTML Tag'=>'HTML Tag','Enter your title'=>'Enter your title','Heading'=>'Heading','London Eye, London, United Kingdom'=>'London Eye, London, United Kingdom','Google Maps'=>'Google Maps','Caption'=>'Caption','Images'=>'Images','Random'=>'Random','Media File'=>'Media File','Attachment Page'=>'Attachment Page','Add Images'=>'Add Images','Gap'=>'Gap','Weight'=>'Weight','Spacer'=>'Spacer','Divider'=>'Divider','Number'=>'Number','Cool Number'=>'Cool Number','Number Suffix'=>'Number Suffix','Number Prefix'=>'Number Prefix','Ending Number'=>'Ending Number','Starting Number'=>'Starting Number','Counter'=>'Counter','Autoplay'=>'Autoplay','Slides to Scroll'=>'Slides to Scroll','Slides to Show'=>'Slides to Show','Choose Image'=>'Choose Image','Image'=>'Image','Color'=>'Colour','Border'=>'Border','After'=>'After','Before'=>'Before','Icon Position'=>'Icon Position','Justified'=>'Justified','XXL'=>'XXL','XL'=>'XL','Large'=>'Large','Medium'=>'Medium','Small'=>'Small','Icon'=>'Icon','Size'=>'Size','Alignment'=>'Alignment','Button'=>'Button','Edit'=>'Edit','Description'=>'Description','I am a description. Click the edit button to change this text.'=>'I am a description. Click the edit button to change this text.','Danger'=>'Danger','Warning'=>'Warning','Success'=>'Success','Info'=>'Info','Border Radius'=>'Border Radius','Title'=>'Title','Hide'=>'Hide','Show'=>'Show','Type'=>'Type','Alert'=>'Alert','View'=>'View','Accordion Content'=>'Accordion Content','Accordion Title'=>'Accordion Title','Accordion #2'=>'Accordion #2','Accordion #1'=>'Accordion #1','Accordion Items'=>'Accordion Items','Accordion'=>'Accordion','Sure! I\'d love to help'=>'Sure! I\'d love to help','Download System Info'=>'Download System Info','You can copy the below info as simple text with Ctrl+C / Ctrl+V:'=>'You can copy the below info as simple text with Ctrl+C / Ctrl+V:','Copy & Paste Info'=>'Copy & Paste Info','System Info'=>'System Info','Name'=>'Name','No'=>'No','Yes'=>'Yes','Exclude Roles'=>'Exclude Roles','Post Types'=>'Post Types','General Settings'=>'General Settings','Accent'=>'Accent','Secondary'=>'Secondary','Primary'=>'Primary','WordPress'=>'WordPress','Navigation'=>'Navigation','Basic'=>'Basic','Visibility'=>'Visibility','Content Position'=>'Content Position','Middle'=>'Middle','Text'=>'Text','Stretch'=>'Stretch','Column Position'=>'Column Position','Minimum Height'=>'Minimum Height','Min Height'=>'Min Height','Fit To Screen'=>'Fit To Screen','Height'=>'Height','Wide'=>'Wide','Narrow'=>'Narrow','No Gap'=>'No Gap','Columns Gap'=>'Columns Gap','Content Width'=>'Content Width','Full Width'=>'Full Width','Boxed'=>'Boxed','Layout'=>'Layout','Column Width'=>'Column Width','CSS Classes'=>'CSS Classes','Margin'=>'Margin','Text Align'=>'Text Align','Width'=>'Width','Padding'=>'Padding','Link Hover Color'=>'Link Hover Colour','Link Color'=>'Link Colour','Heading Color'=>'Heading Colour','Text Color'=>'Text Colour','Typography'=>'Typography','Column'=>'Column','Section'=>'Section','Structure'=>'Structure','Responsive'=>'Responsive','Advanced'=>'Advanced','Style'=>'Style','Content'=>'Content','Columns'=>'Columns','Colors'=>'Colours','Apply'=>'Apply','Discard'=>'Discard','Reset'=>'Reset','Preview'=>'Preview','Save'=>'Save','Help'=>'Help','Tablet'=>'Tablet','Desktop'=>'Desktop','Responsive Mode'=>'Responsive Mode','Widgets Panel'=>'Widgets Panel','Menu'=>'Menu','Search Widget...'=>'Search Widget...','Add New Section'=>'Add New Section','Loading'=>'Loading','Elementor'=>'Elementor','Add Item'=>'Add Item','Delete'=>'Delete','Select Icon'=>'Select Icon','Typography ControlStyle'=>'Style','Typography ControlTransform'=>'Transform','Typography ControlWeight'=>'Weight','Typography ControlFamily'=>'Family','Typography ControlSize'=>'Size','Dashed'=>'Dashed','Dotted'=>'Dotted','Custom'=>'Custom','Normal'=>'Normal','Double'=>'Double','Solid'=>'Solid','Background ControlRepeat'=>'Repeat','Background ControlFixed'=>'Fixed','Background ControlScroll'=>'Scroll','Background ControlAttachment'=>'Attachment','Google'=>'Google','System'=>'System','Edit with Elementor'=>'Edit with Elementor','&#8592; Back to WordPress Editor'=>'&#8592; Back to WordPress Editor','None'=>'None','Link'=>'Link','Left'=>'Left','Player Controls'=>'Player Controls','Bottom'=>'Bottom','Right'=>'Right','Top'=>'Top','Default'=>'Default','You do not have permission to download this file.'=>'You do not have permission to download this file.','Themes'=>'Themes','Inactive'=>'Inactive','Active'=>'Active','Laptop'=>'Laptop','Posts'=>'Posts','Justify'=>'Justify','Add'=>'Add','Page Settings'=>'Page Settings','Mobile Portrait'=>'Mobile Portrait','Mobile Landscape'=>'Mobile Landscape']];