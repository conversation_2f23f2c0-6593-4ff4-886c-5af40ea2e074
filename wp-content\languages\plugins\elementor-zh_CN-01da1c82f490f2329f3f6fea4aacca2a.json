{"translation-revision-date": "2025-06-09 04:34:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Double hyphens are reserved for custom properties.": ["双连字符保留用于自定义属性。"], "Class names can only use letters, numbers, dashes (-), and underscores (_).": ["Class 名称只能使用字母，数字，破折号（ - ）和下划线（_）。"], "Class names can’t start with a hyphen followed by a number.": ["Class 名称不能以连字符开始，然后是一个数字。"], "This name is reserved and can’t be used. Try something more specific.": ["此名称是保留的，无法使用。尝试一些更具体的东西。"], "Class name is too short. Use at least 2 characters.": ["Class 名称太短。使用至少2个字符。"], "Class name is too long. Please keep it under 50 characters.": ["Class 名称太长。请保持50个字符。"], "This class name already exists. Please choose a unique name.": ["此类名称已经存在。请选择一个唯一名称。"], "Class names can’t contain spaces.": ["Class 名称不能包含空格。"]}}, "comment": {"reference": "assets/js/packages/editor-styles-repository/editor-styles-repository.strings.js"}}