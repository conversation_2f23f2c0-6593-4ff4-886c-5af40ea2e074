# GoCardless Deposit Flow Plugin

A custom WordPress plugin that integrates GoCardless deposit payment flow with auction "Buy Now" functionality. Requires users to pay a £200 deposit before completing vehicle purchases.

## Features

- **Deposit Requirement**: Intercepts "Buy Now" button to require deposit payment first
- **Modal Interface**: Clean, responsive modal with terms & conditions checkbox
- **GoCardless Integration**: Secure bank transfer payments via GoCardless API
- **Auction Integration**: Seamlessly integrates with existing auction system
- **Webhook Support**: Real-time payment status updates
- **Admin Dashboard**: Complete settings and statistics interface
- **Security**: Comprehensive security measures and input validation

## Requirements

- WordPress 5.0+
- PHP 7.4+
- GoCardless account (sandbox for testing, live for production)
- Existing auction system with "Buy Now" functionality

## Installation

1. **Upload Plugin**
   ```bash
   # Upload the plugin folder to wp-content/plugins/
   wp-content/plugins/gocardless-deposit-flow/
   ```

2. **Install Dependencies**
   ```bash
   cd wp-content/plugins/gocardless-deposit-flow/
   composer install --no-dev --optimize-autoloader
   ```

3. **Activate Plugin**
   - Go to WordPress Admin → Plugins
   - Find "GoCardless Deposit Flow" and click "Activate"

4. **Configure Settings**
   - Go to Settings → GoCardless Deposit
   - Enter your GoCardless API credentials
   - Configure deposit amount and other settings

## Configuration

### GoCardless API Setup

1. **Create GoCardless Account**
   - Sign up at [GoCardless](https://gocardless.com/)
   - Complete account verification

2. **Get API Credentials**
   - Go to GoCardless Dashboard → Developers → API Keys
   - Copy your access token

3. **Configure Plugin**
   - Environment: Sandbox (testing) or Live (production)
   - Access Token: Your GoCardless access token
   - Deposit Amount: Default £200.00
   - Currency: GBP (or supported currency)

### Webhook Configuration

1. **Set Webhook URL**
   - In GoCardless Dashboard → Developers → Webhooks
   - Add webhook URL: `https://yoursite.com/gocardless-webhook/`

2. **Configure Events**
   - Enable payment events: `created`, `confirmed`, `failed`, `cancelled`
   - Enable mandate events: `cancelled`, `failed`, `expired`

## Usage

### For Users

1. **Browse Auctions**
   - Users browse auction items as normal
   - Items with "Buy Now" option show deposit requirement

2. **Initiate Purchase**
   - Click "Buy Now" button
   - Modal appears requesting deposit payment
   - Accept terms & conditions checkbox

3. **Pay Deposit**
   - Redirected to GoCardless payment flow
   - Complete bank transfer authorization
   - Return to auction site

4. **Purchase Completion**
   - Deposit confirmed via webhook
   - Auction item marked as sold
   - User receives confirmation email

### For Administrators

1. **Monitor Deposits**
   - View deposit statistics in admin dashboard
   - Track payment statuses and amounts

2. **Manage Settings**
   - Adjust deposit amounts per item
   - Configure redirect URLs
   - Enable/disable logging

3. **Handle Issues**
   - View failed payments
   - Process refunds if needed
   - Monitor webhook events

## API Integration

### GoCardless Flow

1. **Create Redirect Flow**
   ```php
   $redirect_data = gcdf()->gocardless_api->create_redirect_flow($post_id, $user_id, $deposit_id);
   ```

2. **Complete Payment**
   ```php
   $result = gcdf()->gocardless_api->complete_redirect_flow($redirect_flow_id, $session_token);
   ```

3. **Handle Webhooks**
   ```php
   // Automatic webhook processing at /gocardless-webhook/
   ```

### Database Schema

```sql
CREATE TABLE wp_gocardless_deposits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    payment_id VARCHAR(255),
    redirect_flow_id VARCHAR(255),
    mandate_id VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL DEFAULT 200.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'GBP',
    status ENUM('pending','processing','completed','failed','cancelled','refunded'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL
);
```

## Customization

### Deposit Amount Per Item

```php
// Set custom deposit amount for specific auction item
update_post_meta($post_id, 'gcdf_custom_deposit_amount', 150.00);

// Require deposit for specific item
update_post_meta($post_id, 'gcdf_require_deposit', '1');
```

### Hooks and Filters

```php
// Triggered when deposit is completed
add_action('gcdf_deposit_completed', 'my_deposit_completed_handler');

// Triggered when deposit fails
add_action('gcdf_deposit_failed', 'my_deposit_failed_handler');

// Filter deposit amount
add_filter('gcdf_deposit_amount', 'my_custom_deposit_amount', 10, 2);
```

## Testing

### Run Basic Tests

```php
// Access: /wp-admin/?run_gcdf_tests=1 (admin users only)
```

### Test Scenarios

1. **Happy Path**
   - User completes deposit successfully
   - Auction item marked as sold

2. **Abandoned Payment**
   - User cancels during GoCardless flow
   - Item remains available

3. **Failed Payment**
   - Payment fails due to insufficient funds
   - User notified, item available

4. **Webhook Processing**
   - Test webhook events
   - Verify status updates

## Security

- **Nonce Verification**: All forms use WordPress nonces
- **Input Sanitization**: All inputs sanitized and validated
- **Rate Limiting**: API calls rate limited per user/IP
- **Webhook Verification**: Webhook signatures verified
- **Access Controls**: Proper capability checks

## Troubleshooting

### Common Issues

1. **Modal Not Appearing**
   - Check if plugin is configured
   - Verify auction item has buy now price
   - Check browser console for JavaScript errors

2. **Payment Fails**
   - Verify GoCardless credentials
   - Check webhook URL is accessible
   - Review error logs

3. **Auction Not Completing**
   - Check webhook processing
   - Verify auction system integration
   - Review deposit status in database

### Debug Mode

```php
// Enable logging in plugin settings
$settings['enable_logging'] = true;

// View logs in error_log or debug.log
```

## Support

- **Documentation**: Full documentation in `/docs/` folder
- **GitHub Issues**: Report bugs and feature requests
- **Email Support**: <EMAIL>

## License

GPL v2 or later

## Changelog

### Version 1.0.1
- **FIXED**: Memory exhaustion error during plugin activation
- **IMPROVED**: Implemented lazy loading for all plugin components
- **OPTIMIZED**: Reduced memory usage by deferring component initialization
- **ENHANCED**: Added getter methods for better component access control
- **FIXED**: Resolved infinite loop issues in auction integration hooks

### Version 1.0.0
- Initial release
- GoCardless integration
- Modal interface
- Auction system integration
- Webhook support
- Admin dashboard
