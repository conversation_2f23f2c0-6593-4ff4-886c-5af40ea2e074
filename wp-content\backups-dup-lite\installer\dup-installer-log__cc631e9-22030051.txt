********************************************************************************
* DUPLICATOR LITE: Install-Log
* STEP-0 START @ 03:12:29
* NOTICE: Do NOT post to public sites or forums!!
********************************************************************************
PACKAGE INFO________ ORIGINAL SERVER                        |CURRENT SERVER
OS__________________: Linux                                 |WINNT
PHP VERSION_________: 8.1.32                                |8.2.12
********************************************************************************
CURRENT SERVER INFO
PHP_________________: 8.2.12 | SAPI: apache2handler
PHP MEMORY__________: 4294967296 | SUHOSIN: disabled
ARCHITECTURE________: 64-bit
SERVER______________: Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12
DOC ROOT____________: "C:/xampp/htdocs/comva"
REQUEST URL_________: "http://localhost/comva"
********************************************************************************
OVERWRITE PARAMS
 *** FROM PACKAGE
PARAM SET KEY[blogname]
********************************************************************************
MAINTENANCE MODE DISABLE
INSTALLER INFO

TEMPLATE____________: "base"
SECURE MODE_________: "none"
URL PLUGINS_________: https://comva.co.uk/wp-content/plugins
VALIDATE ON START___: "normal"
PATH_NEW____________: "C:/xampp/htdocs/comva"
URL_NEW_____________: "http://localhost/comva"
********************************************************************************
ARCHIVE INFO

ARCHIVE NAME________: "C:/xampp/htdocs/comva/20250822_comvavehicleauctions_[HASH]_20250822030051_archive.zip"
ARCHIVE SIZE________: 281.69MB
CREATED_____________: 2025-08-22 03:00:51
WP VERSION__________: 6.5.6
DUP VERSION_________: ********
LICENSE_____________: Free version
DB VERSION__________: 10.11.10
DB FILE SIZE________: 103.34MB
DB TABLES___________: 171
DB ROWS_____________: 347230
URL HOME____________: https://comva.co.uk
URL CORE____________: https://comva.co.uk
URL CONTENT_________: https://comva.co.uk/wp-content
URL UPLOAD__________: https://comva.co.uk/wp-content/uploads
URL PLUGINS_________: https://comva.co.uk/wp-content/plugins
URL MU PLUGINS______: https://comva.co.uk/wp-content/mu-plugins
URL THEMES__________: https://comva.co.uk/wp-content/themes
PATH HOME___________: /home/<USER>/domains/comva.co.uk/public_html
PATH ABS____________: /home/<USER>/domains/comva.co.uk/public_html
PATH WPCONFIG_______: /home/<USER>/domains/comva.co.uk/public_html
PATH WPCONTENT______: /home/<USER>/domains/comva.co.uk/public_html/wp-content
PATH UPLOADS________: /home/<USER>/domains/comva.co.uk/public_html/wp-content/uploads
PATH PLUGINS________: /home/<USER>/domains/comva.co.uk/public_html/wp-content/plugins
PATH MUPLUGINS______: /home/<USER>/domains/comva.co.uk/public_html/wp-content/mu-plugins
PATH THEMES_________: /home/<USER>/domains/comva.co.uk/public_html/wp-content/themes

SUBSITES
SUBSITE [ID:   1] "comva.co.uk/"

PLUGINS
PLUGIN [SLUG:all-in-one-wp-migration/all-in-one-wp-migration.php][ON:true ]  All-in-One WP Migration and Backup
PLUGIN [SLUG:better-search-replace/better-search-replace.php   ][ON:true ]  Better Search Replace
PLUGIN [SLUG:change-admin-email-setting-without-outbound-email/change-admin-email.php][ON:true ]  Change Admin Email Setting Without Outbound Email
PLUGIN [SLUG:cookie-law-info/cookie-law-info.php               ][ON:true ]  CookieYes | GDPR Cookie Consent
PLUGIN [SLUG:definitely-allow-mobile-zooming/definitely-allow-mobile-zooming.php][ON:true ]  Definitely allow mobile zooming
PLUGIN [SLUG:disable-gutenberg/disable-gutenberg.php           ][ON:true ]  Disable Gutenberg
PLUGIN [SLUG:duplicator/duplicator.php                         ][ON:true ]  Duplicator
PLUGIN [SLUG:elementor/elementor.php                           ][ON:true ]  Elementor
PLUGIN [SLUG:elementor-pro/elementor-pro.php                   ][ON:true ]  Elementor Pro
PLUGIN [SLUG:envato-elements/envato-elements.php               ][ON:true ]  Envato Elements
PLUGIN [SLUG:litespeed-cache/litespeed-cache.php               ][ON:true ]  LiteSpeed Cache
PLUGIN [SLUG:meta-box/meta-box.php                             ][ON:true ]  Meta Box
PLUGIN [SLUG:real-media-library-lite/index.php                 ][ON:true ]  Real Media Library (Free)
PLUGIN [SLUG:header-footer-elementor/header-footer-elementor.php][ON:true ]  Ultimate Addons for Elementor Lite
PLUGIN [SLUG:elementor-safe-mode.php                           ][ON:false]  Elementor Safe Mode
PLUGIN [SLUG:hostinger-auto-updates.php                        ][ON:false]  Hostinger Smart Auto Updates
PLUGIN [SLUG:sso.php                                           ][ON:false]  SSO

********************************************************************************
LOG-TIME[C:\xampp\htdocs\comva\dup-installer\ctrls\ctrl.base.php:227][DELTA:   2.46691]  MESSAGE:END RENDER PAGE
LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [sparam_s1] START
AJAX ACTION [sparam_s1] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME
STEP ACTION: "on-validate"
LOG-TIME[C:\xampp\htdocs\comva\dup-installer\ctrls\ctrl.base.php:227][DELTA:   2.33311]  MESSAGE:END RENDER PAGE
LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [validate] START
START TEST "Archive Check" [CLASS: DUPX_Validation_test_archive_check]
LOG-TIME[DELTA:   0.00009]  MESSAGE:TEST "Archive Check" RESULT: passed

START TEST "Duplicator importer version" [CLASS: DUPX_Validation_test_importer_version]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Duplicator importer version" RESULT: skip

START TEST "Overwrite Install" [CLASS: DUPX_Validation_test_owrinstall]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Overwrite Install" RESULT: skip

START TEST "Recovery Point" [CLASS: DUPX_Validation_test_recovery]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Recovery Point" RESULT: skip

START TEST "Package is Importable" [CLASS: DUPX_Validation_test_importable]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Package is Importable" RESULT: skip

START TEST "REST API test" [CLASS: DUPX_Validation_test_rest_api]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "REST API test" RESULT: skip

START TEST "Manual extraction detected" [CLASS: DUPX_Validation_test_manual_extraction]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Manual extraction detected" RESULT: good

START TEST "Database Only" [CLASS: DUPX_Validation_test_dbonly_iswordpress]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Database Only" RESULT: skip

START TEST "Package Age" [CLASS: DUPX_Validation_test_package_age]
LOG-TIME[DELTA:   0.00003]  MESSAGE:TEST "Package Age" RESULT: good

START TEST "Package Size" [CLASS: DUPX_Validation_test_package_size]
LOG-TIME[DELTA:   0.00007]  MESSAGE:TEST "Package Size" RESULT: good

START TEST "Replace PATHs in database" [CLASS: DUPX_Validation_test_replace_paths]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Replace PATHs in database" RESULT: skip

START TEST "Managed hosting supported" [CLASS: DUPX_Validation_test_managed_supported]
LOG-TIME[DELTA:   0.00012]  MESSAGE:TEST "Managed hosting supported" RESULT: skip

START TEST "Siteground" [CLASS: DUPX_Validation_test_siteground]
LOG-TIME[DELTA:   0.00007]  MESSAGE:TEST "Siteground" RESULT: skip

START TEST "Addon Sites" [CLASS: DUPX_Validation_test_addon_sites]
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
LOG-TIME[DELTA:   0.04588]  MESSAGE:TEST "Addon Sites" RESULT: good

START TEST "Wordfence" [CLASS: DUPX_Validation_test_wordfence]
LOG-TIME[DELTA:   0.00040]  MESSAGE:TEST "Wordfence" RESULT: good

START TEST "Table prefix of managed hosting" [CLASS: DUPX_Validation_test_managed_tprefix]
LOG-TIME[DELTA:   0.00014]  MESSAGE:TEST "Table prefix of managed hosting" RESULT: skip

START TEST "PHP Version Mismatch" [CLASS: DUPX_Validation_test_php_version]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Version Mismatch" RESULT: good

START TEST "PHP Open Base" [CLASS: DUPX_Validation_test_open_basedir]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Open Base" RESULT: good

START TEST "PHP Memory Limit" [CLASS: DUPX_Validation_test_memory_limit]
LOG-TIME[DELTA:   0.00003]  MESSAGE:TEST "PHP Memory Limit" RESULT: good

START TEST "PHP Extensions" [CLASS: DUPX_Validation_test_extensions]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Extensions" RESULT: good

START TEST "PHP Mysqli" [CLASS: DUPX_Validation_test_mysql_connect]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Mysqli" RESULT: passed

START TEST "PHP Tokenizer" [CLASS: DUPX_Validation_test_tokenizer]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Tokenizer" RESULT: passed

START TEST "PHP Timeout" [CLASS: DUPX_Validation_test_timeout]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "PHP Timeout" RESULT: good

START TEST "Disk Space" [CLASS: DUPX_Validation_test_disk_space]
LOG-TIME[DELTA:   0.00782]  MESSAGE:TEST "Disk Space" RESULT: good

START TEST "Permissions: General" [CLASS: DUPX_Validation_test_iswritable]
LOG-TIME[DELTA:   0.03499]  MESSAGE:TEST "Permissions: General" RESULT: passed

START TEST "Permissions: Configs Files " [CLASS: DUPX_Validation_test_iswritable_configs]
LOG-TIME[DELTA:   0.00059]  MESSAGE:TEST "Permissions: Configs Files " RESULT: passed

START TEST "Host Name" [CLASS: DUPX_Validation_test_db_host_name]
LOG-TIME[DELTA:   0.00003]  MESSAGE:TEST "Host Name" RESULT: passed

START TEST "Host Connection" [CLASS: DUPX_Validation_test_db_connection]
LOG-TIME[DELTA:   0.01110]  MESSAGE:TEST "Host Connection" RESULT: passed

START TEST "Database Version" [CLASS: DUPX_Validation_test_db_version]
LOG-TIME[DELTA:   0.00095]  MESSAGE:TEST "Database Version" RESULT: passed

START TEST "Create New Database" [CLASS: DUPX_Validation_test_db_create]
LOG-TIME[DELTA:   0.00002]  MESSAGE:TEST "Create New Database" RESULT: skip

START TEST "Database Engine Support" [CLASS: DUPX_Validation_test_db_supported_engine]
LOG-TIME[DELTA:   0.00014]  MESSAGE:TEST "Database Engine Support" RESULT: passed

START TEST "Database GTID Mode" [CLASS: DUPX_Validation_test_db_gtid_mode]
LOG-TIME[DELTA:   0.00009]  MESSAGE:TEST "Database GTID Mode" RESULT: passed

START TEST "Privileges: User Visibility" [CLASS: DUPX_Validation_test_db_visibility]
LOG-TIME[DELTA:   0.00014]  MESSAGE:TEST "Privileges: User Visibility" RESULT: passed

START TEST "Manual Table Check" [CLASS: DUPX_Validation_test_db_manual_tabels_count]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Manual Table Check" RESULT: skip

START TEST "Multiple WordPress Installs" [CLASS: DUPX_Validation_test_db_multiple_wp_installs]
LOG-TIME[DELTA:   0.00042]  MESSAGE:TEST "Multiple WordPress Installs" RESULT: passed

START TEST "Privileges: User Resources" [CLASS: DUPX_Validation_test_db_user_resources]
LOG-TIME[DELTA:   0.00053]  MESSAGE:TEST "Privileges: User Resources" RESULT: passed

START TEST "Privileges: User Table Access" [CLASS: DUPX_Validation_test_db_user_perms]
LOG-TIME[DELTA:   0.03407]  MESSAGE:TEST "Privileges: User Table Access" RESULT: passed

START TEST "Privileges: 'Show Variables' Query" [CLASS: DUPX_Validation_test_db_custom_queries]
LOG-TIME[DELTA:   0.00044]  MESSAGE:TEST "Privileges: 'Show Variables' Query" RESULT: passed

START TEST "Source Database Triggers" [CLASS: DUPX_Validation_test_db_triggers]
LOG-TIME[DELTA:   0.00002]  MESSAGE:TEST "Source Database Triggers" RESULT: passed

START TEST "Character Set and Collation Support" [CLASS: DUPX_Validation_test_db_supported_default_charset]
LOG-TIME[DELTA:   0.00061]  MESSAGE:TEST "Character Set and Collation Support" RESULT: passed

START TEST "Character Set and  Collation Capability" [CLASS: DUPX_Validation_test_db_supported_charset]
LOG-TIME[DELTA:   0.00004]  MESSAGE:TEST "Character Set and  Collation Capability" RESULT: hard warning

START TEST "Tables Case Sensitivity" [CLASS: DUPX_Validation_test_db_case_sensitive_tables]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Tables Case Sensitivity" RESULT: skip

START TEST "Tables Flagged for Removal or Backup" [CLASS: DUPX_Validation_test_db_affected_tables]
LOG-TIME[DELTA:   0.00040]  MESSAGE:TEST "Tables Flagged for Removal or Backup" RESULT: passed

START TEST "Prefix too long" [CLASS: DUPX_Validation_test_db_prefix_too_long]
LOG-TIME[DELTA:   0.00022]  MESSAGE:TEST "Prefix too long" RESULT: passed

START TEST "Database cleanup" [CLASS: DUPX_Validation_test_db_cleanup]
LOG-TIME[DELTA:   0.00001]  MESSAGE:TEST "Database cleanup" RESULT: skip


CTRL PARAMS AFTER VALIDATION
AJAX ACTION [validate] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [proceed_confirm_dialog] START
AJAX ACTION [proceed_confirm_dialog] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [sparam_s1] START
AJAX ACTION [sparam_s1] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
INITIALIZE FILTERS
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
********************************************************************************
* DUPLICATOR LITE: Install-Log
* STEP-1 START @ 03:13:49
* NOTICE: Do NOT post to public sites or forums!!
********************************************************************************
USER INPUTS
INSTALL TYPE________: single site
BLOG NAME___________: "Comva Vehicle Auctions"
HOME URL NEW________: "http://localhost/comva"
SITE URL NEW________: "http://localhost/comva"
CONTENT URL NEW_____: "http://localhost/comva/wp-content"
UPLOAD URL NEW______: "http://localhost/comva/wp-content/uploads"
PLUGINS URL NEW_____: "http://localhost/comva/wp-content/plugins"
MUPLUGINS URL NEW___: "http://localhost/comva/wp-content/mu-plugins"
HOME PATH NEW_______: "C:/xampp/htdocs/comva"
SITE PATH NEW_______: "C:/xampp/htdocs/comva"
CONTENT PATH NEW____: "C:/xampp/htdocs/comva/wp-content"
UPLOAD PATH NEW_____: "C:/xampp/htdocs/comva/wp-content/uploads"
PLUGINS PATH NEW____: "C:/xampp/htdocs/comva/wp-content/plugins"
MUPLUGINS PATH NEW__: "C:/xampp/htdocs/comva/wp-content/mu-plugins"
ARCHIVE ACTION______: "donothing"
SKIP WP FILES_______: "none"
ARCHIVE ENGINE______: "ziparchivechunking"
SET DIR PERMS_______: false
DIR PERMS VALUE_____: 0755
SET FILE PERMS______: false
FILE PERMS VALUE____: 0644
SAFE MODE___________: 0
LOGGING_____________: 1
ZIP THROTTLING______: false
WP CONFIG___________: "modify"
HTACCESS CONFIG_____: "new"
OTHER CONFIG________: "new"
FILE TIME___________: "current"
********************************************************************************

REMOVE FILTERS
	DIR : "C:/xampp/htdocs/comva/dup-installer"
	FILE: "C:/xampp/htdocs/comva/20250822_comvavehicleauctions_[HASH]_20250822030051_installer-backup.php"
	FILE: "C:/xampp/htdocs/comva/20250822_comvavehicleauctions_[HASH]_20250822030051_archive.zip"
	FILE: "C:/xampp/htdocs/comva/installer.php"
	FILE: "C:/xampp/htdocs/comva/dup-installer-bootlog__cc631e9-22030051.txt"
EXTRACTION FILTERS
	DIR : "dup-installer"
	FILE: "20250822_comvavehicleauctions_[HASH]_20250822030051_installer-backup.php"
--------------------------------------


EXTRACTION: ZIP CHUNKING >>> START
MAINTENANCE MODE ENABLE
BEFORE EXTRACION ACTIONS

*** RESET CONFIG FILES IN CURRENT HOSTING >>> START

*** RESET CONFIG FILES IN CURRENT HOSTING >>> END
MAINTENANCE MODE ENABLE

*** CREATE FOLDER AND PERMISSION PREPARE
FOLDER PREPARE DONE
ARCHIVE OFFSET 0
CHUNK COMPLETE - RUNTIME: 5.0116 sec. - Files processed: 3,916 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 3916
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0324 sec. - Files processed: 5,193 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 5193
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.2174 sec. - Files processed: 5,797 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 5797
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0499 sec. - Files processed: 7,011 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 7011
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0123 sec. - Files processed: 8,018 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 8018
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0139 sec. - Files processed: 9,205 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 9205
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0125 sec. - Files processed: 10,670 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 10670
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0184 sec. - Files processed: 12,650 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 12650
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0119 sec. - Files processed: 14,641 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 14641
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0118 sec. - Files processed: 16,241 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 16241
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
CHUNK COMPLETE - RUNTIME: 5.0103 sec. - Files processed: 18,418 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [extract] START
MAINTENANCE MODE ENABLE
ARCHIVE OFFSET 18418
--------------------------------------
PATHS MAPPING : "C:/xampp/htdocs/comva"
--------------------------------------
FILE EXTRACTION: done processing last file in list of 19197

EXTRACTION: ZIP CHUNKING >>> DONE

EXTRACTION COMPLETE @ 03:14:49 - RUNTIME: 59.7755 sec. - Files processed: 102,345 of 102,345
AJAX ACTION [extract] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
ADD PREFIX META MAP ID 0 wp_



********************************************************************************
* DUPLICATOR LITE: INSTALL-LOG
* STEP-2 START @ 03:14:49
* NOTICE: Do NOT post to public sites or forums!!
********************************************************************************
USER INPUTS
DB ENGINE___________: "chunk"
VIEW MODE___________: "basic"
DB ACTION___________: "empty"
DB HOST_____________: "**OBSCURED**"
DB NAME_____________: "**OBSCURED**"
DB PASS_____________: "**OBSCURED**"
DB PORT_____________: "**OBSCURED**"
USER MODE___________: "overwrite"
TABLE PREFIX________: "wp_"
MYSQL MODE__________: "DEFAULT"
MYSQL MODE OPTS_____: ""
CHARSET_____________: "utf8"
COLLATE_____________: ""
CUNKING_____________: true
VIEW CREATION_______: true
STORED PROCEDURE____: true
FUNCTIONS___________: true
REMOVE DEFINER______: false
SPLIT CREATES_______: true
--------------------------------------
TABLES
--------------------------------------
TABLE "wp_actionscheduler_actions"______________________[ROWS:    3067] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_actions]
TABLE "wp_actionscheduler_claims"_______________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_claims]
TABLE "wp_actionscheduler_groups"_______________________[ROWS:      10] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_groups]
TABLE "wp_actionscheduler_logs"_________________________[ROWS:    9194] [EXTRACT|REPLACE] [INST NAME: wp_actionscheduler_logs]
TABLE "wp_addonlibrary_addons"__________________________[ROWS:       9] [EXTRACT|REPLACE] [INST NAME: wp_addonlibrary_addons]
TABLE "wp_addonlibrary_categories"______________________[ROWS:       8] [EXTRACT|REPLACE] [INST NAME: wp_addonlibrary_categories]
TABLE "wp_bm_guests"____________________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_bm_guests]
TABLE "wp_bm_mentions"__________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bm_mentions]
TABLE "wp_bm_message_messages"__________________________[ROWS:      58] [EXTRACT|REPLACE] [INST NAME: wp_bm_message_messages]
TABLE "wp_bm_message_meta"______________________________[ROWS:     235] [EXTRACT|REPLACE] [INST NAME: wp_bm_message_meta]
TABLE "wp_bm_message_recipients"________________________[ROWS:      24] [EXTRACT|REPLACE] [INST NAME: wp_bm_message_recipients]
TABLE "wp_bm_moderation"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bm_moderation]
TABLE "wp_bm_threads"___________________________________[ROWS:      12] [EXTRACT|REPLACE] [INST NAME: wp_bm_threads]
TABLE "wp_bm_thread_meta"_______________________________[ROWS:      16] [EXTRACT|REPLACE] [INST NAME: wp_bm_thread_meta]
TABLE "wp_bm_user_index"________________________________[ROWS:    2411] [EXTRACT|REPLACE] [INST NAME: wp_bm_user_index]
TABLE "wp_bm_user_roles_index"__________________________[ROWS:    3078] [EXTRACT|REPLACE] [INST NAME: wp_bm_user_roles_index]
TABLE "wp_bp_activity"__________________________________[ROWS:      19] [EXTRACT|REPLACE] [INST NAME: wp_bp_activity]
TABLE "wp_bp_activity_meta"_____________________________[ROWS:       8] [EXTRACT|REPLACE] [INST NAME: wp_bp_activity_meta]
TABLE "wp_bp_friends"___________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_friends]
TABLE "wp_bp_groups"____________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_groups]
TABLE "wp_bp_groups_groupmeta"__________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_groups_groupmeta]
TABLE "wp_bp_groups_members"____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_groups_members]
TABLE "wp_bp_invitations"_______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_invitations]
TABLE "wp_bp_messages_messages"_________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_messages_messages]
TABLE "wp_bp_messages_meta"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_messages_meta]
TABLE "wp_bp_messages_notices"__________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_messages_notices]
TABLE "wp_bp_messages_recipients"_______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_messages_recipients]
TABLE "wp_bp_notifications"_____________________________[ROWS:      31] [EXTRACT|REPLACE] [INST NAME: wp_bp_notifications]
TABLE "wp_bp_notifications_meta"________________________[ROWS:      31] [EXTRACT|REPLACE] [INST NAME: wp_bp_notifications_meta]
TABLE "wp_bp_optouts"___________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bp_optouts]
TABLE "wp_bp_user_blogs"________________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_bp_user_blogs]
TABLE "wp_bp_user_blogs_blogmeta"_______________________[ROWS:      10] [EXTRACT|REPLACE] [INST NAME: wp_bp_user_blogs_blogmeta]
TABLE "wp_bp_xprofile_data"_____________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_bp_xprofile_data]
TABLE "wp_bp_xprofile_fields"___________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_bp_xprofile_fields]
TABLE "wp_bp_xprofile_groups"___________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_bp_xprofile_groups]
TABLE "wp_bp_xprofile_meta"_____________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_bp_xprofile_meta]
TABLE "wp_bv_fw_requests"_______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_bv_fw_requests]
TABLE "wp_bv_ip_store"__________________________________[ROWS:    5187] [EXTRACT|REPLACE] [INST NAME: wp_bv_ip_store]
TABLE "wp_bv_lp_requests"_______________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_bv_lp_requests]
TABLE "wp_cky_banners"__________________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_cky_banners]
TABLE "wp_cky_cookies"__________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cky_cookies]
TABLE "wp_cky_cookie_categories"________________________[ROWS:       5] [EXTRACT|REPLACE] [INST NAME: wp_cky_cookie_categories]
TABLE "wp_cleantalk_ac_log"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_ac_log]
TABLE "wp_cleantalk_connection_reports"_________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_connection_reports]
TABLE "wp_cleantalk_no_cookie_data"_____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_no_cookie_data]
TABLE "wp_cleantalk_sessions"___________________________[ROWS:     440] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_sessions]
TABLE "wp_cleantalk_sfw"________________________________[ROWS:   15748] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_sfw]
TABLE "wp_cleantalk_sfw_logs"___________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_sfw_logs]
TABLE "wp_cleantalk_sfw_personal"_______________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_sfw_personal]
TABLE "wp_cleantalk_spamscan_logs"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_spamscan_logs]
TABLE "wp_cleantalk_ua_bl"______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_ua_bl]
TABLE "wp_cleantalk_wc_spam_orders"_____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_cleantalk_wc_spam_orders]
TABLE "wp_commentmeta"__________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_commentmeta]
TABLE "wp_comments"_____________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_comments]
TABLE "wp_core_sessions"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_core_sessions]
TABLE "wp_duplicator_packages"__________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_duplicator_packages]
TABLE "wp_e_events"_____________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_e_events]
TABLE "wp_e_notes"______________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_e_notes]
TABLE "wp_e_notes_users_relations"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_e_notes_users_relations]
TABLE "wp_e_submissions"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_e_submissions]
TABLE "wp_e_submissions_actions_log"____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_e_submissions_actions_log]
TABLE "wp_e_submissions_values"_________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_e_submissions_values]
TABLE "wp_links"________________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_links]
TABLE "wp_litespeed_url"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_litespeed_url]
TABLE "wp_litespeed_url_file"___________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_litespeed_url_file]
TABLE "wp_lmfwc_activations"____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_lmfwc_activations]
TABLE "wp_lmfwc_api_keys"_______________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_lmfwc_api_keys]
TABLE "wp_lmfwc_generators"_____________________________[ROWS:      30] [EXTRACT|REPLACE] [INST NAME: wp_lmfwc_generators]
TABLE "wp_lmfwc_licenses"_______________________________[ROWS:    1750] [EXTRACT|REPLACE] [INST NAME: wp_lmfwc_licenses]
TABLE "wp_lmfwc_licenses_meta"__________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_lmfwc_licenses_meta]
TABLE "wp_mclean_refs"__________________________________[ROWS:      51] [EXTRACT|REPLACE] [INST NAME: wp_mclean_refs]
TABLE "wp_mclean_scan"__________________________________[ROWS:    6421] [EXTRACT|REPLACE] [INST NAME: wp_mclean_scan]
TABLE "wp_mvx_cust_answers"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mvx_cust_answers]
TABLE "wp_mvx_cust_questions"___________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mvx_cust_questions]
TABLE "wp_mvx_products_map"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mvx_products_map]
TABLE "wp_mvx_shipping_zone_locations"__________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mvx_shipping_zone_locations]
TABLE "wp_mvx_shipping_zone_methods"____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mvx_shipping_zone_methods]
TABLE "wp_mvx_vendor_ledger"____________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_mvx_vendor_ledger]
TABLE "wp_mvx_vendor_orders"____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_mvx_vendor_orders]
TABLE "wp_mvx_visitors_stats"___________________________[ROWS:      10] [EXTRACT|REPLACE] [INST NAME: wp_mvx_visitors_stats]
TABLE "wp_options"______________________________________[ROWS:     924] [EXTRACT|REPLACE] [INST NAME: wp_options]
TABLE "wp_peepso_activities"____________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_peepso_activities]
TABLE "wp_peepso_activity_followers"____________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_peepso_activity_followers]
TABLE "wp_peepso_activity_ranking"______________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_peepso_activity_ranking]
TABLE "wp_peepso_activity_views"________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_peepso_activity_views]
TABLE "wp_peepso_api_rate_limit"________________________[ROWS:      18] [EXTRACT|REPLACE] [INST NAME: wp_peepso_api_rate_limit]
TABLE "wp_peepso_blocks"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_blocks]
TABLE "wp_peepso_brute_force_attempts_logs"_____________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_brute_force_attempts_logs]
TABLE "wp_peepso_friends"_______________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_peepso_friends]
TABLE "wp_peepso_friends_cache"_________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_peepso_friends_cache]
TABLE "wp_peepso_friend_requests"_______________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_peepso_friend_requests]
TABLE "wp_peepso_gdpr_request_data"_____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_gdpr_request_data]
TABLE "wp_peepso_hashtags"______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_hashtags]
TABLE "wp_peepso_likes"_________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_likes]
TABLE "wp_peepso_login_failed_attempts_logs"____________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_login_failed_attempts_logs]
TABLE "wp_peepso_mail_queue"____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_mail_queue]
TABLE "wp_peepso_mayfly"________________________________[ROWS:      16] [EXTRACT|REPLACE] [INST NAME: wp_peepso_mayfly]
TABLE "wp_peepso_message_participants"__________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_peepso_message_participants]
TABLE "wp_peepso_message_recipients"____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_message_recipients]
TABLE "wp_peepso_notifications"_________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_notifications]
TABLE "wp_peepso_notifications_queue_log"_______________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_notifications_queue_log]
TABLE "wp_peepso_photos"________________________________[ROWS:      14] [EXTRACT|REPLACE] [INST NAME: wp_peepso_photos]
TABLE "wp_peepso_photos_album"__________________________[ROWS:      13] [EXTRACT|REPLACE] [INST NAME: wp_peepso_photos_album]
TABLE "wp_peepso_polls_user_answers"____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_polls_user_answers]
TABLE "wp_peepso_reactions"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_reactions]
TABLE "wp_peepso_report"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_report]
TABLE "wp_peepso_revisions"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_revisions]
TABLE "wp_peepso_saved_posts"___________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_saved_posts]
TABLE "wp_peepso_search_ranking"________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_peepso_search_ranking]
TABLE "wp_peepso_search_ranking_totals"_________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_peepso_search_ranking_totals]
TABLE "wp_peepso_users"_________________________________[ROWS:      19] [EXTRACT|REPLACE] [INST NAME: wp_peepso_users]
TABLE "wp_peepso_user_autofriends"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_user_autofriends]
TABLE "wp_peepso_user_followers"________________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_peepso_user_followers]
TABLE "wp_peepso_videos"________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_peepso_videos]
TABLE "wp_pmpro_discount_codes"_________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_discount_codes]
TABLE "wp_pmpro_discount_codes_levels"__________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_discount_codes_levels]
TABLE "wp_pmpro_discount_codes_uses"____________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_discount_codes_uses]
TABLE "wp_pmpro_memberships_categories"_________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_memberships_categories]
TABLE "wp_pmpro_memberships_pages"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_memberships_pages]
TABLE "wp_pmpro_memberships_users"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_memberships_users]
TABLE "wp_pmpro_membership_levelmeta"___________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_membership_levelmeta]
TABLE "wp_pmpro_membership_levels"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_membership_levels]
TABLE "wp_pmpro_membership_ordermeta"___________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_membership_ordermeta]
TABLE "wp_pmpro_membership_orders"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_pmpro_membership_orders]
TABLE "wp_postmeta"_____________________________________[ROWS:  182234] [EXTRACT|REPLACE] [INST NAME: wp_postmeta]
TABLE "wp_posts"________________________________________[ROWS:   28989] [EXTRACT|REPLACE] [INST NAME: wp_posts]
TABLE "wp_realmedialibrary"_____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_realmedialibrary]
TABLE "wp_realmedialibrary_meta"________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_realmedialibrary_meta]
TABLE "wp_realmedialibrary_posts"_______________________[ROWS:     624] [EXTRACT|REPLACE] [INST NAME: wp_realmedialibrary_posts]
TABLE "wp_realmedialibrary_tmp"_________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_realmedialibrary_tmp]
TABLE "wp_signups"______________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_signups]
TABLE "wp_termmeta"_____________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_termmeta]
TABLE "wp_terms"________________________________________[ROWS:     119] [EXTRACT|REPLACE] [INST NAME: wp_terms]
TABLE "wp_term_relationships"___________________________[ROWS:     395] [EXTRACT|REPLACE] [INST NAME: wp_term_relationships]
TABLE "wp_term_taxonomy"________________________________[ROWS:     119] [EXTRACT|REPLACE] [INST NAME: wp_term_taxonomy]
TABLE "wp_usermeta"_____________________________________[ROWS:   32961] [EXTRACT|REPLACE] [INST NAME: wp_usermeta]
TABLE "wp_users"________________________________________[ROWS:    1837] [EXTRACT|REPLACE] [INST NAME: wp_users]
TABLE "wp_wc_admin_notes"_______________________________[ROWS:     168] [EXTRACT|REPLACE] [INST NAME: wp_wc_admin_notes]
TABLE "wp_wc_admin_note_actions"________________________[ROWS:     208] [EXTRACT|REPLACE] [INST NAME: wp_wc_admin_note_actions]
TABLE "wp_wc_category_lookup"___________________________[ROWS:      14] [EXTRACT|REPLACE] [INST NAME: wp_wc_category_lookup]
TABLE "wp_wc_customer_lookup"___________________________[ROWS:     257] [EXTRACT|REPLACE] [INST NAME: wp_wc_customer_lookup]
TABLE "wp_wc_download_log"______________________________[ROWS:     927] [EXTRACT|REPLACE] [INST NAME: wp_wc_download_log]
TABLE "wp_wc_order_coupon_lookup"_______________________[ROWS:      52] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_coupon_lookup]
TABLE "wp_wc_order_product_lookup"______________________[ROWS:     485] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_product_lookup]
TABLE "wp_wc_order_stats"_______________________________[ROWS:     448] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_stats]
TABLE "wp_wc_order_tax_lookup"__________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_order_tax_lookup]
TABLE "wp_wc_product_attributes_lookup"_________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_product_attributes_lookup]
TABLE "wp_wc_product_download_directories"______________[ROWS:      13] [EXTRACT|REPLACE] [INST NAME: wp_wc_product_download_directories]
TABLE "wp_wc_product_meta_lookup"_______________________[ROWS:     121] [EXTRACT|REPLACE] [INST NAME: wp_wc_product_meta_lookup]
TABLE "wp_wc_rate_limits"_______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_rate_limits]
TABLE "wp_wc_reserved_stock"____________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_reserved_stock]
TABLE "wp_wc_tax_rate_classes"__________________________[ROWS:       6] [EXTRACT|REPLACE] [INST NAME: wp_wc_tax_rate_classes]
TABLE "wp_wc_webhooks"__________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wc_webhooks]
TABLE "wp_woocommerce_api_keys"_________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_api_keys]
TABLE "wp_woocommerce_attribute_taxonomies"_____________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_attribute_taxonomies]
TABLE "wp_woocommerce_downloadable_product_permissions"_[ROWS:     588] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_downloadable_product_permissions]
TABLE "wp_woocommerce_log"______________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_log]
TABLE "wp_woocommerce_order_itemmeta"___________________[ROWS:    5186] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_order_itemmeta]
TABLE "wp_woocommerce_order_items"______________________[ROWS:     612] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_order_items]
TABLE "wp_woocommerce_payment_tokenmeta"________________[ROWS:      16] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_payment_tokenmeta]
TABLE "wp_woocommerce_payment_tokens"___________________[ROWS:       4] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_payment_tokens]
TABLE "wp_woocommerce_sessions"_________________________[ROWS:      39] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_sessions]
TABLE "wp_woocommerce_shipping_zones"___________________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_shipping_zones]
TABLE "wp_woocommerce_shipping_zone_locations"__________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_shipping_zone_locations]
TABLE "wp_woocommerce_shipping_zone_methods"____________[ROWS:       2] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_shipping_zone_methods]
TABLE "wp_woocommerce_tax_rates"________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_tax_rates]
TABLE "wp_woocommerce_tax_rate_locations"_______________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_woocommerce_tax_rate_locations]
TABLE "wp_wpfm_backup"__________________________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wpfm_backup]
TABLE "wp_wpmailsmtp_debug_events"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wpmailsmtp_debug_events]
TABLE "wp_wpmailsmtp_emails_queue"______________________[ROWS:       0] [EXTRACT|REPLACE] [INST NAME: wp_wpmailsmtp_emails_queue]
TABLE "wp_wpmailsmtp_tasks_meta"________________________[ROWS:       1] [EXTRACT|REPLACE] [INST NAME: wp_wpmailsmtp_tasks_meta]
********************************************************************************

--------------------------------------
DATABASE-ENVIRONMENT
--------------------------------------
MYSQL VERSION:	This Server: 10.4.32 -- Build Server: 10.11.10
FILE SIZE:	dup-database__[HASH].sql (61.65MB)
TIMEOUT:	5000
MAXPACK:	1048576
SQLMODE-GLOBAL:	NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION
SQLMODE-SESSION:NO_AUTO_VALUE_ON_ZERO
DROP ALL TABLES
--------------------------------------
DATABASE RESULTS
--------------------------------------
QUERY FIXES
GLOBAL RULES ADDED: PROC AND VIEWS
GLOBAL RULES ADDED: INVALID CHARSETS
GLOBAL RULES ADDED: INVALID COLLATIONS

QUERY FIXES GLOBAL RULES
	SEARCH  => /^(\s*(?:\/\*!\d+\s)?\s*(?:CREATE.+)?DEFINER\s*=)([^\*\s]+)(.*)$/m
	REPLACE => $1`comva_user`@`localhost`$3

	SEARCH  => /^(\s*CREATE.+(?:PROCEDURE|FUNCTION)[\s\S]*)(BEGIN)([\s\S]*)$/
	REPLACE => $1SQL SECURITY INVOKER
$2$3

	SEARCH  => /(^.*(?:CHARSET|CHARACTER SET)\s*[\s=]\s*[`'"]?)((?:utf8mb3))([`'"]?\s.*COLLATE\s*[\s=]\s*[`'"]?)([^`'"\s;,]+)([`'"]?.*$)/m
	REPLACE => $1utf8$3utf8_general_ci$5

	SEARCH  => /(^.*COLLATE\s*[\s=]\s*[`'"]?)([^`'"\s;,]+)([`'"]?\s.*(?:CHARSET|CHARACTER SET)\s*[\s=]\s*[`'"]?)((?:utf8mb3))([`'"]?[\s;,].*$)/m
	REPLACE => $1utf8_general_ci$3utf8$5

	SEARCH  => /(^.*(?:CHARSET|CHARACTER SET)\s*[\s=]\s*[`'"]?)((?:utf8mb3))([`'"]?[\s;,].*$)/m
	REPLACE => $1utf8$3

	SEARCH  => /(^.*(?:CHARSET|CHARACTER SET)\s*[\s=]\s*[`'"]?)([^`'"\s;,]+)([`'"]?\s.*COLLATE\s*[\s=]\s*[`'"]?)((?:utf8mb3_general_ci|utf8mb3_unicode_ci))([`'"]?[\s;,].*$)/m
	REPLACE => $1utf8$3utf8_general_ci$5

	SEARCH  => /(^.*COLLATE\s*[\s=]\s*[`'"]?)((?:utf8mb3_general_ci|utf8mb3_unicode_ci))([`'"]?\s.*(?:CHARSET|CHARACTER SET)\s*[\s=]\s*[`'"]?)([^`'"\s;,]+)([`'"]?.*$)/m
	REPLACE => $1utf8_general_ci$3utf8$5

	SEARCH  => /(^.*COLLATE\s*[\s=]\s*[`'"]?)((?:utf8mb3_general_ci|utf8mb3_unicode_ci))([`'"]?[\s;,].*$)/m
	REPLACE => $1utf8_general_ci$3

--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 0
Auto Commit set to false successfully
NO TABLE TO SKIP
SET DELIMITER ;; AND SKIP QUERY
SET DELIMITER ; AND SKIP QUERY
DATABASE CHUNK: CREATION TABLE MARKER FOUND
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 1665213
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 2823460
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 3954483
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 4688368
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 5350927
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 6601726
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 8577407
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 11598930
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 12544904
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 13961162
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 14543599
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 16280113
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 16886277
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 18071512
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 19060696
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 19896664
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 22874967
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 27117993
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 28562712
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 30108899
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 31713609
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 32658422
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 34284916
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 35123633
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 36134398
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 36715196
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 37294732
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 38852005
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 39811427
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 40381216
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 46751185
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 50761539
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 53150758
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 55875616
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 58052310
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 59249849
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 60330097
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 61546573
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 62408741
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 63420864
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [dbinstall] START
--------------------------------------
** DATABASE CHUNK install start
--------------------------------------
DATABASE CHUNK SEEK POSITION: 64338116
Auto Commit set to false successfully
NO TABLE TO SKIP
Auto Commit set to true successfully
--------------------------------------
** DATABASE CHUNK install end
--------------------------------------
ERRORS FOUND:	0
DROPPED TABLES:	0
RENAMED TABLES:	0
QUERIES RAN:	305548

TABLES ROWS IN DATABASE AFTER EXTRACTION

TABLE "wp_actionscheduler_actions"______________________[ROWS:  3067]
TABLE "wp_actionscheduler_claims"_______________________[ROWS:     1]
TABLE "wp_actionscheduler_groups"_______________________[ROWS:    10]
TABLE "wp_actionscheduler_logs"_________________________[ROWS:  9194]
TABLE "wp_addonlibrary_addons"__________________________[ROWS:     9]
TABLE "wp_addonlibrary_categories"______________________[ROWS:     8]
TABLE "wp_bm_guests"____________________________________[ROWS:     1]
TABLE "wp_bm_mentions"__________________________________[ROWS:     0]
TABLE "wp_bm_message_messages"__________________________[ROWS:    58]
TABLE "wp_bm_message_meta"______________________________[ROWS:   235]
TABLE "wp_bm_message_recipients"________________________[ROWS:    24]
TABLE "wp_bm_moderation"________________________________[ROWS:     0]
TABLE "wp_bm_thread_meta"_______________________________[ROWS:    16]
TABLE "wp_bm_threads"___________________________________[ROWS:    12]
TABLE "wp_bm_user_index"________________________________[ROWS:  2411]
TABLE "wp_bm_user_roles_index"__________________________[ROWS:  3078]
TABLE "wp_bp_activity"__________________________________[ROWS:    19]
TABLE "wp_bp_activity_meta"_____________________________[ROWS:     8]
TABLE "wp_bp_friends"___________________________________[ROWS:     0]
TABLE "wp_bp_groups"____________________________________[ROWS:     0]
TABLE "wp_bp_groups_groupmeta"__________________________[ROWS:     0]
TABLE "wp_bp_groups_members"____________________________[ROWS:     0]
TABLE "wp_bp_invitations"_______________________________[ROWS:     0]
TABLE "wp_bp_messages_messages"_________________________[ROWS:     0]
TABLE "wp_bp_messages_meta"_____________________________[ROWS:     0]
TABLE "wp_bp_messages_notices"__________________________[ROWS:     0]
TABLE "wp_bp_messages_recipients"_______________________[ROWS:     0]
TABLE "wp_bp_notifications"_____________________________[ROWS:    31]
TABLE "wp_bp_notifications_meta"________________________[ROWS:    31]
TABLE "wp_bp_optouts"___________________________________[ROWS:     0]
TABLE "wp_bp_user_blogs"________________________________[ROWS:     2]
TABLE "wp_bp_user_blogs_blogmeta"_______________________[ROWS:    10]
TABLE "wp_bp_xprofile_data"_____________________________[ROWS:     1]
TABLE "wp_bp_xprofile_fields"___________________________[ROWS:     1]
TABLE "wp_bp_xprofile_groups"___________________________[ROWS:     1]
TABLE "wp_bp_xprofile_meta"_____________________________[ROWS:     2]
TABLE "wp_bv_fw_requests"_______________________________[ROWS:     0]
TABLE "wp_bv_ip_store"__________________________________[ROWS:  5187]
TABLE "wp_bv_lp_requests"_______________________________[ROWS:     2]
TABLE "wp_cky_banners"__________________________________[ROWS:     2]
TABLE "wp_cky_cookie_categories"________________________[ROWS:     5]
TABLE "wp_cky_cookies"__________________________________[ROWS:     0]
TABLE "wp_cleantalk_ac_log"_____________________________[ROWS:     0]
TABLE "wp_cleantalk_connection_reports"_________________[ROWS:     0]
TABLE "wp_cleantalk_no_cookie_data"_____________________[ROWS:     0]
TABLE "wp_cleantalk_sessions"___________________________[ROWS:   440]
TABLE "wp_cleantalk_sfw"________________________________[ROWS: 15748]
TABLE "wp_cleantalk_sfw_logs"___________________________[ROWS:     0]
TABLE "wp_cleantalk_sfw_personal"_______________________[ROWS:     2]
TABLE "wp_cleantalk_spamscan_logs"______________________[ROWS:     0]
TABLE "wp_cleantalk_ua_bl"______________________________[ROWS:     0]
TABLE "wp_cleantalk_wc_spam_orders"_____________________[ROWS:     0]
TABLE "wp_commentmeta"__________________________________[ROWS:     0]
TABLE "wp_comments"_____________________________________[ROWS:     0]
TABLE "wp_core_sessions"________________________________[ROWS:     0]
TABLE "wp_duplicator_packages"__________________________[ROWS:     1]
TABLE "wp_e_events"_____________________________________[ROWS:     0]
TABLE "wp_e_notes"______________________________________[ROWS:     0]
TABLE "wp_e_notes_users_relations"______________________[ROWS:     0]
TABLE "wp_e_submissions"________________________________[ROWS:     0]
TABLE "wp_e_submissions_actions_log"____________________[ROWS:     0]
TABLE "wp_e_submissions_values"_________________________[ROWS:     0]
TABLE "wp_links"________________________________________[ROWS:     0]
TABLE "wp_litespeed_url"________________________________[ROWS:     0]
TABLE "wp_litespeed_url_file"___________________________[ROWS:     0]
TABLE "wp_lmfwc_activations"____________________________[ROWS:     0]
TABLE "wp_lmfwc_api_keys"_______________________________[ROWS:     2]
TABLE "wp_lmfwc_generators"_____________________________[ROWS:    30]
TABLE "wp_lmfwc_licenses"_______________________________[ROWS:  1750]
TABLE "wp_lmfwc_licenses_meta"__________________________[ROWS:     0]
TABLE "wp_mclean_refs"__________________________________[ROWS:    51]
TABLE "wp_mclean_scan"__________________________________[ROWS:  6421]
TABLE "wp_mvx_cust_answers"_____________________________[ROWS:     0]
TABLE "wp_mvx_cust_questions"___________________________[ROWS:     0]
TABLE "wp_mvx_products_map"_____________________________[ROWS:     0]
TABLE "wp_mvx_shipping_zone_locations"__________________[ROWS:     0]
TABLE "wp_mvx_shipping_zone_methods"____________________[ROWS:     0]
TABLE "wp_mvx_vendor_ledger"____________________________[ROWS:     1]
TABLE "wp_mvx_vendor_orders"____________________________[ROWS:     0]
TABLE "wp_mvx_visitors_stats"___________________________[ROWS:    10]
TABLE "wp_options"______________________________________[ROWS:   924]
TABLE "wp_peepso_activities"____________________________[ROWS:     1]
TABLE "wp_peepso_activity_followers"____________________[ROWS:     1]
TABLE "wp_peepso_activity_ranking"______________________[ROWS:     1]
TABLE "wp_peepso_activity_views"________________________[ROWS:     1]
TABLE "wp_peepso_api_rate_limit"________________________[ROWS:    18]
TABLE "wp_peepso_blocks"________________________________[ROWS:     0]
TABLE "wp_peepso_brute_force_attempts_logs"_____________[ROWS:     0]
TABLE "wp_peepso_friend_requests"_______________________[ROWS:     1]
TABLE "wp_peepso_friends"_______________________________[ROWS:     1]
TABLE "wp_peepso_friends_cache"_________________________[ROWS:     2]
TABLE "wp_peepso_gdpr_request_data"_____________________[ROWS:     0]
TABLE "wp_peepso_hashtags"______________________________[ROWS:     0]
TABLE "wp_peepso_likes"_________________________________[ROWS:     0]
TABLE "wp_peepso_login_failed_attempts_logs"____________[ROWS:     0]
TABLE "wp_peepso_mail_queue"____________________________[ROWS:     0]
TABLE "wp_peepso_mayfly"________________________________[ROWS:    16]
TABLE "wp_peepso_message_participants"__________________[ROWS:     2]
TABLE "wp_peepso_message_recipients"____________________[ROWS:     0]
TABLE "wp_peepso_notifications"_________________________[ROWS:     0]
TABLE "wp_peepso_notifications_queue_log"_______________[ROWS:     0]
TABLE "wp_peepso_photos"________________________________[ROWS:    14]
TABLE "wp_peepso_photos_album"__________________________[ROWS:    13]
TABLE "wp_peepso_polls_user_answers"____________________[ROWS:     0]
TABLE "wp_peepso_reactions"_____________________________[ROWS:     0]
TABLE "wp_peepso_report"________________________________[ROWS:     0]
TABLE "wp_peepso_revisions"_____________________________[ROWS:     0]
TABLE "wp_peepso_saved_posts"___________________________[ROWS:     0]
TABLE "wp_peepso_search_ranking"________________________[ROWS:     2]
TABLE "wp_peepso_search_ranking_totals"_________________[ROWS:     2]
TABLE "wp_peepso_user_autofriends"______________________[ROWS:     0]
TABLE "wp_peepso_user_followers"________________________[ROWS:     2]
TABLE "wp_peepso_users"_________________________________[ROWS:    19]
TABLE "wp_peepso_videos"________________________________[ROWS:     0]
TABLE "wp_pmpro_discount_codes"_________________________[ROWS:     0]
TABLE "wp_pmpro_discount_codes_levels"__________________[ROWS:     0]
TABLE "wp_pmpro_discount_codes_uses"____________________[ROWS:     0]
TABLE "wp_pmpro_membership_levelmeta"___________________[ROWS:     0]
TABLE "wp_pmpro_membership_levels"______________________[ROWS:     0]
TABLE "wp_pmpro_membership_ordermeta"___________________[ROWS:     0]
TABLE "wp_pmpro_membership_orders"______________________[ROWS:     0]
TABLE "wp_pmpro_memberships_categories"_________________[ROWS:     0]
TABLE "wp_pmpro_memberships_pages"______________________[ROWS:     0]
TABLE "wp_pmpro_memberships_users"______________________[ROWS:     0]
TABLE "wp_postmeta"_____________________________________[ROWS:182234]
TABLE "wp_posts"________________________________________[ROWS: 28989]
TABLE "wp_realmedialibrary"_____________________________[ROWS:     0]
TABLE "wp_realmedialibrary_meta"________________________[ROWS:     0]
TABLE "wp_realmedialibrary_posts"_______________________[ROWS:   624]
TABLE "wp_realmedialibrary_tmp"_________________________[ROWS:     0]
TABLE "wp_signups"______________________________________[ROWS:     0]
TABLE "wp_term_relationships"___________________________[ROWS:   395]
TABLE "wp_term_taxonomy"________________________________[ROWS:   119]
TABLE "wp_termmeta"_____________________________________[ROWS:     0]
TABLE "wp_terms"________________________________________[ROWS:   119]
TABLE "wp_usermeta"_____________________________________[ROWS: 32961]
TABLE "wp_users"________________________________________[ROWS:  1837]
TABLE "wp_wc_admin_note_actions"________________________[ROWS:   208]
TABLE "wp_wc_admin_notes"_______________________________[ROWS:   168]
TABLE "wp_wc_category_lookup"___________________________[ROWS:    14]
TABLE "wp_wc_customer_lookup"___________________________[ROWS:   257]
TABLE "wp_wc_download_log"______________________________[ROWS:   927]
TABLE "wp_wc_order_coupon_lookup"_______________________[ROWS:    52]
TABLE "wp_wc_order_product_lookup"______________________[ROWS:   485]
TABLE "wp_wc_order_stats"_______________________________[ROWS:   448]
TABLE "wp_wc_order_tax_lookup"__________________________[ROWS:     0]
TABLE "wp_wc_product_attributes_lookup"_________________[ROWS:     0]
TABLE "wp_wc_product_download_directories"______________[ROWS:    13]
TABLE "wp_wc_product_meta_lookup"_______________________[ROWS:   121]
TABLE "wp_wc_rate_limits"_______________________________[ROWS:     0]
TABLE "wp_wc_reserved_stock"____________________________[ROWS:     0]
TABLE "wp_wc_tax_rate_classes"__________________________[ROWS:     6]
TABLE "wp_wc_webhooks"__________________________________[ROWS:     0]
TABLE "wp_woocommerce_api_keys"_________________________[ROWS:     1]
TABLE "wp_woocommerce_attribute_taxonomies"_____________[ROWS:     1]
TABLE "wp_woocommerce_downloadable_product_permissions"_[ROWS:   588]
TABLE "wp_woocommerce_log"______________________________[ROWS:     0]
TABLE "wp_woocommerce_order_itemmeta"___________________[ROWS:  5186]
TABLE "wp_woocommerce_order_items"______________________[ROWS:   612]
TABLE "wp_woocommerce_payment_tokenmeta"________________[ROWS:    16]
TABLE "wp_woocommerce_payment_tokens"___________________[ROWS:     4]
TABLE "wp_woocommerce_sessions"_________________________[ROWS:    39]
TABLE "wp_woocommerce_shipping_zone_locations"__________[ROWS:     1]
TABLE "wp_woocommerce_shipping_zone_methods"____________[ROWS:     2]
TABLE "wp_woocommerce_shipping_zones"___________________[ROWS:     2]
TABLE "wp_woocommerce_tax_rate_locations"_______________[ROWS:     0]
TABLE "wp_woocommerce_tax_rates"________________________[ROWS:     0]
TABLE "wp_wpfm_backup"__________________________________[ROWS:     0]
TABLE "wp_wpmailsmtp_debug_events"______________________[ROWS:     0]
TABLE "wp_wpmailsmtp_emails_queue"______________________[ROWS:     0]
TABLE "wp_wpmailsmtp_tasks_meta"________________________[ROWS:     1]

INSERT DATA RUNTIME: 211.9204 sec.
STEP-2 COMPLETE @ 03:18:21 - RUNTIME: 211.9207 sec.
AJAX ACTION [dbinstall] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [webupdate] START

====================================
SET SEARCH AND REPLACE LIST INSTALL TYPE single site
====================================
SEARCH ITEM[T:path |P:12] SEARCH: /home/<USER>/domains/comva.co.uk/public_html REPLACE: C:/xampp/htdocs/comva [SCOPE: ALL]
SEARCH ITEM[T:urlnd|P:12] SEARCH: https://comva.co.uk REPLACE: http://localhost/comva [SCOPE: ALL]
CHUNK LOAD DATA: IS NULL 
CHUNK ACTION: CURRENT [start][][]


********************************************************************************
DUPLICATOR LITE: INSTALL-LOG
STEP-3 START @ 03:18:22
NOTICE: Do NOT post to public sites or forums
********************************************************************************
CHARSET SERVER:	"utf8"
CHARSET CLIENT:	"utf8"
********************************************************************************
OPTIONS:
SKIP PATH REPLACE_____: false
DISALLOW_FILE_EDIT____: [value = ], [inWpConfig = ]
DISALLOW_FILE_MODS____: [value = ], [inWpConfig = ]
AUTOSAVE_INTERVAL_____: [value = 60], [inWpConfig = ]
WP_POST_REVISIONS_____: [value = 1], [inWpConfig = ]
FORCE_SSL_ADMIN_______: [value = 1], [inWpConfig = ]
WP_AUTO_UPDATE_CORE___: [value = minor], [inWpConfig = 1]
WP_CACHE______________: [value = 1], [inWpConfig = 1]
WPCACHEHOME___________: [value = /], [inWpConfig = ]
WP_DEBUG______________: [value = ], [inWpConfig = 1]
WP_DEBUG_LOG__________: [value = ], [inWpConfig = ]
WP_DEBUG_DISPLAY______: [value = 1], [inWpConfig = ]
WP_DISABLE_FATAL_ERROR_HANDLER: [value = ], [inWpConfig = ]
SCRIPT_DEBUG__________: [value = ], [inWpConfig = ]
CONCATENATE_SCRIPTS___: [value = ], [inWpConfig = ]
SAVEQUERIES___________: [value = ], [inWpConfig = ]
ALTERNATE_WP_CRON_____: [value = ], [inWpConfig = ]
DISABLE_WP_CRON_______: [value = ], [inWpConfig = ]
WP_CRON_LOCK_TIMEOUT__: [value = 60], [inWpConfig = ]
COOKIE_DOMAIN_________: [value = ], [inWpConfig = ]
WP_MEMORY_LIMIT_______: [value = 40M], [inWpConfig = ]
WP_MAX_MEMORY_LIMIT___: [value = 2048M], [inWpConfig = ]
WP_TEMP_DIR___________: [value = ], [inWpConfig = ]
********************************************************************************

********************************************************************************
CHUNK PARAMS:
maxIteration__________: 0
timeOut_______________: 5000
throttling____________: 2
rowsPerPage___________: 1000
********************************************************************************

CHUNK ACTION: CURRENT [cleanup_trans][][]
[PHP ERR][E_WARNING] MSG:foreach() argument must be of type array|object, string given [CODE:2|FILE:C:\xampp\htdocs\comva\dup-installer\src\Core\Deploy\Database\DbCleanup.php|LINE:95]
CLEAN OPTIONS [wp_options]
	`option_name` = "duplicator_plugin_data_stats"
	`option_name` LIKE "\_transient%"
	`option_name` LIKE "\_site\_transient%"
DATABASE OPTIONS DELETED [ROWS:    28]
CHUNK ACTION: CURRENT [cleanup_extra][][]
CLEANUP EXTRA
	- SKIP DROP VIEWS
	- SKIP DROP PROCS
	- SKIP DROP FUNCS
CHUNK ACTION: CURRENT [cleanup_packages][][]
EMPTY PACKAGES TABLE
CLEAN PACKAGES
DATABASE PACKAGE DELETED [ROWS:     1]
CHUNK ACTION: CURRENT [init][][]

EVALUATE TABLE: "wp_actionscheduler_actions"______________________[ROWS:  3067][PG:   4][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][0]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][1]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][2]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_actions][3]

EVALUATE TABLE: "wp_actionscheduler_claims"_______________________[ROWS:     1][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_actionscheduler_groups"_______________________[ROWS:    10][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_groups][0]

EVALUATE TABLE: "wp_actionscheduler_logs"_________________________[ROWS:  9194][PG:  10][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][0]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][1]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][2]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][3]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][4]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][5]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][6]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][7]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][8]
	CHUNK ACTION: CURRENT [search_replace][wp_actionscheduler_logs][9]

EVALUATE TABLE: "wp_addonlibrary_addons"__________________________[ROWS:     9][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_addonlibrary_addons][0]

EVALUATE TABLE: "wp_addonlibrary_categories"______________________[ROWS:     8][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_addonlibrary_categories][0]

EVALUATE TABLE: "wp_bm_guests"____________________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bm_guests][0]

EVALUATE TABLE: "wp_bm_mentions"__________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bm_message_messages"__________________________[ROWS:    58][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bm_message_messages][0]

EVALUATE TABLE: "wp_bm_message_meta"______________________________[ROWS:   235][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bm_message_meta][0]

EVALUATE TABLE: "wp_bm_message_recipients"________________________[ROWS:    24][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_bm_moderation"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bm_threads"___________________________________[ROWS:    12][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bm_threads][0]

EVALUATE TABLE: "wp_bm_thread_meta"_______________________________[ROWS:    16][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bm_thread_meta][0]

EVALUATE TABLE: "wp_bm_user_index"________________________________[ROWS:  2411][PG:   3][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bm_user_index][0]
	CHUNK ACTION: CURRENT [search_replace][wp_bm_user_index][1]
	CHUNK ACTION: CURRENT [search_replace][wp_bm_user_index][2]

EVALUATE TABLE: "wp_bm_user_roles_index"__________________________[ROWS:  3078][PG:   4][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bm_user_roles_index][0]
	CHUNK ACTION: CURRENT [search_replace][wp_bm_user_roles_index][1]
	CHUNK ACTION: CURRENT [search_replace][wp_bm_user_roles_index][2]
	CHUNK ACTION: CURRENT [search_replace][wp_bm_user_roles_index][3]

EVALUATE TABLE: "wp_bp_activity"__________________________________[ROWS:    19][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_activity][0]

EVALUATE TABLE: "wp_bp_activity_meta"_____________________________[ROWS:     8][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_activity_meta][0]

EVALUATE TABLE: "wp_bp_friends"___________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_groups"____________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_groups_groupmeta"__________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_groups_members"____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_invitations"_______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_messages_messages"_________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_messages_meta"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_messages_notices"__________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_messages_recipients"_______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_notifications"_____________________________[ROWS:    31][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_notifications][0]

EVALUATE TABLE: "wp_bp_notifications_meta"________________________[ROWS:    31][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_notifications_meta][0]

EVALUATE TABLE: "wp_bp_optouts"___________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_user_blogs"________________________________[ROWS:     2][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_bp_user_blogs_blogmeta"_______________________[ROWS:    10][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_user_blogs_blogmeta][0]

EVALUATE TABLE: "wp_bp_xprofile_data"_____________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_xprofile_data][0]

EVALUATE TABLE: "wp_bp_xprofile_fields"___________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_xprofile_fields][0]

EVALUATE TABLE: "wp_bp_xprofile_groups"___________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_xprofile_groups][0]

EVALUATE TABLE: "wp_bp_xprofile_meta"_____________________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bp_xprofile_meta][0]

EVALUATE TABLE: "wp_bv_fw_requests"_______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_bv_ip_store"__________________________________[ROWS:  5187][PG:   6][SCAN:no columns  ]

EVALUATE TABLE: "wp_bv_lp_requests"_______________________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_bv_lp_requests][0]

EVALUATE TABLE: "wp_cky_banners"__________________________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_cky_banners][0]

EVALUATE TABLE: "wp_cky_cookies"__________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_cky_cookie_categories"________________________[ROWS:     5][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_cky_cookie_categories][0]

EVALUATE TABLE: "wp_cleantalk_ac_log"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_connection_reports"_________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_no_cookie_data"_____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_sessions"___________________________[ROWS:   440][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_cleantalk_sessions][0]

EVALUATE TABLE: "wp_cleantalk_sfw"________________________________[ROWS: 15748][PG:  16][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_sfw_logs"___________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_sfw_personal"_______________________[ROWS:     2][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_spamscan_logs"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_ua_bl"______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_cleantalk_wc_spam_orders"_____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_commentmeta"__________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_comments"_____________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_core_sessions"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_duplicator_packages"__________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_e_events"_____________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_e_notes"______________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_e_notes_users_relations"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_e_submissions"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_e_submissions_actions_log"____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_e_submissions_values"_________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_links"________________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_litespeed_url"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_litespeed_url_file"___________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_lmfwc_activations"____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_lmfwc_api_keys"_______________________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_lmfwc_api_keys][0]

EVALUATE TABLE: "wp_lmfwc_generators"_____________________________[ROWS:    30][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_lmfwc_generators][0]

EVALUATE TABLE: "wp_lmfwc_licenses"_______________________________[ROWS:  1750][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_lmfwc_licenses][0]
	CHUNK ACTION: CURRENT [search_replace][wp_lmfwc_licenses][1]

EVALUATE TABLE: "wp_lmfwc_licenses_meta"__________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mclean_refs"__________________________________[ROWS:    51][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_refs][0]

EVALUATE TABLE: "wp_mclean_scan"__________________________________[ROWS:  6421][PG:   7][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][0]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][1]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][2]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][3]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][4]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][5]
	CHUNK ACTION: CURRENT [search_replace][wp_mclean_scan][6]

EVALUATE TABLE: "wp_mvx_cust_answers"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mvx_cust_questions"___________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mvx_products_map"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mvx_shipping_zone_locations"__________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mvx_shipping_zone_methods"____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mvx_vendor_ledger"____________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_mvx_vendor_ledger][0]

EVALUATE TABLE: "wp_mvx_vendor_orders"____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_mvx_visitors_stats"___________________________[ROWS:    10][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_mvx_visitors_stats][0]

EVALUATE TABLE: "wp_options"______________________________________[ROWS:   896][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_options][0]

EVALUATE TABLE: "wp_peepso_activities"____________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_activities][0]

EVALUATE TABLE: "wp_peepso_activity_followers"____________________[ROWS:     1][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_activity_ranking"______________________[ROWS:     1][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_activity_views"________________________[ROWS:     1][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_api_rate_limit"________________________[ROWS:    18][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_api_rate_limit][0]

EVALUATE TABLE: "wp_peepso_blocks"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_brute_force_attempts_logs"_____________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_friends"_______________________________[ROWS:     1][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_friends_cache"_________________________[ROWS:     2][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_friend_requests"_______________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_friend_requests][0]

EVALUATE TABLE: "wp_peepso_gdpr_request_data"_____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_hashtags"______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_likes"_________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_login_failed_attempts_logs"____________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_mail_queue"____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_mayfly"________________________________[ROWS:    16][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_mayfly][0]

EVALUATE TABLE: "wp_peepso_message_participants"__________________[ROWS:     2][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_message_recipients"____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_notifications"_________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_notifications_queue_log"_______________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_photos"________________________________[ROWS:    14][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_photos][0]

EVALUATE TABLE: "wp_peepso_photos_album"__________________________[ROWS:    13][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_photos_album][0]

EVALUATE TABLE: "wp_peepso_polls_user_answers"____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_reactions"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_report"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_revisions"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_saved_posts"___________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_search_ranking"________________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_search_ranking][0]

EVALUATE TABLE: "wp_peepso_search_ranking_totals"_________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_search_ranking_totals][0]

EVALUATE TABLE: "wp_peepso_users"_________________________________[ROWS:    19][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_peepso_users][0]

EVALUATE TABLE: "wp_peepso_user_autofriends"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_user_followers"________________________[ROWS:     2][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_peepso_videos"________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_discount_codes"_________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_discount_codes_levels"__________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_discount_codes_uses"____________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_memberships_categories"_________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_memberships_pages"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_memberships_users"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_membership_levelmeta"___________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_membership_levels"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_membership_ordermeta"___________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_pmpro_membership_orders"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_postmeta"_____________________________________[ROWS:182234][PG: 183][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][1]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][2]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][3]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][4]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][5]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][6]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][7]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][8]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][9]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][10]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][11]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][12]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][13]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][14]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][15]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][16]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][17]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][18]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][19]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][20]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][21]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][22]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][23]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][24]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][25]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][26]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][27]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][28]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][29]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][30]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][31]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][32]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][33]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][34]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][35]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][36]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][37]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][38]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][39]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][40]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][41]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][42]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][43]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][44]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][45]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][46]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][47]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][48]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][49]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][50]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][51]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][52]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][53]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][54]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][55]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][56]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][57]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][58]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][59]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][60]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][61]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][62]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][63]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][64]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][65]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][66]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][67]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][68]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][69]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][70]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][71]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][72]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][73]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][74]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][75]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][76]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][77]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][78]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][79]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][80]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][81]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][82]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][83]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][84]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][85]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][86]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][87]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][88]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][89]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][90]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][91]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][92]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][93]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][94]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][95]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][96]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][97]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][98]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][99]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][100]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][101]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][102]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][103]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][104]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][105]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][106]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][107]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][108]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][109]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][110]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][111]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][112]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][113]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][114]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][115]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][116]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][117]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][118]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][119]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][120]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][121]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][122]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][123]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][124]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][125]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][126]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][127]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][128]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][129]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][130]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][131]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][132]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][133]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][134]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][135]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][136]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][137]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][138]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][139]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][140]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][141]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][142]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][143]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][144]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][145]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][146]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][147]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][148]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][149]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][150]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][151]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][152]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][153]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][154]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][155]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][156]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][157]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][158]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][159]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][160]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][161]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][162]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][163]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][164]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][165]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][166]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][167]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][168]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][169]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][170]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][171]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][172]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][173]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][174]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][175]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][176]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][177]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][178]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][179]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][180]
	CHUNK ACTION: CURRENT [search_replace][wp_postmeta][181]
	
STEP-3 CHUNK STOP @ 03:18:27 - RUNTIME: 5.0125 sec. 


	AJAX ACTION [webupdate] SUCCESS
	-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [webupdate] START
CHUNK ACTION: CURRENT [search_replace][wp_postmeta][182]

EVALUATE TABLE: "wp_posts"________________________________________[ROWS: 28989][PG:  29][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_posts][0]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][1]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][2]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][3]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][4]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][5]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][6]
	CHUNK ACTION: CURRENT [search_replace][wp_posts][7]
	
STEP-3 CHUNK STOP @ 03:18:32 - RUNTIME: 5.0948 sec. 


	AJAX ACTION [webupdate] SUCCESS
	-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [webupdate] START
CHUNK ACTION: CURRENT [search_replace][wp_posts][8]
CHUNK ACTION: CURRENT [search_replace][wp_posts][9]
CHUNK ACTION: CURRENT [search_replace][wp_posts][10]
CHUNK ACTION: CURRENT [search_replace][wp_posts][11]
CHUNK ACTION: CURRENT [search_replace][wp_posts][12]
CHUNK ACTION: CURRENT [search_replace][wp_posts][13]
CHUNK ACTION: CURRENT [search_replace][wp_posts][14]
CHUNK ACTION: CURRENT [search_replace][wp_posts][15]

STEP-3 CHUNK STOP @ 03:18:37 - RUNTIME: 5.2527 sec. 


AJAX ACTION [webupdate] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [webupdate] START
CHUNK ACTION: CURRENT [search_replace][wp_posts][16]
CHUNK ACTION: CURRENT [search_replace][wp_posts][17]
CHUNK ACTION: CURRENT [search_replace][wp_posts][18]
CHUNK ACTION: CURRENT [search_replace][wp_posts][19]
CHUNK ACTION: CURRENT [search_replace][wp_posts][20]
CHUNK ACTION: CURRENT [search_replace][wp_posts][21]
CHUNK ACTION: CURRENT [search_replace][wp_posts][22]
CHUNK ACTION: CURRENT [search_replace][wp_posts][23]

STEP-3 CHUNK STOP @ 03:18:43 - RUNTIME: 5.4158 sec. 


AJAX ACTION [webupdate] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [webupdate] START
CHUNK ACTION: CURRENT [search_replace][wp_posts][24]
CHUNK ACTION: CURRENT [search_replace][wp_posts][25]
CHUNK ACTION: CURRENT [search_replace][wp_posts][26]
CHUNK ACTION: CURRENT [search_replace][wp_posts][27]
CHUNK ACTION: CURRENT [search_replace][wp_posts][28]

EVALUATE TABLE: "wp_realmedialibrary"_____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_realmedialibrary_meta"________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_realmedialibrary_posts"_______________________[ROWS:   624][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_realmedialibrary_posts][0]

EVALUATE TABLE: "wp_realmedialibrary_tmp"_________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_signups"______________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_termmeta"_____________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_terms"________________________________________[ROWS:   119][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_terms][0]

EVALUATE TABLE: "wp_term_relationships"___________________________[ROWS:   395][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_term_taxonomy"________________________________[ROWS:   119][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_term_taxonomy][0]

EVALUATE TABLE: "wp_usermeta"_____________________________________[ROWS: 32961][PG:  33][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][1]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][2]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][3]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][4]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][5]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][6]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][7]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][8]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][9]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][10]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][11]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][12]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][13]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][14]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][15]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][16]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][17]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][18]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][19]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][20]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][21]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][22]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][23]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][24]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][25]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][26]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][27]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][28]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][29]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][30]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][31]
	CHUNK ACTION: CURRENT [search_replace][wp_usermeta][32]

EVALUATE TABLE: "wp_users"________________________________________[ROWS:  1837][PG:   2][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_users][0]
	CHUNK ACTION: CURRENT [search_replace][wp_users][1]

EVALUATE TABLE: "wp_wc_admin_notes"_______________________________[ROWS:   168][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_admin_notes][0]

EVALUATE TABLE: "wp_wc_admin_note_actions"________________________[ROWS:   208][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_admin_note_actions][0]

EVALUATE TABLE: "wp_wc_category_lookup"___________________________[ROWS:    14][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_customer_lookup"___________________________[ROWS:   257][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_customer_lookup][0]

EVALUATE TABLE: "wp_wc_download_log"______________________________[ROWS:   927][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_download_log][0]

EVALUATE TABLE: "wp_wc_order_coupon_lookup"_______________________[ROWS:    52][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_product_lookup"______________________[ROWS:   485][PG:   1][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_order_stats"_______________________________[ROWS:   448][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_order_stats][0]

EVALUATE TABLE: "wp_wc_order_tax_lookup"__________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_product_attributes_lookup"_________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_product_download_directories"______________[ROWS:    13][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_download_directories][0]

EVALUATE TABLE: "wp_wc_product_meta_lookup"_______________________[ROWS:   121][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_product_meta_lookup][0]

EVALUATE TABLE: "wp_wc_rate_limits"_______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_reserved_stock"____________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wc_tax_rate_classes"__________________________[ROWS:     6][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wc_tax_rate_classes][0]

EVALUATE TABLE: "wp_wc_webhooks"__________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_api_keys"_________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_api_keys][0]

EVALUATE TABLE: "wp_woocommerce_attribute_taxonomies"_____________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_attribute_taxonomies][0]

EVALUATE TABLE: "wp_woocommerce_downloadable_product_permissions"_[ROWS:   588][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_downloadable_product_permissions][0]

EVALUATE TABLE: "wp_woocommerce_log"______________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_order_itemmeta"___________________[ROWS:  5186][PG:   6][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][0]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][1]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][2]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][3]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][4]
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_itemmeta][5]

EVALUATE TABLE: "wp_woocommerce_order_items"______________________[ROWS:   612][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_order_items][0]

EVALUATE TABLE: "wp_woocommerce_payment_tokenmeta"________________[ROWS:    16][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_payment_tokenmeta][0]

EVALUATE TABLE: "wp_woocommerce_payment_tokens"___________________[ROWS:     4][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_payment_tokens][0]

EVALUATE TABLE: "wp_woocommerce_sessions"_________________________[ROWS:    39][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_sessions][0]

EVALUATE TABLE: "wp_woocommerce_shipping_zones"___________________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_shipping_zones][0]

EVALUATE TABLE: "wp_woocommerce_shipping_zone_locations"__________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_shipping_zone_locations][0]

EVALUATE TABLE: "wp_woocommerce_shipping_zone_methods"____________[ROWS:     2][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_woocommerce_shipping_zone_methods][0]

EVALUATE TABLE: "wp_woocommerce_tax_rates"________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_woocommerce_tax_rate_locations"_______________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wpfm_backup"__________________________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wpmailsmtp_debug_events"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wpmailsmtp_emails_queue"______________________[ROWS:     0][PG:   0][SCAN:no columns  ]

EVALUATE TABLE: "wp_wpmailsmtp_tasks_meta"________________________[ROWS:     1][PG:   1][SCAN:text columns]
	--- BASE STRINGS ---
	SEARCH[path ]  1:"/home/<USER>/domains/comva.co.uk/public_html" => "C:/xampp/htdocs/comva"
	SEARCH[urlnd]  2:"https://comva.co.uk" =============================> "http://localhost/comva"
	CHUNK ACTION: CURRENT [search_replace][wp_wpmailsmtp_tasks_meta][0]
--------------------------------------
SCANNED:	Tables:171 	|	 Rows:283383 	|	 Cells:1817401 
UPDATED:	Tables:14 	|	 Rows:29358 	|	 Cells:29702 
ERRORS:		0 
RUNTIME:	25.422800 sec
CHUNK ACTION: CURRENT [rem_maintenance][][]

====================================
REMOVE MAINTENANCE MODE
====================================
MAINTENANCE MODE DISABLE
CHUNK ACTION: CURRENT [config_update][][]
SET CONFIG FILES
Retained original entry wpconfig target:C:/xampp/htdocs/comva/wp-config.php
New htaccess file created:C:/xampp/htdocs/comva/.htaccess

====================================
CONFIGURATION FILE UPDATES
====================================
	UPDATE ABSPATH "__DIR__ . '/'"
	UPDATE DB_NAME ""comva_app""
	UPDATE DB_USER "** OBSCURED **"
	UPDATE DB_PASSWORD "** OBSCURED **"
	UPDATE DB_HOST ""localhost""
	UPDATE DB_CHARSET "utf8"
	UPDATE DB_COLLATE ""
	WP CONFIG UPDATE WP_CACHE "true"
	WP CONFIG UPDATE WP_DEBUG "false"
	WP CONFIG UPDATE WP_AUTO_UPDATE_CORE "minor"
	
*** UPDATED WP CONFIG FILE ***

====================================
HTACCESS UPDATE
====================================

WEB SERVER CONFIGURATION FILE UPDATED:
- Preparing .htaccess file with basic setup.
HTACCESS FILE - Successfully updated the .htaccess file setting.

====================================
INDEX.PHP UPDATE
====================================
INDEX.PHP updated with new blog header "__DIR__ . '/wp-blog-header.php'"

CHUNK ACTION: CURRENT [gen_update][][]

====================================
GENERAL UPDATES
====================================

====================================
MANAGE PLUGINS
====================================
CHUNK ACTION: CURRENT [gen_clean][][]

====================================
GENERAL CLEANUP
====================================
RESET ALL USERS SESSION TOKENS
MIGRATION INFO SET
CHUNK ACTION: CURRENT [create_admin][][]

====================================
RESET USERS PASSWORD
====================================
CHUNK ACTION: CURRENT [notice_test][][]

====================================
CHECK FOR INDEX.HTML
====================================
NO INDEX.HTML WAS FOUND

====================================
NOTICES TEST
====================================
No General Notices Found

CHUNK ACTION: CURRENT [cleanup_tmp_files][][]

====================================
CLEANUP TMP FILES
====================================
CHUNK ACTION: CURRENT [set_files_perms][][]

====================================
SET PARAMS PERMISSION
====================================
\n SKIP FOLDER PERMISSION AFTER EXTRACTION
CHUNK ACTION: CURRENT [final_report][][]

====================================
FINAL REPORT NOTICES
====================================

STEP-3 COMPLETE @ 03:18:47 - RUNTIME: 4.5750 sec. 


AJAX ACTION [webupdate] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [finalpre] START
AJAX ACTION [finalpre] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

-------------------------
AJAX ACTION [finalafter] START
AJAX ACTION [finalafter] SUCCESS
-------------------------

LOG-TIME[C:\xampp\htdocs\comva\dup-installer\src\Core\Bootstrap.php:71] RESET TIME

====================================
FINAL REPORT NOTICES LIST
====================================
-----------------------
[NOTICE] Warnings or notices on WordPress front-end tests!
	SECTIONS: general
	LONG MSG: SCRIPT FILE TEST: C:/xampp/htdocs/comva/index.php
E_DEPRECATED Optional parameter $postid declared before required parameter $amount is implicitly treated as a required parameter
	FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\_auction\functions.php[1697]
	--- TRACE ---
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\framework\_config.php[86]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\functions.php[6]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-settings.php[663]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-config.php[108]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-load.php[50]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-blog-header.php[13]
	FUNCTION: require                       FILE: C:\xampp\htdocs\comva\index.php[17]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\wp_test_script_cc631e9-22030051.php[117]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base/ 
E_DEPRECATED Optional parameter $userid declared before required parameter $amount is implicitly treated as a required parameter
	FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\_auction\functions.php[1697]
	--- TRACE ---
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\framework\_config.php[86]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\functions.php[6]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-settings.php[663]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-config.php[108]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-load.php[50]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-blog-header.php[13]
	FUNCTION: require                       FILE: C:\xampp\htdocs\comva\index.php[17]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\wp_test_script_cc631e9-22030051.php[117]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base/ 


-----------------------
[NOTICE] Warnings or notices on WordPress backend tests!
	SECTIONS: general
	LONG MSG: SCRIPT FILE TEST: C:/xampp/htdocs/comva/wp-login.php
E_DEPRECATED Optional parameter $postid declared before required parameter $amount is implicitly treated as a required parameter
	FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\_auction\functions.php[1697]
	--- TRACE ---
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\framework\_config.php[86]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\functions.php[6]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-settings.php[663]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-config.php[108]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-load.php[50]
	FUNCTION: require                       FILE: C:\xampp\htdocs\comva\wp-login.php[12]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\wp_test_script_cc631e9-22030051.php[117]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base/ 
E_DEPRECATED Optional parameter $userid declared before required parameter $amount is implicitly treated as a required parameter
	FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\_auction\functions.php[1697]
	--- TRACE ---
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\framework\_config.php[86]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-content\themes\AT10\functions.php[6]
	FUNCTION: include                       FILE: C:\xampp\htdocs\comva\wp-settings.php[663]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-config.php[108]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-load.php[50]
	FUNCTION: require                       FILE: C:\xampp\htdocs\comva\wp-login.php[12]
	FUNCTION: require_once                  FILE: C:\xampp\htdocs\comva\wp-content\wp_test_script_cc631e9-22030051.php[117]
-----
For solutions to these issues see the online FAQs 
https://duplicator.com/knowledge-base/ 


====================================
LOG-TIME[C:\xampp\htdocs\comva\dup-installer\ctrls\ctrl.base.php:227][DELTA:   0.09923]  MESSAGE:END RENDER PAGE
