<?php
return ['x-generator'=>'GlotPress/4.0.0-beta.2','translation-revision-date'=>'2022-04-01 11:20:38+0000','plural-forms'=>'nplurals=2; plural=n != 1;','project-id-version'=>'Plugins - Disable Gutenberg - Stable (latest release)','language'=>'en_GB','messages'=>['Disable "Try Gutenberg" nag (for older versions of WP)'=>'Disable "Try Gutenberg" nag (for older versions of WP)','Disable Block Widgets and enable Classic Widgets'=>'Disable Block Widgets and enable Classic Widgets','Classic Widgets'=>'Classic Widgets','Select the theme template files for which <PERSON><PERSON><PERSON> should be disabled (e.g., custom-page.php).'=>'Select the theme template files for which <PERSON><PERSON><PERSON> should be disabled (e.g. custom-page.php).','Display Edit Links'=>'Display Edit Links','Whitelist Options'=>'Whitelist Options','Enable Frontend'=>'Enable front end','Enable frontend Gutenberg/block styles'=>'Enable front-end Gutenberg/block styles','Click here'=>'Click here','to display more tools and options. Note: these options remain in effect even when hidden on this page.'=>'to display more tools and options. Note: these options remain in effect even when hidden on this page.','learn more'=>'learn more','Homepage'=>'Homepage','Toggle More Tools'=>'Toggle more tools','Enable Custom Fields Meta Box (ACF disables by default),'=>'Enable Custom Fields Meta Box (ACF disables by default),','ACF Support'=>'ACF Support','Display "Add New (Classic)" menu link and Classic/Block edit links'=>'Display "Add New (Classic)" menu link and Classic/Block edit links','Display Whitelist settings'=>'Display Whitelist settings','Whitelist Post Titles'=>'Whitelist Post Titles','Whitelist Post Slugs'=>'Whitelist Post Slugs','Whitelist Post IDs'=>'Whitelist Post IDs','Classic Edit'=>'Classic Edit','Block Edit'=>'Block Edit','Plugin Homepage'=>'Plugin Homepage','Select the posts that always should use the Gutenberg Block Editor. Separate multiple values with commas.'=>'Select the posts that should always use the Gutenberg Block Editor. Separate multiple values with commas.','Post titles that always should use the Block Editor'=>'Post titles that should always use the block editor','Post slugs that always should use the Block Editor'=>'Post slugs that should always use the block editor','Post IDs that always should use the Block Editor'=>'Post IDs that should always use the block editor','Edit &#8220;%s&#8221; in the Block Editor'=>'Edit &#8220;%s&#8221; in the block editor','(Classic)'=>'(Classic)','Disables Gutenberg Block Editor and restores the Classic Editor and original Edit Post screen. Provides options to enable on specific post types, user roles, and more.'=>'Disables Gutenberg Block Editor and restores the Classic Editor and original Edit Post screen. Provides options to enable on specific post types, user roles, and more.','Hide Gutenberg plugin&rsquo;s menu item (for WP &lt; 5.0)'=>'Hide Gutenberg plugin&rsquo;s menu item (for WP &lt; 5.0)','Edit &#8220;%s&#8221; in the Classic Editor'=>'Edit &#8220;%s&#8221; in the Classic Editor','Please give a 5-star rating! A huge THANK YOU for your support!'=>'Please give a 5-star rating! A huge THANK YOU for your support!','Show support with a 5-star rating&nbsp;&raquo;'=>'Show support with a 5-star rating&nbsp;&raquo;','Rate Plugin'=>'Rate Plugin','Separate multiple post IDs with commas'=>'Separate multiple post IDs with commas','Disable Post IDs'=>'Disable Post IDs','Separate multiple templates with commas'=>'Separate multiple templates with commas','Disable Templates'=>'Disable Templates','Plugin Menu Item'=>'Plugin Menu Item','Select the post IDs for which Gutenberg should be disabled.'=>'Select the post IDs for which Gutenberg should be disabled.','Select the post types for which Gutenberg should be disabled.'=>'Select the post types for which Gutenberg should be disabled.','Select the user roles for which Gutenberg should be disabled.'=>'Select the user roles for which Gutenberg should be disabled.','Gutenberg Menu Item'=>'Gutenberg Menu Item','https://plugin-planet.com/'=>'https://plugin-planet.com/','Jeff Starr'=>'Jeff Starr','https://perishablepress.com/disable-gutenberg/'=>'https://perishablepress.com/disable-gutenberg/','No changes made to options.'=>'No changes made to options.','Default options restored.'=>'Default options restored.','Restore default plugin options'=>'Restore default plugin options','Reset Options'=>'Reset Options','Hide this plugin&rsquo;s menu item'=>'Hide this plugin&rsquo;s menu item','Disable Nag'=>'Disable Nag','requires WordPress '=>'requires WordPress ','Rate this plugin'=>'Rate this plugin','Click here to rate and review this plugin on WordPress.org'=>'Click here to rate and review this plugin on WordPress.org','Settings'=>'Settings','Disable Gutenberg'=>'Disable Gutenberg','Post Type ='=>'Post Type =','User Role ='=>'User Role =','Disable for'=>'Disable for','Complete Disable'=>'Complete Disable','No, abort mission.'=>'No, abort mission.','Yes, make it so.'=>'Yes, make it so.','Restore default options?'=>'Restore default options?','Confirm Reset'=>'Confirm reset','Sorry, pal!'=>'Sorry, pal!','to upgrade WordPress and try again.'=>'to upgrade WordPress and try again.','Please return to the'=>'Please return to the',' or higher, and has been deactivated! '=>' or higher, and has been deactivated! ','WP Admin Area'=>'WP Admin Area','Enable this setting to completely disable Gutenberg (and restore the Classic Editor). Or, disable this setting to display more options.'=>'Enable this setting to completely disable Gutenberg (and restore the Classic Editor). Or, disable this setting to display more options.','Disable Gutenberg everywhere'=>'Disable Gutenberg everywhere']];