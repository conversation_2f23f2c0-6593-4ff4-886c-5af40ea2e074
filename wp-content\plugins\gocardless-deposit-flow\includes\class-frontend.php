<?php
/**
 * Frontend functionality for GoCardless Deposit Flow
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GCDF_Frontend {

    /**
     * Constructor
     */
    public function __construct() {
        // Don't initialize in admin
        if (is_admin()) {
            return;
        }

        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'render_deposit_modal'));
        add_action('wp_ajax_gcdf_create_deposit', array($this, 'ajax_create_deposit'));
        add_action('wp_ajax_nopriv_gcdf_create_deposit', array($this, 'ajax_create_deposit'));
        add_action('init', array($this, 'handle_redirect_returns'));
        
        // Hook into auction system
        add_action('init', array($this, 'hook_auction_system'), 20);
    }

    /**
     * Initialize frontend
     */
    public function init() {
        // Check if we're on a single auction post
        if (!is_single()) {
            return;
        }

        global $post;
        if (!$post || !$this->is_auction_post($post)) {
            return;
        }

        // Check if plugin is configured
        if (!gcdf()->is_configured()) {
            return;
        }

        // Initialize frontend components
        $this->init_frontend_hooks();
    }

    /**
     * Initialize frontend hooks
     */
    private function init_frontend_hooks() {
        // Override the buy now process
        add_filter('wp_footer', array($this, 'override_buy_now_script'), 5);
    }

    /**
     * Check if post is an auction post
     */
    private function is_auction_post($post) {
        // Check if post has buy now price (bin_price)
        $bin_price = get_post_meta($post->ID, 'price_bin', true);
        return !empty($bin_price) && is_numeric($bin_price) && $bin_price > 0;
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        if (!is_single()) {
            return;
        }

        global $post;
        if (!$post || !$this->is_auction_post($post)) {
            return;
        }

        if (!gcdf()->is_configured()) {
            return;
        }

        wp_enqueue_script(
            'gcdf-frontend',
            GCDF_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            GCDF_VERSION,
            true
        );

        wp_enqueue_style(
            'gcdf-frontend',
            GCDF_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            GCDF_VERSION
        );

        // Localize script
        wp_localize_script('gcdf-frontend', 'gcdf_frontend', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gcdf_frontend_nonce'),
            'post_id' => $post->ID,
            'deposit_amount' => gcdf()->get_deposit_amount(),
            'currency' => gcdf()->get_currency(),
            'strings' => array(
                'processing' => __('Processing...', 'gocardless-deposit-flow'),
                'error' => __('An error occurred. Please try again.', 'gocardless-deposit-flow'),
                'terms_required' => __('You must accept the terms and conditions to continue.', 'gocardless-deposit-flow'),
                'confirm_deposit' => __('Are you sure you want to proceed with the deposit payment?', 'gocardless-deposit-flow')
            )
        ));
    }

    /**
     * Override buy now script
     */
    public function override_buy_now_script() {
        if (!is_single()) {
            return;
        }

        global $post;
        if (!$post || !$this->is_auction_post($post)) {
            return;
        }

        if (!gcdf()->is_configured()) {
            return;
        }

        // Check if this post requires deposit
        if (!gcdf()->auction_integration->requires_deposit($post->ID)) {
            return; // No deposit required
        }

        // Check if user already has completed deposit
        $user_id = get_current_user_id();
        if ($user_id && gcdf()->database->has_completed_deposit($post->ID, $user_id)) {
            return; // Let normal buy now process continue
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            console.log('GCDF: Initializing Buy Now override for post <?php echo $post->ID; ?>');

            // Store original function if it exists
            if (typeof window.processbuynow_original === 'undefined') {
                window.processbuynow_original = window.processbuynow || function() {};
                console.log('GCDF: Stored original processbuynow function');
            }

            // Override the processbuynow function completely
            window.processbuynow = function() {
                console.log('GCDF: Buy now intercepted - showing deposit modal');

                // Show deposit modal instead
                if ($('#gcdf-deposit-modal').length) {
                    console.log('GCDF: Modal found, showing it');
                    $('#gcdf-modal-overlay').show();
                    $('#gcdf-deposit-modal').show();
                    $('body').addClass('gcdf-modal-open');
                } else {
                    console.log('GCDF: Modal not found!');
                    alert('Deposit payment required. Modal not found - please refresh the page and try again.');
                }

                return false;
            };

            // Also override any direct click handlers on the buy now button
            $(document).on('click', '.buynowbtn', function(e) {
                console.log('GCDF: Buy now button clicked - preventing default');
                e.preventDefault();
                e.stopPropagation();

                // Show deposit modal
                if ($('#gcdf-deposit-modal').length) {
                    console.log('GCDF: Modal found via button click, showing it');
                    $('#gcdf-modal-overlay').show();
                    $('#gcdf-deposit-modal').show();
                    $('body').addClass('gcdf-modal-open');
                } else {
                    console.log('GCDF: Modal not found via button click!');
                    alert('Deposit payment required. Modal not found - please refresh the page and try again.');
                }

                return false;
            });

            // Prevent form submission entirely
            $(document).on('submit', '#buynowform', function(e) {
                console.log('GCDF: Buy now form submission prevented');
                e.preventDefault();

                // Show deposit modal instead
                if ($('#gcdf-deposit-modal').length) {
                    console.log('GCDF: Modal found via form submit, showing it');
                    $('#gcdf-modal-overlay').show();
                    $('#gcdf-deposit-modal').show();
                    $('body').addClass('gcdf-modal-open');
                } else {
                    console.log('GCDF: Modal not found via form submit!');
                    alert('Deposit payment required. Modal not found - please refresh the page and try again.');
                }

                return false;
            });

            // Debug: Check if modal exists after page load
            setTimeout(function() {
                if ($('#gcdf-deposit-modal').length) {
                    console.log('GCDF: Modal found in DOM after page load');
                } else {
                    console.log('GCDF: Modal NOT found in DOM after page load');
                }
            }, 1000);
        });
        </script>
        <?php
    }

    /**
     * Render deposit modal
     */
    public function render_deposit_modal() {
        if (!is_single()) {
            return;
        }

        global $post;
        if (!$post || !$this->is_auction_post($post)) {
            return;
        }

        if (!gcdf()->is_configured()) {
            return;
        }

        // Check if this post requires deposit
        if (!gcdf()->auction_integration->requires_deposit($post->ID)) {
            return; // No deposit required
        }

        // Check if user already has completed deposit
        $user_id = get_current_user_id();
        if ($user_id && gcdf()->database->has_completed_deposit($post->ID, $user_id)) {
            return;
        }

        $settings = gcdf()->get_settings();
        $terms_page_id = $settings['terms_page_id'] ?? 0;
        $terms_url = $terms_page_id ? get_permalink($terms_page_id) : '#';
        $deposit_amount = gcdf()->auction_integration->get_post_deposit_amount($post->ID);
        $currency = gcdf()->get_currency();

        ?>
        <div id="gcdf-deposit-modal" class="gcdf-modal" style="display: none;">
            <div class="gcdf-modal-content">
                <div class="gcdf-modal-header">
                    <h2><?php _e('Please Pay The Deposit', 'gocardless-deposit-flow'); ?></h2>
                    <span class="gcdf-modal-close">&times;</span>
                </div>
                
                <div class="gcdf-modal-body">
                    <div class="gcdf-deposit-info">
                        <p class="gcdf-deposit-message">
                            <?php 
                            printf(
                                __('Please pay the %s %s deposit to secure this vehicle.', 'gocardless-deposit-flow'),
                                esc_html($currency),
                                number_format($deposit_amount, 2)
                            ); 
                            ?>
                        </p>
                        
                        <div class="gcdf-vehicle-info">
                            <h3><?php echo esc_html($post->post_title); ?></h3>
                            <?php if (has_post_thumbnail($post->ID)): ?>
                                <div class="gcdf-vehicle-image">
                                    <?php echo get_the_post_thumbnail($post->ID, 'medium'); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <form id="gcdf-deposit-form">
                        <div class="gcdf-terms-section">
                            <label class="gcdf-checkbox-label">
                                <input type="checkbox" id="gcdf-terms-checkbox" name="terms_accepted" required>
                                <span class="gcdf-checkmark"></span>
                                <?php if ($terms_page_id): ?>
                                    <?php printf(
                                        __('I confirm I have read and understood the <a href="%s" target="_blank">terms and conditions of purchase</a>', 'gocardless-deposit-flow'),
                                        esc_url($terms_url)
                                    ); ?>
                                <?php else: ?>
                                    <?php _e('I confirm I have read and understood the terms and conditions of purchase', 'gocardless-deposit-flow'); ?>
                                <?php endif; ?>
                            </label>
                        </div>

                        <div class="gcdf-deposit-details">
                            <div class="gcdf-detail-row">
                                <span class="gcdf-label"><?php _e('Deposit Amount:', 'gocardless-deposit-flow'); ?></span>
                                <span class="gcdf-value"><?php echo esc_html($currency . ' ' . number_format($deposit_amount, 2)); ?></span>
                            </div>
                            <div class="gcdf-detail-row">
                                <span class="gcdf-label"><?php _e('Payment Method:', 'gocardless-deposit-flow'); ?></span>
                                <span class="gcdf-value"><?php _e('Bank Transfer (Direct Debit)', 'gocardless-deposit-flow'); ?></span>
                            </div>
                        </div>

                        <div class="gcdf-modal-actions">
                            <button type="button" class="gcdf-btn gcdf-btn-secondary" id="gcdf-cancel-btn">
                                <?php _e('Cancel', 'gocardless-deposit-flow'); ?>
                            </button>
                            <button type="submit" class="gcdf-btn gcdf-btn-primary" id="gcdf-continue-btn" disabled>
                                <?php _e('Continue to Payment', 'gocardless-deposit-flow'); ?>
                            </button>
                        </div>
                    </form>

                    <div id="gcdf-loading" class="gcdf-loading" style="display: none;">
                        <div class="gcdf-spinner"></div>
                        <p><?php _e('Setting up your payment...', 'gocardless-deposit-flow'); ?></p>
                    </div>

                    <div id="gcdf-error" class="gcdf-error" style="display: none;">
                        <p class="gcdf-error-message"></p>
                        <button type="button" class="gcdf-btn gcdf-btn-secondary" id="gcdf-retry-btn">
                            <?php _e('Try Again', 'gocardless-deposit-flow'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="gcdf-modal-overlay" class="gcdf-modal-overlay" style="display: none;"></div>
        <?php
    }

    /**
     * AJAX handler for creating deposit
     */
    public function ajax_create_deposit() {
        check_ajax_referer('gcdf_frontend_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => __('You must be logged in to make a deposit.', 'gocardless-deposit-flow')
            ));
        }

        $post_id = intval($_POST['post_id'] ?? 0);
        $terms_accepted = !empty($_POST['terms_accepted']);

        if (!$post_id) {
            wp_send_json_error(array(
                'message' => __('Invalid post ID.', 'gocardless-deposit-flow')
            ));
        }

        if (!$terms_accepted) {
            wp_send_json_error(array(
                'message' => __('You must accept the terms and conditions.', 'gocardless-deposit-flow')
            ));
        }

        $user_id = get_current_user_id();
        $post = get_post($post_id);

        if (!$post || !$this->is_auction_post($post)) {
            wp_send_json_error(array(
                'message' => __('Invalid auction item.', 'gocardless-deposit-flow')
            ));
        }

        // Check if user already has a pending or completed deposit
        $existing_deposit = gcdf()->database->get_pending_deposit($post_id, $user_id);
        if ($existing_deposit) {
            wp_send_json_error(array(
                'message' => __('You already have a pending deposit for this item.', 'gocardless-deposit-flow')
            ));
        }

        $completed_deposit = gcdf()->database->get_completed_deposit($post_id, $user_id);
        if ($completed_deposit) {
            wp_send_json_error(array(
                'message' => __('You have already paid the deposit for this item.', 'gocardless-deposit-flow')
            ));
        }

        try {
            // Create deposit record
            $deposit_id = gcdf()->database->create_deposit($post_id, $user_id);
            
            if (!$deposit_id) {
                throw new Exception(__('Failed to create deposit record.', 'gocardless-deposit-flow'));
            }

            // Create GoCardless redirect flow
            $redirect_data = gcdf()->gocardless_api->create_redirect_flow($post_id, $user_id, $deposit_id);

            wp_send_json_success(array(
                'redirect_url' => $redirect_data['redirect_url'],
                'deposit_id' => $deposit_id
            ));

        } catch (Exception $e) {
            gcdf()->log('Failed to create deposit: ' . $e->getMessage(), 'error');
            
            wp_send_json_error(array(
                'message' => $e->getMessage()
            ));
        }
    }

    /**
     * Handle redirect returns from GoCardless
     */
    public function handle_redirect_returns() {
        if (!isset($_GET['gcdf_action'])) {
            return;
        }

        $action = sanitize_text_field($_GET['gcdf_action']);
        $deposit_id = intval($_GET['deposit_id'] ?? 0);

        switch ($action) {
            case 'success':
                $this->handle_success_return($deposit_id);
                break;
                
            case 'cancel':
                $this->handle_cancel_return($deposit_id);
                break;
        }
    }

    /**
     * Handle successful payment return
     */
    private function handle_success_return($deposit_id) {
        if (!$deposit_id) {
            return;
        }

        $redirect_flow_id = sanitize_text_field($_GET['redirect_flow_id'] ?? '');
        $session_token = sanitize_text_field($_GET['session_token'] ?? '');

        if (!$redirect_flow_id || !$session_token) {
            return;
        }

        try {
            // Complete the redirect flow
            $result = gcdf()->gocardless_api->complete_redirect_flow($redirect_flow_id, $session_token);
            
            // Redirect to auction item with success message
            $deposit = $result['deposit'];
            $post_url = get_permalink($deposit->post_id);
            $redirect_url = add_query_arg('deposit_success', '1', $post_url);
            
            wp_redirect($redirect_url);
            exit;

        } catch (Exception $e) {
            gcdf()->log('Failed to complete redirect flow: ' . $e->getMessage(), 'error');
            
            // Redirect with error
            $deposit = gcdf()->database->get_deposit($deposit_id);
            if ($deposit) {
                $post_url = get_permalink($deposit->post_id);
                $redirect_url = add_query_arg('deposit_error', urlencode($e->getMessage()), $post_url);
                wp_redirect($redirect_url);
                exit;
            }
        }
    }

    /**
     * Handle cancelled payment return
     */
    private function handle_cancel_return($deposit_id) {
        if (!$deposit_id) {
            return;
        }

        // Update deposit status to cancelled
        gcdf()->database->update_deposit($deposit_id, array(
            'status' => 'cancelled'
        ));

        // Redirect back to auction item
        $deposit = gcdf()->database->get_deposit($deposit_id);
        if ($deposit) {
            $post_url = get_permalink($deposit->post_id);
            $redirect_url = add_query_arg('deposit_cancelled', '1', $post_url);
            wp_redirect($redirect_url);
            exit;
        }
    }

    /**
     * Hook into auction system
     */
    public function hook_auction_system() {
        // Hook into deposit completion to trigger auction completion
        add_action('gcdf_deposit_completed', array($this, 'complete_auction_purchase'), 10, 1);
    }

    /**
     * Complete auction purchase after deposit is confirmed
     */
    public function complete_auction_purchase($deposit) {
        global $post, $userdata;

        // Set up globals for auction system
        $post = get_post($deposit->post_id);
        $userdata = get_userdata($deposit->user_id);

        if (!$post || !$userdata) {
            gcdf()->log("Failed to complete auction: missing post or user data", 'error');
            return;
        }

        // Simulate the buy now action
        $_POST['auction_action'] = 'buynow';

        // Get the auction class and trigger buy now
        if (class_exists('AT_AUCTION')) {
            $auction = new AT_AUCTION();
            
            // Call the auction actions method directly
            if (method_exists($auction, 'auction_actions')) {
                $auction->auction_actions();
                gcdf()->log("Completed auction purchase for deposit {$deposit->id}");
            }
        }

        // Clean up
        unset($_POST['auction_action']);
    }
}
