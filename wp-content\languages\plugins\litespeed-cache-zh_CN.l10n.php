<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2023-10-31 10:25:59+0000','plural-forms'=>'nplurals=1; plural=0;','project-id-version'=>'Plugins - LiteSpeed Cache - Stable (latest release)','language'=>'zh_CN','messages'=>['Preconnecting speeds up future loads from a given origin.'=>'预连接可加快未来来自特定原产地的负载。','If your theme does not use JS to update the mini cart, you must enable this option to display the correct cart contents.'=>'如果您的主题不使用 JS 更新迷你购物车，则必须启用此选项才能显示正确的购物车内容。','Generate a separate vary cache copy for the mini cart when the cart is not empty.'=>'当购物车不是空的时候，为迷你购物车生成一个单独的不同缓存副本。','Vary for Mini Cart'=>'因迷你推车而异','DNS Preconnect'=>'DNS 预连接','This setting is %1$s for certain qualifying requests due to %2$s!'=>'由于 %2$s，对于某些符合条件的请求，此设置为 %1$s！','Listed JS files or inline JS code will be delayed.'=>'列出的 JS 文件或内联 JS 代码将被延迟。','URL Search'=>'URL 搜索','JS Delayed Includes'=>'联署材料延迟包括','Your domain_key has been temporarily blocklisted to prevent abuse. You may contact support at QUIC.cloud to learn more.'=>'您的 domain_key 已被暂时列入黑名单以防止滥用。您可以联系 QUIC.cloud 的支持人员以了解更多信息。','Cloud server refused the current request due to unpulled images. Please pull the images first.'=>'由于未提取图像，云服务器拒绝了当前请求。请先提取图像。','Current server load'=>'当前服务器负载','Started async image optimization request'=>'开始异步图像优化请求','Started async crawling'=>'开始异步爬行','Saving option failed. IPv4 only for %s.'=>'保存选项失败。仅 IPv4 适用于 %s。','Cloud server refused the current request due to rate limiting. Please try again later.'=>'由于速率限制，云服务器拒绝了当前请求。请稍后再试。','Maximum image post id'=>'最大图像帖子 ID','Current image post id position'=>'当前图像帖子 ID 位置','Images ready to request'=>'图片可随时索取','Redetect'=>'重新检测','If you are using a %1$s socket, %2$s should be set to %3$s'=>'如果使用 %1$s 插座，%2$s 应设置为 %3$s','All QUIC.cloud service queues have been cleared.'=>'已清除所有 QUIC.cloud 服务队列。','Cache key must be integer or non-empty string, %s given.'=>'缓存键必须是整数或非空字符串，%s 已给出。','Cache key must not be an empty string.'=>'缓存键不能为空字符串。','JS Deferred / Delayed Excludes'=>'联署材料推迟/延迟 不包括','The queue is processed asynchronously. It may take time.'=>'队列是异步处理的。这可能需要时间。','In order to use QC services, need a real domain name, cannot use an IP.'=>'要使用 QC 服务，需要一个真实的域名，不能使用 IP。','Restore Settings'=>'恢复设置','This will restore the backup settings created %1$s before applying the %2$s preset. Any changes made since then will be lost. Do you want to continue?'=>'这将恢复应用 %2$s 预设前创建的 %1$s 备份设置。此后所做的任何更改都将丢失。要继续吗？','Backup created %1$s before applying the %2$s preset'=>'在应用 %2$s 预置之前创建的备份 %1$s','Applied the %1$s preset %2$s'=>'应用预设 %1$s %2$s','Restored backup settings %1$s'=>'恢复备份设置 %1$s','Error: Failed to apply the settings %1$s'=>'错误：应用设置失败 %1$s','History'=>'历史','unknown'=>'未知','Apply Preset'=>'应用预设','This will back up your current settings and replace them with the %1$s preset settings. Do you want to continue?'=>'这将备份您当前的设置，并用 %1$s 预设设置取而代之。要继续吗？','Who should use this preset?'=>'谁应该使用此预置？','Use an official LiteSpeed-designed Preset to configure your site in one click. Try no-risk caching essentials, extreme optimization, or something in between.'=>'使用 LiteSpeed 官方设计的预设，一键配置您的网站。尝试无风险缓存要领、极端优化或两者之间的其他方法。','LiteSpeed Cache Standard Presets'=>'LiteSpeed 缓存标准预设','A Domain Key is required to use this preset. Enables the maximum level of optimizations for improved page speed scores.'=>'使用此预设需要域密钥。启用最大程度的优化，以提高页面速度分数。','This preset almost certainly will require testing and exclusions for some CSS, JS and Lazy Loaded images. Pay special attention to logos, or HTML-based slider images.'=>'此预设几乎肯定需要对某些 CSS、JS 和懒加载图像进行测试和排除。请特别注意徽标或基于 HTML 的滑块图像。','Inline CSS added to Combine'=>'为组合添加内联 CSS','Inline JS added to Combine'=>'在组合中添加内联 JS','JS Delayed'=>'联署材料延迟','Viewport Image Generation'=>'视口图像生成','Lazy Load for Images'=>'懒加载图像','Everything in Aggressive, Plus'=>'一切都在进取，加','Extreme'=>'极高','This preset might work out of the box for some websites, but be sure to test! Some CSS or JS exclusions may be necessary in Page Optimization > Tuning.'=>'该预设可能对某些网站有效，但请务必进行测试！可能需要在页面优化 > 调整中排除一些 CSS 或 JS。','Lazy Load for Iframes'=>'框架的懒加载','Removed Unused CSS for Users'=>'为用户删除未使用的 CSS','Asynchronous CSS Loading with Critical CSS'=>'使用关键 CSS 异步加载 CSS','CSS & JS Combine'=>'CSS 与 JS 结合','Everything in Advanced, Plus'=>'高级版中的所有内容，以及','Aggressive'=>'积极','A Domain Key is required to use this preset. Includes many optimizations known to improve page speed scores.'=>'使用此预设需要域密钥。包含许多已知的优化功能，可提高页面速度得分。','This preset is good for most websites, and is unlikely to cause conflicts. Any CSS or JS conflicts may be resolved with Page Optimization > Tuning tools.'=>'此预设适用于大多数网站，不太可能导致冲突。任何 CSS 或 JS 冲突都可以通过页面优化 > 调整工具来解决。','Remove Query Strings from Static Files'=>'删除静态文件中的查询字符串','DNS Prefetch for static files'=>'静态文件的 DNS 预取','JS Defer for both external and inline JS'=>'针对外部和内联 JS 的 JS 延迟','CSS, JS and HTML Minification'=>'CSS、JS 和 HTML 最小化','Guest Mode and Guest Optimization'=>'访客模式和访客优化','Everything in Basic, Plus'=>'基础版中的所有内容，Plus','Advanced (Recommended)'=>'高级（推荐）','A Domain Key is required to use this preset. Includes optimizations known to improve site score in page speed measurement tools.'=>'使用此预设需要域密钥。包括已知可提高网站在页面速度测量工具中得分的优化。','This low-risk preset introduces basic optimizations for speed and user experience. Appropriate for enthusiastic beginners.'=>'该低风险预设介绍了对速度和用户体验的基本优化。适合热心的初学者。','Mobile Cache'=>'移动缓存','Everything in Essentials, Plus'=>'所有必需品，外加','A Domain Key is not required to use this preset. Only basic caching features are enabled.'=>'使用此预设不需要域密钥。仅启用基本缓存功能。','This no-risk preset is appropriate for all websites. Good for new users, simple websites, or cache-oriented development.'=>'这种无风险预设适用于所有网站。适合新用户、简单网站或面向缓存的开发。','Higher TTL'=>'更高的 TTL','Default Cache'=>'默认缓存','Essentials'=>'必备','LiteSpeed Cache Configuration Presets'=>'LiteSpeed 缓存配置预设','Standard Presets'=>'标准预置','Listed CSS files will be excluded from UCSS and saved to inline.'=>'列出的 CSS 文件将从 UCSS 中排除，并保存为内联文件。','UCSS File Excludes and Inline'=>'UCSS 文件排除和内联','UCSS Selector Allowlist'=>'UCSS 选择器允许列表','Presets'=>'预设','Partner Benefits Provided by'=>'合作伙伴提供的福利','LiteSpeed Logs'=>'LiteSpeed 日志','Crawler Log'=>'履带日志','Purge Log'=>'清除日志','Prevent writing log entries that include listed strings.'=>'防止写入包含列出字符串的日志条目。','View Site Before Cache'=>'查看缓存前的网站','View Site Before Optimization'=>'查看优化前的网站','Debug Helpers'=>'调试助手','Enable Viewport Images auto generation cron.'=>'启用自动生成视口图像的 cron。','This enables the page\'s initial screenful of imagery to be fully displayed without delay.'=>'这样，页面的初始画面就能立即完全显示出来。','The Viewport Images service detects which images appear above the fold, and excludes them from lazy load.'=>'Viewport Images 服务会检测哪些图片出现在折叠上方，并将其排除在懒加载之外。','When you use Lazy Load, it will delay the loading of all images on a page.'=>'使用 "懒加载 "时，页面上所有图片的加载时间都会延迟。','Use %1$s to bypass remote image dimension check when %2$s is ON.'=>'当 %2$s 打开时，使用 %1$s 可绕过远程图像尺寸检查。','VPI'=>'VPI','%s must be turned ON for this setting to work.'=>'必须打开 %s 才能使用此设置。','Viewport Image'=>'视口图像','Please consider disabling the following detected plugins, as they may conflict with LiteSpeed Cache:'=>'请考虑禁用以下检测到的插件，因为它们可能与 LiteSpeed Cache 冲突：','Mobile'=>'手机','Disable VPI'=>'禁用 VPI','Disable Image Lazyload'=>'禁用图像懒加载','Disable Cache'=>'禁用缓存','Debug String Excludes'=>'调试字符串排除','Viewport Images Cron'=>'视口图像 Cron','Viewport Images'=>'视口图像','Alias is in use by another QUIC.cloud account.'=>'另一个 QUIC.cloud 账户正在使用别名。','Unable to automatically add %1$s as a Domain Alias for main %2$s domain.'=>'无法自动添加 %1$s 作为主 %2$s 域的域别名。','Unable to automatically add %1$s as a Domain Alias for main %2$s domain, due to potential CDN conflict.'=>'由于潜在的 CDN 冲突，无法自动添加 %1$s 作为主 %2$s 域的域别名。','You cannot remove this DNS zone, because it is still in use. Please update the domain\'s nameservers, then try to delete this zone again, otherwise your site will become inaccessible.'=>'您无法删除此 DNS 区域，因为它仍在使用中。请更新域名服务器，然后再尝试删除此区域，否则您的网站将无法访问。','The site is not a valid alias on QUIC.cloud.'=>'该网站不是 QUIC.cloud 上的有效别名。','Please thoroughly test each JS file you add to ensure it functions as expected.'=>'请彻底测试您添加的每个 JS 文件，确保其功能符合预期。','Please thoroughly test all items in %s to ensure they function as expected.'=>'请彻底测试 %s 中的所有项目，确保其功能符合预期。','Use %1$s to bypass UCSS for the pages which page type is %2$s.'=>'对于页面类型为 %2$s 的页面，使用 %1$s 可绕过 UCSS。','Use %1$s to generate one single UCSS for the pages which page type is %2$s while other page types still per URL.'=>'使用 %1$s 为页面类型为 %2$s 的页面生成一个单一的 UCSS，其他页面类型仍按 URL 生成。','Filter %s available for UCSS per page type generation.'=>'每个页面类型生成的 UCSS 可用过滤器 %s。','Guest Mode failed to test.'=>'访客模式测试失败。','Guest Mode passed testing.'=>'访客模式通过测试。','Testing'=>'测试','Guest Mode testing result'=>'访客模式测试结果','Not blocklisted'=>'未列入封锁名单','Learn more about when this is needed'=>'进一步了解何时需要','Cleaned all localized resource entries.'=>'清理了所有本地化资源条目。','View .htaccess'=>'查看 .htaccess','You can use this code %1$s in %2$s to specify the htaccess file path.'=>'您可以在 %2$s 中使用此代码 %1$s 来指定 htaccess 文件路径。','PHP Constant %s is supported.'=>'支持 PHP 常量 %s。','Default path is'=>'默认路径为','.htaccess Path'=>'.htaccess 路径','Please read all warnings before enabling this option.'=>'启用此选项前，请阅读所有警告。','This will delete all generated unique CSS files'=>'这将删除所有生成的唯一 CSS 文件','In order to avoid an upgrade error, you must be using %1$s or later before you can upgrade to %2$s versions.'=>'为了避免升级错误，您必须在使用 %1$s 或更高版本后才能升级到 %2$s 版本。','Use latest GitHub Dev/Master commit'=>'使用最新的 GitHub Dev/Master 提交','Press the %s button to use the most recent GitHub commit. Master is for release candidate & Dev is for experimental testing.'=>'按 %s 按钮可使用最新的 GitHub 提交。Master 用于候选发布版本，Dev 用于实验测试。','Downgrade not recommended. May cause fatal error due to refactored code.'=>'不建议降级。由于代码重构，可能会导致致命错误。','Only optimize pages for guest (not logged in) visitors. If turned this OFF, CSS/JS/CCSS files will be doubled by each user group.'=>'只为访客（未登录）优化页面。如果关闭此选项，CSS/JS/CCSS 文件将按每个用户组加倍。','Listed JS files or inline JS code will not be optimized by %s.'=>'列出的 JS 文件或内联 JS 代码将不会被 %s 优化。','Listed URI will not generate UCSS.'=>'列出的 URI 不会生成 UCSS。','The selector must exist in the CSS. Parent classes in the HTML will not work.'=>'选择器必须存在于 CSS 中。HTML 中的父类不起作用。','Wildcard %s supported.'=>'支持通配符 %s。','Useful for above-the-fold images causing CLS (a Core Web Vitals metric).'=>'适用于导致 CLS（核心网络活力指标）的折叠上方图像。','Set an explicit width and height on image elements to reduce layout shifts and improve CLS (a Core Web Vitals metric).'=>'在图像元素上设置明确的宽度和高度，以减少布局偏移并提高 CLS（Core Web Vitals 指标）。','Changes to this setting do not apply to already-generated LQIPs. To regenerate existing LQIPs, please %s first from the admin bar menu.'=>'对该设置的更改不适用于已生成的 LQIP。要重新生成现有的 LQIP，请先从管理栏菜单中选择 %s。','Deferring until page is parsed or delaying till interaction can help reduce resource contention and improve performance causing a lower FID (Core Web Vitals metric).'=>'推迟页面解析或推迟交互时间有助于减少资源争用，提高性能，从而降低 FID（核心网络生命指数指标）。','Delayed'=>'延迟','JS error can be found from the developer console of browser by right clicking and choosing Inspect.'=>'在浏览器的开发人员控制台中单击右键并选择 "检查"，即可发现 JS 错误。','This option may result in a JS error or layout issue on frontend pages with certain themes/plugins.'=>'使用某些主题/插件时，该选项可能会导致前台页面出现 JS 错误或布局问题。','This will also add a preconnect to Google Fonts to establish a connection earlier.'=>'这还将为 Google 字体添加一个预连接，以便更早地建立连接。','Delay rendering off-screen HTML elements by its selector.'=>'通过选择器延迟渲染屏幕外的 HTML 元素。','Disable this option to generate CCSS per Post Type instead of per page. This can save significant CCSS quota, however it may result in incorrect CSS styling if your site uses a page builder.'=>'禁用此选项可按 "帖子类型 "而不是按页面生成 CCSS。这可以节省大量 CCSS 配额，但如果您的网站使用页面生成器，则可能导致 CSS 样式不正确。','This option is bypassed due to %s option.'=>'由于使用了 %s 选项，该选项被绕过。','Elements with attribute %s in HTML code will be excluded.'=>'在 HTML 代码中带有属性 %s 的元素将被排除在外。','Use QUIC.cloud online service to generate critical CSS and load remaining CSS asynchronously.'=>'使用 QUIC.cloud 在线服务生成关键 CSS 并异步加载剩余 CSS。','This option will automatically bypass %s option.'=>'该选项将自动绕过 %s 选项。','Inline UCSS to reduce the extra CSS file loading. This option will not be automatically turned on for %1$s pages. To use it on %1$s pages, please set it to ON.'=>'内联 UCSS 以减少额外 CSS 文件的加载。对于 %1$s 页面，此选项不会自动开启。要在 %1$s 页面上使用，请将其设置为 "开启"。','Run %s Queue Manually'=>'手动运行 %s 队列','This option is bypassed because %1$s option is %2$s.'=>'由于 %1$s 选项为 %2$s，因此绕过了该选项。','Automatic generation of unique CSS is in the background via a cron-based queue.'=>'通过基于 cron 的队列在后台自动生成独特的 CSS。','This will drop the unused CSS on each page from the combined file.'=>'这将从合并文件中删除每个页面上未使用的 CSS。','HTML Settings'=>'HTML 设置','LiteSpeed cache plugin upgraded. Please refresh the page to complete the configuration data upgrade.'=>'LiteSpeed 缓存插件已升级。请刷新页面以完成配置数据升级。','Listed IPs will be considered as Guest Mode visitors.'=>'列出的 IP 将被视为访客模式访客。','Listed User Agents will be considered as Guest Mode visitors.'=>'列出的用户代理将被视为访客模式游客。','This option can help to correct the cache vary for certain advanced mobile or tablet visitors.'=>'此选项可帮助纠正某些高级手机或平板电脑访问者的缓存变化。','Guest Mode provides an always cacheable landing page for an automated guest\'s first time visit, and then attempts to update cache varies via AJAX.'=>'访客模式为自动访客的首次访问提供一个始终可缓存的登陆页面，然后尝试通过 AJAX 更新缓存变化。','Please make sure this IP is the correct one for visiting your site.'=>'请确保该 IP 是访问您网站的正确 IP。','the auto-detected IP may not be accurate if you have an additional outgoing IP set, or you have multiple IPs configured on your server.'=>'如果您设置了额外的外发 IP，或在服务器上配置了多个 IP，自动检测到的 IP 可能不准确。','You need to turn %s on and finish all WebP generation to get maximum result.'=>'您需要打开 %s 并完成所有 WebP 生成，以获得最佳效果。','You need to turn %s on to get maximum result.'=>'您需要打开 %s 以获得最大效果。','This option enables maximum optimization for Guest Mode visitors.'=>'该选项可最大限度地优化访客模式访客。','More'=>'更多','Remaining Daily Quota'=>'每日剩余配额','Successfully Crawled'=>'成功爬行','Already Cached'=>'已缓存','The crawler will use your XML sitemap or sitemap index. Enter the full URL to your sitemap here.'=>'爬虫将使用您的 XML 网站地图或网站地图索引。请在此处输入网站地图的完整 URL。','Optional when API token used.'=>'使用 API 令牌时为可选项。','Recommended to generate the token from Cloudflare API token template "WordPress".'=>'建议从 Cloudflare API 令牌模板 "WordPress "生成令牌。','Global API Key / API Token'=>'全球应用程序接口密钥/应用程序接口令牌','Use external object cache functionality.'=>'使用外部对象缓存功能。','Serve a separate cache copy for mobile visitors.'=>'为移动访客提供单独的缓存副本。','By default, the My Account, Checkout, and Cart pages are automatically excluded from caching. Misconfiguration of page associations in WooCommerce settings may cause some pages to be erroneously excluded.'=>'默认情况下，"我的账户"、"结账 "和 "购物车 "页面会自动从缓存中排除。WooCommerce 设置中的页面关联配置错误可能会导致某些页面被错误地排除在外。','Cleaned all Unique CSS files.'=>'清理了所有独特的 CSS 文件。','Add Missing Sizes'=>'添加缺失的尺寸','Optimize for Guests Only'=>'仅为访客优化','Guest Mode JS Excludes'=>'访客模式 JS 排除','CCSS Per URL'=>'CCSS 每个 URL','HTML Lazy Load Selectors'=>'HTML 懒加载选择器','UCSS URI Excludes'=>'UCSS URI 不包括','UCSS Inline'=>'UCSS 内联','Guest Optimization'=>'访客优化','Guest Mode'=>'访客模式','Guest Mode IPs'=>'访客模式 IP','Guest Mode User Agents'=>'访客模式用户代理','Online node needs to be redetected.'=>'需要重新检测在线节点。','The current server is under heavy load.'=>'当前服务器负载过重。','Please see %s for more details.'=>'详情请参见 %s。','This setting will regenerate crawler list and clear the disabled list!'=>'此设置将重新生成爬虫列表并清除禁用列表！','%1$s %2$s files left in queue'=>'%1$s %2$s 队列中剩余的文件','Crawler disabled list is cleared! All crawlers are set to active! '=>'爬虫禁用列表已清除！所有爬虫都设置为激活状态！ ','Redetected node'=>'重新检测节点','No available Cloud Node after checked server load.'=>'检查服务器负载后，没有可用的云节点。','Localization Files'=>'本地化文件','Purged!'=>'已清除！','Resources listed here will be copied and replaced with local URLs.'=>'此处列出的资源将被复制并替换为本地 URL。','Use latest GitHub Master commit'=>'使用最新的GitHub主提交','Use latest GitHub Dev commit'=>'使用最新的GitHub开发提交','No valid sitemap parsed for crawler.'=>'没有为爬虫解析出有效的网站地图。','CSS Combine External and Inline'=>'CSS结合外部和内联','Include external CSS and inline CSS in combined file when %1$s is also enabled. This option helps maintain the priorities of CSS, which should minimize potential errors caused by CSS Combine.'=>'当%1$s也启用时，在组合文件中包括外部CSS和内联CSS。此选项有助于保持CSS的优先级，从而将CSS合并导致的潜在错误降至最低。','Minify CSS files and inline CSS code.'=>'缩小CSS文件并内联CSS代码。','Predefined list will also be combined w/ the above settings'=>'预定义列表也将与上述设置相结合','Localization'=>'本地化','Include external JS and inline JS in combined file when %1$s is also enabled. This option helps maintain the priorities of JS execution, which should minimize potential errors caused by JS Combine.'=>'当%1$s也被启用时，在合并文件中包含外部JS和内联JS。这个选项有助于保持JS执行的优先级，这应该可以最大限度地减少JS Combine引起的潜在错误。','Combine all local JS files into a single file.'=>'将所有本地JS文件合并为一个文件。','Listed JS files or inline JS code will not be deferred or delayed.'=>'列出的 JS 文件或内联 JS 代码不会被推迟或延迟。','Click here to settings'=>'点击此处进行设置','JS Defer'=>'JS延迟','LiteSpeed Cache upgraded successfully. NOTE: Due to changes in this version, the settings %1$s and %2$s have been turned OFF. Please turn them back on manually and verify that your site layout is correct, and you have no JS errors.'=>'LiteSpeed Cache升级成功。注意：由于此版本中的更改，设置%1$s和%2$s已关闭。
请手动重新打开它们，并验证您的站点布局是否正确，并且没有JS错误。','JS Combine External and Inline'=>'JS结合外部和内联','Dismiss'=>'忽略','The latest data file is'=>'最新的数据文件是','The list will be merged with the predefined nonces in your local data file.'=>'该列表将与您本地数据文件中预定义的随机数合并。','Combine CSS files and inline CSS code.'=>'合并CSS文件和内联CSS代码。','Minify JS files and inline JS codes.'=>'缩小JS文件和内联JS代码。','This setting is overwritten by the Network setting'=>'此设置将被网络设置覆盖','LQIP Excludes'=>'LQIP排除','These images will not generate LQIP.'=>'这些图像不会生成LQIP。','Are you sure you want to reset all settings back to the default settings?'=>'你确定要将所有设置恢复到默认设置吗？','This option will remove all %s tags from HTML.'=>'这个选项将删除HTML中所有%s标签。','Are you sure you want to clear all cloud nodes?'=>'你确定你要清除所有云节点？','Remove Noscript Tags'=>'移除 Noscript 标签','The site is not registered on QUIC.cloud.'=>'该网站未在QUIC.cloud上注册。','Click here to set.'=>'单击此处进行设置。','Localize Resources'=>'本地化资源','Setting Up Custom Headers'=>'设置自定义标题','This will delete all localized resources'=>'这将删除所有本地化的资源','Localized Resources'=>'本地化资源','Comments are supported. Start a line with a %s to turn it into a comment line.'=>'支持评论。以%s开始一行，将其转换为注释行。','HTTPS sources only.'=>'仅HTTPS源。','Localize external resources.'=>'本地化外部资源。','Localization Settings'=>'本地化设置','Use QUIC.cloud online service to generate unique CSS.'=>'使用QUIC.cloud在线服务生成唯一的CSS。','Generate UCSS'=>'生成UCSS','Unique CSS'=>'独特的 CSS','Purge the cache entries created by this plugin except for Critical CSS & Unique CSS & LQIP caches'=>'清除此插件创建的缓存条目，关键 CSS、唯一 CSS 和 LQIP 缓存除外','LiteSpeed Report'=>'LiteSpeed报告','Image Thumbnail Group Sizes'=>'图像缩略图组大小','Ignore certain query strings when caching. (LSWS %s required)'=>'缓存时忽略某些查询字符串。 （需要LSWS%s）','For URLs with wildcards, there may be a delay in initiating scheduled purge.'=>'对于带有通配符的URL，初始化计划清除可能会有所延迟。','By design, this option may serve stale content. Do not enable this option, if that is not OK with you.'=>'根据设计，此选项可能会提供过时的内容。 如果您不满意，请不要启用此选项。','Serve Stale'=>'服务过期','This setting is overwritten by the primary site setting'=>'此设置被主站点设置覆盖','One or more pulled images does not match with the notified image md5'=>'一个或多个拉取的图像与通知的图像MD5不匹配','Some optimized image file(s) has expired and was cleared.'=>'某些优化的图像文件已过期并被清除。','You have too many requested images, please try again in a few minutes.'=>'您请求的图片过多，请在几分钟后重试。','Pulled WebP image md5 does not match the notified WebP image md5.'=>'拉动的WebP图像md5与通知的WebP图像md5不匹配。','Read LiteSpeed Documentation'=>'阅读LiteSpeed文档','There is proceeding queue not pulled yet. Queue info: %s.'=>'有进行中的队列还没有拉开。队列信息： %s.','Specify how long, in seconds, Gravatar files are cached.'=>'指定Gravatar文件缓存的时间（以秒为单位）。','Cleared %1$s invalid images.'=>'已清除%1$s无效图像。','LiteSpeed Cache General Settings'=>'LiteSpeed缓存常规设置','This will delete all cached Gravatar files'=>'这将删除所有缓存的Gravatar文件','Prevent any debug log of listed pages.'=>'防止列出页面的任何调试日志。','Only log listed pages.'=>'仅记录列出的页面。','Specify the maximum size of the log file.'=>'指定日志文件的最大大小。','To prevent filling up the disk, this setting should be OFF when everything is working.'=>'为避免填满磁盘，一切正常时，此设置应为OFF。','Press the %s button to stop beta testing and go back to the current release from the WordPress Plugin Directory.'=>'按%s按钮停止beta测试，然后从WordPress插件目录返回当前版本。','Use latest WordPress release version'=>'使用最新的WordPress发布版本','OR'=>'或','Use this section to switch plugin versions. To beta test a GitHub commit, enter the commit URL in the field below.'=>'使用此部分切换插件版本。要测试GitHub提交，请在下面的字段中输入提交URL。','Reset Settings'=>'重置设置','LiteSpeed Cache Toolbox'=>'LiteSpeed缓存工具箱','Beta Test'=>'Beta测试','Log View'=>'日志查看','Debug Settings'=>'调试设置','Turn ON to control heartbeat in backend editor.'=>'在后端编辑器中打开以控制心跳。','Turn ON to control heartbeat on backend.'=>'开启控制后台的心跳。','Set to %1$s to forbid heartbeat on %2$s.'=>'设置为%1$s在%2$s禁止心跳。','WordPress valid interval is %s seconds.'=>'WordPress的有效间隔为%s秒。','Specify the %s heartbeat interval in seconds.'=>'指定%s心跳间隔（以秒为单位）。','Turn ON to control heartbeat on frontend.'=>'开启控制前端的心跳。','Disable WordPress interval heartbeat to reduce server load.'=>'禁用WordPress间隔心跳以减少服务器负载。','Heartbeat Control'=>'心跳控制','provide more information here to assist the LiteSpeed team with debugging.'=>'在此处提供更多信息，以帮助LiteSpeed团队进行调试。','Optional'=>'可选','Generate Link for Current User'=>'为当前用户生成链接','Passwordless Link'=>'无密码链接','System Information'=>'系统信息','Go to plugins list'=>'转到插件列表','Install DoLogin Security'=>'安装 DoLogin Security','Check my public IP from'=>'检查我的公共IP','Your server IP'=>'您的服务器 IP','Enter this site\'s IP address to allow cloud services directly call IP instead of domain name. This eliminates the overhead of DNS and CDN lookups.'=>'输入此站点的IP地址，以允许云服务直接调用IP而不是域名。 这消除了DNS和CDN查找的开销。','This will enable crawler cron.'=>'这将启用爬虫cron。','Crawler General Settings'=>'爬虫常规设置','Remove from Blocklist'=>'从阻止列表中删除','Empty blocklist'=>'空拦截列表','Are you sure to delete all existing blocklist items?'=>'您确定要删除所有现有的拦截列表项目吗？','Blocklisted due to not cacheable'=>'因无法缓存而被屏蔽','Add to Blocklist'=>'添加到封锁清单','Operation'=>'操作','Sitemap Total'=>'站点地图总计','Sitemap List'=>'站点地图列表','Refresh Crawler Map'=>'刷新爬虫地图','Clean Crawler Map'=>'清理爬虫地图','Blocklist'=>'阻止清单','Map'=>'地图','Summary'=>'概要','Cache Miss'=>'缓存未命中','Cache Hit'=>'缓存命中','Waiting to be Crawled'=>'等待被爬虫','Blocklisted'=>'屏蔽列表','Miss'=>'未命中','Hit'=>'命中','Waiting'=>'等待中','Running'=>'运行','Use %1$s in %2$s to indicate this cookie has not been set.'=>'在%2$s中使用%1$s表示尚未设置此Cookie。','Add new cookie to simulate'=>'添加新的cookie来模拟','Remove cookie simulation'=>'删除Cookie模拟','Htaccess rule is: %s'=>'Htaccess 规则是: %s','More settings available under %s menu'=>'%s菜单下有更多可用设置','The amount of time, in seconds, that files will be stored in browser cache before expiring.'=>'过期之前，文件将存储在浏览器缓存中的时间（以秒为单位）。','OpenLiteSpeed users please check this'=>'OpenLiteSpeed用户请检查此','Browser Cache Settings'=>'浏览器缓存设置','Paths containing these strings will be forced to public cached regardless of no-cacheable settings.'=>'包含这些字符串的路径将被强制公开缓存，而不顾不可缓存设置如何。','With QUIC.cloud CDN enabled, you may still be seeing cache headers from your local server.'=>'启用QUIC.cloud CDN后，您可能仍会从本地服务器看到缓存头。','An optional second parameter may be used to specify cache control. Use a space to separate'=>'可选的第二个参数可用于指定缓存控制。 用空格隔开','The above nonces will be converted to ESI automatically.'=>'以上随机数将自动转换为ESI。','Browser'=>'浏览器','Object'=>'对象','Default port for %1$s is %2$s.'=>'%1$s的默认端口是%2$s。','Object Cache Settings'=>'对象缓存设置','Specify an HTTP status code and the number of seconds to cache that page, separated by a space.'=>'指定HTTP状态代码和缓存该页面的秒数，以空格分隔。','Specify how long, in seconds, the front page is cached.'=>'指定首页缓存多长时间（以秒为单位）。','TTL'=>'TTL','If ON, the stale copy of a cached page will be shown to visitors until a new cache copy is available. Reduces the server load for following visits. If OFF, the page will be dynamically generated while visitors wait.'=>'如果开启，则将向访问者显示已缓存页面的陈旧副本，直到有新的缓存副本可用为止。 减少后续访问的服务器负载。 如果设置为关闭，则会在访客等待时动态生成页面。','Swap'=>'交换','Set this to append %1$s to all %2$s rules before caching CSS to specify how fonts should be displayed while being downloaded.'=>'将此选项设置为在缓存CSS之前将%1$s附加到所有%2$s规则，以指定下载时字体的显示方式。','Avatar list in queue waiting for update'=>'队列中的头像列表等待更新','Refresh Gravatar cache by cron.'=>'通过cron刷新Gravatar缓存。','Accelerates the speed by caching Gravatar (Globally Recognized Avatars).'=>'通过缓存Gravatar（全球公认的头像）来加快速度。','Store Gravatar locally.'=>'将Gravatar存储在本地。','Failed to create Avatar table. Please follow <a %s>Table Creation guidance from LiteSpeed Wiki</a> to finish setup.'=>'无法创建头像表。 请按照 <a %s>LiteSpeed Wiki中的表创建指南</a> 完成设置。','LQIP requests will not be sent for images where both width and height are smaller than these dimensions.'=>'对于宽度和高度均小于这些尺寸的图像，不会发送LQIP请求。','pixels'=>'像素','Larger number will generate higher resolution quality placeholder, but will result in larger files which will increase page size and consume more points.'=>'数字越大，生成的占位符分辨率越高，但文件也会越大，从而增加页面大小并消耗更多的点数。','Specify the quality when generating LQIP.'=>'指定生成LQIP时的质量。','Keep this off to use plain color placeholders.'=>'请勿使用纯色占位符。','Use QUIC.cloud LQIP (Low Quality Image Placeholder) generator service for responsive image previews while loading.'=>'在加载时，使用QUIC.cloud LQIP（低质量图像占位符）生成器服务进行响应的图像预览。','Specify the responsive placeholder SVG color.'=>'指定响应式占位符SVG颜色。','Variables %s will be replaced with the configured background color.'=>'变量%s将被替换为配置的背景颜色。','Variables %s will be replaced with the corresponding image properties.'=>'变量%s将替换为相应的图像属性。','It will be converted to a base64 SVG placeholder on-the-fly.'=>'它将即时转换为base64 SVG占位符。','Specify an SVG to be used as a placeholder when generating locally.'=>'指定在本地生成时用作占位符的SVG。','Prevent any lazy load of listed pages.'=>'防止延迟加载列出的页面。','Iframes having these parent class names will not be lazy loaded.'=>'具有这些父类名称的iframe不会被延迟加载。','Iframes containing these class names will not be lazy loaded.'=>'包含这些类名称的iframe不会被延迟加载。','Images having these parent class names will not be lazy loaded.'=>'具有这些父类名称的图像将不会被延迟加载。','LiteSpeed Cache Page Optimization'=>'LiteSpeed缓存页面优化','Media Excludes'=>'媒体排除','CSS Settings'=>'CSS 设置','%s is recommended.'=>'建议使用%s。','Deferred'=>'递延','Default'=>'默认','This can improve the page loading speed.'=>'这样可以提高页面加载速度。','Automatically enable DNS prefetching for all URLs in the document, including images, CSS, JavaScript, and so forth.'=>'自动为文档中的所有 URL（包括图像、CSS、JavaScript 等）启用 DNS 预取。','New developer version %s is available now.'=>'新的开发版本%s现在可用。','New Developer Version Available!'=>'新的开发版本可用！','Dismiss this notice'=>'忽略此通知','Tweet this'=>'推特分享','Tweet preview'=>'微博预览','Learn more'=>'了解更多','You just unlocked a promotion from QUIC.cloud!'=>'你刚刚解锁了QUIC.cloud的促销活动！','The image compression quality setting of WordPress out of 100.'=>'WordPress的图像压缩质量设置（满分100）。','Image Optimization Settings'=>'图像优化设置','Are you sure to destroy all optimized images?'=>'您确定要销毁所有优化的图像吗？','Use Optimized Files'=>'使用优化文件','Switch back to using optimized images on your site'=>'切换回使用您网站上的优化图像','Use Original Files'=>'使用原始文件','Use original images (unoptimized) on your site'=>'在您的网站上使用原始图片（未经优化）','You can quickly switch between using original (unoptimized versions) and optimized image files. It will affect all images on your website, both regular and webp versions if available.'=>'您可以在使用原始（未优化的版本）和优化的图像文件之间快速切换。 它将影响您网站上的所有图像，包括常规版本和webp版本（如果可用）。','Optimization Tools'=>'优化工具','Rescan New Thumbnails'=>'重新扫描新缩略图','What is an image group?'=>'什么是图像组？','Delete all backups of the original images'=>'删除原始图像的所有备份','Calculate Backups Disk Space'=>'计算备份磁盘空间','Optimization Status'=>'优化状态','Current limit is'=>'当前限制为','To make sure our server can communicate with your server without any issues and everything works fine, for the few first requests the number of image groups allowed in a single request is limited.'=>'为确保我们的服务器能与您的服务器顺利通信，并确保一切运行正常，对于少数几个首次请求，单个请求中允许的图像组数量是有限的。','You can request a maximum of %s images at once.'=>'您一次最多可以请求 %s张图片。','Optimize images with our QUIC.cloud server'=>'使用我们的QUIC.cloud服务器优化图像','Revisions newer than this many days will be kept when cleaning revisions.'=>'清理修订版本时，将保留比这几天新的修订版本。','Day(s)'=>'天','Specify the number of most recent revisions to keep when cleaning revisions.'=>'指定清理修订时要保留的最新修订的数量。','LiteSpeed Cache Database Optimization'=>'LiteSpeed缓存数据库优化','DB Optimization Settings'=>'数据库优化设置','Option Name'=>'选项名称','Database Summary'=>'数据库摘要','We are good. No table uses MyISAM engine.'=>'我们很好。 没有表使用MyISAM引擎。','Convert to InnoDB'=>'转换为InnoDB','Tool'=>'工具','Engine'=>'引擎','Table'=>'数据表','Database Table Engine Converter'=>'数据库表引擎转换器','Clean revisions older than %1$s day(s), excluding %2$s latest revisions'=>'清理%1$s天之前的修订版本，%2$s 最新版本除外','Currently active crawler'=>'当前活动的爬虫','Crawler(s)'=>'爬虫','Crawler Status'=>'爬虫状态','Force cron'=>'强制 cron','Requests in queue'=>'队列中的请求','Private Cache'=>'私有缓存','Public Cache'=>'公共缓存','Cache Status'=>'缓存状态','Last Pull'=>'最后一拉','Image Optimization Summary'=>'图像优化摘要','Refresh page score'=>'刷新页面分数','Are you sure you want to redetect the closest cloud server for this service?'=>'您确定要重新检测与此服务器最近的云服务器吗？','Refresh page load time'=>'刷新页面加载时间','Go to QUIC.cloud dashboard'=>'转到QUIC.cloud仪表盘','Low Quality Image Placeholder'=>'低质量图像占位符','Sync data from Cloud'=>'从云同步数据','QUIC.cloud Service Usage Statistics'=>'QUIC.cloud 服务使用情况统计','Total images optimized in this month'=>'本月优化的总图像','Total Usage'=>'总用量','Pay as You Go Usage Statistics'=>'按量付费使用统计','PAYG Balance'=>'现收余额','Pay as You Go'=>'随用随付','Usage'=>'用法','Fast Queue Usage'=>'快速队列使用','CDN Bandwidth'=>'CDN带宽','LiteSpeed Cache Dashboard'=>'LiteSpeed缓存仪表盘','Network Dashboard'=>'网络仪表盘','No cloud services currently in use'=>'目前没有使用云服务','Click to clear all nodes for further redetection.'=>'单击以清除所有节点，以便进一步重新检测。','Current Cloud Nodes in Service'=>'当前服务中的云节点','Link to QUIC.cloud'=>'链接到QUIC.cloud','General Settings'=>'常规设置','Specify which HTML element attributes will be replaced with CDN Mapping.'=>'指定将用CDN映射替换哪些HTML元素属性。','Add new CDN URL'=>'新增CDN URL','Remove CDN URL'=>'删除CDN URL','To enable the following functionality, turn ON Cloudflare API in CDN Settings.'=>'要启用以下功能，请在CDN设置中打开Cloudflare API。','QUIC.cloud'=>'QUIC.cloud','WooCommerce Settings'=>'WooCommerce 设置','Current Online Server IPs'=>'当前在线服务器ip','Before generating key, please verify all IPs on this list are allowlisted'=>'生成密钥前，请确认此列表中的所有 IP 均已列入允许列表','For online services to work correctly, you must allowlist all %s server IPs.'=>'要使在线服务正常工作，必须允许列出所有 %s 服务器 IP。','LQIP Cache'=>'LQIP 缓存','Options saved.'=>'选项已保存。','Removed backups successfully.'=>'成功删除了备份。','Calculated backups successfully.'=>'计算备份成功。','Rescanned %d images successfully.'=>'成功重新扫描 %d 幅图像。','Rescanned successfully.'=>'重新扫描成功。','Destroy all optimization data successfully.'=>'成功销毁所有优化数据。','Cleaned up unfinished data successfully.'=>'成功清除未完成的数据。','Pull Cron is running'=>'拉动 Cron 正在运行','No valid image found by Cloud server in the current request.'=>'云服务器在当前请求中找不到有效的图像。','No valid image found in the current request.'=>'在当前请求中找不到有效的图像。','Pushed %1$s to Cloud server, accepted %2$s.'=>'已将%1$s推送至云端服务器，已接受%2$s。','Revisions Max Age'=>'修订最长时间','Revisions Max Number'=>'修订版本最大数量','Debug URI Excludes'=>'调试URI排除','Debug URI Includes'=>'调试URI包括','HTML Attribute To Replace'=>'要替换的HTML属性','Use CDN Mapping'=>'使用CDN映射','Editor Heartbeat TTL'=>'编辑心跳TTL','Editor Heartbeat'=>'编辑心跳','Backend Heartbeat TTL'=>'后端心跳TTL','Backend Heartbeat Control'=>'后端心跳控制','Frontend Heartbeat TTL'=>'前端心跳TTL','Frontend Heartbeat Control'=>'前端心跳控制','Backend .htaccess Path'=>'后端.htaccess路径','Frontend .htaccess Path'=>'前端.htaccess路径','ESI Nonces'=>'ESI随机数','WordPress Image Quality Control'=>'WordPress图像质量控制','Auto Request Cron'=>'自动请求计划','Generate LQIP In Background'=>'在后台生成LQIP','LQIP Minimum Dimensions'=>'LQIP最小尺寸','LQIP Quality'=>'LQIP质量','LQIP Cloud Generator'=>'LQIP云生成器','Responsive Placeholder SVG'=>'响应式占位符SVG','Responsive Placeholder Color'=>'响应式占位符颜色','Basic Image Placeholder'=>'基本图像占位符','Lazy Load URI Excludes'=>'延迟加载URI排除','Lazy Load Iframe Parent Class Name Excludes'=>'延迟加载iframe父类名称排除项','Lazy Load Iframe Class Name Excludes'=>'延迟加载iFrame类名排除','Lazy Load Image Parent Class Name Excludes'=>'延迟加载图像父类名称排除','Gravatar Cache TTL'=>'Gravatar 缓存 TTL','Gravatar Cache Cron'=>'Gravatar 缓存 Cron','Gravatar Cache'=>'Gravatar 缓存','DNS Prefetch Control'=>'DNS Prefetch Control','Font Display Optimization'=>'字体显示优化','Force Public Cache URIs'=>'强制公共缓存URI','Notifications'=>'通知','Default HTTP Status Code Page TTL'=>'默认HTTP状态代码页TTL','Default REST TTL'=>'默认 REST TTL','Enable Cache'=>'启用缓存','Server IP'=>'服务器IP','Images not requested'=>'未请求图像','Sync credit allowance with Cloud Server successfully.'=>'成功地与云服务器同步信用额度。','Failed to communicate with QUIC.cloud server'=>'与QUIC.cloud服务器通信失败','Good news from QUIC.cloud server'=>'来自QUIC.cloud服务器的好消息','Message from QUIC.cloud server'=>'来自QUIC.cloud服务器的消息','Please try after %1$s for service %2$s.'=>'请在%1$s后尝试使用服务%2$s。','No available Cloud Node.'=>'没有可用的云节点。','Cloud Error'=>'云错误','The database has been upgrading in the background since %s. This message will disappear once upgrade is complete.'=>'自%s起，数据库一直在后台升级。 升级完成后，此消息将消失。','Restore from backup'=>'从备份还原','No backup of unoptimized WebP file exists.'=>'不存在未优化的WebP文件的备份。','WebP file reduced by %1$s (%2$s)'=>'WebP文件减少了 %1$s (%2$s)','Currently using original (unoptimized) version of WebP file.'=>'当前使用WebP文件的原始（未优化）版本。','Currently using optimized version of WebP file.'=>'当前正在使用WebP文件的优化版本。','Orig'=>'原件','(no savings)'=>'(no savings)','Orig %s'=>'原件 %s','Congratulation! Your file was already optimized'=>'恭喜您您的文件已经优化','No backup of original file exists.'=>'不存在原始文件的备份。','Using optimized version of file. '=>'使用文件的优化版本。','Orig saved %s'=>'比原文件节省 %s','Original file reduced by %1$s (%2$s)'=>'原始文件减少了%1$s (%2$s)','Click to switch to optimized version.'=>'单击以切换到优化版本。','Currently using original (unoptimized) version of file.'=>'当前使用文件的原始（未优化）版本。','(non-optm)'=>'(non-optm)','Click to switch to original (unoptimized) version.'=>'单击以切换到原始（未优化）版本。','Currently using optimized version of file.'=>'当前使用文件的优化版本。','(optm)'=>'(optm)','LQIP image preview for size %s'=>'LQIP图片预览的大小为%s','LQIP'=>'LQIP','Previously existed in blocklist'=>'以前存在于拦截列表中','Manually added to blocklist'=>'手动添加到拦截列表','Mobile Agent Rules'=>'移动代理规则','Sitemap created successfully: %d items'=>'站点地图创建成功：%d个项目','Sitemap cleaned successfully'=>'网站地图成功清除','Invalid IP'=>'无效的 IP','Value range'=>'取值范围','Smaller than'=>'小于','Larger than'=>'大于','Zero, or'=>'0,或','Maximum value'=>'最大值','Minimum value'=>'最小值','Path must end with %s'=>'路径必须以%s结尾','Invalid rewrite rule'=>'无效的重写规则','currently set to %s'=>'当前设置为 %s','This setting is overwritten by the PHP constant %s'=>'此设置被PHP常量%s覆盖','Toolbox'=>'工具箱','Database'=>'数据库','Page Optimization'=>'页面优化','Dashboard'=>'仪表盘','Converted to InnoDB successfully.'=>'成功转换为InnoDB。','Cleaned all Gravatar files.'=>'清除了所有头像文件。','Cleaned all LQIP files.'=>'清除所有LQIP文件。','Unknown error'=>'未知错误','Your domain has been forbidden from using our services due to a previous policy violation.'=>'由于先前违反了政策，因此您的域已被禁止使用我们的服务。','The callback validation to your domain failed. Please make sure there is no firewall blocking our servers. Response code: '=>'对您的域的回调验证失败。 请确保没有防火墙阻止我们的服务器。 响应码：','The callback validation to your domain failed. Please make sure there is no firewall blocking our servers.'=>'对您的域的回调验证失败。 请确保没有防火墙阻止我们的服务器。','The callback validation to your domain failed due to hash mismatch.'=>'由于哈希不匹配，对您域的回调验证失败。','Your application is waiting for approval.'=>'您的申请正在等待批准。','Previous request too recent. Please try again after %s.'=>'之前的请求太近。请在%s后再试。','Previous request too recent. Please try again later.'=>'之前的请求太近。请稍后再试。','Crawler disabled by the server admin.'=>'爬虫被服务器管理员禁用。','Could not find %1$s in %2$s.'=>'在 %2$s中找不到%1$s .','Credits are not enough to proceed the current request.'=>'积分不足以进行当前请求。','The domain key is not correct. Please try to sync your domain key again.'=>'域密钥不正确。 请尝试再次同步您的域密钥。','There is proceeding queue not pulled yet.'=>'有进行中的队列还没有拉开。','Not enough parameters. Please check if the domain key is set correctly'=>'参数不足。 请检查域密钥设置是否正确','The image list is empty.'=>'图像列表为空。','LiteSpeed Crawler Cron'=>'LiteSpeed 爬虫 Cron','Every Minute'=>'每分钟','Turn this option ON to show latest news automatically, including hotfixes, new releases, available beta versions, and promotions.'=>'启用此选项可以自动显示最新的新闻（其中包括修补程序、插件的新版本、与测试版、促销消息）','To grant wp-admin access to the LiteSpeed Support Team, please generate a passwordless link for the current logged-in user to be sent with the report.'=>'需要给予"wp-admin"对"LiteSpeed插件"的访问权限，请为当前登录用户生成一个无密码链接，以便与报告一起发送。','Please do NOT share the above passwordless link with anyone.'=>'请不要与任何人共享上述无密码链接。','To generate a passwordless link for LiteSpeed Support Team access, you must install %s.'=>'要生成用于LiteSpeed支持团队访问的无密码链接，必须安装%s。','Install'=>'安装','These options are only available with LiteSpeed Enterprise Web Server or QUIC.cloud CDN.'=>'这些选项仅适用于LiteSpeed Enterprise Web Server或QUIC.cloud CDN。','PageSpeed Score'=>'"PageSpeed"评分','Improved by'=>'改进了','After'=>'之后','Before'=>'之前','Page Load Time'=>'页面加载时间','To use the caching functions you must have a LiteSpeed web server or be using QUIC.cloud CDN.'=>'要使用缓存功能，您必须具有LiteSpeed Web服务器或正在使用QUIC.cloud CDN。','Preserve EXIF/XMP data'=>'保留EXIF/XMP数据','Try GitHub Version'=>'试用GitHub版本','If you turn any of the above settings OFF, please remove the related file types from the %s box.'=>'如果您关闭上述任何设置，请从%s框中删除相关的文件类型。','Both full and partial strings can be used.'=>'完整和部分字符串都可以使用。','Images containing these class names will not be lazy loaded.'=>'包含这些类名的图像不会被延迟加载。','Lazy Load Image Class Name Excludes'=>'延迟加载图像类名排除','For example, %1$s defines a TTL of %2$s seconds for %3$s.'=>'例如，%1$s为%3$s定义了%2$s秒的TTL。','To define a custom TTL for a URI, add a space followed by the TTL value to the end of the URI.'=>'要为URI定义自定义TTL，请在URI的末尾添加空格和TTL值。','Maybe Later'=>'稍后再说','Turn On Auto Upgrade'=>'开启自动升级','Upgrade'=>'升级','New release %s is available now.'=>'新版本%s现已发布。','New Version Available!'=>'新版本可用！','Sure I\'d love to review!'=>'当然，我很乐意评论!','Thank You for Using the LiteSpeed Cache Plugin!'=>'感谢您使用LiteSpeed Cache插件！','Upgraded successfully.'=>'升级成功。','Failed to upgrade.'=>'升级失败。','Changed setting successfully.'=>'更改设置成功。','ESI sample for developers'=>'针对开发人员的ESI示例','Replace %1$s with %2$s.'=>'将%1$s替换为%2$s。','You can turn shortcodes into ESI blocks.'=>'您可以将快捷代码转换为ESI块。','WpW: Private Cache vs. Public Cache'=>'WPW：私有缓存与公共缓存','Append query string %s to the resources to bypass this action.'=>'将查询字符串%s附加到资源以绕过此操作。','Google reCAPTCHA will be bypassed automatically.'=>'Google reCAPTCHA将被自动绕过。','To crawl for a particular cookie, enter the cookie name, and the values you wish to crawl for. Values should be one per line. There will be one crawler created per cookie value, per simulated role.'=>'要抓取特定 cookie，请输入 cookie 名称和要抓取的值。值应每行一个。每个 cookie 值、每个模拟角色将创建一个爬虫。','Cookie Values'=>'Cookie值','Cookie Name'=>'Cookie名称','Cookie Simulation'=>'Cookie模拟','Use Web Font Loader library to load Google Fonts asynchronously while leaving other CSS intact.'=>'使用Web Font Loader库以异步方式加载Google字体，同时保持其他CSS不变。','Turn this option ON to have LiteSpeed Cache updated automatically, whenever a new version is released. If OFF, update manually as usual.'=>'每当发布新版本时，将此选项设置为ON即可自动更新LiteSpeed缓存。 如果关闭，则照常手动更新。','Automatically Upgrade'=>'自动升级','Your IP'=>'您的IP','Reset successfully.'=>'重置成功。','This will reset all settings to default settings.'=>'这会将所有设置重置为默认设置。','Reset All Settings'=>'重置所有设置','Separate critical CSS files will be generated for paths containing these strings.'=>'将为包含这些字符串的路径生成单独的关键CSS文件。','Separate CCSS Cache URIs'=>'单独的CCSS缓存URI','For example, if every Page on the site has different formatting, enter %s in the box. Separate critical CSS files will be stored for every Page on the site.'=>'例如，如果站点上的每个页面都有不同的格式，请在框中输入%s。 网站上每个页面都将存储单独的关键CSS文件。','List post types where each item of that type should have its own CCSS generated.'=>'列出文章类型，其中该类型的每个项目都应生成自己的CCSS。','Separate CCSS Cache Post Types'=>'单独的 CCSS 缓存帖子类型','Size list in queue waiting for cron'=>'等待cron的队列中的大小列表','If set to %1$s, before the placeholder is localized, the %2$s configuration will be used.'=>'如果设置为%1$s，则在定位占位符之前，将使用%2$s配置。','Automatically generate LQIP in the background via a cron-based queue.'=>'通过基于cron的队列在后台自动生成LQIP。','This will generate the placeholder with same dimensions as the image if it has the width and height attributes.'=>'如果具有width和height属性，它将生成与图像尺寸相同的占位符。','Responsive image placeholders can help to reduce layout reshuffle when images are loaded.'=>'响应式图像占位符可以帮助减少加载图像时的布局重新排列。','Responsive Placeholder'=>'响应式占位符','This will delete all generated image LQIP placeholder files'=>'这将删除所有生成的图像LQIP占位符文件','Please enable LiteSpeed Cache in the plugin settings.'=>'请在插件设置中启用LiteSpeed缓存。','Please enable the LSCache Module at the server level, or ask your hosting provider.'=>'请在服务器级别启用LSCache模块，或询问您的主机提供商。','Failed to request via WordPress'=>'无法通过WordPress请求','High-performance page caching and site optimization from LiteSpeed'=>'LiteSpeed的高性能页面缓存和站点优化','Reset the optimized data successfully.'=>'成功重置优化数据。','Update %s now'=>'现在更新%s','View %1$s version %2$s details'=>'查看%1$s版本%2$s详情','<a href="%1$s" %2$s>View version %3$s details</a> or <a href="%4$s" %5$s target="_blank">update now</a>.'=>'<a href="%1$s" %2$s>查看版本 %3$s 详情</a> or <a href="%4$s" %5$s target="_blank">立即更新</a>.','Install %s'=>'安装%s','LSCache caching functions on this page are currently unavailable!'=>'该页面上的LSCache缓存功能当前不可用！','%1$s plugin version %2$s required for this action.'=>'此操作需要%1$s插件版本%2$s。','We are working hard to improve your online service experience. The service will be unavailable while we work. We apologize for any inconvenience.'=>'我们正在努力改善您的在线服务体验。当我们工作时，服务将不可用。给您带来的不便，我们深表歉意。','Automatically remove the original image backups after fetching optimized images.'=>'获取到优化图像后删除原始图像的备份。','Remove Original Backups'=>'移除备份的原始文件','Automatically request optimization via cron job.'=>'通过cron任务进行自动优化。','A backup of each image is saved before it is optimized.'=>'在优化前备份图像。','Switched images successfully.'=>'切换图像成功。','This can improve quality but may result in larger images than lossy compression will.'=>'这可以提高图像品质，但是也会增加图片的体积。','Optimize images using lossless compression.'=>'使用无损压缩优化图像。','Optimize Losslessly'=>'无损压缩','Optimize images and save backups of the originals in the same folder.'=>'优化图像并将原始图像备份在相同目录中。','Optimize Original Images'=>'优化原始图像','When this option is turned %s, it will also load Google Fonts asynchronously.'=>'将此选项设为%s时，它还将异步加载Google字体。','Cleaned all Critical CSS files.'=>'清理了所有关键的CSS文件。','This will inline the asynchronous CSS library to avoid render blocking.'=>'这将内联异步CSS库以避免渲染阻塞。','Inline CSS Async Lib'=>'内联CSS异步库','Run Queue Manually'=>'手动运行队列','URL list in %s queue waiting for cron'=>'URL 列表在 %s 队列中，等待 cron','Last requested cost'=>'上次请求的成本','Last generated'=>'上次生成的时间','If set to %s this is done in the foreground, which may slow down page load.'=>'如果设置为%s，则此操作在前台完成，这可能会减慢页面加载速度。','Automatic generation of critical CSS is in the background via a cron-based queue.'=>'通过基于 cron 的队列在后台自动生成关键 CSS。','Optimize CSS delivery.'=>'优化CSS传输。','This will delete all generated critical CSS files'=>'这将删除所有生成的关键CSS文件','Critical CSS'=>'Critical CSS','This site utilizes caching in order to facilitate a faster response time and better user experience. Caching potentially stores a duplicate copy of every web page that is on display on this site. All cache files are temporary, and are never accessed by any third party, except as necessary to obtain technical support from the cache plugin vendor. Cache files expire on a schedule set by the site administrator, but may easily be purged by the admin before their natural expiration, if necessary. We may use QUIC.cloud services to process & cache your data temporarily.'=>'本网站采用缓存技术，以加快响应速度和改善用户体验。缓存可能会存储本网站上显示的每个网页的副本。所有缓存文件均为临时文件，任何第三方均不得访问，除非在必要时从缓存插件供应商处获得技术支持。缓存文件的过期时间由网站管理员设定，但在必要时，管理员可在缓存文件自然过期前轻松清除缓存文件。我们可能使用 QUIC.cloud 服务暂时处理和缓存您的数据。','Disabling this may cause WordPress tasks triggered by AJAX to stop working.'=>'禁用此功能可能会导致AJAX触发的WordPress任务停止工作。','right now'=>'现在','just now'=>'刚才','Saved'=>'已保存','Last ran'=>'最后一次运行','You will be unable to Revert Optimization once the backups are deleted!'=>'一旦删除备份，您将无法还原优化！','This is irreversible.'=>'该操作不可逆。','Remove Original Image Backups'=>'删除原始图像备份','Are you sure you want to remove all image backups?'=>'确定要删除所有的图像备份？','Total'=>'总共','Files'=>'文件','Last calculated'=>'最后计算','Calculate Original Image Storage'=>'计算原始图像占用空间','Storage Optimization'=>'存储优化','Use the format %1$s or %2$s (element is optional).'=>'使用格式 %1$s 或 %2$s (元素可选).','Only attributes listed here will be replaced.'=>'只有列在此处的属性会被替换。','Only files within these directories will be pointed to the CDN.'=>'只有这些目录下的文件会指向CDN。','Included Directories'=>'包括目录','A Purge All will be executed when WordPress runs these hooks.'=>'当WordPress运行这些hook时会运行清除全部。','Purge All Hooks'=>'清除全部的Hook','Purged all caches successfully.'=>'成功清除全部缓存。','LSCache'=>'LSCache','Forced cacheable'=>'强制缓存','Paths containing these strings will be cached regardless of no-cacheable settings.'=>'包括这些字串的路径会无视不缓存的设定而直接缓存。','Force Cache URIs'=>'强制缓存URI','Exclude Settings'=>'排除设定','This will disable LSCache and all optimization features for debug purpose.'=>'这会禁用LSCache和所有优化选项以便debug。','Disable All Features'=>'禁用全部功能','Opcode Cache'=>'Opcode Cache','CSS/JS Cache'=>'CSS/JS Cache','Remove all previous unfinished image optimization requests.'=>'移除所有先前完成的图片优化请求。','Clean Up Unfinished Data'=>'清理未完成的数据','Join Us on Slack'=>'加入我们的 Slack','Join the %s community.'=>'加入 %s 社区。','Want to connect with other LiteSpeed users?'=>'希望和其它LiteSpeed用户保持联系？','Your API key / token is used to access %s APIs.'=>'您的 API 密钥/令牌用于访问 %s API。','Your Email address on %s.'=>'您的电子邮件地址%s。','Use %s API functionality.'=>'使用 %s API 功能。','To randomize CDN hostname, define multiple hostnames for the same resources.'=>'要随机输出CDN主机名，请为相同资源定义多个主机名。','Join LiteSpeed Slack community'=>'加入LiteSpeed Slack社区','Visit LSCWP support forum'=>'访问LSCWP支持论坛','Images notified to pull'=>'图片已通知待取回','What is a group?'=>'什么是图片组？','%s image'=>'%s张图像','%s group'=>'%s个群组','%s images'=>'%s 张图像','%s groups'=>'%s 组','Guest'=>'游客','To crawl the site as a logged-in user, enter the user ids to be simulated.'=>'要以登录用户身份预读取网站，请输入要模拟的用户ID。','Role Simulation'=>'角色模拟','running'=>'运行','Size'=>'大小','Ended reason'=>'结束原因','Last interval'=>'上次间隔','Current crawler started at'=>'当前爬虫始于','Run time for previous crawler'=>'上一个爬虫运行时间','%d seconds'=>'%d 秒','Last complete run time for all crawlers'=>'上次完整的全部爬虫运行时间','Current sitemap crawl started at'=>'当前站点地图爬行始于','Save transients in database when %1$s is %2$s.'=>'当 %1$s 为 %2$s 时保存transient到数据库。','Store Transients'=>'保存 Transient','If %1$s is %2$s, then %3$s must be populated!'=>'当 %1$s 为 %2$s时，%3$s 必须被设定！','NOTE'=>'注意','Server variable(s) %s available to override this setting.'=>'服务器变量 %s 可用来覆盖本设定。','API'=>'API','Imported setting file %s successfully.'=>'成功导入设定文件 %s 。','Import failed due to file error.'=>'由于文件错误导致导入失败。','How to Fix Problems Caused by CSS/JS Optimization.'=>'如何修正由 CSS/JS 优化导致的问题。','This will generate extra requests to the server, which will increase server load.'=>'这会产生额外的服务器请求，将增加服务器负担。','When a visitor hovers over a page link, preload that page. This will speed up the visit to that link.'=>'当访问者将鼠标悬停在页面链接上时，预载该页面。这将加快访问该链接的速度。','Instant Click'=>'即时点击','Reset the entire opcode cache'=>'重置整个opcode缓存','This will import settings from a file and override all current LiteSpeed Cache settings.'=>'这会从文件中导入设定并覆盖所有已有的设定。','Last imported'=>'上次导入','Import'=>'导入','Import Settings'=>'导入设定','This will export all current LiteSpeed Cache settings and save them as a file.'=>'这会导出所有当前的 LiteSpeed 缓存设定并存为文件。','Last exported'=>'上次导出','Export'=>'导出','Export Settings'=>'导出设定','Import / Export'=>'导入/导出','Use keep-alive connections to speed up cache operations.'=>'使用长链接加速缓存操作。','Database to be used'=>'要使用的数据库','Redis Database ID'=>'Redis 数据库 ID','Specify the password used when connecting.'=>'指定连接时使用的密码。','Password'=>'密码','Only available when %s is installed.'=>'仅当 %s 已安装时可用。','Username'=>'用户名','Your %s Hostname or IP address.'=>'您的 %s 主机名或者IP地址。','Method'=>'方法','Purge all object caches successfully.'=>'成功清除所有对象缓存。','Object cache is not enabled.'=>'对象缓存未激活。','Improve wp-admin speed through caching. (May encounter expired data)'=>'通过缓存改进wp-admin的加载速度。（可能碰到过期数据）','Cache WP-Admin'=>'缓存 WP-Admin','Persistent Connection'=>'永久连接','Do Not Cache Groups'=>'不缓存群组','Groups cached at the network level.'=>'站点网络级别的缓存群组。','Global Groups'=>'全局群组','Connection Test'=>'连接测试','%s Extension'=>'%s 扩展','Status'=>'状态','Default TTL for cached objects.'=>'默认的对象缓存的TTL。','Default Object Lifetime'=>'默认的对象生命周期','Port'=>'端口','Host'=>'主机','Object Cache'=>'对象缓存','Failed'=>'失败','Passed'=>'通过','Not Available'=>'不可用','Purge all the object caches'=>'清除全部对象缓存','Failed to communicate with Cloudflare'=>'和 Cloudflare 通讯失败','Communicated with Cloudflare successfully.'=>'和 CLoudflare 通讯成功。','No available Cloudflare zone'=>'无可用的 Cloudflare 区域','Notified Cloudflare to purge all successfully.'=>'已成功通知 Cloudflare 清除全部。','Cloudflare API is set to off.'=>'Cloudflare API 被设置为关闭。','Notified Cloudflare to set development mode to %s successfully.'=>'已成功通知 Cloudflare 设定开发模式为 %s。','Once saved, it will be matched with the current list and completed automatically.'=>'一旦保存，它将自动匹配现有的列表并自动完成。','You can just type part of the domain.'=>'您可以输入域名的部分内容。','Domain'=>'域名','Cloudflare API'=>'Cloudflare API','Purge Everything'=>'清除全部','Cloudflare Cache'=>'Cloudflare 缓存','Development Mode will be turned off automatically after three hours.'=>'开发模式会在三小时后自动关闭。','Temporarily bypass Cloudflare cache. This allows changes to the origin server to be seen in realtime.'=>'临时跳过 Cloudflare 缓存。这可以让原始服务器的改变实时展现。','Development mode will be automatically turned off in %s.'=>'开发模式将在 %s 后自动关闭。','Current status is %s.'=>'当前状态为 %s。','Current status is %1$s since %2$s.'=>'当前状态自 %2$s 开始为 %1$s。','Check Status'=>'检查状态','Turn OFF'=>'关闭','Turn ON'=>'开启','Development Mode'=>'开发模式','Cloudflare Zone'=>'Cloudflare 区域','Cloudflare Domain'=>'Cloudflare 域名','Cloudflare'=>'Cloudflare','For example'=>'比如','Prefetching DNS can reduce latency for visitors.'=>'预读取DNS可降低访客的延迟。','DNS Prefetch'=>'DNS预读取','Adding Style to Your Lazy-Loaded Images'=>'增加样式到您的延迟加载图片中','Default value'=>'默认值','Static file type links to be replaced by CDN links.'=>'将被CDN链接替换的静态文件类型。','Drop Query String'=>'丢弃 Query String','Enable this option if you are using both HTTP and HTTPS in the same domain and are noticing cache irregularities.'=>'当您在同一域名下同时使用HTTP和HTTPS并注意到有缓存问题时，请激活此选项。','Improve HTTP/HTTPS Compatibility'=>'改善 HTTP/HTTPS 兼容性','Remove all previous image optimization requests/results, revert completed optimizations, and delete all optimization files.'=>'移除之前的全部图片优化请求和结果，重置已完成的优化并删除全部已优化的文件。','Destroy All Optimization Data'=>'销毁所有优化数据','Scan for any new unoptimized image thumbnail sizes and resend necessary image optimization requests.'=>'扫描新的未优化图片缩略图并发送图片优化请求。','This will increase the size of optimized files.'=>'这会增加优化后图片的体积。','Preserve EXIF data (copyright, GPS, comments, keywords, etc) when optimizing.'=>'在优化时保留EXIF信息 ( 如版权，GPS，描述等 ) 。','Clear Logs'=>'清除日志','To test the cart, visit the <a %s>FAQ</a>.'=>'要测试购物车，请访问 <a %s>FAQ</a>。',' %s ago'=>'%s 之前','WebP saved %s'=>'WebP 节省 %s','If you run into any issues, please refer to the report number in your support message.'=>'如果您遇到任何问题，请在客服信息里提及本报告号码。','Last pull initiated by cron at %s.'=>'最后运行cron的时间为%s。','Images will be pulled automatically if the cron job is running.'=>'如果计划任务在运行，图片会自动被取回。','Only press the button if the pull cron job is disabled.'=>'仅当取回计划任务被仅用时点击本按钮。','Pull Images'=>'取回图片','This process is automatic.'=>'整个过程是自动的。','Last Request'=>'上次请求','Images Pulled'=>'已取回的图片','Report'=>'报告','Send this report to LiteSpeed. Refer to this report number when posting in the WordPress support forum.'=>'发送本报告给LiteSpeed。在WordPress论坛发帖时您可以提到所生成的报告号码。','Send to LiteSpeed'=>'发送给LiteSpeed','LiteSpeed Optimization'=>'LiteSpeed 优化','Load Google Fonts Asynchronously'=>'异步加载Google Fonts','Browser Cache TTL'=>'浏览器缓存TTL','Learn More'=>'了解更多','Image groups total'=>'图片组总数','Images optimized and pulled'=>'已优化并抓回的图片','Images requested'=>'已请求的图片','Switched to optimized file successfully.'=>'成功切换到已优化的文件。','Restored original file successfully.'=>'成功恢复原始文件。','Enabled WebP file successfully.'=>'启用WebP文件成功。','Disabled WebP file successfully.'=>'禁用WebP文件成功。','Significantly improve load time by replacing images with their optimized %s versions.'=>'通过以优化过的 %s 版本替换图片来显著的改善加载时间。','Selected roles will be excluded from cache.'=>'选择的角色将不被缓存。','Tuning'=>'调整','Selected roles will be excluded from all optimizations.'=>'选择的角色将不做任何优化。','Role Excludes'=>'角色排除','Tuning Settings'=>'调整设定','If the tag slug is not found, the tag will be removed from the list on save.'=>'如果标签未被发现，标签将会在保存时从列表中移除。','If the category name is not found, the category will be removed from the list on save.'=>'如果分类未被发现，分类将会在保存时从列表中移除。','After the QUIC.cloud Image Optimization server finishes optimization, it will notify your site to pull the optimized images.'=>'在LiteSpeed图片优化服务器完成优化后，它会通知您的网站去抓取优化后的图片。','Send Optimization Request'=>'发送优化请求','Image Information'=>'图片信息','Total Reduction'=>'总共减少','Optimization Summary'=>'优化概要','LiteSpeed Cache Image Optimization'=>'LiteSpeed缓存图片优化','Image Optimization'=>'图片优化','For example, %s can be used for a transparent placeholder.'=>'比如，%s可以被用作背景透明的占位符。','By default a gray image placeholder %s will be used.'=>'默认一个灰色的图片占位符%s会被使用。','This can be predefined in %2$s as well using constant %1$s, with this setting taking priority.'=>'这也可以通过在%2$s中预定义%1$s实现。本选项优先级较预定义高。','Specify a base64 image to be used as a simple placeholder while images finish loading.'=>'指定要在图像加载完成时用作简单占位符的base64图像。','Elements with attribute %s in html code will be excluded.'=>'有属性%s的元素将被排除。','Filter %s is supported.'=>'支持过滤器%s。','Listed images will not be lazy loaded.'=>'列出的图片将不被延迟加载。','Lazy Load Image Excludes'=>'延迟加载图片排除','No optimization'=>'无优化','Prevent any optimization of listed pages.'=>'列出的页面将不被优化。','URI Excludes'=>'URI排除','Stop loading WordPress.org emoji. Browser default emoji will be displayed instead.'=>'停止加载 wordpress.org 表情包。浏览器默认表情包将被显示。','Both full URLs and partial strings can be used.'=>'完整URL和部分匹配字串都可以使用。','Load iframes only when they enter the viewport.'=>'仅在Iframe进入视野时加载它们。','Lazy Load Iframes'=>'延迟加载Iframe','This can improve page loading time by reducing initial HTTP requests.'=>'这可以通过减少初始HTTP请求数量来改善页面加载时间。','Load images only when they enter the viewport.'=>'仅在图片进入视野时加载它们。','Lazy Load Images'=>'延迟加载图片','Media Settings'=>'多媒体设定','Wildcard %1$s supported (match zero or more characters). For example, to match %2$s and %3$s, use %4$s.'=>'支持泛匹配%1$s (匹配空或多字符)。比如，要匹配%2$s和%3$s，使用%4$s。','To match the beginning, add %s to the beginning of the item.'=>'要匹配开头，在条目开头加上%s。','Maybe later'=>'来日方长','I\'ve already left a review'=>'我已经留过评论了','Welcome to LiteSpeed'=>'欢迎来到LiteSpeed','Remove WordPress Emoji'=>'移除WordPress表情包','More settings'=>'更多设定','Private cache'=>'私有缓存','Non cacheable'=>'不可缓存','Mark this page as '=>'标注此页为','Purge this page'=>'清除此页','Load JS Deferred'=>'延迟加载JS','Specify critical CSS rules for above-the-fold content when enabling %s.'=>'注明当激活%s时需要在页面显示时用到的关键CSS内容。','Critical CSS Rules'=>'关键CSS','Load CSS Asynchronously'=>'异步加载CSS','Prevent Google Fonts from loading on all pages.'=>'禁止在所有页面上加载Google字体。','Remove Google Fonts'=>'移除Google字体','This can improve your speed score in services like Pingdom, GTmetrix and PageSpeed.'=>'这会在诸如Pingdom、GTmetrix和PageSpeed等服务上改良您的速度评分。','Remove query strings from internal static resources.'=>'从内部静态资源中删除查询字符串。','Remove Query Strings'=>'移除 Query Strings','user agents'=>'用户代理','cookies'=>'cookie','Browser caching stores static files locally in the user\'s browser. Turn on this setting to reduce repeated requests for static files.'=>'浏览器缓存将静态文件本地化存储在用户浏览器。开启这个设定以降低对静态文件的重复请求。','Browser Cache'=>'浏览器缓存','tags'=>'标签','Do Not Cache Tags'=>'不缓存的标签','To exclude %1$s, insert %2$s.'=>'要排除%1$s，请插入%2$s。','categories'=>'类别','To prevent %s from being cached, enter them here.'=>'为了防止%s被缓存，请在此处输入它们。','Do Not Cache Categories'=>'不缓存的类别','Query strings containing these parameters will not be cached.'=>'包含这些参数的查询字符串将不会被缓存。','Do Not Cache Query Strings'=>'不缓存的Query String','Paths containing these strings will not be cached.'=>'包含这些字串的路径将不被缓存。','Do Not Cache URIs'=>'不缓存的URI','One per line.'=>'每行一个。','URI Paths containing these strings will NOT be cached as public.'=>'包含这些字串URI路径将不被存储为公开。','Private Cached URIs'=>'私有缓存URI','Paths containing these strings will not be served from the CDN.'=>'包含这些字串的路径将不通过CDN服务。','Exclude Path'=>'排除路径','Include File Types'=>'包括文件类型','Serve all JavaScript files through the CDN. This will affect all enqueued WP JavaScript files.'=>'将所有JavaScript文件通过CDN服务。这会影响到所有WP内嵌的JavaScript文件。','Include JS'=>'包含JS','Serve all CSS files through the CDN. This will affect all enqueued WP CSS files.'=>'将所有CSS文件通过CDN服务。这会影响到所有WP内嵌的CSS文件。','Include CSS'=>'包含CSS','Include Images'=>'包含图片','CDN URL to be used. For example, %s'=>'要使用的CDN URL。比如%s','CDN URL'=>'CDN URL','Site URL to be served through the CDN. Beginning with %1$s. For example, %2$s.'=>'要通过CDN服务的站内URL。以%1$s开始。例如%2$s。','Original URLs'=>'原始URl','CDN Settings'=>'CDN设定','CDN'=>'CDN','OFF'=>'关闭','ON'=>'开启','Notified LiteSpeed Web Server to purge CSS/JS entries.'=>'通知LiteSpeed服务器清除CSS/JS缓存。','Minify HTML content.'=>'最小化HTML代码。','HTML Minify'=>'HTML最小化','JS Excludes'=>'JS排除','JS Combine'=>'JS合并','JS Minify'=>'JS最小化','CSS Excludes'=>'CSS排除','CSS Combine'=>'CSS合并','CSS Minify'=>'CSS最小化','Please test thoroughly when enabling any option in this list. After changing Minify/Combine settings, please do a Purge All action.'=>'请详细测试本处所有功能，在您正式使用它们之前。修改了最小化/合并设定后，请务必执行清除全部的操作。','This will purge all minified/combined CSS/JS entries only'=>'这将清除所有最小化或合并的CSS/JS缓存','Purge %s Error'=>'清除 %s 错误','Database Optimizer'=>'数据库优化','Optimize all tables in your database'=>'优化您数据库中的所有数据表','Optimize Tables'=>'优化数据表','Clean all transient options'=>'清理全部即时Transient内容','All Transients'=>'全部即时Transients内容','Clean expired transient options'=>'清理过期transient选项','Expired Transients'=>'过期Transients','Clean all trackbacks and pingbacks'=>'清理全部trackback和pingback','Trackbacks/Pingbacks'=>'Trackbacks/Pingbacks','Clean all trashed comments'=>'清理所有回收站的评论','Trashed Comments'=>'回收站评论','Clean all spam comments'=>'清理所有垃圾评论','Spam Comments'=>'垃圾评论','Clean all trashed posts and pages'=>'清理所有回收站的文章和页面','Trashed Posts'=>'回收站的文章','Clean all auto saved drafts'=>'清理所有自动保存的草稿','Auto Drafts'=>'自动保存草稿','Clean all post revisions'=>'清理全部文章修订记录','Post Revisions'=>'文章修订记录','Clean All'=>'清理全部','Optimized all tables.'=>'已优化全部数据表。','Clean all transients successfully.'=>'成功清理全部Transient。','Clean expired transients successfully.'=>'成功清理过期Transient。','Clean trackbacks and pingbacks successfully.'=>'成功清理trackback和pingback。','Clean trashed comments successfully.'=>'成功清理回收站评论。','Clean spam comments successfully.'=>'成功清理垃圾评论。','Clean trashed posts and pages successfully.'=>'成功清理回收站文章和页面。','Clean auto drafts successfully.'=>'成功清理自动保存的草稿。','Clean post revisions successfully.'=>'成功清理文章修订记录。','Clean all successfully.'=>'全部清理成功。','Default Private Cache TTL'=>'默认私有缓存TTL时间','If your site contains public content that certain user roles can see but other roles cannot, you can specify a Vary Group for those user roles. For example, specifying an administrator vary group allows there to be a separate publicly-cached page tailored to administrators (with “edit” links, etc), while all other user roles see the default public page.'=>'如果您的网站包含公开的内容，其只能被特定的用户组看到但其他不能看到，您可以设定一个Vary群组。比如，设定管理员vary群组为不同值，特定页面（如包含像编辑按钮等只有管理员能看到的按钮），将会被单独存储为公开页面，而其他用户将看到默认的公开页面。','Vary Group'=>'Vary 群组','Cache the built-in Comment Form ESI block.'=>'缓存内置的评论表单ESI块。','Cache Comment Form'=>'缓存评论表单','Cache Admin Bar'=>'缓存管理员条','Turn ON to cache public pages for logged in users, and serve the Admin Bar and Comment Form via ESI blocks. These two blocks will be uncached unless enabled below.'=>'启用则将缓存登录用户为公开缓存，而管理员条和评论表单将通过ESI块单独缓存。这两个块仅在下面的设定启用时方可缓存。','ESI allows you to designate parts of your dynamic page as separate fragments that are then assembled together to make the whole page. In other words, ESI lets you “punch holes” in a page, and then fill those holes with content that may be cached privately, cached publicly with its own TTL, or not cached at all.'=>'ESI允许您指定动态页面为不同区块，然后将它们合并为一个完整页面。换句话说，ESI允许您在一个页面上打洞，然后将这些洞洞用独立的缓存内容(有自己的TTL存储时间的私有或公有缓存)填充，或者用不缓存的内容填充亦可。','With ESI (Edge Side Includes), pages may be served from cache for logged-in users.'=>'有ESI(Edge Side Includes)相伴，登录用户也可以看到缓存的页面。','Private'=>'私有','Public'=>'公开','Purge Settings'=>'清除设定','Cache Mobile'=>'缓存手机访客','Advanced level will log more details.'=>'高级模式会记录更多细节。','Basic'=>'基本','The maximum average server load allowed while crawling. The number of crawler threads in use will be actively reduced until average server load falls under this limit. If this cannot be achieved with a single thread, the current crawler run will be terminated.'=>'爬行时允许的最大化平均服务器荷载。使用中的爬虫线程的数量将会主动减少直到平均服务器荷载降到这个限制为止。如果单线程仍不能降到该限制，当前爬虫将自觉退出。','Cache Login Page'=>'缓存登录页面','Cache requests made by WordPress REST API calls.'=>'缓存由WordPress REST API发出的请求。','Cache REST API'=>'缓存REST API','Privately cache commenters that have pending comments. Disabling this option will serve non-cacheable pages to commenters. (LSWS %s required)'=>'用私有缓存存储有待审核评论的评论者。禁用此选项会提供没有缓存的页面给评论者。(需要LSWS %s)','Cache Commenters'=>'缓存评论者','Privately cache frontend pages for logged-in users. (LSWS %s required)'=>'用私有缓存为登录用户存储前台页面。(需要LSWS %s)','Cache Logged-in Users'=>'缓存登录用户','Cache Control Settings'=>'缓存控制设定','ESI'=>'ESI','Excludes'=>'例外规则','Purge'=>'清除规则','Cache'=>'缓存规则','WooCommerce'=>'WooCommerce','Current server time is %s.'=>'当前服务器时间为%s。','Specify the time to purge the "%s" list.'=>'设定清除"%s"列表的时间。','Both %1$s and %2$s are acceptable.'=>'%1$s和%2$s都可以使用。','Scheduled Purge Time'=>'计划清除时间','The URLs here (one per line) will be purged automatically at the time set in the option "%s".'=>'这里填写的URL(一行一个)将会在设定"%s"的时间自动被清除。','Scheduled Purge URLs'=>'计划清除URL','Shorten query strings in the debug log to improve readability.'=>'缩短Query Strings在debug日志中的长度以增强可读性。','Heartbeat'=>'心跳包','MB'=>'MB','Log File Size Limit'=>'日志文件尺寸限制','<p>Please add/replace the following codes into the beginning of %1$s:</p> %2$s'=>'<p>请添加/替换下列代码到%1$s的开头位置:</p> %2$s','%s file not writable.'=>'%s文件不可写。','%s file not readable.'=>'%s文件不可读。','Collapse Query Strings'=>'缩短Query Strings','ESI Settings'=>'ESI设定','A TTL of 0 indicates do not cache.'=>'TTL为0标明不缓存。','Recommended value: 28800 seconds (8 hours).'=>'推荐值：28800秒（8小时）。','Enable ESI'=>'启用ESI','Custom Sitemap'=>'自定义站点地图','Purge pages by relative or full URL.'=>'清除页面基于相对或完整URL。','The crawler feature is not enabled on the LiteSpeed server. Please consult your server admin or hosting provider.'=>'爬虫功能尚未在LiteSpeed服务器上启用。请咨询您的服务器管理员。','WARNING'=>'警告','The next complete sitemap crawl will start at'=>'下次完整的站点地图爬行将始于','Failed to write to %s.'=>'写入%s失败。','Folder is not writable: %s.'=>'文件夹不可写: %s.','Can not create folder: %1$s. Error: %2$s'=>'无法创建文件夹:%1$s. 错误: %2$s','Folder does not exist: %s'=>'文件夹不存在: %s','Notified LiteSpeed Web Server to purge the list.'=>'已知会LiteSpeed服务器清除列表。','Allows listed IPs (one per line) to perform certain actions from their browsers.'=>'允许列表中IP(每行一个)从它们的浏览器中执行特定操作。','Server Load Limit'=>'服务器荷载上限','Specify how long in seconds before the crawler should initiate crawling the entire sitemap again.'=>'设定爬虫再从网站地图起始开始爬的间隔时间。','Crawl Interval'=>'爬行间隔','Then another WordPress is installed (NOT MULTISITE) at %s'=>'另一份WordPress(非多站点)安装在%s','LiteSpeed Cache Network Cache Settings'=>'LiteSpeed缓存网络设定','Select below for "Purge by" options.'=>'选择清除选项。','LiteSpeed Cache CDN'=>'LiteSpeed缓存CDN','No crawler meta file generated yet'=>'尚无爬虫爬过的痕迹','Show crawler status'=>'显示爬虫状态','Watch Crawler Status'=>'查看爬虫状态','Run frequency is set by the Interval Between Runs setting.'=>'运行频率由运行中的间隔设定。','Manually run'=>'手动运行','Reset position'=>'重置位置','Run Frequency'=>'运行频率','Cron Name'=>'Cron名字','Crawler Cron'=>'爬虫Cron','%d minute'=>'%d分钟','%d minutes'=>'%d分钟','%d hour'=>'%d小时','%d hours'=>'%d小时','Generated at %s'=>'生成于%s','LiteSpeed Cache Crawler'=>'LiteSpeed缓存爬虫','Crawler'=>'爬虫','https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration'=>'https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration','Notified LiteSpeed Web Server to purge all pages.'=>'已通知LiteSpeed网络服务器清除页面。','All pages with Recent Posts Widget'=>'所有包含最新帖部件的页面','Pages'=>'独立页面','This will Purge Pages only'=>'这将只清除独立页面','Purge Pages'=>'清除独立页面','Cancel'=>'取消','Activate'=>'启用','Email Address'=>'Email 地址','Install Now'=>'现在安装','Purged the blog!'=>'清除Blog！','Purged All!'=>'清除全部！','Notified LiteSpeed Web Server to purge error pages.'=>'已通知LiteSpeed网络服务器清除错误页面。','If using OpenLiteSpeed, the server must be restarted once for the changes to take effect.'=>'如果正在使用OpenLiteSpeed，服务器必须从新启动一次以使更改生效。','If the login cookie was recently changed in the settings, please log out and back in.'=>'如果设置中登录Cookie最近有改动，请登出后从新登录。','However, there is no way of knowing all the possible customizations that were implemented.'=>'然而，不可能知道所有已安装的定制可能情况。','The LiteSpeed Cache plugin is used to cache pages - a simple way to improve the performance of the site.'=>'LiteSpeed缓存插件被用作缓存页面——一个简单的改善网站性能的方式。','The network admin setting can be overridden here.'=>'网络管理员设置可以在这里被覆盖。','Specify how long, in seconds, public pages are cached.'=>'指定公共页面缓存的时间（以秒为单位）。','Specify how long, in seconds, private pages are cached.'=>'指定私有页面缓存的时间（以秒为单位）。','Purge pages by post ID.'=>'根据贴子ID清除页面。','Purge the LiteSpeed cache entries created by this plugin'=>'清除此插件创建的LiteSpeed缓存条目','Purge %s error pages'=>'清除 %s 个错误页面','This will Purge Front Page only'=>'这将仅清除首页','Purge pages by tag name - e.g. %2$s should be used for the URL %1$s.'=>'根据标签名清除页面。例如：%2$s应该被用于URL %1$s。','Purge pages by category name - e.g. %2$s should be used for the URL %1$s.'=>'根据分类名清除页面。例如：%2$s应该被用于URL %1$s。','If only the WordPress site should be purged, use Purge All.'=>'如果只是WordPress网站需要清除，请使用清除全部。','Notified LiteSpeed Web Server to purge everything.'=>'已知会LiteSpeed网页服务器清除所有缓存。','Use Primary Site Configuration'=>'使用主站配置','This will disable the settings page on all subsites.'=>'这会禁用所有子站点的设置页面。','Check this option to use the primary site\'s configuration for all subsites.'=>'选择本选项以使用主站的配置来应用到全部子站上。','Save Changes'=>'保存修改','The following options are selected, but are not editable in this settings page.'=>'在这个设置页面中下面的选项被选择，但不可修改。','The network admin selected use primary site configs for all subsites.'=>'网络管理员选择全部子站点使用首站点配置。','Empty Entire Cache'=>'清空整个缓存','This action should only be used if things are cached incorrectly.'=>'仅当不能正常缓存时方可使用本操作。','This may cause heavy load on the server.'=>'这样可能导致服务器高负荷。','This will clear EVERYTHING inside the cache.'=>'这将清空缓存内的所有内容。','LiteSpeed Cache Purge All'=>'LiteSpeed缓存清除全部','If you would rather not move at litespeed, you can deactivate this plugin.'=>'如果您宁愿不迁移到LiteSpeed，您可以禁用本插件。','Create a post, make sure the front page is accurate.'=>'创建一个文章，确保前台页面的精确。','Visit the site while logged out.'=>'登出状态下访问本站。','Examples of test cases include:'=>'测试情况示例包括：','For that reason, please test the site to make sure everything still functions properly.'=>'因为那个原因，请测试网站确保所有功能正常运行。','This message indicates that the plugin was installed by the server admin.'=>'这则消息表明本插件由服务器管理员安装。','LiteSpeed Cache plugin is installed!'=>'LiteSpeed缓存插件已经安装！','Debug Log'=>'Debug日志','Admin IP Only'=>'仅管理员IP','Specify how long, in seconds, REST calls are cached.'=>'指定REST调用被缓存的时间（以秒为单位）。','The environment report contains detailed information about the WordPress configuration.'=>'环境报告包含WordPress配置的所有详细信息。','The server will determine if the user is logged in based on the existence of this cookie.'=>'服务器将根据 cookie 的存在判断用户是否已登录。','Note'=>'注释','After verifying that the cache works in general, please test the cart.'=>'检查缓存正常工作后，请测试购物车。','When enabled, the cache will automatically purge when any plugin, theme or the WordPress core is upgraded.'=>'如果启用，当任何插件、主题或WordPress内核更新时，缓存将自动清除。','Purge All On Upgrade'=>'升级时清除全部','Product Update Interval'=>'产品更新间隔','Determines how changes in product quantity and product stock status affect product pages and their associated category pages.'=>'决定修改产品数量和库存状态时怎样影响产品页和它们相关的分类页。','Always purge both product and categories on changes to the quantity or stock status.'=>'当数量或库存状态改变时总是清除产品和分类页。','Do not purge categories on changes to the quantity or stock status.'=>'当数量或库存状态改变时不要清除分类。','Purge product only when the stock status changes.'=>'仅当库存状态改变时清除产品页。','Purge product and categories only when the stock status changes.'=>'仅当库存状态改变时清除产品和分类页。','Purge categories only when stock status changes.'=>'仅当库存状态改变时清除分类页。','Purge product on changes to the quantity or stock status.'=>'当数量或库存状态改变时清除产品页。','Htaccess did not match configuration option.'=>'Htaccess和配置内容不匹配。','If this is set to a number less than 30, feeds will not be cached.'=>'如果设定为小于30的数，feeds将不会缓存。','Specify how long, in seconds, feeds are cached.'=>'指定feeds缓存多久，单位秒。','Default Feed TTL'=>'默认Feed TTL','Failed to get %s file contents.'=>'获取%s文件内容失败。','Disabling this option may negatively affect performance.'=>'禁用该选项或会影响性能。','Invalid login cookie. Invalid characters found.'=>'无效的登录cookie。 找到无效的字符。','WARNING: The .htaccess login cookie and Database login cookie do not match.'=>'警告: .htaccess中的登录cookie信息和数据库的登录cookie内容不匹配。','Invalid login cookie. Please check the %s file.'=>'无效的登录cookie。 请检查%s文件。','The cache needs to distinguish who is logged into which WordPress site in order to cache correctly.'=>'缓存需要分辨谁登录到哪个WordPress网站以正确缓存。','There is a WordPress installed for %s.'=>'有一份WordPress安装到%s。','Example use case:'=>'示范使用案例：','The cookie set here will be used for this WordPress installation.'=>'本处设置的cookie将用于这个WordPress安装。','If every web application uses the same cookie, the server may confuse whether a user is logged in or not.'=>'如果所有网页应用都使用同样的cookie，服务器将混淆一个用户是否已登录。','This setting is useful for those that have multiple web applications for the same domain.'=>'本设置对同一域名下有多个网页应用的情况很有用。','The default login cookie is %s.'=>'默认的登录cookie是%s。','Login Cookie'=>'登录Cookie','More information about the available commands can be found here.'=>'有关可用命令的更多信息，请参见此处。','These settings are meant for ADVANCED USERS ONLY.'=>'这些设置仅供高级用户使用。','Current %s Contents'=>'当前%s内容','Advanced'=>'高级','Advanced Settings'=>'高级设置','Purge List'=>'清除列表','Purge By...'=>'清除...','URL'=>'URL','Tag'=>'标签','Post ID'=>'文章ID','Category'=>'分类','NOTICE: Database login cookie did not match your login cookie.'=>'注意: 数据库登录cookie和您的登录cookie不匹配。','Purge url %s'=>'清除url %s','Purge tag %s'=>'清除标签%s','Purge category %s'=>'清除分类%s','When disabling the cache, all cached entries for this site will be purged.'=>'禁用缓存时，本博客的全部缓存都将被清除。','NOTICE'=>'注意','This setting will edit the .htaccess file.'=>'这个选项将修改.htaccess文件。','LiteSpeed Cache View .htaccess'=>'LiteSpeed 缓存查看 .htaccess','Failed to back up %s file, aborted changes.'=>'无法备份%s文件，中止更改。','Do Not Cache Cookies'=>'不缓存的Cookie','Do Not Cache User Agents'=>'不缓存的用户代理','This is to ensure compatibility prior to enabling the cache for all sites.'=>'这用于确保在全部站点上激活缓存前的兼容性。','Network Enable Cache'=>'网络级激活缓存','NOTICE:'=>'注意：','Other checkboxes will be ignored.'=>'其它勾选框将会被无视。','Select "All" if there are dynamic widgets linked to posts on pages other than the front or home pages.'=>'如果有动态小部件链接到除首页和主页外的文章或页面，请选择“全部”。','List of Mobile User Agents'=>'移动用户代理列表','File %s is not writable.'=>'文件%s不可写。','JS Settings'=>'JS 设置','Manage'=>'管理','Default Front Page TTL'=>'默认首页TTL','Notified LiteSpeed Web Server to purge the front page.'=>'已知会LiteSpeed网页服务器清除首页缓存。','Purge Front Page'=>'清除首页','Example'=>'示例','All tags are cached by default.'=>'所有的标签默认都会被缓存。','All categories are cached by default.'=>'所有的分类默认都会被缓存。','To do an exact match, add %s to the end of the URL.'=>'若要做精确匹配，请添加“%s”到URL的结尾。','The URLs will be compared to the REQUEST_URI server variable.'=>'URL将会和服务器变量REQUEST_URI对比。','Select only the archive types that are currently used, the others can be left unchecked.'=>'只选择正使用中的存档类型，其它类型可留空。','Notes'=>'提示','Use Network Admin Setting'=>'使用网络管理员设定','Disable'=>'取消','Enabling LiteSpeed Cache for WordPress here enables the cache for the network.'=>'在此启用Wordpress专用的LiteSpeed缓存会给整个网络激活缓存。','Disabled'=>'不启用','Enabled'=>'已启用','Do Not Cache Roles'=>'勿存我的规则设定','https://www.litespeedtech.com'=>'https://www.litespeedtech.com','LiteSpeed Technologies'=>'LiteSpeed Technologies','LiteSpeed Cache'=>'LiteSpeed缓存','Debug Level'=>'Debug等级','Notice'=>'消息','Term archive (include category, tag, and tax)'=>'类目存档(包含分类，标签和类别)','Daily archive'=>'按日存档','Monthly archive'=>'按月存档','Yearly archive'=>'按年存档','Post type archive'=>'主题类型存档','Author archive'=>'作者存档','Home page'=>'首页','Front page'=>'静态首页','All pages'=>'所有页面','Select which pages will be automatically purged when posts are published/updated.'=>'选择发表或更新文章时哪些页面将被清除。','Auto Purge Rules For Publish/Update'=>'发表/更新时的自动清除规则','Default Public Cache TTL'=>'默认公开缓存的TTL','seconds'=>'秒','Admin IPs'=>'管理员 IP','General'=>'常规','LiteSpeed Cache Settings'=>'LiteSpeed缓存设置','Notified LiteSpeed Web Server to purge all LSCache entries.'=>'已知会LiteSpeed网页服务器清除全部缓存。','Purge All'=>'清除全部','Settings'=>'设置']];