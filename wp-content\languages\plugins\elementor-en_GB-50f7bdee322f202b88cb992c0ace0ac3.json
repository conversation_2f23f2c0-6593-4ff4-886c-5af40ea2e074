{"translation-revision-date": "2024-12-16 16:53:45+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Customize every part of your site": ["Customise every part of your site"], "Get total control, consistency and a faster workflow by designing the recurring parts that make up a complete website like the Header & Footer, Archive, 404, WooCommerce pages and more.": ["Get total control, consistency and a faster workflow by designing the recurring parts that make up a complete website like the Header & Footer, Archive, 404, WooCommerce pages and more."], "Upgrade Now": ["Upgrade Now"], "Upgrade": ["Upgrade"], "404 page": ["404 page"], "search results page": ["search results page"], "This file type is not allowed": ["This file type is not allowed"], "First, enable unfiltered file uploads.": ["First, enable unfiltered file uploads."], "Header": ["Header"], "Footer": ["Footer"], "Archive": ["Archive"], "Single Post": ["Single Post"], "Single Page": ["Single Page"], "Error:": ["Error:"], "Products Archive": ["Products Archive"], "What is a 404 Page Template?": ["What is a 404 Page Template?"], "What is a Products Archive Template?": ["What is a Products Archive Template?"], "What is a Single Product Template?": ["What is a Single Product Template?"], "What is a Footer Template?": ["What is a Footer Template?"], "What is a Single Page Template?": ["What is a Single Page Template?"], "What is a Single Post Template?": ["What is a Single Post Template?"], "What is an Archive Template?": ["What is an Archive Template?"], "App could not be loaded": ["App could not be loaded"], "What is a Header Template?": ["What is a <PERSON><PERSON> Template?"], "A 404 page template allows you to easily design the layout and style of the page that is displayed when a visitor arrives at a page that does not exist.": ["A 404 page template allows you to easily design the layout and style of the page that is displayed when a visitor arrives at a page that does not exist."], "A single product template allows you to easily design the layout and style of WooCommerce single product pages, and apply that template to various conditions that you assign.": ["A single product template allows you to easily design the layout and style of WooCommerce single product pages, and apply that template to various conditions that you assign."], "If you’d like a different style for a specific category, it’s easy to create a separate archive template whose condition is to only display when users are viewing that category’s list of posts.": ["If you’d like a different style for a specific category, it’s easy to create a separate archive template whose condition is to only display when users are viewing that category’s list of posts."], "Product": ["Product"], "What is a Search Results Template?": ["What is a Search Results Template?"], "You can customize the message if there are no results for the search term.": ["You can customise the message if there are no results for the search term."], "You can easily control the layout and design of the Search Results page with the Search Results template, which is simply a special archive template just for displaying search results.": ["You can easily control the layout and design of the Search Results page with the Search Results template, which is simply a special archive template just for displaying search results."], "Keep your site's visitors happy when they get lost by displaying your recent posts, a search bar, or any information that might help the user find what they were looking for.": ["Keep your site's visitors happy when they get lost by displaying your recent posts, a search bar, or any information that might help the user find what they were looking for."], "You can create multiple archive product templates, and assign each to different categories of products. This gives you the freedom to customize the appearance for each type of product being shown.": ["You can create multiple archive product templates, and assign each to different categories of products. This gives you the freedom to customise the appearance for each type of product being shown."], "A products archive template allows you to easily design the layout and style of your WooCommerce shop page or other product archive pages - those pages that show a list of products, which may be filtered by terms such as categories, tags, etc.": ["A products archive template allows you to easily design the layout and style of your WooCommerce shop page or other product archive pages - those pages that show a list of products, which may be filtered by terms such as categories, tags, etc."], "You can create multiple single product templates, and assign each to different types of products, enabling a custom design for each group of similar products.": ["You can create multiple single product templates, and assign each to different types of products, enabling a custom design for each group of similar products."], "An archive template allows you to easily design the layout and style of archive pages - those pages that show a list of posts (e.g. a blog’s list of recent posts), which may be filtered by terms such as authors, categories, tags, search results, etc.": ["An archive template allows you to easily design the layout and style of archive pages - those pages that show a list of posts (e.g. a blog’s list of recent posts), which may be filtered by terms such as authors, categories, tags, search results, etc."], "A single page template allows you to easily create the layout and style of pages, ensuring design consistency across all the pages of your site.": ["A single page template allows you to easily create the layout and style of pages, ensuring design consistency across all the pages of your site."], "A single post template allows you to easily design the layout and style of posts, ensuring a design consistency throughout all your blog posts, for example.": ["A single post template allows you to easily design the layout and style of posts, ensuring a design consistency throughout all your blog posts, for example."], "The footer template allows you to easily design and edit custom WordPress footers without the limits of your theme’s footer design constraints": ["The footer template allows you to easily design and edit custom WordPress footers without the limits of your theme’s footer design constraints"], "The header template allows you to easily design and edit custom WordPress headers so you are no longer constrained by your theme’s header design limitations.": ["The header template allows you to easily design and edit custom WordPress headers so you are no longer constrained by your theme’s header design limitations."], "You can create multiple single post templates, and assign each to a different category.": ["You can create multiple single post templates, and assign each to a different category."], "You can create multiple single page templates, and assign each to different areas of your site.": ["You can create multiple single page templates, and assign each to different areas of your site."], "You can create multiple footers, and assign each to different areas of your site.": ["You can create multiple footers, and assign each to different areas of your site."], "You can create multiple headers, and assign each to different areas of your site.": ["You can create multiple headers, and assign each to different areas of your site."], "All Parts": ["All Parts"], "Not Found": ["Not Found"], "Site Parts": ["Site Parts"], "Theme Builder could not be loaded": ["Theme Builder could not be loaded"], "Tip": ["Tip"], "Watch Video": ["Watch Video"], "We’re sorry, but something went wrong. Click on ‘Learn more’ and follow each of the steps to quickly solve it.": ["We’re sorry, but something went wrong. <PERSON>lick on ‘Learn more’ and follow each of the steps to quickly solve it."], "Theme Builder": ["Theme Builder"], "Continue": ["Continue"], "Skip": ["<PERSON><PERSON>"], "Something went wrong.": ["Something went wrong."], "Add New": ["Add New"], "Select File": ["Select File"], "Enable": ["Enable"], "Close": ["Close"], "Learn More": ["Learn More"], "Info": ["Info"], "Go Back": ["Go Back"], "Loading": ["Loading"], "Elementor": ["<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/js/app-packages.js"}}