# Translation of Themes - Twenty Twenty in Chinese (China)
# This file is distributed under the same license as the Themes - Twenty Twenty package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-11-14 20:54:34+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Twenty Twenty\n"

#. Description of the theme
msgid "Our default theme for 2020 is designed to take full advantage of the flexibility of the block editor. Organizations and businesses have the ability to create dynamic landing pages with endless layouts using the group and column blocks. The centered content column and fine-tuned typography also makes it perfect for traditional blogs. Complete editor styles give you a good idea of what your content will look like, even before you publish. You can give your site a personal touch by changing the background colors and the accent color in the Customizer. The colors of all elements on your site are automatically calculated based on the colors you pick, ensuring a high, accessible color contrast for your visitors."
msgstr "我们的2020年默认主题旨在充分利用区块编辑器的灵活性。组织和企业可以使用群组和栏目区块创建具有无限布局的动态到达页面。居中的内容栏和经过微调的版式也使其非常适合传统博客。完整的编辑器样式使您即使在发布之前也可以清楚了解内容的外观。您可以通过在定制器中更改背景色和强调色来使您的网站更具个性。网站上所有元素的颜色都是根据您选择的颜色自动计算得出的，让访问者在何处都能看到足够高的颜色对比度，籍此保证访问无障碍。"

#. Theme Name of the theme
msgid "Twenty Twenty"
msgstr "二〇二〇"

#: template-parts/entry-author-bio.php:29
msgid "View Archive <span aria-hidden=\"true\">&rarr;</span>"
msgstr "查看归档 <span aria-hidden=\"true\">&rarr;</span>"

#: inc/starter-content.php:38
msgctxt "Theme starter content"
msgid "The New UMoMA Opens its Doors"
msgstr "全新UMoMA开门迎客"

#: inc/template-tags.php:393
msgctxt "A string that is output before one or more categories"
msgid "In"
msgstr "分类于"

#: inc/starter-content.php:150
msgid "Join the Club"
msgstr "加入我们"

#: inc/starter-content.php:147
msgid "Members get access to exclusive exhibits and sales. Our memberships cost $99.99 and are billed annually."
msgstr "会员可以参观独家展览和销售。我们的会员费为$99.99，按年收费。"

#: inc/starter-content.php:144
msgid "Become a Member and Get Exclusive Offers!"
msgstr "成为会员并获得独家优惠！"

#: inc/starter-content.php:136
msgid "The exhibitions are produced by UMoMA in collaboration with artists and museums around the world and they often attract international attention. UMoMA has received a Special Commendation from the European Museum of the Year, and was among the top candidates for the Swedish Museum of the Year Award as well as for the Council of Europe Museum Prize."
msgstr "这些展览是由UMoMA与世界各地的艺术家和博物馆合作制作的，它们经常引起国际关注。UMoMA已获得欧洲年度博物馆的特别表彰，并且是瑞典年度博物馆奖和欧洲理事会博物馆奖的最佳候选人之一。"

#: inc/starter-content.php:133
msgid "With seven floors of striking architecture, UMoMA shows exhibitions of international contemporary art, sometimes along with art historical retrospectives. Existential, political and philosophical issues are intrinsic to our programme. As visitor you are invited to guided tours artist talks, lectures, film screenings and other events with free admission"
msgstr "UMoMA共有七层引人注目的建筑，可举办国际当代艺术展览，有时还包括艺术史回顾展。存在性、政治性和哲学性的问题是我们展览的固有内容。作为访客，您可以免费参观艺术家的导览、讲座、电影放映和其他活动"

#: inc/starter-content.php:129
msgid "”Cyborgs, as the philosopher Donna Haraway established, are not reverent. They do not remember the cosmos.”"
msgstr "“Cyborg，正如哲学家唐娜·哈拉维所讲，是不恭敬的。他们没有记住宇宙。”"

#: inc/starter-content.php:113
msgid "From Signac to Matisse"
msgstr "从希涅克到马蒂斯"

#: inc/starter-content.php:98
msgid "The Life I Deserve"
msgstr "我应得的生活"

#: inc/starter-content.php:84 inc/starter-content.php:116
msgid "October 1 -- December 1"
msgstr "10月1日—12月1日"

#: inc/starter-content.php:81
msgid "Theatre of Operations"
msgstr "战区"

#: inc/starter-content.php:72 inc/starter-content.php:87
#: inc/starter-content.php:104 inc/starter-content.php:119
msgid "Read More"
msgstr "阅读更多"

#: inc/starter-content.php:69 inc/starter-content.php:101
msgid "August 1 -- December 1"
msgstr "8月1日—12月1日"

#: inc/starter-content.php:66
msgid "Works and Days"
msgstr "工作与日子"

#: inc/starter-content.php:55
msgid "The premier destination for modern art in Northern Sweden. Open from 10 AM to 6 PM every day during the summer months."
msgstr "瑞典北部现代艺术的主要目的地。在夏季每天从10时到18时开放。"

#: inc/starter-content.php:47
msgid "The New UMoMA Opens its Doors"
msgstr "全新UMoMA开门迎客"

#: classes/class-twentytwenty-customize.php:395
msgid "Overlay Opacity"
msgstr "覆层不透明度"

#: classes/class-twentytwenty-customize.php:375
msgid "The color used for the text in the overlay."
msgstr "覆层内文字的颜色。"

#: classes/class-twentytwenty-customize.php:374
msgid "Overlay Text Color"
msgstr "覆层文字颜色"

#: classes/class-twentytwenty-customize.php:353
msgid "The color used for the overlay. Defaults to the accent color."
msgstr "覆层的颜色，默认为强调色。"

#: classes/class-twentytwenty-customize.php:352
msgid "Overlay Background Color"
msgstr "覆层背景色"

#: classes/class-twentytwenty-customize.php:283
msgid "Settings for the \"Cover Template\" page template. Add a featured image to use as background."
msgstr "”封面模板“页面模板设置。"

#: classes/class-twentytwenty-customize.php:183
msgid "Apply a custom color for links, buttons, featured images."
msgstr "向按钮、链接、特色图像应用自定义颜色"

#: classes/class-twentytwenty-customize.php:126
msgid "Custom"
msgstr "自定义"

#: classes/class-twentytwenty-customize.php:125
msgid "Default"
msgstr "默认"

#: classes/class-twentytwenty-customize.php:123
msgid "Primary Color"
msgstr "主颜色"

#: template-parts/modal-search.php:20 searchform.php:24
msgid "Search for:"
msgstr "搜索："

#: index.php:99
msgid "search again"
msgstr "重新搜索"

#. translators: %s: Number of search results
#: index.php:39
msgid "We found %s result for your search."
msgid_plural "We found %s results for your search."
msgstr[0] "我们搜索到了%s个结果。"

#. translators: %s: HTML character for up arrow
#: footer.php:49
msgid "Up %s"
msgstr "向上 %s"

#. translators: %s: HTML character for up arrow
#: footer.php:43
msgid "To the top %s"
msgstr "至顶部 %s"

#. translators: Copyright date format, see https://secure.php.net/date
#: footer.php:25
msgctxt "copyright date format"
msgid "Y"
msgstr "Y"

#: 404.php:24
msgid "404 not found"
msgstr "404未找到"

#: comments.php:127
msgid "Comments are closed."
msgstr "评论已关闭。"

#: inc/starter-content.php:196
msgid "Social Links Menu"
msgstr "社交网络链接菜单"

#: template-parts/content.php:48 template-parts/content-cover.php:138
msgid "Pages:"
msgstr "页码："

#: functions.php:496
msgid "Secondary"
msgstr "从"

#: functions.php:258
msgid "Footer Menu"
msgstr "页脚菜单"

#: template-parts/content.php:48 template-parts/content-cover.php:138
msgid "Page"
msgstr "页"

#: template-parts/footer-menus-widgets.php:37
msgid "Footer"
msgstr "页脚"

#. translators: %s: Post title. Only visible to screen readers.
#: inc/template-tags.php:206
msgid "Edit <span class=\"screen-reader-text\">%s</span>"
msgstr "编辑<span class=\"screen-reader-text\">%s</span>"

#: functions.php:491 inc/starter-content.php:176 inc/starter-content.php:186
msgid "Primary"
msgstr "主"

#: comments.php:88
msgid "Comments"
msgstr "评论"

#. translators: 1: number of comments, 2: post title
#: comments.php:41
msgctxt "comments title"
msgid "%1$s reply on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s replies on &ldquo;%2$s&rdquo;"
msgstr[0] "&ldquo;%2$s&rdquo;上的%1$s条回复"

#. translators: %s: post title
#: comments.php:37
msgctxt "comments title"
msgid "One reply on &ldquo;%s&rdquo;"
msgstr "&ldquo;%s&rdquo;上的一条回复"

#: comments.php:34
msgid "Leave a comment"
msgstr "留下评论"

#: classes/class-twentytwenty-walker-comment.php:92
msgid "Your comment is awaiting moderation."
msgstr "您的评论正在等待审核。"

#: classes/class-twentytwenty-walker-comment.php:77
msgid "Edit"
msgstr "编辑"

#. Translators: 1 = comment date, 2 = comment time
#: classes/class-twentytwenty-walker-comment.php:69
msgid "%1$s at %2$s"
msgstr "%1$s %2$s"

#. Template Name of the theme
msgid "Full Width Template"
msgstr "全宽模板"

#: template-parts/pagination.php:25
msgid "Older <span class=\"nav-short\">Posts</span>"
msgstr "较早<span class=\"nav-short\">文章</span>"

#: template-parts/pagination.php:21
msgid "Newer <span class=\"nav-short\">Posts</span>"
msgstr "较新<span class=\"nav-short\">文章</span>"

#: template-parts/navigation.php:25
msgid "Post"
msgstr "文章"

#: template-parts/modal-search.php:26
msgid "Close search"
msgstr "关闭搜索"

#: template-parts/modal-menu.php:117
msgid "Expanded Social links"
msgstr "展开的社交网络链接"

#: template-parts/modal-menu.php:73
msgid "Mobile"
msgstr "移动"

#: template-parts/modal-menu.php:48
msgid "Expanded"
msgstr "展开的"

#: template-parts/modal-menu.php:21
msgid "Close Menu"
msgstr "关闭菜单"

#: template-parts/footer-menus-widgets.php:57
msgid "Social links"
msgstr "社交网络链接"

#. translators: %s: Author name
#: template-parts/entry-author-bio.php:20 inc/template-tags.php:354
msgid "By %s"
msgstr "由%s"

#: template-parts/content-cover.php:88
msgid "Scroll Down"
msgstr "向下滚动"

#: searchform.php:27
msgctxt "submit button"
msgid "Search"
msgstr "搜索"

#: searchform.php:25
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr "搜索…"

#: index.php:48
msgid "We could not find any results for your search. You can give it another try through the search form below."
msgstr "我们未能就您的搜索找到任何结果。您可以通过下方的搜索表单再试一次。"

#: index.php:32
msgid "Search:"
msgstr "搜索："

#: inc/template-tags.php:445
msgid "Sticky post"
msgstr "置顶文章"

#: inc/template-tags.php:407
msgid "Tags"
msgstr "标签"

#: template-parts/content-cover.php:70 template-parts/entry-header.php:36
#: inc/template-tags.php:389
msgid "Categories"
msgstr "分类"

#: inc/template-tags.php:371
msgid "Post date"
msgstr "发布日期"

#: inc/template-tags.php:347
msgid "Post author"
msgstr "文章作者"

#: header.php:88
msgid "Horizontal"
msgstr "水平"

#: header.php:76 header.php:137
msgid "Menu"
msgstr "菜单"

#: header.php:53 header.php:157
msgid "Search"
msgstr "搜索"

#: template-parts/content.php:36
msgid "Continue reading"
msgstr "继续阅读"

#: functions.php:548
msgctxt "Short name of the larger font size in the block editor."
msgid "XL"
msgstr "XL"

#: functions.php:547
msgctxt "Name of the larger font size in the block editor"
msgid "Larger"
msgstr "更大"

#: functions.php:542
msgctxt "Short name of the large font size in the block editor."
msgid "L"
msgstr "L"

#: functions.php:541
msgctxt "Name of the large font size in the block editor"
msgid "Large"
msgstr "大"

#: functions.php:536
msgctxt "Short name of the regular font size in the block editor."
msgid "M"
msgstr "M"

#: functions.php:535
msgctxt "Name of the regular font size in the block editor"
msgid "Regular"
msgstr "常规"

#: functions.php:530
msgctxt "Short name of the small font size in the block editor."
msgid "S"
msgstr "S"

#: functions.php:529
msgctxt "Name of the small font size in the block editor"
msgid "Small"
msgstr "小"

#: functions.php:514
msgid "Background Color"
msgstr "背景色"

#: functions.php:501
msgid "Subtle Background"
msgstr "轻微背景"

#: functions.php:486
msgid "Accent Color"
msgstr "强调色"

#: functions.php:377
msgid "Widgets in this area will be displayed in the second column in the footer."
msgstr "此区域中的小工具会在页脚第二栏显示。"

#: functions.php:375
msgid "Footer #2"
msgstr "页脚#2"

#: functions.php:365
msgid "Widgets in this area will be displayed in the first column in the footer."
msgstr "此区域中的小工具会在页脚第一栏显示。"

#: functions.php:363
msgid "Footer #1"
msgstr "页脚#1"

#: functions.php:338
msgid "Skip to the content"
msgstr "跳至内容"

#: functions.php:259
msgid "Social Menu"
msgstr "社交网络菜单"

#: functions.php:257
msgid "Mobile Menu"
msgstr "移动菜单"

#: functions.php:256
msgid "Desktop Expanded Menu"
msgstr "桌面展开式菜单"

#: functions.php:255
msgid "Desktop Horizontal Menu"
msgstr "桌面水平菜单"

#: comments.php:75
msgid "Older Comments"
msgstr "较早评论"

#: comments.php:74
msgid "Newer Comments"
msgstr "较新评论"

#: classes/class-twentytwenty-walker-page.php:134 inc/template-tags.php:555
msgid "Show sub menu"
msgstr "显示子菜单"

#. translators: %d: ID of a post
#: classes/class-twentytwenty-walker-page.php:82
msgid "#%d (no title)"
msgstr "#%d（无标题）"

#: classes/class-twentytwenty-walker-comment.php:127
msgid "By Post Author"
msgstr "由文章作者"

#: classes/class-twentytwenty-walker-comment.php:56
msgid "says:"
msgstr "说："

#: classes/class-twentytwenty-customize.php:396
msgid "Make sure that the contrast is high enough so that the text is readable."
msgstr "确保对比度足够高，以使文本可读。"

#: classes/class-twentytwenty-customize.php:306
msgid "Creates a parallax effect when the visitor scrolls."
msgstr "在访客滚动时创建视差效果。"

#: classes/class-twentytwenty-customize.php:305
msgid "Fixed Background Image"
msgstr "固定背景图像"

#. Template Name of the theme
#: classes/class-twentytwenty-customize.php:281
msgid "Cover Template"
msgstr "封面模板"

#: classes/class-twentytwenty-customize.php:270
msgid "Summary"
msgstr "摘要"

#: classes/class-twentytwenty-customize.php:269
msgid "Full text"
msgstr "全文"

#: classes/class-twentytwenty-customize.php:267
msgid "On archive pages, posts show:"
msgstr "在归档页面上，文章显示："

#: classes/class-twentytwenty-customize.php:225
msgid "Show search in header"
msgstr "在页首显示搜索栏"

#: classes/class-twentytwenty-customize.php:202
msgid "Theme Options"
msgstr "主题选项"

#: classes/class-twentytwenty-customize.php:101
msgid "Header &amp; Footer Background Color"
msgstr "页首、页脚背景色"

#: classes/class-twentytwenty-customize.php:82
msgid "Scales the logo to half its uploaded size, making it sharp on high-res screens."
msgstr "将标志缩小到上传尺寸的一半，让其在高分辨率屏幕上清晰可见。"

#: classes/class-twentytwenty-customize.php:81
msgid "Retina logo"
msgstr "视网膜显示屏标志"

#: 404.php:19
msgid "The page you were looking for could not be found. It might have been removed, renamed, or did not exist in the first place."
msgstr "找不到您要访问的页面。它可能已被移除、更名或从未存在。"

#: 404.php:17
msgid "Page Not Found"
msgstr "页面未找到"

#: footer.php:33
msgid "Powered by WordPress"
msgstr "由WordPress强力驱动"

#. Author of the theme
msgid "the WordPress team"
msgstr "WordPress团队"

#. Author URI of the theme
#: footer.php:32
msgid "https://wordpress.org/"
msgstr "https://cn.wordpress.org/"

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentytwenty/"
msgstr "https://wordpress.org/themes/twentytwenty/"
