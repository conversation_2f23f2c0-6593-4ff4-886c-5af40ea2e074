<?php
/**
 * Admin functionality for GoCardless Deposit Flow
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GCDF_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_gcdf_test_connection', array($this, 'test_gocardless_connection'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('GoCardless Deposit Settings', 'gocardless-deposit-flow'),
            __('GoCardless Deposit', 'gocardless-deposit-flow'),
            'manage_options',
            'gocardless-deposit-flow',
            array($this, 'admin_page')
        );
    }

    /**
     * Initialize settings
     */
    public function init_settings() {
        register_setting(
            'gcdf_settings_group',
            'gcdf_settings',
            array($this, 'sanitize_settings')
        );

        // API Settings Section
        add_settings_section(
            'gcdf_api_section',
            __('GoCardless API Settings', 'gocardless-deposit-flow'),
            array($this, 'api_section_callback'),
            'gocardless-deposit-flow'
        );

        add_settings_field(
            'gocardless_environment',
            __('Environment', 'gocardless-deposit-flow'),
            array($this, 'environment_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_api_section'
        );

        add_settings_field(
            'gocardless_access_token',
            __('Access Token', 'gocardless-deposit-flow'),
            array($this, 'access_token_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_api_section'
        );

        // Deposit Settings Section
        add_settings_section(
            'gcdf_deposit_section',
            __('Deposit Settings', 'gocardless-deposit-flow'),
            array($this, 'deposit_section_callback'),
            'gocardless-deposit-flow'
        );

        add_settings_field(
            'deposit_amount',
            __('Deposit Amount', 'gocardless-deposit-flow'),
            array($this, 'deposit_amount_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_deposit_section'
        );

        add_settings_field(
            'currency',
            __('Currency', 'gocardless-deposit-flow'),
            array($this, 'currency_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_deposit_section'
        );

        add_settings_field(
            'terms_page_id',
            __('Terms & Conditions Page', 'gocardless-deposit-flow'),
            array($this, 'terms_page_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_deposit_section'
        );

        // Redirect Settings Section
        add_settings_section(
            'gcdf_redirect_section',
            __('Redirect Settings', 'gocardless-deposit-flow'),
            array($this, 'redirect_section_callback'),
            'gocardless-deposit-flow'
        );

        add_settings_field(
            'success_redirect_url',
            __('Success Redirect URL', 'gocardless-deposit-flow'),
            array($this, 'success_redirect_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_redirect_section'
        );

        add_settings_field(
            'cancel_redirect_url',
            __('Cancel Redirect URL', 'gocardless-deposit-flow'),
            array($this, 'cancel_redirect_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_redirect_section'
        );

        // Advanced Settings Section
        add_settings_section(
            'gcdf_advanced_section',
            __('Advanced Settings', 'gocardless-deposit-flow'),
            array($this, 'advanced_section_callback'),
            'gocardless-deposit-flow'
        );

        add_settings_field(
            'require_deposit_all',
            __('Require Deposit for All Auctions', 'gocardless-deposit-flow'),
            array($this, 'require_deposit_all_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_advanced_section'
        );

        add_settings_field(
            'enable_logging',
            __('Enable Logging', 'gocardless-deposit-flow'),
            array($this, 'logging_field_callback'),
            'gocardless-deposit-flow',
            'gcdf_advanced_section'
        );
    }

    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();

        $sanitized['gocardless_environment'] = sanitize_text_field($input['gocardless_environment'] ?? 'sandbox');
        $sanitized['gocardless_access_token'] = sanitize_text_field($input['gocardless_access_token'] ?? '');
        $sanitized['deposit_amount'] = floatval($input['deposit_amount'] ?? 200.00);
        $sanitized['currency'] = sanitize_text_field($input['currency'] ?? 'GBP');
        $sanitized['terms_page_id'] = intval($input['terms_page_id'] ?? 0);
        $sanitized['success_redirect_url'] = esc_url_raw($input['success_redirect_url'] ?? '');
        $sanitized['cancel_redirect_url'] = esc_url_raw($input['cancel_redirect_url'] ?? '');
        $sanitized['require_deposit_all'] = !empty($input['require_deposit_all']);
        $sanitized['enable_logging'] = !empty($input['enable_logging']);

        return $sanitized;
    }

    /**
     * Admin page
     */
    public function admin_page() {
        $settings = gcdf()->get_settings();
        $stats = gcdf()->get_database()->get_deposit_stats();
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <?php if (!gcdf()->is_configured()): ?>
                <div class="notice notice-warning">
                    <p><?php _e('Please configure your GoCardless API settings below to start accepting deposits.', 'gocardless-deposit-flow'); ?></p>
                </div>
            <?php endif; ?>

            <div class="gcdf-admin-container">
                <div class="gcdf-main-content">
                    <form method="post" action="options.php">
                        <?php
                        settings_fields('gcdf_settings_group');
                        do_settings_sections('gocardless-deposit-flow');
                        submit_button();
                        ?>
                    </form>

                    <?php if (gcdf()->is_configured()): ?>
                        <div class="gcdf-test-connection">
                            <h3><?php _e('Test Connection', 'gocardless-deposit-flow'); ?></h3>
                            <button type="button" id="gcdf-test-connection" class="button button-secondary">
                                <?php _e('Test GoCardless Connection', 'gocardless-deposit-flow'); ?>
                            </button>
                            <div id="gcdf-test-result"></div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="gcdf-sidebar">
                    <div class="gcdf-stats-widget">
                        <h3><?php _e('Deposit Statistics', 'gocardless-deposit-flow'); ?></h3>
                        <table class="widefat">
                            <tr>
                                <td><?php _e('Total Deposits', 'gocardless-deposit-flow'); ?></td>
                                <td><?php echo esc_html($stats->total_deposits ?? 0); ?></td>
                            </tr>
                            <tr>
                                <td><?php _e('Completed', 'gocardless-deposit-flow'); ?></td>
                                <td><?php echo esc_html($stats->completed_deposits ?? 0); ?></td>
                            </tr>
                            <tr>
                                <td><?php _e('Pending', 'gocardless-deposit-flow'); ?></td>
                                <td><?php echo esc_html($stats->pending_deposits ?? 0); ?></td>
                            </tr>
                            <tr>
                                <td><?php _e('Failed', 'gocardless-deposit-flow'); ?></td>
                                <td><?php echo esc_html($stats->failed_deposits ?? 0); ?></td>
                            </tr>
                            <tr>
                                <td><?php _e('Total Amount', 'gocardless-deposit-flow'); ?></td>
                                <td><?php echo esc_html($settings['currency'] ?? 'GBP') . ' ' . number_format($stats->total_amount ?? 0, 2); ?></td>
                            </tr>
                        </table>
                    </div>

                    <div class="gcdf-help-widget">
                        <h3><?php _e('Need Help?', 'gocardless-deposit-flow'); ?></h3>
                        <p><?php _e('For support and documentation, please visit:', 'gocardless-deposit-flow'); ?></p>
                        <ul>
                            <li><a href="https://developer.gocardless.com/" target="_blank"><?php _e('GoCardless Developer Docs', 'gocardless-deposit-flow'); ?></a></li>
                            <li><a href="#" target="_blank"><?php _e('Plugin Documentation', 'gocardless-deposit-flow'); ?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .gcdf-admin-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .gcdf-main-content {
            flex: 2;
        }
        .gcdf-sidebar {
            flex: 1;
        }
        .gcdf-stats-widget, .gcdf-help-widget {
            background: #fff;
            border: 1px solid #ccd0d4;
            padding: 15px;
            margin-bottom: 20px;
        }
        .gcdf-test-connection {
            margin-top: 30px;
            padding: 20px;
            background: #f9f9f9;
            border: 1px solid #ddd;
        }
        #gcdf-test-result {
            margin-top: 10px;
        }
        .gcdf-test-success {
            color: #46b450;
        }
        .gcdf-test-error {
            color: #dc3232;
        }
        </style>
        <?php
    }

    /**
     * Section callbacks
     */
    public function api_section_callback() {
        echo '<p>' . __('Configure your GoCardless API credentials. You can get these from your GoCardless dashboard.', 'gocardless-deposit-flow') . '</p>';
    }

    public function deposit_section_callback() {
        echo '<p>' . __('Configure deposit amount and related settings.', 'gocardless-deposit-flow') . '</p>';
    }

    public function redirect_section_callback() {
        echo '<p>' . __('Configure where users are redirected after payment completion or cancellation.', 'gocardless-deposit-flow') . '</p>';
    }

    public function advanced_section_callback() {
        echo '<p>' . __('Advanced plugin settings.', 'gocardless-deposit-flow') . '</p>';
    }

    /**
     * Field callbacks
     */
    public function environment_field_callback() {
        $settings = gcdf()->get_settings();
        $value = $settings['gocardless_environment'] ?? 'sandbox';
        ?>
        <select name="gcdf_settings[gocardless_environment]">
            <option value="sandbox" <?php selected($value, 'sandbox'); ?>><?php _e('Sandbox (Testing)', 'gocardless-deposit-flow'); ?></option>
            <option value="live" <?php selected($value, 'live'); ?>><?php _e('Live (Production)', 'gocardless-deposit-flow'); ?></option>
        </select>
        <p class="description"><?php _e('Use sandbox for testing, live for production.', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function access_token_field_callback() {
        $settings = gcdf()->get_settings();
        $value = $settings['gocardless_access_token'] ?? '';
        ?>
        <input type="password" name="gcdf_settings[gocardless_access_token]" value="<?php echo esc_attr($value); ?>" class="regular-text" />
        <p class="description"><?php _e('Your GoCardless access token from the dashboard.', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function deposit_amount_field_callback() {
        $settings = gcdf()->get_settings();
        $value = $settings['deposit_amount'] ?? 200.00;
        ?>
        <input type="number" name="gcdf_settings[deposit_amount]" value="<?php echo esc_attr($value); ?>" step="0.01" min="0" class="small-text" />
        <p class="description"><?php _e('The deposit amount required (e.g., 200.00).', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function currency_field_callback() {
        $settings = gcdf()->get_settings();
        $value = $settings['currency'] ?? 'GBP';
        ?>
        <select name="gcdf_settings[currency]">
            <option value="GBP" <?php selected($value, 'GBP'); ?>>GBP (British Pound)</option>
            <option value="EUR" <?php selected($value, 'EUR'); ?>>EUR (Euro)</option>
            <option value="USD" <?php selected($value, 'USD'); ?>>USD (US Dollar)</option>
        </select>
        <p class="description"><?php _e('Currency for deposits.', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function terms_page_field_callback() {
        $settings = gcdf()->get_settings();
        $value = $settings['terms_page_id'] ?? 0;
        
        wp_dropdown_pages(array(
            'name' => 'gcdf_settings[terms_page_id]',
            'selected' => $value,
            'show_option_none' => __('Select a page', 'gocardless-deposit-flow'),
            'option_none_value' => 0
        ));
        ?>
        <p class="description"><?php _e('Page containing terms and conditions that users must accept.', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function success_redirect_field_callback() {
        $settings = gcdf()->get_settings();
        $value = $settings['success_redirect_url'] ?? '';
        ?>
        <input type="url" name="gcdf_settings[success_redirect_url]" value="<?php echo esc_attr($value); ?>" class="regular-text" />
        <p class="description"><?php _e('URL to redirect users after successful payment (leave empty for default).', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function cancel_redirect_field_callback() {
        $settings = gcdf()->get_settings();
        $value = $settings['cancel_redirect_url'] ?? '';
        ?>
        <input type="url" name="gcdf_settings[cancel_redirect_url]" value="<?php echo esc_attr($value); ?>" class="regular-text" />
        <p class="description"><?php _e('URL to redirect users after cancelled payment (leave empty for default).', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function require_deposit_all_field_callback() {
        $settings = gcdf()->get_settings();
        $value = !empty($settings['require_deposit_all']);
        ?>
        <label>
            <input type="checkbox" name="gcdf_settings[require_deposit_all]" value="1" <?php checked($value); ?> />
            <?php _e('Require deposit for all auction items with Buy Now price', 'gocardless-deposit-flow'); ?>
        </label>
        <p class="description"><?php _e('When enabled, all auction items with a Buy Now price will require deposit payment.', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    public function logging_field_callback() {
        $settings = gcdf()->get_settings();
        $value = !empty($settings['enable_logging']);
        ?>
        <label>
            <input type="checkbox" name="gcdf_settings[enable_logging]" value="1" <?php checked($value); ?> />
            <?php _e('Enable debug logging', 'gocardless-deposit-flow'); ?>
        </label>
        <p class="description"><?php _e('Log plugin activities for debugging purposes.', 'gocardless-deposit-flow'); ?></p>
        <?php
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'settings_page_gocardless-deposit-flow') {
            return;
        }

        wp_enqueue_script(
            'gcdf-admin',
            GCDF_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            GCDF_VERSION,
            true
        );

        wp_localize_script('gcdf-admin', 'gcdf_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gcdf_admin_nonce'),
            'strings' => array(
                'testing' => __('Testing connection...', 'gocardless-deposit-flow'),
                'success' => __('Connection successful!', 'gocardless-deposit-flow'),
                'error' => __('Connection failed:', 'gocardless-deposit-flow')
            )
        ));
    }

    /**
     * Test GoCardless connection via AJAX
     */
    public function test_gocardless_connection() {
        check_ajax_referer('gcdf_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'gocardless-deposit-flow'));
        }

        try {
            $api = gcdf()->gocardless_api;
            $result = $api->test_connection();
            
            if ($result) {
                wp_send_json_success(array(
                    'message' => __('Connection successful! GoCardless API is working correctly.', 'gocardless-deposit-flow')
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Connection failed. Please check your API credentials.', 'gocardless-deposit-flow')
                ));
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => $e->getMessage()
            ));
        }
    }
}
