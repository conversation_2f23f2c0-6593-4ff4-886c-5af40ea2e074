# Translation of Plugins - All-in-One WP Migration and Backup - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - All-in-One WP Migration and Backup - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-09-18 04:29:40+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - All-in-One WP Migration and Backup - Stable (latest release)\n"

#: lib/view/reset/index.php:41
msgid "Reset Hub Demo"
msgstr "Reset Hub Demo"

#: lib/view/reset/index.php:41
msgid "Upgrade to Premium "
msgstr "升级到高级版 "

#: lib/view/schedules/index.php:163
msgid "Tailor your backup schedules to fit the complexity of your WordPress Multisite. Choose to export the entire network or only a selection of subsites according to your requirements. Effortless management for even the most intricate site networks."
msgstr "定制您的备份时间表，以适应WordPress Multisite的复杂性。选择导出整个网络或按需选择子站点。即使是最复杂的网站网络，也能轻松管理。"

#: lib/view/schedules/index.php:150
msgid "We've got you covered with an array of supported storage providers. Whether you prefer Box, Amazon S3, WebDav or something else, you can choose the one that fits your needs best. Secure your backups exactly where you want them."
msgstr "我们为您提供了一些受支持的存储提供商。无论您喜欢Box、Amazon S3、WebDav还是其他，您都可以选择最符合您需求的服务商，将您的备份保存在您想要的位置。"

#: lib/view/schedules/index.php:137
msgid "Enjoy the flexibility of FTP storage. Direct your scheduled backups to your own FTP server. You'll have full control over your data, providing you with a versatile and private storage solution."
msgstr "得益于FTP存储的灵活性，您可以将计划备份定向到您自己的FTP服务器。您将完全控制您的数据，为自己提供多功能的私密存储解决方案。"

#: lib/view/schedules/index.php:124
msgid "Harness the power of OneDrive for your backups. Set up your scheduled backups to be saved directly in your OneDrive. It's secure, integrated with your Microsoft account, and keeps your data readily accessible."
msgstr "利用OneDrive的强大功能进行备份。将您的计划备份设置为直接保存在OneDrive中。它是安全的，与您的微软帐户集成，并可随时访问您的数据。"

#: lib/view/schedules/index.php:98
msgid "Benefit from the robustness of Google Drive. Schedule your backups to be saved directly to your Google Drive account. Simple, secure, and integrated into a platform you already use."
msgstr "得益于 Google Drive 的稳健性。安排您的备份直接保存到您的 Google 云端硬盘帐户。简单、安全并集成到您已使用的平台中。"

#: lib/view/schedules/index.php:85
msgid "Manage your storage effectively with our flexible retention settings. Decide how many backups you want to keep at a time. Old backups are automatically cleared, keeping your storage neat and efficient."
msgstr "使用我们灵活的备份保留设置有效管理您的存储。决定一次要保留多少备份。旧备份会自动清除，保持您的存储整洁高效。"

#: lib/view/schedules/index.php:77 lib/view/schedules/index.php:82
msgid "Retention settings"
msgstr "保留设置"

#: lib/view/schedules/index.php:72
msgid "Stay informed, not overwhelmed. Tailor your notification preferences to get updates that matter to you. Whether it's the status of each backup, or just critical alerts, control what you want to be notified about."
msgstr "随时了解情况，不要不知所措。定制您的通知首选项，以获得对您重要的更新。无论是每个备份的状态，还是只是关键警报，都可以控制您想要收到的通知。"

#: lib/view/schedules/index.php:64 lib/view/schedules/index.php:69
msgid "Notification settings"
msgstr "通知设置"

#: lib/view/schedules/index.php:59
msgid "Never worry about forgetting to back up your site again. Choose from various scheduling options, from daily to monthly, and we'll automate the rest. Backups happen like clockwork, giving you peace of mind and a solid safety net"
msgstr "永远不要担心忘记备份您的网站了。从各种日程安排选项中进行选择，从每天到每月，我们将自动完成其余工作。备份就像发条一样，给你安心和坚实的安全网。"

#: lib/view/schedules/index.php:56 lib/view/schedules/index.php:69
#: lib/view/schedules/index.php:82 lib/view/schedules/index.php:95
#: lib/view/schedules/index.php:108 lib/view/schedules/index.php:121
#: lib/view/schedules/index.php:134 lib/view/schedules/index.php:147
#: lib/view/schedules/index.php:160
msgid "Enable this feature"
msgstr "启用此功能"

#: lib/view/schedules/index.php:51
msgid "Backup scheduler"
msgstr "备份调度程序"

#: lib/view/schedules/index.php:45 lib/view/schedules/index.php:155
#: lib/view/schedules/index.php:160
msgid "Multisite Schedules"
msgstr "多站点时间表"

#: lib/view/schedules/index.php:44 lib/view/schedules/index.php:142
#: lib/view/schedules/index.php:147
msgid "More Storage Providers"
msgstr "更多存储服务提供商"

#: lib/view/schedules/index.php:43 lib/view/schedules/index.php:129
#: lib/view/schedules/index.php:134
msgid "FTP Storage"
msgstr "FTP存储"

#: lib/view/schedules/index.php:42 lib/view/schedules/index.php:116
#: lib/view/schedules/index.php:121
msgid "OneDrive Storage"
msgstr "OneDrive存储"

#: lib/view/schedules/index.php:41 lib/view/schedules/index.php:103
#: lib/view/schedules/index.php:108
msgid "Dropbox Storage"
msgstr "Dropbox存储"

#: lib/view/schedules/index.php:40 lib/view/schedules/index.php:90
#: lib/view/schedules/index.php:95
msgid "Google Drive Storage"
msgstr "谷歌云端硬盘存储"

#: lib/view/schedules/index.php:39
msgid "Retention Settings"
msgstr "保留设置"

#: lib/view/schedules/index.php:38
msgid "Notification Settings"
msgstr "通知设置"

#: lib/view/schedules/index.php:37 lib/view/schedules/index.php:56
msgid "Backup Scheduler"
msgstr "备份调度程序"

#: lib/controller/class-ai1wm-main-controller.php:681
#: lib/controller/class-ai1wm-main-controller.php:682
msgid "Schedules"
msgstr "时间表"

#: lib/model/import/class-ai1wm-import-check-encryption.php:61
msgid "Backup is encrypted. Please provide decryption password: "
msgstr "备份已加密，请提供解密密码："

#: lib/view/export/advanced-settings.php:66
msgid "Password-protect and encrypt backups"
msgstr "设置密码并加密备份"

#: lib/view/export/advanced-settings.php:53
msgid "A password is required"
msgstr "密码为必填项。"

#: lib/controller/class-ai1wm-main-controller.php:797
#: lib/view/export/advanced-settings.php:58
msgid "The passwords do not match"
msgstr "您两次输入的密码不符。"

#: lib/controller/class-ai1wm-main-controller.php:796
#: lib/view/export/advanced-settings.php:56
msgid "Repeat the password"
msgstr "重新输入密码"

#: lib/controller/class-ai1wm-main-controller.php:794
msgid "Submit"
msgstr "提交"

#: lib/controller/class-ai1wm-main-controller.php:792
msgid "The backup is encrypted"
msgstr "此备份已加密"

#: lib/view/backups/backups-list.php:131
msgid "List"
msgstr "列表"

#: lib/view/backups/backups-list.php:129
msgid "Show backup content"
msgstr "显示备份内容"

#: lib/view/backups/backups-list.php:122
msgid "Downloading is not possible because backups directory is not accessible."
msgstr "无法下载，因为无法访问备份目录。"

#: lib/controller/class-ai1wm-main-controller.php:838
msgid "Reading..."
msgstr "正在读取..."

#: lib/controller/class-ai1wm-main-controller.php:837
msgid "List the content of the backup"
msgstr "列出备份内容"

#: lib/controller/class-ai1wm-main-controller.php:835
msgid "Error"
msgstr "错误"

#: lib/view/backups/backups-list.php:95
msgid "More"
msgstr "更多"

#. translators: 1: Number of backups.
#: lib/controller/class-ai1wm-main-controller.php:771
msgid "You have %d backups"
msgstr "你有%d个备份"

#. translators: 1: Number of backups.
#. translators: Numbers of backups.
#: lib/controller/class-ai1wm-main-controller.php:769
#: lib/view/main/backups.php:33
msgid "You have %d backup"
msgid_plural "You have %d backups"
msgstr[0] "你有%d个备份"

#. translators: 1: Error message.
#: lib/view/updater/error.php:33
msgid "Error: %s"
msgstr "错误: %s"

#: lib/view/main/contact-support.php:33
msgid "Contact Support"
msgstr "联系技术支持"

#: lib/view/main/translate.php:33
msgid "Translate"
msgstr "翻译"

#: lib/controller/class-ai1wm-main-controller.php:789
msgid "I have enough disk space"
msgstr "我有足够的磁盘空间"

#: lib/view/backups/backups-list.php:48 lib/view/backups/index.php:51
msgid "Refreshing backup list..."
msgstr "正在刷新备份列表..."

#: lib/view/import/button-file.php:33
msgid "To choose a file please go inside the link and click on the browse button."
msgstr "要选择文件，请进入链接，然后单击浏览按钮。"

#: lib/controller/class-ai1wm-main-controller.php:787
msgid "Finish"
msgstr "完成"

#: lib/view/import/import-buttons.php:41
msgid "Drag & Drop a backup to import it"
msgstr "拖放备份文件来导入"

#: lib/controller/class-ai1wm-main-controller.php:274
msgid "All-in-One WP Migration Command"
msgstr "All-in-One WP Migration 命令 "

#: lib/controller/class-ai1wm-main-controller.php:760
#: lib/model/export/class-ai1wm-export-init.php:45
msgid "Preparing to export..."
msgstr "准备导出..."

#. translators: Human time diff
#: lib/view/backups/backups-list.php:84
msgid "%s ago"
msgstr "%s前"

#: lib/controller/class-ai1wm-main-controller.php:791
msgid "Please do not close this browser window or your import will fail"
msgstr "请不要关闭此浏览器窗口，否则导入将失败"

#: lib/controller/class-ai1wm-main-controller.php:790
msgid "Continue"
msgstr "继续"

#: lib/controller/class-ai1wm-main-controller.php:788
msgid "Proceed"
msgstr "继续"

#: lib/controller/class-ai1wm-main-controller.php:786
msgid "Stop import"
msgstr "停止导入"

#: lib/controller/class-ai1wm-main-controller.php:767
msgid "Stop export"
msgstr "停止导出"

#: lib/controller/class-ai1wm-main-controller.php:766
#: lib/controller/class-ai1wm-main-controller.php:785
msgid "Close"
msgstr "关闭"

#. translators: Progress.
#: lib/model/import/class-ai1wm-import-validate.php:90
#: lib/model/import/class-ai1wm-import-validate.php:162
msgid "Unpacking archive...<br />%d%% complete"
msgstr "解压存档...<br />已完成 %d%%"

#. translators: Progress.
#: lib/model/export/class-ai1wm-export-database-file.php:69
#: lib/model/export/class-ai1wm-export-database-file.php:105
msgid "Archiving database...<br />%d%% complete"
msgstr "存档数据库...<br />已完成 %d%%"

#. translators: Progress.
#: lib/model/import/class-ai1wm-import-database.php:81
#: lib/model/import/class-ai1wm-import-database.php:1037
msgid "Restoring database...<br />%d%% complete"
msgstr "恢复数据库...<br />已完成 %d%%"

#: lib/controller/class-ai1wm-main-controller.php:777
msgid "Preparing to import..."
msgstr "准备导入..."

#: lib/controller/class-ai1wm-main-controller.php:1314
msgid "Monthly"
msgstr "每月"

#: lib/controller/class-ai1wm-main-controller.php:1310
msgid "Weekly"
msgstr "每周"

#: lib/model/import/class-ai1wm-import-mu-plugins.php:37
msgid "Activating mu-plugins..."
msgstr "激活mu-plugins..."

#: lib/model/import/class-ai1wm-import-blogs.php:37
msgid "Preparing blogs..."
msgstr "准备博客..."

#: lib/view/updater/modal.php:57
msgid "There is an update available. To update, you must enter your"
msgstr "有更新可用。 要更新，您必须输入"

#: lib/view/updater/modal.php:50
msgid "Discard"
msgstr "丢弃"

#: lib/view/updater/modal.php:48
msgid "Save"
msgstr "保存"

#: lib/view/updater/modal.php:44
msgid "here"
msgstr "此处"

#: lib/view/updater/modal.php:43
msgid "Don't have a Purchase ID? You can find your Purchase ID"
msgstr "没有购买ID？ 你可以找到你的购买ID"

#: lib/view/updater/modal.php:39 lib/view/updater/modal.php:58
msgid "Purchase ID"
msgstr "购买ID"

#: lib/view/updater/modal.php:36
msgid "To update your plugin/extension to the latest version, please fill your Purchase ID below."
msgstr "要将您的插件/扩展程序更新到最新版本，请在下面填写您的购买ID。"

#: lib/view/updater/modal.php:35
msgid "Enter your Purchase ID"
msgstr "输入您的购买ID"

#: lib/controller/class-ai1wm-main-controller.php:755
#: lib/view/updater/check.php:33
msgid "Check for updates"
msgstr "检查更新"

#. Author of the plugin
#: all-in-one-wp-migration.php
msgid "ServMask"
msgstr "ServMask"

#. Plugin URI of the plugin
#. Author URI of the plugin
#: all-in-one-wp-migration.php
msgid "https://servmask.com/"
msgstr "https://servmask.com/"

#: lib/view/import/index.php:39
msgid "Import Site"
msgstr "导入站点"

#: lib/view/import/import-buttons.php:45
msgid "Import From"
msgstr "导入来自"

#: lib/view/export/index.php:39
msgid "Export Site"
msgstr "导出站点"

#: lib/view/export/help-section.php:52
msgid "Once the file is successfully downloaded on your computer, you can import it to any of your WordPress sites."
msgstr "文件在您电脑上成功下载后，您可以将其导入任何WordPress站点。"

#: lib/view/export/help-section.php:48
msgid "Press \"Export\" button and the site archive file will pop up in your browser."
msgstr "按“导出”按钮，站点归档文件将弹出浏览器。"

#: lib/view/export/help-section.php:44
msgid "In the advanced settings section you can configure more precisely the way of exporting."
msgstr "在高级设置部分，您可以更精确地配置导出方式。"

#: lib/view/export/find-replace.php:54
msgid "Add"
msgstr "添加"

#: lib/view/export/find-replace.php:41
msgid "in the database"
msgstr "在数据库中"

#: lib/view/export/find-replace.php:40
msgid "<another-text>"
msgstr "<另一个文本>"

#: lib/view/export/find-replace.php:39 lib/view/export/find-replace.php:47
msgid "Replace with"
msgstr "替换为"

#: lib/view/export/find-replace.php:38
msgid "<text>"
msgstr "<文本>"

#: lib/view/export/button-file.php:33 lib/view/import/button-file.php:34
msgid "File"
msgstr "文件"

#: lib/view/export/advanced-settings.php:38
msgid "(click to expand)"
msgstr "（点击展开）"

#: lib/view/export/advanced-settings.php:37
msgid "Advanced options"
msgstr "高级选项"

#: lib/view/common/share-buttons.php:73
msgid "Tweet"
msgstr "Tweet"

#: lib/view/common/leave-feedback.php:69
msgid "Cancel"
msgstr "取消"

#: lib/view/common/leave-feedback.php:72
msgid "Send"
msgstr "发送"

#: lib/controller/class-ai1wm-main-controller.php:795
#: lib/view/export/advanced-settings.php:51
msgid "Enter a password"
msgstr "输入密码"

#: lib/view/common/sidebar-right.php:41
msgid "Leave Feedback"
msgstr "留下反馈"

#: lib/view/backups/index.php:59
msgid "Create backup"
msgstr "创建备份"

#: lib/view/backups/backups-list.php:136 lib/view/backups/backups-list.php:138
msgid "Delete"
msgstr "删除"

#: lib/view/backups/backups-list.php:101 lib/view/backups/backups-list.php:103
msgid "Restore"
msgstr "还原"

#: lib/view/backups/backups-list.php:109 lib/view/backups/backups-list.php:111
#: lib/view/backups/backups-list.php:114 lib/view/backups/backups-list.php:116
#: lib/view/backups/backups-list.php:124
msgid "Download"
msgstr "下载"

#: lib/view/backups/backups-list.php:40
msgid "Size"
msgstr "尺寸"

#: lib/view/backups/backups-list.php:39
msgid "Date"
msgstr "日期"

#: lib/view/backups/backups-list.php:38
msgid "Name"
msgstr "名称"

#: lib/model/import/class-ai1wm-import-done.php:365
#: lib/model/import/class-ai1wm-import-done.php:367
#: lib/model/import/class-ai1wm-import-done.php:369
msgid "Your site has been imported successfully!"
msgstr "您的数据已成功导入！"

#: lib/controller/class-ai1wm-main-controller.php:662
#: lib/controller/class-ai1wm-main-controller.php:663
#: lib/view/backups/index.php:39
msgid "Backups"
msgstr "备份"

#: lib/controller/class-ai1wm-main-controller.php:653
#: lib/controller/class-ai1wm-main-controller.php:654
msgid "Import"
msgstr "导入"

#: lib/controller/class-ai1wm-main-controller.php:644
#: lib/controller/class-ai1wm-main-controller.php:645
msgid "Export"
msgstr "导出"

#: lib/view/main/multisite-notice.php:43
msgid "Get multisite"
msgstr "获得多站点"