<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2023-06-04 17:52:23+0000','plural-forms'=>'nplurals=2; plural=n != 1;','project-id-version'=>'Plugins - LiteSpeed Cache - Stable (latest release)','language'=>'en_GB','messages'=>['Redetect'=>'Redetect','History'=>'History','unknown'=>'unknown','Extreme'=>'Extreme','Aggressive'=>'Aggressive','Higher TTL'=>'Higher TTL','Essentials'=>'Essentials','Presets'=>'Presets','Partner Benefits Provided by'=>'Partner Benefits Provided by','LiteSpeed Logs'=>'LiteSpeed Logs','Crawler Log'=>'Crawler Log','Purge Log'=>'Purge Log','Prevent writing log entries that include listed strings.'=>'Prevent writing log entries that include listed strings.','View Site Before Cache'=>'View Site Before Cache','View Site Before Optimization'=>'View Site Before Optimisation','Debug Helpers'=>'Debug Helpers','Enable Viewport Images auto generation cron.'=>'Enable Viewport Images auto generation cron.','This enables the page\'s initial screenful of imagery to be fully displayed without delay.'=>'This enables the page\'s initial screenful of imagery to be fully displayed without delay.','The Viewport Images service detects which images appear above the fold, and excludes them from lazy load.'=>'The Viewport Images service detects which images appear above the fold, and excludes them from lazy load.','When you use Lazy Load, it will delay the loading of all images on a page.'=>'When you use Lazy Load, it will delay the loading of all images on a page.','Use %1$s to bypass remote image dimension check when %2$s is ON.'=>'Use %1$s to bypass remote image dimension check when %2$s is ON.','VPI'=>'VPI','%s must be turned ON for this setting to work.'=>'%s must be turned ON for this setting to work.','Viewport Image'=>'Viewport Image','Mobile'=>'Mobile','Please thoroughly test each JS file you add to ensure it functions as expected.'=>'Please thoroughly test each JS file you add to ensure it functions as expected.','Please thoroughly test all items in %s to ensure they function as expected.'=>'Please thoroughly test all items in %s to ensure they function as expected.','Use %1$s to bypass UCSS for the pages which page type is %2$s.'=>'Use %1$s to bypass UCSS for the pages which page type is %2$s.','Use %1$s to generate one single UCSS for the pages which page type is %2$s while other page types still per URL.'=>'Use %1$s to generate one single UCSS for the pages which page type is %2$s while other page types still per URL.','Filter %s available for UCSS per page type generation.'=>'Filter %s available for UCSS per page type generation.','Guest Mode failed to test.'=>'Guest Mode failed to test.','Guest Mode passed testing.'=>'Guest Mode passed testing.','Testing'=>'Testing','Guest Mode testing result'=>'Guest Mode testing result','Not blocklisted'=>'Not blacklisted','Learn more about when this is needed'=>'Learn more about when this is needed','Cleaned all localized resource entries.'=>'Cleaned all localised resource entries.','View .htaccess'=>'View .htaccess','You can use this code %1$s in %2$s to specify the htaccess file path.'=>'You can use this code %1$s in %2$s to specify the .htaccess file path.','PHP Constant %s is supported.'=>'PHP Constant %s is supported.','.htaccess Path'=>'.htaccess Path','Only optimize pages for guest (not logged in) visitors. If turned this OFF, CSS/JS/CCSS files will be doubled by each user group.'=>'Only optimise pages for guest (not logged in) visitors. If turned this OFF, CSS/JS/CCSS files will be doubled by each user group.','Listed JS files or inline JS code will not be optimized by %s.'=>'Listed JS files or inline JS code will not be optimised by %s.','Set an explicit width and height on image elements to reduce layout shifts and improve CLS (a Core Web Vitals metric).'=>'Set an explicit width and height on image elements to reduce layout shifts and improve CLS (a Core Web Vitals metric).','Changes to this setting do not apply to already-generated LQIPs. To regenerate existing LQIPs, please %s first from the admin bar menu.'=>'Changes to this setting do not apply to already-generated LQIPs. To regenerate existing LQIPs, please %s first from the Admin Bar menu.','Delayed'=>'Delayed','Automatic generation of unique CSS is in the background via a cron-based queue.'=>'Automatic generation of unique CSS is in the background via a cron-based queue.','HTML Settings'=>'HTML Settings','This option enables maximum optimization for Guest Mode visitors.'=>'This option enables maximum optimisation for Guest Mode visitors.','More'=>'More','Add Missing Sizes'=>'Add Missing Sizes','Optimize for Guests Only'=>'Optimise for Guests Only','Guest Optimization'=>'Guest Optimisation','Guest Mode'=>'Guest Mode','The current server is under heavy load.'=>'The current server is under heavy load.','Please see %s for more details.'=>'Please see %s for more details.','This setting will regenerate crawler list and clear the disabled list!'=>'This setting will regenerate crawler list and clear the disabled list!','%1$s %2$s files left in queue'=>'%1$s %2$s files left in queue','Crawler disabled list is cleared! All crawlers are set to active! '=>'Crawler disabled list is cleared! All crawlers are set to active! ','Redetected node'=>'Redetected node','No available Cloud Node after checked server load.'=>'No available Cloud Node after checked server load.','Localization Files'=>'Localisation Files','Purged!'=>'Purged!','Resources listed here will be copied and replaced with local URLs.'=>'Resources listed here will be copied and replaced with local URLs.','Use latest GitHub Master commit'=>'Use latest GitHub Master commit','Use latest GitHub Dev commit'=>'Use latest GitHub Dev commit','No valid sitemap parsed for crawler.'=>'No valid sitemap parsed for crawler.','CSS Combine External and Inline'=>'CSS Combine External and Inline','Include external CSS and inline CSS in combined file when %1$s is also enabled. This option helps maintain the priorities of CSS, which should minimize potential errors caused by CSS Combine.'=>'Include external CSS and inline CSS in combined file when %1$s is also enabled. This option helps maintain the priorities of CSS, which should minimise potential errors caused by CSS Combine.','Minify CSS files and inline CSS code.'=>'Minify CSS files and inline CSS code.','Predefined list will also be combined w/ the above settings'=>'Predefined list will also be combined w/ the above settings','Localization'=>'Localisation','Include external JS and inline JS in combined file when %1$s is also enabled. This option helps maintain the priorities of JS execution, which should minimize potential errors caused by JS Combine.'=>'Include external JS and inline JS in combined file when %1$s is also enabled. This option helps maintain the priorities of JS execution, which should minimise potential errors caused by JS Combine.','Combine all local JS files into a single file.'=>'Combine all local JS files into a single file.','Listed JS files or inline JS code will not be deferred or delayed.'=>'Listed JS files or inline JS code will not be deferred or delayed.','Click here to settings'=>'Click here to settings','JS Defer'=>'JS Defer','LiteSpeed Cache upgraded successfully. NOTE: Due to changes in this version, the settings %1$s and %2$s have been turned OFF. Please turn them back on manually and verify that your site layout is correct, and you have no JS errors.'=>'LiteSpeed Cache upgraded successfully. NOTE: Due to changes in this version, the settings %1$s and %2$s have been turned OFF. Please turn them back on manually and verify that your site layout is correct, and you have no JS errors.','JS Combine External and Inline'=>'JS Combine External and Inline','Dismiss'=>'Dismiss','The latest data file is'=>'The latest data file is','The list will be merged with the predefined nonces in your local data file.'=>'The list will be merged with the predefined nonces in your local data file.','Combine CSS files and inline CSS code.'=>'Combine CSS files and inline CSS code.','Minify JS files and inline JS codes.'=>'Minify JS files and inline JS codes.','This setting is overwritten by the Network setting'=>'This setting is overwritten by the Network setting','LQIP Excludes'=>'LQIP Excludes','These images will not generate LQIP.'=>'These images will not generate LQIP.','Are you sure you want to reset all settings back to the default settings?'=>'Are you sure you want to reset all settings back to the default settings?','This option will remove all %s tags from HTML.'=>'This option will remove all %s tags from HTML.','Are you sure you want to clear all cloud nodes?'=>'Are you sure you want to clear all cloud nodes?','Remove Noscript Tags'=>'Remove Noscript Tags','The site is not registered on QUIC.cloud.'=>'The site is not registered on QUIC.cloud.','Click here to set.'=>'Click here to set.','Localize Resources'=>'Localise Resources','Setting Up Custom Headers'=>'Setting Up Custom Headers','This will delete all localized resources'=>'This will delete all localised resources','Localized Resources'=>'Localised Resources','Comments are supported. Start a line with a %s to turn it into a comment line.'=>'Comments are supported. Start a line with a %s to turn it into a comment line.','HTTPS sources only.'=>'HTTPS sources only.','Localize external resources.'=>'Localise external resources.','Localization Settings'=>'Localisation Settings','Use QUIC.cloud online service to generate unique CSS.'=>'Use QUIC.cloud online service to generate unique CSS.','Generate UCSS'=>'Generate UCSS','Unique CSS'=>'Unique CSS','Purge the cache entries created by this plugin except for Critical CSS & Unique CSS & LQIP caches'=>'Purge the cache entries created by this plugin except for Critical CSS & Unique CSS & LQIP caches','LiteSpeed Report'=>'LiteSpeed Report','Image Thumbnail Group Sizes'=>'Image Thumbnail Group Sizes','Ignore certain query strings when caching. (LSWS %s required)'=>'Ignore certain query strings when caching. (LSWS %s required)','For URLs with wildcards, there may be a delay in initiating scheduled purge.'=>'For URLs with wildcards, there may be a delay in initiating scheduled purge.','By design, this option may serve stale content. Do not enable this option, if that is not OK with you.'=>'By design, this option may serve stale content. Do not enable this option, if that is not OK with you.','Serve Stale'=>'Serve Stale','This setting is overwritten by the primary site setting'=>'This setting is overwritten by the primary site setting','One or more pulled images does not match with the notified image md5'=>'One or more pulled images does not match with the notified image md5','Some optimized image file(s) has expired and was cleared.'=>'Some optimised image file(s) have expired and were cleared.','You have too many requested images, please try again in a few minutes.'=>'You have too many requested images, please try again in a few minutes.','Pulled WebP image md5 does not match the notified WebP image md5.'=>'Pulled WebP image md5 does not match the notified WebP image md5.','Read LiteSpeed Documentation'=>'Read LiteSpeed Documentation','There is proceeding queue not pulled yet. Queue info: %s.'=>'There is proceeding queue not pulled yet. Queue info: %s.','Specify how long, in seconds, Gravatar files are cached.'=>'Specify how long, in seconds, Gravatar files are cached.','Cleared %1$s invalid images.'=>'Cleared %1$s invalid images.','LiteSpeed Cache General Settings'=>'LiteSpeed Cache General Settings','This will delete all cached Gravatar files'=>'This will delete all cached Gravatar files','Prevent any debug log of listed pages.'=>'Prevent any debug log of listed pages.','Only log listed pages.'=>'Only log listed pages.','Specify the maximum size of the log file.'=>'Specify the maximum size of the log file.','To prevent filling up the disk, this setting should be OFF when everything is working.'=>'To prevent filling up the disk, this setting should be OFF when everything is working.','Press the %s button to stop beta testing and go back to the current release from the WordPress Plugin Directory.'=>'Press the %s button to stop beta testing and go back to the current release from the WordPress Plugin Directory.','Use latest WordPress release version'=>'Use latest WordPress release version','OR'=>'OR','Use this section to switch plugin versions. To beta test a GitHub commit, enter the commit URL in the field below.'=>'Use this section to switch plugin versions. To beta test a GitHub commit, enter the commit URL in the field below.','Reset Settings'=>'Reset Settings','LiteSpeed Cache Toolbox'=>'LiteSpeed Cache Toolbox','Beta Test'=>'Beta Test','Log View'=>'Log View','Debug Settings'=>'Debug Settings','Turn ON to control heartbeat in backend editor.'=>'Turn ON to control heartbeat in back end editor.','Turn ON to control heartbeat on backend.'=>'Turn ON to control heartbeat on back end.','Set to %1$s to forbid heartbeat on %2$s.'=>'Set to %1$s to forbid heartbeat on %2$s.','WordPress valid interval is %s seconds.'=>'WordPress valid interval is %s seconds.','Specify the %s heartbeat interval in seconds.'=>'Specify the %s heartbeat interval in seconds.','Turn ON to control heartbeat on frontend.'=>'Turn ON to control heartbeat on front end.','Disable WordPress interval heartbeat to reduce server load.'=>'Disable WordPress interval heartbeat to reduce server load.','Heartbeat Control'=>'Heartbeat Control','provide more information here to assist the LiteSpeed team with debugging.'=>'provide more information here to assist the LiteSpeed team with debugging.','Optional'=>'Optional','Generate Link for Current User'=>'Generate Link for Current User','Passwordless Link'=>'Passwordless Link','System Information'=>'System Information','Go to plugins list'=>'Go to plugins list','Install DoLogin Security'=>'Install DoLogin Security','Check my public IP from'=>'Check my public IP from','Your server IP'=>'Your server IP','Enter this site\'s IP address to allow cloud services directly call IP instead of domain name. This eliminates the overhead of DNS and CDN lookups.'=>'Enter this site\'s IP address to allow cloud services to directly call IP instead of domain name. This eliminates the overhead of DNS and CDN lookups.','This will enable crawler cron.'=>'This will enable crawler cron.','Crawler General Settings'=>'Crawler General Settings','Remove from Blocklist'=>'Remove from Blocklist','Empty blocklist'=>'Empty blocklist','Are you sure to delete all existing blocklist items?'=>'Are you sure to delete all existing blocklist items?','Blocklisted due to not cacheable'=>'Blocklisted due to not cacheable','Add to Blocklist'=>'Add to Blocklist','Operation'=>'Operation','Sitemap Total'=>'Sitemap Total','Sitemap List'=>'Sitemap List','Refresh Crawler Map'=>'Refresh Crawler Map','Clean Crawler Map'=>'Clean Crawler Map','Blocklist'=>'Blocklist','Map'=>'Map','Summary'=>'Summary','Cache Miss'=>'Cache Miss','Cache Hit'=>'Cache Hit','Waiting to be Crawled'=>'Waiting to be Crawled','Blocklisted'=>'Blocklisted','Miss'=>'Miss','Hit'=>'Hit','Waiting'=>'Waiting','Running'=>'Running','Use %1$s in %2$s to indicate this cookie has not been set.'=>'Use %1$s in %2$s to indicate this cookie has not been set.','Add new cookie to simulate'=>'Add new cookie to simulate','Remove cookie simulation'=>'Remove cookie simulation','Htaccess rule is: %s'=>'.htaccess rule is: %s','More settings available under %s menu'=>'More settings available under %s menu','The amount of time, in seconds, that files will be stored in browser cache before expiring.'=>'The amount of time, in seconds, that files will be stored in browser cache before expiring.','OpenLiteSpeed users please check this'=>'OpenLiteSpeed users, please check this','Browser Cache Settings'=>'Browser Cache Settings','Paths containing these strings will be forced to public cached regardless of no-cacheable settings.'=>'Paths containing these strings will be forced to public cached regardless of no-cacheable settings.','With QUIC.cloud CDN enabled, you may still be seeing cache headers from your local server.'=>'With QUIC.cloud CDN enabled, you may still be seeing cache headers from your local server.','An optional second parameter may be used to specify cache control. Use a space to separate'=>'An optional second parameter may be used to specify cache control. Use a space to separate','The above nonces will be converted to ESI automatically.'=>'The above nonces will be converted to ESI automatically.','Browser'=>'Browser','Object'=>'Object','Default port for %1$s is %2$s.'=>'Default port for %1$s is %2$s.','Object Cache Settings'=>'Object Cache Settings','Specify an HTTP status code and the number of seconds to cache that page, separated by a space.'=>'Specify an http status code and the number of seconds to cache that page, separated by a space.','Specify how long, in seconds, the front page is cached.'=>'Specify how long, in seconds, the front page is cached.','TTL'=>'TTL','If ON, the stale copy of a cached page will be shown to visitors until a new cache copy is available. Reduces the server load for following visits. If OFF, the page will be dynamically generated while visitors wait.'=>'If ON, the stale copy of a cached page will be shown to visitors until a new cache copy is available. Reduces the server load for following visits. If OFF, the page will be dynamically generated while visitors wait.','Swap'=>'Swap','Set this to append %1$s to all %2$s rules before caching CSS to specify how fonts should be displayed while being downloaded.'=>'Set this to append %1$s to all %2$s rules before caching CSS to specify how fonts should be displayed while being downloaded.','Avatar list in queue waiting for update'=>'Avatar list in queue waiting for update','Refresh Gravatar cache by cron.'=>'Refresh Gravatar cache by cron.','Accelerates the speed by caching Gravatar (Globally Recognized Avatars).'=>'Accelerates the speed by caching Gravatar (Globally Recognised Avatars).','Store Gravatar locally.'=>'Store Gravatar locally.','Failed to create Avatar table. Please follow <a %s>Table Creation guidance from LiteSpeed Wiki</a> to finish setup.'=>'Failed to create Avatar table. Please follow <a %s>Table Creation guidance from LiteSpeed Wiki</a> to finish setup.','LQIP requests will not be sent for images where both width and height are smaller than these dimensions.'=>'LQIP requests will not be sent for images where both width and height are smaller than these dimensions.','pixels'=>'pixels','Larger number will generate higher resolution quality placeholder, but will result in larger files which will increase page size and consume more points.'=>'Larger number will generate higher resolution quality placeholder, but will result in larger files which will increase page size and consume more points.','Specify the quality when generating LQIP.'=>'Specify the quality when generating LQIP.','Keep this off to use plain color placeholders.'=>'Keep this off to use plain colour placeholders.','Use QUIC.cloud LQIP (Low Quality Image Placeholder) generator service for responsive image previews while loading.'=>'Use QUIC.cloud LQIP (Low Quality Image Placeholder) generator service for responsive image previews while loading.','Specify the responsive placeholder SVG color.'=>'Specify the responsive placeholder SVG colour.','Variables %s will be replaced with the configured background color.'=>'Variables %s will be replaced with the configured background colour.','Variables %s will be replaced with the corresponding image properties.'=>'Variables %s will be replaced with the corresponding image properties.','It will be converted to a base64 SVG placeholder on-the-fly.'=>'It will be converted to a base64 SVG placeholder on-the-fly.','Specify an SVG to be used as a placeholder when generating locally.'=>'Specify an SVG to be used as a placeholder when generating locally.','Prevent any lazy load of listed pages.'=>'Prevent any lazy load of listed pages.','Iframes having these parent class names will not be lazy loaded.'=>'Iframes having these parent class names will not be lazy loaded.','Iframes containing these class names will not be lazy loaded.'=>'Iframes containing these class names will not be lazy loaded.','Images having these parent class names will not be lazy loaded.'=>'Images having these parent class names will not be lazy loaded.','LiteSpeed Cache Page Optimization'=>'LiteSpeed Cache Page Optimisation','Media Excludes'=>'Media Excludes','CSS Settings'=>'CSS Settings','%s is recommended.'=>'%s is recommended.','Deferred'=>'Deferred','Default'=>'Default','This can improve the page loading speed.'=>'This can improve the page loading speed.','Automatically enable DNS prefetching for all URLs in the document, including images, CSS, JavaScript, and so forth.'=>'Automatically enable DNS prefetching for all URLs in the document, including images, CSS, JavaScript, and so forth.','New developer version %s is available now.'=>'New developer version %s is available now.','New Developer Version Available!'=>'New Developer Version Available!','Dismiss this notice'=>'Dismiss this notice','Tweet this'=>'Tweet this','Tweet preview'=>'Tweet preview','Learn more'=>'Learn more','You just unlocked a promotion from QUIC.cloud!'=>'You just unlocked a promotion from QUIC.cloud!','The image compression quality setting of WordPress out of 100.'=>'The image compression quality setting of WordPress out of 100.','Image Optimization Settings'=>'Image Optimisation Settings','Are you sure to destroy all optimized images?'=>'Are you sure to destroy all optimised images?','Use Optimized Files'=>'Use Optimised Files','Switch back to using optimized images on your site'=>'Switch back to using optimised images on your site','Use Original Files'=>'Use Original Files','Use original images (unoptimized) on your site'=>'Use original images (unoptimised) on your site','You can quickly switch between using original (unoptimized versions) and optimized image files. It will affect all images on your website, both regular and webp versions if available.'=>'You can quickly switch between using original (unoptimised versions) and optimised image files. It will affect all images on your website, both regular and webp versions if available.','Optimization Tools'=>'Optimisation Tools','Rescan New Thumbnails'=>'Rescan New Thumbnails','Congratulations, all gathered!'=>'Congratulations, all gathered!','What is an image group?'=>'What is an image group?','Delete all backups of the original images'=>'Delete all backups of the original images','Calculate Backups Disk Space'=>'Calculate Backups Disk Space','Optimization Status'=>'Optimisation Status','Current limit is'=>'Current limit is','You can request a maximum of %s images at once.'=>'You can request a maximum of %s images at once.','Optimize images with our QUIC.cloud server'=>'Optimise images with our QUIC.cloud server','Revisions newer than this many days will be kept when cleaning revisions.'=>'Revisions newer than this many days will be kept when cleaning revisions.','Day(s)'=>'Day(s)','Specify the number of most recent revisions to keep when cleaning revisions.'=>'Specify the number of most recent revisions to keep when cleaning revisions.','LiteSpeed Cache Database Optimization'=>'LiteSpeed Cache Database Optimisation','DB Optimization Settings'=>'DB Optimisation Settings','Option Name'=>'Option Name','Database Summary'=>'Database Summary','We are good. No table uses MyISAM engine.'=>'We are good. No table uses MyISAM engine.','Convert to InnoDB'=>'Convert to InnoDB','Tool'=>'Tool','Engine'=>'Engine','Table'=>'Table','Database Table Engine Converter'=>'Database Table Engine Converter','Clean revisions older than %1$s day(s), excluding %2$s latest revisions'=>'Clean revisions older than %1$s day(s), excluding %2$s latest revisions','Currently active crawler'=>'Currently active crawler','Crawler(s)'=>'Crawler(s)','Crawler Status'=>'Crawler Status','Force cron'=>'Force cron','Requests in queue'=>'Requests in queue','Private Cache'=>'Private Cache','Public Cache'=>'Public Cache','Cache Status'=>'Cache Status','Last Pull'=>'Last Pull','Image Optimization Summary'=>'Image Optimisation Summary','Refresh page score'=>'Refresh page score','Are you sure you want to redetect the closest cloud server for this service?'=>'Are you sure you want to redetect the closest cloud server for this service?','Refresh page load time'=>'Refresh page load time','Go to QUIC.cloud dashboard'=>'Go to QUIC.cloud dashboard','Low Quality Image Placeholder'=>'Low Quality Image Placeholder','Sync data from Cloud'=>'Sync data from Cloud','QUIC.cloud Service Usage Statistics'=>'QUIC.cloud Service Usage Statistics','Total images optimized in this month'=>'Total images optimised in this month','Total Usage'=>'Total Usage','Pay as You Go Usage Statistics'=>'Pay as You Go Usage Statistics','PAYG Balance'=>'PAYG Balance','Pay as You Go'=>'Pay as You Go','Usage'=>'Usage','Fast Queue Usage'=>'Fast Queue Usage','CDN Bandwidth'=>'CDN Bandwidth','LiteSpeed Cache Dashboard'=>'LiteSpeed Cache Dashboard','Network Dashboard'=>'Network Dashboard','No cloud services currently in use'=>'No cloud services currently in use','Click to clear all nodes for further redetection.'=>'Click to clear all nodes for further redetection.','Current Cloud Nodes in Service'=>'Current Cloud Nodes in Service','Link to QUIC.cloud'=>'Link to QUIC.cloud','General Settings'=>'General Settings','Specify which HTML element attributes will be replaced with CDN Mapping.'=>'Specify which HTML element attributes will be replaced with CDN Mapping.','Add new CDN URL'=>'Add new CDN URL','Remove CDN URL'=>'Remove CDN URL','To enable the following functionality, turn ON Cloudflare API in CDN Settings.'=>'To enable the following functionality, turn ON Cloudflare API in CDN Settings.','QUIC.cloud'=>'QUIC.cloud','WooCommerce Settings'=>'WooCommerce Settings','Current Online Server IPs'=>'Current Online Server IPs','LQIP Cache'=>'LQIP Cache','Options saved.'=>'Options saved.','Removed backups successfully.'=>'Removed backups successfully.','Calculated backups successfully.'=>'Calculated backups successfully.','Rescanned %d images successfully.'=>'Rescanned %d images successfully.','Rescanned successfully.'=>'Rescanned successfully.','Destroy all optimization data successfully.'=>'Destroyed all optimisation data successfully.','Cleaned up unfinished data successfully.'=>'Cleaned up unfinished data successfully.','Pull Cron is running'=>'Pull Cron is running','No valid image found by Cloud server in the current request.'=>'No valid image found by Cloud server in the current request.','No valid image found in the current request.'=>'No valid image found in the current request.','Pushed %1$s to Cloud server, accepted %2$s.'=>'Pushed %1$s to Cloud server, accepted %2$s.','Revisions Max Age'=>'Revisions Max Age','Revisions Max Number'=>'Revisions Max Number','Debug URI Excludes'=>'Debug URI Excludes','Debug URI Includes'=>'Debug URI Includes','HTML Attribute To Replace'=>'HTML Attribute To Replace','Use CDN Mapping'=>'Use CDN Mapping','Editor Heartbeat TTL'=>'Editor Heartbeat TTL','Editor Heartbeat'=>'Editor Heartbeat','Backend Heartbeat TTL'=>'Back end Heartbeat TTL','Backend Heartbeat Control'=>'Back end Heartbeat Control','Frontend Heartbeat TTL'=>'Front end Heartbeat TTL','Frontend Heartbeat Control'=>'Front end Heartbeat Control','Backend .htaccess Path'=>'Back end .htaccess Path','Frontend .htaccess Path'=>'Front end .htaccess Path','ESI Nonces'=>'ESI Nonces','WordPress Image Quality Control'=>'WordPress Image Quality Control','Auto Request Cron'=>'Auto Request Cron','Generate LQIP In Background'=>'Generate LQIP In Background','LQIP Minimum Dimensions'=>'LQIP Minimum Dimensions','LQIP Quality'=>'LQIP Quality','LQIP Cloud Generator'=>'LQIP Cloud Generator','Responsive Placeholder SVG'=>'Responsive Placeholder SVG','Responsive Placeholder Color'=>'Responsive Placeholder Colour','Basic Image Placeholder'=>'Basic Image Placeholder','Lazy Load URI Excludes'=>'Lazy Load URI Excludes','Lazy Load Iframe Parent Class Name Excludes'=>'Lazy Load Iframe Parent Class Name Excludes','Lazy Load Iframe Class Name Excludes'=>'Lazy Load Iframe Class Name Excludes','Lazy Load Image Parent Class Name Excludes'=>'Lazy Load Image Parent Class Name Excludes','Gravatar Cache TTL'=>'Gravatar Cache TTL','Gravatar Cache Cron'=>'Gravatar Cache Cron','Gravatar Cache'=>'Gravatar Cache','DNS Prefetch Control'=>'DNS Prefetch Control','Font Display Optimization'=>'Font Display Optimisation','Force Public Cache URIs'=>'Force Public Cache URIs','Notifications'=>'Notifications','Default HTTP Status Code Page TTL'=>'Default HTTP Status Code Page TTL','Default REST TTL'=>'Default REST TTL','Enable Cache'=>'Enable Cache','Server IP'=>'Server IP','Images not requested'=>'Images not requested','Sync credit allowance with Cloud Server successfully.'=>'Sync credit allowance with Cloud Server successfully.','Failed to communicate with QUIC.cloud server'=>'Failed to communicate with QUIC.cloud server','Good news from QUIC.cloud server'=>'Good news from QUIC.cloud server','Message from QUIC.cloud server'=>'Message from QUIC.cloud server','Please try after %1$s for service %2$s.'=>'Please try after %1$s for service %2$s.','No available Cloud Node.'=>'No available Cloud Node.','Cloud Error'=>'Cloud Error','The database has been upgrading in the background since %s. This message will disappear once upgrade is complete.'=>'The database has been upgrading in the background since %s. This message will disappear once upgrade is complete.','Restore from backup'=>'Restore from backup','No backup of unoptimized WebP file exists.'=>'No backup of unoptimised WebP file exists.','WebP file reduced by %1$s (%2$s)'=>'WebP file reduced by %1$s (%2$s)','Currently using original (unoptimized) version of WebP file.'=>'Currently using original (unoptimised) version of WebP file.','Currently using optimized version of WebP file.'=>'Currently using optimised version of WebP file.','Orig'=>'Orig','(no savings)'=>'(no savings)','Orig %s'=>'Orig %s','Congratulation! Your file was already optimized'=>'Congratulation! Your file was already optimised','No backup of original file exists.'=>'No backup of original file exists.','Using optimized version of file. '=>'Using optimised version of file. ','Orig saved %s'=>'Orig saved %s','Original file reduced by %1$s (%2$s)'=>'Original file reduced by %1$s (%2$s)','Click to switch to optimized version.'=>'Click to switch to optimised version.','Currently using original (unoptimized) version of file.'=>'Currently using original (unoptimised) version of file.','(non-optm)'=>'(non-optm)','Click to switch to original (unoptimized) version.'=>'Click to switch to original (unoptimised) version.','Currently using optimized version of file.'=>'Currently using optimised version of file.','(optm)'=>'(optm)','LQIP image preview for size %s'=>'LQIP image preview for size %s','LQIP'=>'LQIP','Previously existed in blocklist'=>'Previously existed in blocklist','Manually added to blocklist'=>'Manually added to blocklist','Mobile Agent Rules'=>'Mobile Agent Rules','Sitemap created successfully: %d items'=>'Sitemap created successfully: %d items','Sitemap cleaned successfully'=>'Sitemap cleaned successfully','Invalid IP'=>'Invalid IP','Value range'=>'Value range','Smaller than'=>'Smaller than','Larger than'=>'Larger than','Zero, or'=>'Zero, or','Maximum value'=>'Maximum value','Minimum value'=>'Minimum value','Path must end with %s'=>'Path must end with %s','Invalid rewrite rule'=>'Invalid rewrite rule','currently set to %s'=>'currently set to %s','This setting is overwritten by the PHP constant %s'=>'This setting is overwritten by the PHP constant %s','Toolbox'=>'Toolbox','Database'=>'Database','Page Optimization'=>'Page Optimisation','Dashboard'=>'Dashboard','Converted to InnoDB successfully.'=>'Converted to InnoDB successfully.','Cleaned all Gravatar files.'=>'Cleaned all Gravatar files.','Cleaned all LQIP files.'=>'Cleaned all LQIP files.','Unknown error'=>'Unknown error','Your domain has been forbidden from using our services due to a previous policy violation.'=>'Your domain has been forbidden from using our services due to a previous policy violation.','The callback validation to your domain failed. Please make sure there is no firewall blocking our servers. Response code: '=>'The callback validation to your domain failed. Please make sure there is no firewall blocking our servers. Response code: ','The callback validation to your domain failed. Please make sure there is no firewall blocking our servers.'=>'The callback validation to your domain failed. Please make sure there is no firewall blocking our servers.','The callback validation to your domain failed due to hash mismatch.'=>'The callback validation to your domain failed due to hash mismatch.','Your application is waiting for approval.'=>'Your application is waiting for approval.','Previous request too recent. Please try again after %s.'=>'Previous request too recent. Please try again after %s.','Previous request too recent. Please try again later.'=>'Previous request too recent. Please try again later.','Crawler disabled by the server admin.'=>'Crawler disabled by the server admin.','Could not find %1$s in %2$s.'=>'Could not find %1$s in %2$s.','Credits are not enough to proceed the current request.'=>'Credits are not enough to proceed with the current request.','The domain key is not correct. Please try to sync your domain key again.'=>'The domain key is not correct. Please try to sync your domain key again.','There is proceeding queue not pulled yet.'=>'There is proceeding queue not pulled yet.','Not enough parameters. Please check if the domain key is set correctly'=>'Not enough parameters. Please check if the domain key is set correctly','The image list is empty.'=>'The image list is empty.','LiteSpeed Crawler Cron'=>'LiteSpeed Crawler Cron','Every Minute'=>'Every Minute','Turn this option ON to show latest news automatically, including hotfixes, new releases, available beta versions, and promotions.'=>'Turn this option ON to show latest news automatically, including hotfixes, new releases, available beta versions, and promotions.','To grant wp-admin access to the LiteSpeed Support Team, please generate a passwordless link for the current logged-in user to be sent with the report.'=>'To grant wp-admin access to the LiteSpeed Support Team, please generate a passwordless link for the current logged-in user to be sent with the report.','Please do NOT share the above passwordless link with anyone.'=>'Please do NOT share the above passwordless link with anyone.','To generate a passwordless link for LiteSpeed Support Team access, you must install %s.'=>'To generate a passwordless link for LiteSpeed Support Team access, you must install %s.','Install'=>'Install','These options are only available with LiteSpeed Enterprise Web Server or QUIC.cloud CDN.'=>'These options are only available with LiteSpeed Enterprise Web Server or QUIC.cloud CDN.','PageSpeed Score'=>'PageSpeed Score','Improved by'=>'Improved by','After'=>'After','Before'=>'Before','Page Load Time'=>'Page Load Time','To use the caching functions you must have a LiteSpeed web server or be using QUIC.cloud CDN.'=>'To use the caching functions you must have a LiteSpeed web server or be using QUIC.cloud CDN.','Preserve EXIF/XMP data'=>'Preserve EXIF/XMP data','Try GitHub Version'=>'Try GitHub Version','If you turn any of the above settings OFF, please remove the related file types from the %s box.'=>'If you turn any of the above settings OFF, please remove the related file types from the %s box.','Both full and partial strings can be used.'=>'Both full and partial strings can be used.','Images containing these class names will not be lazy loaded.'=>'Images containing these class names will not be lazy loaded.','Lazy Load Image Class Name Excludes'=>'Lazy Load Image Class Name Excludes','For example, %1$s defines a TTL of %2$s seconds for %3$s.'=>'For example, %1$s defines a TTL of %2$s seconds for %3$s.','To define a custom TTL for a URI, add a space followed by the TTL value to the end of the URI.'=>'To define a custom TTL for a URI, add a space followed by the TTL value to the end of the URI.','Maybe Later'=>'Maybe later','Turn On Auto Upgrade'=>'Turn on auto upgrade','Upgrade'=>'Upgrade','New release %s is available now.'=>'New release %s is available now.','New Version Available!'=>'New version available!','Sure I\'d love to review!'=>'Sure I\'d love to review!','Thank You for Using the LiteSpeed Cache Plugin!'=>'Thank you for using the LiteSpeed Cache plugin!','Upgraded successfully.'=>'Upgraded successfully.','Failed to upgrade.'=>'Failed to upgrade.','Changed setting successfully.'=>'Changed setting successfully.','ESI sample for developers'=>'ESI sample for developers','Replace %1$s with %2$s.'=>'Replace %1$s with %2$s.','You can turn shortcodes into ESI blocks.'=>'You can turn shortcodes into ESI blocks.','WpW: Private Cache vs. Public Cache'=>'WpW: Private Cache vs. Public Cache','Append query string %s to the resources to bypass this action.'=>'Append query string %s to the resources to bypass this action.','Google reCAPTCHA will be bypassed automatically.'=>'Google reCAPTCHA will be bypassed automatically.','Cookie Values'=>'Cookie Values','Cookie Name'=>'Cookie Name','Cookie Simulation'=>'Cookie Simulation','Use Web Font Loader library to load Google Fonts asynchronously while leaving other CSS intact.'=>'Use Web Font Loader library to load Google Fonts asynchronously while leaving other CSS intact.','Turn this option ON to have LiteSpeed Cache updated automatically, whenever a new version is released. If OFF, update manually as usual.'=>'Turn this option ON to have LiteSpeed Cache updated automatically, whenever a new version is released. If OFF, update manually as usual.','Automatically Upgrade'=>'Automatically Upgrade','Your IP'=>'Your IP','Reset successfully.'=>'Reset successfully.','This will reset all settings to default settings.'=>'This will reset all settings to default settings.','Reset All Settings'=>'Reset All Settings','Separate critical CSS files will be generated for paths containing these strings.'=>'Separate critical CSS files will be generated for paths containing these strings.','Separate CCSS Cache URIs'=>'Separate CCSS Cache URIs','For example, if every Page on the site has different formatting, enter %s in the box. Separate critical CSS files will be stored for every Page on the site.'=>'For example, if every Page on the site has different formatting, enter %s in the box. Separate critical CSS files will be stored for every Page on the site.','List post types where each item of that type should have its own CCSS generated.'=>'List post types where each item of that type should have its own CCSS generated.','Separate CCSS Cache Post Types'=>'Separate CCSS Cache Post Types','Size list in queue waiting for cron'=>'Size list in queue waiting for cron','If set to %1$s, before the placeholder is localized, the %2$s configuration will be used.'=>'If set to %1$s, before the placeholder is localised, the %2$s configuration will be used.','Automatically generate LQIP in the background via a cron-based queue.'=>'Automatically generate LQIP in the background via a cron-based queue.','This will generate the placeholder with same dimensions as the image if it has the width and height attributes.'=>'This will generate the placeholder with same dimensions as the image if it has the width and height attributes.','Responsive image placeholders can help to reduce layout reshuffle when images are loaded.'=>'Responsive image placeholders can help to reduce layout reshuffle when images are loaded.','Responsive Placeholder'=>'Responsive Placeholder','This will delete all generated image LQIP placeholder files'=>'This will delete all generated image LQIP placeholder files','Please enable LiteSpeed Cache in the plugin settings.'=>'Please enable LiteSpeed Cache in the plugin settings.','Please enable the LSCache Module at the server level, or ask your hosting provider.'=>'Please enable the LSCache Module at the server level, or ask your hosting provider.','Failed to request via WordPress'=>'Failed to request via WordPress','High-performance page caching and site optimization from LiteSpeed'=>'High-performance page caching and site optimisation from LiteSpeed','Reset the optimized data successfully.'=>'Reset the optimised data successfully.','Update %s now'=>'Update %s now','View %1$s version %2$s details'=>'View %1$s version %2$s details','<a href="%1$s" %2$s>View version %3$s details</a> or <a href="%4$s" %5$s target="_blank">update now</a>.'=>'<a href="%1$s" %2$s>View version %3$s details</a> or <a href="%4$s" %5$s target="_blank">update now</a>.','Install %s'=>'Install %s','LSCache caching functions on this page are currently unavailable!'=>'LSCache caching functions on this page are currently unavailable!','%1$s plugin version %2$s required for this action.'=>'%1$s plugin version %2$s required for this action.','We are working hard to improve your online service experience. The service will be unavailable while we work. We apologize for any inconvenience.'=>'We are working hard to improve your online service experience. The service will be unavailable while we work. We apologise for any inconvenience.','Automatically remove the original image backups after fetching optimized images.'=>'Automatically remove the original image backups after fetching optimised images.','Remove Original Backups'=>'Remove Original Backups','Automatically request optimization via cron job.'=>'Automatically request optimisation via cron job.','A backup of each image is saved before it is optimized.'=>'A backup of each image is saved before it is optimised.','Switched images successfully.'=>'Switched images successfully.','This can improve quality but may result in larger images than lossy compression will.'=>'This can improve quality but may result in larger images than lossy compression will.','Optimize images using lossless compression.'=>'Optimise images using lossless compression.','Optimize Losslessly'=>'Optimise Losslessly','Optimize images and save backups of the originals in the same folder.'=>'Optimise images and save backups of the originals in the same folder.','Optimize Original Images'=>'Optimise Original Images','When this option is turned %s, it will also load Google Fonts asynchronously.'=>'When this option is turned %s, it will also load Google Fonts asynchronously.','Cleaned all Critical CSS files.'=>'Cleaned all Critical CSS files.','This will inline the asynchronous CSS library to avoid render blocking.'=>'This will inline the asynchronous CSS library to avoid render blocking.','Inline CSS Async Lib'=>'Inline CSS Async Lib','Run Queue Manually'=>'Run Queue Manually','Last requested cost'=>'Last requested cost','Last generated'=>'Last generated','If set to %s this is done in the foreground, which may slow down page load.'=>'If set to %s this is done in the foreground, which may slow down page load.','Optimize CSS delivery.'=>'Optimise CSS delivery.','This will delete all generated critical CSS files'=>'This will delete all generated critical CSS files','Critical CSS'=>'Critical CSS','This site utilizes caching in order to facilitate a faster response time and better user experience. Caching potentially stores a duplicate copy of every web page that is on display on this site. All cache files are temporary, and are never accessed by any third party, except as necessary to obtain technical support from the cache plugin vendor. Cache files expire on a schedule set by the site administrator, but may easily be purged by the admin before their natural expiration, if necessary. We may use QUIC.cloud services to process & cache your data temporarily.'=>'This site utilises caching in order to facilitate a faster response time and better user experience. Caching potentially stores a duplicate copy of every web page that is on display on this site. All cache files are temporary, and are never accessed by any third party, except as necessary to obtain technical support from the cache plugin vendor. Cache files expire on a schedule set by the site administrator, but may easily be purged by the admin before their natural expiration, if necessary. We may use QUIC.cloud services to process & cache your data temporarily.','Disabling this may cause WordPress tasks triggered by AJAX to stop working.'=>'Disabling this may cause WordPress tasks triggered by Ajax to stop working.','right now'=>'right now','just now'=>'just now','Saved'=>'Saved','Last ran'=>'Last ran','You will be unable to Revert Optimization once the backups are deleted!'=>'You will be unable to Revert Optimisation once the backups are deleted!','This is irreversible.'=>'This is irreversible.','Remove Original Image Backups'=>'Remove Original Image Backups','Are you sure you want to remove all image backups?'=>'Are you sure you want to remove all image backups?','Total'=>'Total','Files'=>'Files','Last calculated'=>'Last calculated','Calculate Original Image Storage'=>'Calculate Original Image Storage','Storage Optimization'=>'Storage Optimisation','Use the format %1$s or %2$s (element is optional).'=>'Use the format %1$s or %2$s (element is optional).','Only attributes listed here will be replaced.'=>'Only attributes listed here will be replaced.','Only files within these directories will be pointed to the CDN.'=>'Only files within these directories will be pointed to the CDN.','Included Directories'=>'Included Directories','A Purge All will be executed when WordPress runs these hooks.'=>'A Purge All will be executed when WordPress runs these hooks.','Purge All Hooks'=>'Purge All Hooks','Purged all caches successfully.'=>'Purged all caches successfully.','LSCache'=>'LSCache','Forced cacheable'=>'Forced cacheable','Paths containing these strings will be cached regardless of no-cacheable settings.'=>'Paths containing these strings will be cached regardless of no-cacheable settings.','Force Cache URIs'=>'Force Cache URIs','Exclude Settings'=>'Exclude Settings','This will disable LSCache and all optimization features for debug purpose.'=>'This will disable LSCache and all optimisation features for debug purpose.','Disable All Features'=>'Disable All Features','Opcode Cache'=>'Opcode Cache','CSS/JS Cache'=>'CSS/JS Cache','Remove all previous unfinished image optimization requests.'=>'Remove all previous unfinished image optimisation requests.','Clean Up Unfinished Data'=>'Clean Up Unfinished Data','Join Us on Slack'=>'Join Us on Slack','Join the %s community.'=>'Join the %s community.','Want to connect with other LiteSpeed users?'=>'Want to connect with other LiteSpeed users?','Your Email address on %s.'=>'Your Email address on %s.','Use %s API functionality.'=>'Use %s API functionality.','To randomize CDN hostname, define multiple hostnames for the same resources.'=>'To randomise CDN hostname, define multiple hostnames for the same resources.','Join LiteSpeed Slack community'=>'Join LiteSpeed Slack community','Visit LSCWP support forum'=>'Visit LSCWP support forum','Images notified to pull'=>'Images notified to pull','What is a group?'=>'What is a group?','%s image'=>'%s image','%s group'=>'%s group','%s images'=>'%s images','%s groups'=>'%s groups','Guest'=>'Guest','To crawl the site as a logged-in user, enter the user ids to be simulated.'=>'To crawl the site as a logged-in user, enter the user ids to be simulated.','Role Simulation'=>'Role Simulation','running'=>'running','Size'=>'Size','Ended reason'=>'Ended reason','Last interval'=>'Last interval','Current crawler started at'=>'Current crawler started at','Run time for previous crawler'=>'Run time for previous crawler','%d seconds'=>'%d seconds','Last complete run time for all crawlers'=>'Last complete run time for all crawlers','Current sitemap crawl started at'=>'Current sitemap crawl started at','Save transients in database when %1$s is %2$s.'=>'Save transients in database when %1$s is %2$s.','Store Transients'=>'Store Transients','If %1$s is %2$s, then %3$s must be populated!'=>'If %1$s is %2$s, then %3$s must be populated!','NOTE'=>'NOTE','Server variable(s) %s available to override this setting.'=>'Server variable(s) %s available to override this setting.','API'=>'API','Imported setting file %s successfully.'=>'Imported setting file %s successfully.','Import failed due to file error.'=>'Import failed due to file error.','How to Fix Problems Caused by CSS/JS Optimization.'=>'How to Fix Problems Caused by CSS/JS Optimisation.','This will generate extra requests to the server, which will increase server load.'=>'This will generate extra requests to the server, which will increase server load.','Instant Click'=>'Instant Click','Reset the entire opcode cache'=>'Reset the entire opcode cache','This will import settings from a file and override all current LiteSpeed Cache settings.'=>'This will import settings from a file and override all current LiteSpeed Cache settings.','Last imported'=>'Last imported','Import'=>'Import','Import Settings'=>'Import Settings','This will export all current LiteSpeed Cache settings and save them as a file.'=>'This will export all current LiteSpeed Cache settings and save them as a file.','Last exported'=>'Last exported','Export'=>'Export','Export Settings'=>'Export Settings','Import / Export'=>'Import / Export','Use keep-alive connections to speed up cache operations.'=>'Use keep-alive connections to speed up cache operations.','Database to be used'=>'Database to be used','Redis Database ID'=>'Redis Database ID','Specify the password used when connecting.'=>'Specify the password used when connecting.','Password'=>'Password','Only available when %s is installed.'=>'Only available when %s is installed.','Username'=>'Username','Your %s Hostname or IP address.'=>'Your %s Hostname or IP address.','Method'=>'Method','Purge all object caches successfully.'=>'Purge all object caches successfully.','Object cache is not enabled.'=>'Object cache is not enabled.','Improve wp-admin speed through caching. (May encounter expired data)'=>'Improve wp-admin speed through caching. (May encounter expired data)','Persistent Connection'=>'Persistent Connection','Do Not Cache Groups'=>'Do Not Cache Groups','Groups cached at the network level.'=>'Groups cached at the network level.','Global Groups'=>'Global Groups','Connection Test'=>'Connection Test','%s Extension'=>'%s Extension','Status'=>'Status','Default TTL for cached objects.'=>'Default TTL for cached objects.','Default Object Lifetime'=>'Default Object Lifetime','Port'=>'Port','Host'=>'Host','Object Cache'=>'Object Cache','Failed'=>'Failed','Passed'=>'Passed','Not Available'=>'Not Available','Purge all the object caches'=>'Purge all the object caches','Failed to communicate with Cloudflare'=>'Failed to communicate with Cloudflare','Communicated with Cloudflare successfully.'=>'Communicated with Cloudflare successfully.','No available Cloudflare zone'=>'No available Cloudflare zone','Notified Cloudflare to purge all successfully.'=>'Notified Cloudflare to purge all successfully.','Cloudflare API is set to off.'=>'Cloudflare API is set to off.','Notified Cloudflare to set development mode to %s successfully.'=>'Notified Cloudflare to set development mode to %s successfully.','Once saved, it will be matched with the current list and completed automatically.'=>'Once saved, it will be matched with the current list and completed automatically.','You can just type part of the domain.'=>'You can just type part of the domain.','Domain'=>'Domain','Cloudflare API'=>'Cloudflare API','Purge Everything'=>'Purge Everything','Cloudflare Cache'=>'Cloudflare Cache','Development Mode will be turned off automatically after three hours.'=>'Development Mode will be turned off automatically after three hours.','Temporarily bypass Cloudflare cache. This allows changes to the origin server to be seen in realtime.'=>'Temporarily bypass Cloudflare cache. This allows changes to the origin server to be seen in realtime.','Development mode will be automatically turned off in %s.'=>'Development mode will be automatically turned off in %s.','Current status is %s.'=>'Current status is %s.','Current status is %1$s since %2$s.'=>'Current status is %1$s since %2$s.','Check Status'=>'Check Status','Turn OFF'=>'Turn OFF','Turn ON'=>'Turn ON','Development Mode'=>'Development Mode','Cloudflare Zone'=>'Cloudflare Zone','Cloudflare Domain'=>'Cloudflare Domain','Cloudflare'=>'Cloudflare','For example'=>'For example','Prefetching DNS can reduce latency for visitors.'=>'Prefetching DNS can reduce latency for visitors.','DNS Prefetch'=>'DNS Prefetch','Adding Style to Your Lazy-Loaded Images'=>'Adding Style to Your Lazy-Loaded Images','Default value'=>'Default value','Static file type links to be replaced by CDN links.'=>'Static file type links to be replaced by CDN links.','Drop Query String'=>'Drop Query String','Enable this option if you are using both HTTP and HTTPS in the same domain and are noticing cache irregularities.'=>'Enable this option if you are using both HTTP and HTTPS in the same domain and are noticing cache irregularities.','Improve HTTP/HTTPS Compatibility'=>'Improve HTTP/HTTPS Compatibility','Remove all previous image optimization requests/results, revert completed optimizations, and delete all optimization files.'=>'Remove all previous image optimisation requests/results, revert completed optimisations, and delete all optimisation files.','Destroy All Optimization Data'=>'Destroy All Optimisation Data','Scan for any new unoptimized image thumbnail sizes and resend necessary image optimization requests.'=>'Scan for any new unoptimised image thumbnail sizes and resend necessary image optimisation requests.','This will increase the size of optimized files.'=>'This will increase the size of optimised files.','Preserve EXIF data (copyright, GPS, comments, keywords, etc) when optimizing.'=>'Preserve EXIF data (copyright, GPS, comments, keywords, etc) when optimising.','Clear Logs'=>'Clear Logs','To test the cart, visit the <a %s>FAQ</a>.'=>'To test the basket, visit the <a %s>FAQ</a>.',' %s ago'=>' %s ago','WebP saved %s'=>'WebP saved %s','If you run into any issues, please refer to the report number in your support message.'=>'If you run into any issues, please refer to the report number in your support message.','Last pull initiated by cron at %s.'=>'Last pull initiated by cron at %s.','Images will be pulled automatically if the cron job is running.'=>'Images will be pulled automatically if the cron job is running.','Only press the button if the pull cron job is disabled.'=>'Only press the button if the pull cron job is disabled.','Pull Images'=>'Pull Images','This process is automatic.'=>'This process is automatic.','Last Request'=>'Last Request','Images Pulled'=>'Images Pulled','Report'=>'Report','Send this report to LiteSpeed. Refer to this report number when posting in the WordPress support forum.'=>'Send this report to LiteSpeed. Refer to this report number when posting in the WordPress support forum.','Send to LiteSpeed'=>'Send to LiteSpeed','LiteSpeed Optimization'=>'LiteSpeed Optimisation','Load Google Fonts Asynchronously'=>'Load Google Fonts Asynchronously','Browser Cache TTL'=>'Browser Cache TTL','Learn More'=>'Learn More','Images optimized and pulled'=>'Images optimised and pulled','Images requested'=>'Images requested','Switched to optimized file successfully.'=>'Switched to optimised file successfully.','Restored original file successfully.'=>'Restored original file successfully.','Enabled WebP file successfully.'=>'Enabled WebP file successfully.','Disabled WebP file successfully.'=>'Disabled WebP file successfully.','Significantly improve load time by replacing images with their optimized %s versions.'=>'Significantly improve load time by replacing images with their optimised %s versions.','Selected roles will be excluded from cache.'=>'Selected roles will be excluded from cache.','Tuning'=>'Tuning','Selected roles will be excluded from all optimizations.'=>'Selected roles will be excluded from all optimisations.','Role Excludes'=>'Role Excludes','Tuning Settings'=>'Tuning Settings','If the tag slug is not found, the tag will be removed from the list on save.'=>'If the tag slug is not found, the tag will be removed from the list on save.','If the category name is not found, the category will be removed from the list on save.'=>'If the category name is not found, the category will be removed from the list on save.','After the QUIC.cloud Image Optimization server finishes optimization, it will notify your site to pull the optimized images.'=>'After the QUIC.cloud Image Optimisation server finishes optimisation, it will notify your site to pull the optimised images.','Send Optimization Request'=>'Send Optimisation Request','Image Information'=>'Image Information','Total Reduction'=>'Total Reduction','Optimization Summary'=>'Optimisation Summary','LiteSpeed Cache Image Optimization'=>'LiteSpeed Cache Image Optimisation','Image Optimization'=>'Image Optimisation','For example, %s can be used for a transparent placeholder.'=>'For example, %s can be used for a transparent placeholder.','By default a gray image placeholder %s will be used.'=>'By default a grey image placeholder %s will be used.','This can be predefined in %2$s as well using constant %1$s, with this setting taking priority.'=>'This can be predefined in %2$s as well using constant %1$s, with this setting taking priority.','Specify a base64 image to be used as a simple placeholder while images finish loading.'=>'Specify a base64 image to be used as a simple placeholder while images finish loading.','Elements with attribute %s in html code will be excluded.'=>'Elements with attribute %s in HTML code will be excluded.','Filter %s is supported.'=>'Filter %s is supported.','Listed images will not be lazy loaded.'=>'Listed images will not be lazy loaded.','Lazy Load Image Excludes'=>'Lazy Load Image Excludes','No optimization'=>'No optimisation','Prevent any optimization of listed pages.'=>'Prevent any optimisation of listed pages.','URI Excludes'=>'URI Excludes','Stop loading WordPress.org emoji. Browser default emoji will be displayed instead.'=>'Stop loading WordPress.org emoji. Browser default emoji will be displayed instead.','Both full URLs and partial strings can be used.'=>'Both full URLs and partial strings can be used.','Load iframes only when they enter the viewport.'=>'Load iframes only when they enter the viewport.','Lazy Load Iframes'=>'Lazy Load Iframes','This can improve page loading time by reducing initial HTTP requests.'=>'This can improve page loading time by reducing initial HTTP requests.','Load images only when they enter the viewport.'=>'Load images only when they enter the viewport.','Lazy Load Images'=>'Lazy Load Images','Media Settings'=>'Media Settings','Wildcard %1$s supported (match zero or more characters). For example, to match %2$s and %3$s, use %4$s.'=>'Wildcard %1$s supported (match zero or more characters). For example, to match %2$s and %3$s, use %4$s.','To match the beginning, add %s to the beginning of the item.'=>'To match the beginning, add %s to the beginning of the item.','Maybe later'=>'Maybe later','I\'ve already left a review'=>'I\'ve already left a review','Welcome to LiteSpeed'=>'Welcome to LiteSpeed','Remove WordPress Emoji'=>'Remove WordPress Emoji','More settings'=>'More settings','Private cache'=>'Private cache','Non cacheable'=>'Non cacheable','Mark this page as '=>'Mark this page as ','Purge this page'=>'Purge this page','Load JS Deferred'=>'Load JS Deferred','Specify critical CSS rules for above-the-fold content when enabling %s.'=>'Specify critical CSS rules for above-the-fold content when enabling %s.','Critical CSS Rules'=>'Critical CSS Rules','Load CSS Asynchronously'=>'Load CSS Asynchronously','Prevent Google Fonts from loading on all pages.'=>'Prevent Google Fonts from loading on all pages.','Remove Google Fonts'=>'Remove Google Fonts','This can improve your speed score in services like Pingdom, GTmetrix and PageSpeed.'=>'This can improve your speed score in services like Pingdom, GTmetrix and PageSpeed.','Remove query strings from internal static resources.'=>'Remove query strings from internal static resources.','Remove Query Strings'=>'Remove Query Strings','user agents'=>'user agents','cookies'=>'cookies','Browser caching stores static files locally in the user\'s browser. Turn on this setting to reduce repeated requests for static files.'=>'Browser caching stores static files locally in the user\'s browser. Turn on this setting to reduce repeated requests for static files.','Browser Cache'=>'Browser Cache','tags'=>'tags','Do Not Cache Tags'=>'Do Not Cache Tags','To exclude %1$s, insert %2$s.'=>'To exclude %1$s, insert %2$s.','categories'=>'categories','To prevent %s from being cached, enter them here.'=>'To prevent %s from being cached, enter them here.','Do Not Cache Categories'=>'Do Not Cache Categories','Query strings containing these parameters will not be cached.'=>'Query strings containing these parameters will not be cached.','Do Not Cache Query Strings'=>'Do Not Cache Query Strings','Paths containing these strings will not be cached.'=>'Paths containing these strings will not be cached.','Do Not Cache URIs'=>'Do Not Cache URIs','One per line.'=>'One per line.','URI Paths containing these strings will NOT be cached as public.'=>'URI Paths containing these strings will NOT be cached as public.','Private Cached URIs'=>'Private Cached URIs','Paths containing these strings will not be served from the CDN.'=>'Paths containing these strings will not be served from the CDN.','Exclude Path'=>'Exclude Path','Include File Types'=>'Include File Types','Serve all JavaScript files through the CDN. This will affect all enqueued WP JavaScript files.'=>'Serve all JavaScript files through the CDN. This will affect all enqueued WP JavaScript files.','Include JS'=>'Include JS','Serve all CSS files through the CDN. This will affect all enqueued WP CSS files.'=>'Serve all CSS files through the CDN. This will affect all enqueued WP CSS files.','Include CSS'=>'Include CSS','Include Images'=>'Include Images','CDN URL to be used. For example, %s'=>'CDN URL to be used. For example, %s','CDN URL'=>'CDN URL','Site URL to be served through the CDN. Beginning with %1$s. For example, %2$s.'=>'Site URL to be served through the CDN. Beginning with %1$s. For example, %2$s.','Original URLs'=>'Original URLs','CDN Settings'=>'CDN Settings','CDN'=>'CDN','OFF'=>'OFF','ON'=>'ON','Notified LiteSpeed Web Server to purge CSS/JS entries.'=>'Notified LiteSpeed Web Server to purge CSS/JS entries.','Minify HTML content.'=>'Minify HTML content.','HTML Minify'=>'HTML Minify','JS Excludes'=>'JS Excludes','JS Combine'=>'JS Combine','JS Minify'=>'JS Minify','CSS Excludes'=>'CSS Excludes','CSS Combine'=>'CSS Combine','CSS Minify'=>'CSS Minify','Please test thoroughly when enabling any option in this list. After changing Minify/Combine settings, please do a Purge All action.'=>'Please test thoroughly when enabling any option in this list. After changing Minify/Combine settings, please do a Purge All action.','This will purge all minified/combined CSS/JS entries only'=>'This will purge all minified/combined CSS/JS entries only','Purge %s Error'=>'Purge %s Error','Database Optimizer'=>'Database Optimiser','Optimize all tables in your database'=>'Optimise all tables in your database','Optimize Tables'=>'Optimise Tables','Clean all transient options'=>'Clean all transient options','All Transients'=>'All Transients','Clean expired transient options'=>'Clean expired transient options','Expired Transients'=>'Expired Transients','Clean all trackbacks and pingbacks'=>'Clean all trackbacks and pingbacks','Trackbacks/Pingbacks'=>'Trackbacks/Pingbacks','Clean all trashed comments'=>'Clean all binned comments','Trashed Comments'=>'Binned comments','Clean all spam comments'=>'Clean all spam comments','Spam Comments'=>'Spam Comments','Clean all trashed posts and pages'=>'Clean all binned posts and pages','Trashed Posts'=>'Binned Posts','Clean all auto saved drafts'=>'Clean all auto saved drafts','Auto Drafts'=>'Auto Drafts','Clean all post revisions'=>'Clean all post revisions','Post Revisions'=>'Post Revisions','Clean All'=>'Clean All','Optimized all tables.'=>'Optimised all tables.','Clean all transients successfully.'=>'Clean all transients successfully.','Clean expired transients successfully.'=>'Clean expired transients successfully.','Clean trackbacks and pingbacks successfully.'=>'Clean trackbacks and pingbacks successfully.','Clean trashed comments successfully.'=>'Clean binned comments successfully.','Clean spam comments successfully.'=>'Clean spam comments successfully.','Clean trashed posts and pages successfully.'=>'Clean binned posts and pages successfully.','Clean auto drafts successfully.'=>'Clean auto drafts successfully.','Clean post revisions successfully.'=>'Clean post revisions successfully.','Clean all successfully.'=>'Clean all successfully.','Default Private Cache TTL'=>'Default Private Cache TTL','If your site contains public content that certain user roles can see but other roles cannot, you can specify a Vary Group for those user roles. For example, specifying an administrator vary group allows there to be a separate publicly-cached page tailored to administrators (with “edit” links, etc), while all other user roles see the default public page.'=>'If your site contains public content that certain user roles can see but other roles cannot, you can specify a Vary Group for those user roles. For example, specifying an administrator vary group allows there to be a separate publicly-cached page tailored to administrators (with “edit” links, etc), while all other user roles see the default public page.','Vary Group'=>'Vary Group','Cache the built-in Comment Form ESI block.'=>'Cache the built-in Comment Form ESI block.','Cache Comment Form'=>'Cache Comment Form','Cache Admin Bar'=>'Cache Admin Bar','Turn ON to cache public pages for logged in users, and serve the Admin Bar and Comment Form via ESI blocks. These two blocks will be uncached unless enabled below.'=>'Turn ON to cache public pages for logged in users, and serve the Admin Bar and Comment Form via ESI blocks. These two blocks will be uncached unless enabled below.','ESI allows you to designate parts of your dynamic page as separate fragments that are then assembled together to make the whole page. In other words, ESI lets you “punch holes” in a page, and then fill those holes with content that may be cached privately, cached publicly with its own TTL, or not cached at all.'=>'ESI allows you to designate parts of your dynamic page as separate fragments that are then assembled together to make the whole page. In other words, ESI lets you “punch holes” in a page, and then fill those holes with content that may be cached privately, cached publicly with its own TTL, or not cached at all.','With ESI (Edge Side Includes), pages may be served from cache for logged-in users.'=>'With ESI (Edge Side Includes), pages may be served from cache for logged-in users.','Private'=>'Private','Public'=>'Public','Purge Settings'=>'Purge Settings','Cache Mobile'=>'Cache Mobile','Advanced level will log more details.'=>'Advanced level will log more details.','Basic'=>'Basic','The maximum average server load allowed while crawling. The number of crawler threads in use will be actively reduced until average server load falls under this limit. If this cannot be achieved with a single thread, the current crawler run will be terminated.'=>'The maximum average server load allowed while crawling. The number of crawler threads in use will be actively reduced until average server load falls under this limit. If this cannot be achieved with a single thread, the current crawler run will be terminated.','Cache Login Page'=>'Cache Login Page','Cache requests made by WordPress REST API calls.'=>'Cache requests made by WordPress REST API calls.','Cache REST API'=>'Cache REST API','Privately cache commenters that have pending comments. Disabling this option will serve non-cacheable pages to commenters. (LSWS %s required)'=>'Privately cache commenters that have pending comments. Disabling this option will serve non-cacheable pages to commenters. (LSWS %s required)','Cache Commenters'=>'Cache Commenters','Privately cache frontend pages for logged-in users. (LSWS %s required)'=>'Privately cache frontend pages for logged-in users. (LSWS %s required)','Cache Logged-in Users'=>'Cache Logged-in Users','Cache Control Settings'=>'Cache Control Settings','ESI'=>'ESI','Excludes'=>'Excludes','Purge'=>'Purge','Cache'=>'Cache','WooCommerce'=>'WooCommerce','Current server time is %s.'=>'Current server time is %s.','Specify the time to purge the "%s" list.'=>'Specify the time to purge the "%s" list.','Both %1$s and %2$s are acceptable.'=>'Both %1$s and %2$s are acceptable.','Scheduled Purge Time'=>'Scheduled Purge Time','The URLs here (one per line) will be purged automatically at the time set in the option "%s".'=>'The URLs here (one per line) will be purged automatically at the time set in the option "%s".','Scheduled Purge URLs'=>'Scheduled Purge URLs','Shorten query strings in the debug log to improve readability.'=>'Shorten query strings in the debug log to improve readability.','Heartbeat'=>'Heartbeat','MB'=>'MB','Log File Size Limit'=>'Log File Size Limit','<p>Please add/replace the following codes into the beginning of %1$s:</p> %2$s'=>'<p>Please add/replace the following codes into the beginning of %1$s:</p> %2$s','%s file not writable.'=>'%s file not writable.','%s file not readable.'=>'%s file not readable.','Collapse Query Strings'=>'Collapse Query Strings','ESI Settings'=>'ESI Settings','A TTL of 0 indicates do not cache.'=>'A TTL of 0 indicates do not cache.','Recommended value: 28800 seconds (8 hours).'=>'Recommended value: 28800 seconds (8 hours).','Enable ESI'=>'Enable ESI','Custom Sitemap'=>'Custom Sitemap','Purge pages by relative or full URL.'=>'Purge pages by relative or full URL.','The crawler feature is not enabled on the LiteSpeed server. Please consult your server admin or hosting provider.'=>'The crawler feature is not enabled on the LiteSpeed server. Please consult your server admin or hosting provider.','WARNING'=>'WARNING','The next complete sitemap crawl will start at'=>'The next complete sitemap crawl will start at','Failed to write to %s.'=>'Failed to write to %s.','Folder is not writable: %s.'=>'Folder is not writable: %s.','Can not create folder: %1$s. Error: %2$s'=>'Can not create folder: %1$s. Error: %2$s','Folder does not exist: %s'=>'Folder does not exist: %s','Notified LiteSpeed Web Server to purge the list.'=>'Notified LiteSpeed Web Server to purge the list.','Allows listed IPs (one per line) to perform certain actions from their browsers.'=>'Allows listed IPs (one per line) to perform certain actions from their browsers.','Server Load Limit'=>'Server Load Limit','Specify how long in seconds before the crawler should initiate crawling the entire sitemap again.'=>'Specify how long in seconds before the crawler should initiate crawling the entire sitemap again.','Crawl Interval'=>'Crawl Interval','Then another WordPress is installed (NOT MULTISITE) at %s'=>'Then another WordPress is installed (NOT MULTISITE) at %s','LiteSpeed Cache Network Cache Settings'=>'LiteSpeed Cache Network Cache Settings','Select below for "Purge by" options.'=>'Select below for "Purge by" options.','LiteSpeed Cache CDN'=>'LiteSpeed Cache CDN','No crawler meta file generated yet'=>'No crawler meta file generated yet','Show crawler status'=>'Show crawler status','Watch Crawler Status'=>'Watch Crawler Status','Run frequency is set by the Interval Between Runs setting.'=>'Run frequency is set by the Interval Between Runs setting.','Manually run'=>'Manually run','Reset position'=>'Reset position','Run Frequency'=>'Run Frequency','Cron Name'=>'Cron Name','Crawler Cron'=>'Crawler Cron','%d minute'=>'%d minute','%d minutes'=>'%d minutes','%d hour'=>'%d hour','%d hours'=>'%d hours','Generated at %s'=>'Generated at %s','LiteSpeed Cache Crawler'=>'LiteSpeed Cache Crawler','Crawler'=>'Crawler','https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration'=>'https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration','Notified LiteSpeed Web Server to purge all pages.'=>'Notified LiteSpeed Web Server to purge all pages.','All pages with Recent Posts Widget'=>'All pages with Recent Posts Widget','Pages'=>'Pages','This will Purge Pages only'=>'This will Purge Pages only','Purge Pages'=>'Purge Pages','Cancel'=>'Cancel','Activate'=>'Activate','Email Address'=>'Email Address','Install Now'=>'Install Now','Purged the blog!'=>'Purged the blog!','Purged All!'=>'Purged All!','Notified LiteSpeed Web Server to purge error pages.'=>'Notified LiteSpeed Web Server to purge error pages.','If using OpenLiteSpeed, the server must be restarted once for the changes to take effect.'=>'If using OpenLiteSpeed, the server must be restarted once for the changes to take effect.','If the login cookie was recently changed in the settings, please log out and back in.'=>'If the login cookie was recently changed in the settings, please log out and back in.','However, there is no way of knowing all the possible customizations that were implemented.'=>'However, there is no way of knowing all the possible customisations that were implemented.','The LiteSpeed Cache plugin is used to cache pages - a simple way to improve the performance of the site.'=>'The LiteSpeed Cache plugin is used to cache pages - a simple way to improve the performance of the site.','The network admin setting can be overridden here.'=>'The network admin setting can be overridden here.','Specify how long, in seconds, public pages are cached.'=>'Specify how long, in seconds, public pages are cached.','Specify how long, in seconds, private pages are cached.'=>'Specify how long, in seconds, private pages are cached.','Purge pages by post ID.'=>'Purge pages by post ID.','Purge the LiteSpeed cache entries created by this plugin'=>'Purge the LiteSpeed cache entries created by this plugin','This will Purge Front Page only'=>'This will Purge Front Page only','Purge pages by tag name - e.g. %2$s should be used for the URL %1$s.'=>'Purge pages by tag name - e.g. %2$s should be used for the URL %1$s.','Purge pages by category name - e.g. %2$s should be used for the URL %1$s.'=>'Purge pages by category name - e.g. %2$s should be used for the URL %1$s.','If only the WordPress site should be purged, use Purge All.'=>'If only the WordPress site should be purged, use Purge All.','Notified LiteSpeed Web Server to purge everything.'=>'Notified LiteSpeed Web Server to purge everything.','Use Primary Site Configuration'=>'Use Primary Site Configuration','This will disable the settings page on all subsites.'=>'This will disable the settings page on all subsites.','Check this option to use the primary site\'s configuration for all subsites.'=>'Check this option to use the primary site\'s configuration for all subsites.','Save Changes'=>'Save Changes','The following options are selected, but are not editable in this settings page.'=>'The following options are selected, but are not editable in this settings page.','The network admin selected use primary site configs for all subsites.'=>'The network admin selected use primary site configs for all subsites.','Empty Entire Cache'=>'Empty Entire Cache','This action should only be used if things are cached incorrectly.'=>'This action should only be used if things are cached incorrectly.','This may cause heavy load on the server.'=>'This may cause heavy load on the server.','This will clear EVERYTHING inside the cache.'=>'This will clear EVERYTHING inside the cache.','LiteSpeed Cache Purge All'=>'LiteSpeed Cache Purge All','If you would rather not move at litespeed, you can deactivate this plugin.'=>'If you would rather not move at litespeed, you can deactivate this plugin.','Create a post, make sure the front page is accurate.'=>'Create a post, make sure the front page is accurate.','Visit the site while logged out.'=>'Visit the site while logged out.','Examples of test cases include:'=>'Examples of test cases include:','For that reason, please test the site to make sure everything still functions properly.'=>'For that reason, please test the site to make sure everything still functions properly.','This message indicates that the plugin was installed by the server admin.'=>'This message indicates that the plugin was installed by the server admin.','LiteSpeed Cache plugin is installed!'=>'LiteSpeed Cache plugin is installed!','Debug Log'=>'Debug Log','Admin IP Only'=>'Admin IP Only','Specify how long, in seconds, REST calls are cached.'=>'Specify how long, in seconds, REST calls are cached.','The environment report contains detailed information about the WordPress configuration.'=>'The environment report contains detailed information about the WordPress configuration.','The server will determine if the user is logged in based on the existence of this cookie.'=>'The server will determine if the user is logged in based on the existence of this cookie.','Note'=>'Note','After verifying that the cache works in general, please test the cart.'=>'After verifying that the cache works in general, please test the basket.','When enabled, the cache will automatically purge when any plugin, theme or the WordPress core is upgraded.'=>'When enabled, the cache will automatically purge when any plugin, theme or the WordPress core is upgraded.','Purge All On Upgrade'=>'Purge All On Upgrade','Product Update Interval'=>'Product Update Interval','Determines how changes in product quantity and product stock status affect product pages and their associated category pages.'=>'Determines how changes in product quantity and product stock status affect product pages and their associated category pages.','Always purge both product and categories on changes to the quantity or stock status.'=>'Always purge both product and categories on changes to the quantity or stock status.','Do not purge categories on changes to the quantity or stock status.'=>'Do not purge categories on changes to the quantity or stock status.','Purge product only when the stock status changes.'=>'Purge product only when the stock status changes.','Purge product and categories only when the stock status changes.'=>'Purge product and categories only when the stock status changes.','Purge categories only when stock status changes.'=>'Purge categories only when stock status changes.','Purge product on changes to the quantity or stock status.'=>'Purge product on changes to the quantity or stock status.','Htaccess did not match configuration option.'=>'.htaccess did not match configuration option.','If this is set to a number less than 30, feeds will not be cached.'=>'If this is set to a number less than 30, feeds will not be cached.','Specify how long, in seconds, feeds are cached.'=>'Specify how long, in seconds, feeds are cached.','Default Feed TTL'=>'Default Feed TTL','Failed to get %s file contents.'=>'Failed to get %s file contents.','Disabling this option may negatively affect performance.'=>'Disabling this option may negatively affect performance.','Invalid login cookie. Invalid characters found.'=>'Invalid login cookie. Invalid characters found.','WARNING: The .htaccess login cookie and Database login cookie do not match.'=>'WARNING: The .htaccess login cookie and Database login cookie do not match.','Invalid login cookie. Please check the %s file.'=>'Invalid login cookie. Please check the %s file.','The cache needs to distinguish who is logged into which WordPress site in order to cache correctly.'=>'The cache needs to distinguish who is logged into which WordPress site in order to cache correctly.','There is a WordPress installed for %s.'=>'There is a WordPress installed for %s.','Example use case:'=>'Example use case:','The cookie set here will be used for this WordPress installation.'=>'The cookie set here will be used for this WordPress installation.','If every web application uses the same cookie, the server may confuse whether a user is logged in or not.'=>'If every web application uses the same cookie, the server may confuse whether a user is logged in or not.','This setting is useful for those that have multiple web applications for the same domain.'=>'This setting is useful for those that have multiple web applications for the same domain.','The default login cookie is %s.'=>'The default login cookie is %s.','Login Cookie'=>'Login Cookie','More information about the available commands can be found here.'=>'More information about the available commands can be found here.','These settings are meant for ADVANCED USERS ONLY.'=>'These settings are meant for ADVANCED USERS ONLY.','Current %s Contents'=>'Current %s Contents','Advanced'=>'Advanced','Advanced Settings'=>'Advanced Settings','Purge List'=>'Purge List','Purge By...'=>'Purge By...','URL'=>'URL','Tag'=>'Tag','Post ID'=>'Post ID','Category'=>'Category','NOTICE: Database login cookie did not match your login cookie.'=>'NOTICE: Database login cookie did not match your login cookie.','Purge url %s'=>'Purge URL %s','Purge tag %s'=>'Purge tag %s','Purge category %s'=>'Purge category %s','When disabling the cache, all cached entries for this site will be purged.'=>'When disabling the cache, all cached entries for this site will be purged.','NOTICE'=>'NOTICE','This setting will edit the .htaccess file.'=>'This setting will edit the .htaccess file.','LiteSpeed Cache View .htaccess'=>'LiteSpeed Cache View .htaccess','Failed to back up %s file, aborted changes.'=>'Failed to back up %s file, aborted changes.','Do Not Cache Cookies'=>'Do Not Cache Cookies','Do Not Cache User Agents'=>'Do Not Cache User Agents','This is to ensure compatibility prior to enabling the cache for all sites.'=>'This is to ensure compatibility prior to enabling the cache for all sites.','Network Enable Cache'=>'Network Enable Cache','NOTICE:'=>'NOTICE:','Other checkboxes will be ignored.'=>'Other checkboxes will be ignored.','Select "All" if there are dynamic widgets linked to posts on pages other than the front or home pages.'=>'Select "All" if there are dynamic widgets linked to posts on pages other than the front or home pages.','List of Mobile User Agents'=>'List of Mobile User Agents','File %s is not writable.'=>'File %s is not writable.','JS Settings'=>'JS Settings','Manage'=>'Manage','Default Front Page TTL'=>'Default Front Page TTL','Notified LiteSpeed Web Server to purge the front page.'=>'Notified LiteSpeed Web Server to purge the front page.','Purge Front Page'=>'Purge Front Page','Example'=>'Example','All tags are cached by default.'=>'All tags are cached by default.','All categories are cached by default.'=>'All categories are cached by default.','To do an exact match, add %s to the end of the URL.'=>'To do an exact match, add %s to the end of the URL.','The URLs will be compared to the REQUEST_URI server variable.'=>'The URLs will be compared to the REQUEST_URI server variable.','Select only the archive types that are currently used, the others can be left unchecked.'=>'Select only the archive types that are currently used, the others can be left unchecked.','Notes'=>'Notes','Use Network Admin Setting'=>'Use Network Admin Setting','Disable'=>'Disable','Enabling LiteSpeed Cache for WordPress here enables the cache for the network.'=>'Enabling LiteSpeed Cache for WordPress here enables the cache for the network.','Disabled'=>'Disabled','Enabled'=>'Enabled','Do Not Cache Roles'=>'Do Not Cache Roles','https://www.litespeedtech.com'=>'https://www.litespeedtech.com','LiteSpeed Technologies'=>'LiteSpeed Technologies','LiteSpeed Cache'=>'LiteSpeed Cache','Debug Level'=>'Debug Level','Notice'=>'Notice','Term archive (include category, tag, and tax)'=>'Term archive (include category, tag, and tax)','Daily archive'=>'Daily archive','Monthly archive'=>'Monthly archive','Yearly archive'=>'Yearly archive','Post type archive'=>'Post type archive','Author archive'=>'Author archive','Home page'=>'Home page','Front page'=>'Front page','All pages'=>'All pages','Select which pages will be automatically purged when posts are published/updated.'=>'Select which pages will be automatically purged when posts are published/updated.','Auto Purge Rules For Publish/Update'=>'Auto Purge Rules For Publish/Update','Default Public Cache TTL'=>'Default Public Cache TTL','seconds'=>'seconds','Admin IPs'=>'Admin IPs','General'=>'General','LiteSpeed Cache Settings'=>'LiteSpeed Cache Settings','Notified LiteSpeed Web Server to purge all LSCache entries.'=>'Notified LiteSpeed Web Server to purge all LSCache entries.','Purge All'=>'Purge All','Settings'=>'Settings']];