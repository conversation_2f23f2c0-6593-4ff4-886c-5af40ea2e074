<?php
/**
 * Debug page for GoCardless Deposit Flow
 * Access: /wp-content/plugins/gocardless-deposit-flow/debug.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>GoCardless Deposit Flow - Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>GoCardless Deposit Flow - Debug Information</h1>
    
    <?php
    // Check plugin status
    $plugin_active = is_plugin_active('gocardless-deposit-flow/gocardless-deposit-flow.php');
    $plugin_exists = file_exists(WP_PLUGIN_DIR . '/gocardless-deposit-flow/gocardless-deposit-flow.php');
    $gcdf_available = function_exists('gcdf') && gcdf();
    
    echo '<h2>Plugin Status</h2>';
    echo '<div class="status ' . ($plugin_exists ? 'success' : 'error') . '">Plugin Files: ' . ($plugin_exists ? 'Found' : 'Not Found') . '</div>';
    echo '<div class="status ' . ($plugin_active ? 'success' : 'error') . '">Plugin Active: ' . ($plugin_active ? 'Yes' : 'No') . '</div>';
    echo '<div class="status ' . ($gcdf_available ? 'success' : 'error') . '">GCDF Function: ' . ($gcdf_available ? 'Available' : 'Not Available') . '</div>';
    
    if ($gcdf_available) {
        $settings = gcdf()->get_settings();
        $configured = gcdf()->is_configured();
        
        echo '<h2>Configuration</h2>';
        echo '<div class="status ' . ($configured ? 'success' : 'warning') . '">Plugin Configured: ' . ($configured ? 'Yes' : 'No') . '</div>';
        
        echo '<table>';
        echo '<tr><th>Setting</th><th>Value</th></tr>';
        echo '<tr><td>Environment</td><td>' . esc_html($settings['gocardless_environment'] ?? 'Not Set') . '</td></tr>';
        echo '<tr><td>Access Token</td><td>' . (empty($settings['gocardless_access_token']) ? 'Not Set' : 'Set (Hidden)') . '</td></tr>';
        echo '<tr><td>Deposit Amount</td><td>' . esc_html($settings['deposit_amount'] ?? 'Not Set') . '</td></tr>';
        echo '<tr><td>Currency</td><td>' . esc_html($settings['currency'] ?? 'Not Set') . '</td></tr>';
        echo '</table>';
        
        // Database check
        global $wpdb;
        $table_name = $wpdb->prefix . 'gocardless_deposits';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        echo '<h2>Database</h2>';
        echo '<div class="status ' . ($table_exists ? 'success' : 'error') . '">Deposits Table: ' . ($table_exists ? 'Exists' : 'Missing') . '</div>';
        
        if ($table_exists) {
            $deposit_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
            echo '<div class="status info">Total Deposits: ' . $deposit_count . '</div>';
            
            // Show recent deposits
            $recent_deposits = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT 5");
            if ($recent_deposits) {
                echo '<h3>Recent Deposits</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Post ID</th><th>User ID</th><th>Amount</th><th>Status</th><th>Created</th></tr>';
                foreach ($recent_deposits as $deposit) {
                    echo '<tr>';
                    echo '<td>' . $deposit->id . '</td>';
                    echo '<td>' . $deposit->post_id . '</td>';
                    echo '<td>' . $deposit->user_id . '</td>';
                    echo '<td>' . $deposit->currency . ' ' . $deposit->amount . '</td>';
                    echo '<td>' . $deposit->status . '</td>';
                    echo '<td>' . $deposit->created_at . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
        }
        
        // Test current post
        if (isset($_GET['test_post'])) {
            $post_id = intval($_GET['test_post']);
            $post = get_post($post_id);
            
            if ($post) {
                echo '<h2>Post Test: ' . esc_html($post->post_title) . '</h2>';
                
                $bin_price = get_post_meta($post_id, 'price_bin', true);
                $requires_deposit = gcdf()->auction_integration->requires_deposit($post_id);
                $deposit_amount = gcdf()->auction_integration->get_post_deposit_amount($post_id);
                
                echo '<table>';
                echo '<tr><th>Property</th><th>Value</th></tr>';
                echo '<tr><td>Post ID</td><td>' . $post_id . '</td></tr>';
                echo '<tr><td>Post Title</td><td>' . esc_html($post->post_title) . '</td></tr>';
                echo '<tr><td>Buy Now Price</td><td>' . esc_html($bin_price ?: 'Not Set') . '</td></tr>';
                echo '<tr><td>Requires Deposit</td><td>' . ($requires_deposit ? 'Yes' : 'No') . '</td></tr>';
                echo '<tr><td>Deposit Amount</td><td>' . $deposit_amount . '</td></tr>';
                echo '</table>';
                
                if (is_user_logged_in()) {
                    $user_id = get_current_user_id();
                    $has_completed = gcdf()->database->has_completed_deposit($post_id, $user_id);
                    $pending_deposit = gcdf()->database->get_pending_deposit($post_id, $user_id);
                    
                    echo '<h3>User Status</h3>';
                    echo '<table>';
                    echo '<tr><th>Property</th><th>Value</th></tr>';
                    echo '<tr><td>User ID</td><td>' . $user_id . '</td></tr>';
                    echo '<tr><td>Has Completed Deposit</td><td>' . ($has_completed ? 'Yes' : 'No') . '</td></tr>';
                    echo '<tr><td>Has Pending Deposit</td><td>' . ($pending_deposit ? 'Yes' : 'No') . '</td></tr>';
                    echo '</table>';
                }
            }
        }
        
        // Test GoCardless connection
        if (isset($_GET['test_connection']) && $configured) {
            echo '<h2>GoCardless Connection Test</h2>';
            try {
                $result = gcdf()->gocardless_api->test_connection();
                echo '<div class="status success">Connection successful!</div>';
            } catch (Exception $e) {
                echo '<div class="status error">Connection failed: ' . esc_html($e->getMessage()) . '</div>';
            }
        }
    }
    ?>
    
    <h2>Actions</h2>
    <button class="test-btn" onclick="location.href='?test_connection=1'">Test GoCardless Connection</button>
    <button class="test-btn" onclick="location.href='<?php echo admin_url('admin.php?page=gocardless-deposit-flow'); ?>'">Plugin Settings</button>
    <button class="test-btn" onclick="location.href='<?php echo home_url(); ?>'">Back to Site</button>
    
    <h3>Test Specific Post</h3>
    <form method="get">
        <input type="number" name="test_post" placeholder="Enter Post ID" value="<?php echo isset($_GET['test_post']) ? intval($_GET['test_post']) : ''; ?>">
        <button type="submit" class="test-btn">Test Post</button>
    </form>
    
    <h2>JavaScript Test</h2>
    <button class="test-btn" onclick="testModal()">Test Modal</button>
    <button class="test-btn" onclick="testGlobalFunction()">Test Global Function</button>
    
    <script>
    function testModal() {
        if (typeof GCDF !== 'undefined' && GCDF.showModal) {
            GCDF.showModal();
        } else {
            alert('GCDF object not found. Make sure you are on a page with the plugin loaded.');
        }
    }
    
    function testGlobalFunction() {
        if (typeof showDepositModal !== 'undefined') {
            showDepositModal();
        } else {
            alert('showDepositModal function not found. Make sure you are on a page with the plugin loaded.');
        }
    }
    </script>
    
    <h2>Instructions</h2>
    <ol>
        <li>Make sure the plugin is activated and configured</li>
        <li>Go to an auction post with a Buy Now price</li>
        <li>Enable deposit requirement for that post (in post edit screen)</li>
        <li>Try clicking the Buy Now button - it should show the deposit modal</li>
        <li>Use this debug page to test specific posts and check status</li>
    </ol>
    
</body>
</html>
