{"translation-revision-date": "2025-04-06 12:51:41+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Item selected.": ["Item selected."], "Hours": ["Hours"], "Custom color picker.": ["Custom colour picker."], "Color code: %s": ["Colour code: %s"], "Color: %s": ["Colour: %s"], "Minutes": ["Minutes"], "Add item": ["Add item"], "Item added.": ["<PERSON><PERSON> added."], "Item removed.": ["Item removed."], "Remove item": ["Remove item"], "%1$s (%2$s of %3$s)": ["%1$s (%2$s of %3$s)"], "Dismiss this notice": ["Dismiss this notice"], "Order by": ["Order by"], "Newest to oldest": ["Newest to oldest"], "Oldest to newest": ["Oldest to newest"], "A → Z": ["A \t Z"], "Z → A": ["Z \t A"], "Category": ["Category"], "Number of items": ["Number of items"], "Separate with commas, spaces, or the Enter key.": ["Separate with commas, spaces, or the Enter key."], "Separate with commas or the Enter key.": ["Separate with commas or the Enter key."], "Angle": ["<PERSON><PERSON>"], "Extra Large": ["Extra Large"], "Gradient control point at position %1$s%% with color code %2$s.": ["Gradient control point at position %1$s%% with colour code %2$s."], "Use your left or right arrow keys or drag and drop with the mouse to change the gradient position. Press the button to change the color or remove the control point.": ["Use your left or right arrow keys or drag and drop with the mouse to change the gradient position. Press the button to change the colour or remove the control point."], "Remove Control Point": ["Remove Control Point"], "Gradient code: %s": ["Gradient code: %s"], "Gradient: %s": ["Gradient: %s"], "Guide controls": ["Guide controls"], "Page %1$d of %2$d": ["Page %1$d of %2$d"], "Finish": ["Finish"], "Top Center": ["Top Centre"], "Center Left": ["Centre Left"], "Center Right": ["Centre Right"], "Bottom Center": ["Bottom Centre"], "Alignment Matrix Control": ["Alignment Matrix Control"], "Box Control": ["Box Control"], "Reset search": ["Reset search"], "Color name": ["Colour name"], "Remove color": ["Remove colour"], "Coordinated Universal Time": ["Coordinated Universal Time"], "Media preview": ["Media preview"], "Linear": ["Linear"], "Radial": ["Radial"], "Select unit": ["Select unit"], "Search in %s": ["Search in %s"], "Close search": ["Close search"], "Percentage (%)": ["Percentage (%)"], "Pixels (px)": ["Pixels (px)"], "Relative to parent font size (em)": ["Relative to parent font size (em)"], "Relative to root font size (rem)": ["Relative to root font size (rem)"], "Viewport width (vw)": ["Viewport width (vw)"], "Viewport height (vh)": ["Viewport height (vh)"], "Dashed": ["Dashed"], "Dotted": ["Dotted"], "Border width": ["Border width"], "Percent (%)": ["Percent (%)"], "Viewport smallest dimension (vmin)": ["Viewport smallest dimension (vmin)"], "Viewport largest dimension (vmax)": ["Viewport largest dimension (vmax)"], "Width of the zero (0) character (ch)": ["Width of the zero (0) character (ch)"], "x-height of the font (ex)": ["x-height of the font (ex)"], "Centimeters (cm)": ["Centimetres (cm)"], "Millimeters (mm)": ["Millimetres (mm)"], "Inches (in)": ["Inches (in)"], "Picas (pc)": ["Picas (pc)"], "Points (pt)": ["Points (pt)"], "Relative to parent font size (em)\u0004ems": ["ems"], "Relative to root font size (rem)\u0004rems": ["rems"], "%1$s. There is %2$d event": ["%1$s. There is %2$d event", "%1$s. There are %2$d events"], "Duotone code: %s": ["Duotone code: %s"], "Duotone: %s": ["Duotone: %s"], "Shadows": ["Shadows"], "Invalid item": ["Invalid item"], "Hex color": ["Hex colour"], "Color format": ["Colour format"], "Color %s": ["Colour %s"], "Gradient name": ["Gradient name"], "Add gradient": ["Add gradient"], "Add color": ["Add colour"], "Gradient options": ["Gradient options"], "Color options": ["Colour options"], "Remove all gradients": ["Remove all gradients"], "Remove all colors": ["Remove all colours"], "Reset gradient": ["Reset gradient"], "Reset colors": ["Reset colours"], "No selection": ["No selection"], "Currently selected: %s": ["Currently selected: %s"], "Size of a UI element\u0004None": ["None"], "Size of a UI element\u0004Small": ["Small"], "Size of a UI element\u0004Medium": ["Medium"], "Size of a UI element\u0004Large": ["Large"], "Size of a UI element\u0004Extra Large": ["Extra Large"], "Highlights": ["Highlights"], "Currently selected font size: %s": ["Currently selected font size: %s"], "Search %s": ["Search %s"], "Reset %s": ["Reset %s"], "Hide and reset %s": ["Hide and reset %s"], "Show %s": ["Show %s"], "Button label to reveal tool panel options\u0004%s options": ["%s options"], "Reset all": ["Reset all"], "Unlink sides": ["Unlink sides"], "Link sides": ["Link sides"], "Custom color picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\".": ["Custom colour picker. The currently selected colour is called \"%1$s\" and has a value of \"%2$s\"."], "Border color and style picker.": ["Border colour and style picker."], "Border color picker.": ["Border colour picker."], "Close border color": ["Close border colour"], "Top border": ["Top border"], "Left border": ["Left border"], "Right border": ["Right border"], "Bottom border": ["Bottom border"], "All sides": ["All sides"], "Loading …": ["Loading …"], "Border color and style picker": ["Border colour and style picker"], "View previous month": ["View previous month"], "View next month": ["View next month"], "%1$s. Selected. There is %2$d event": ["%1$s. Selected. There is %2$d event", "%1$s. Selected. There are %2$d events"], "%1$s. Selected": ["%1$s. selected"], "S": ["S"], "M": ["M"], "L": ["L"], "XL": ["XL"], "XXL": ["XXL"], "%s reset to default": ["%s reset to default"], "%s hidden and reset to default": ["%s hidden and reset to default"], "%s is now visible": ["%s is now visible"], "All options are currently hidden": ["All options are currently hidden"], "All options reset": ["All options reset"], "Increment": ["Increment"], "Decrement": ["Decrement"], "Show details": ["Show details"], "Extra Extra Large": ["Extra Extra Large"], "Initial %d result loaded. Type to filter all available results. Use up and down arrow keys to navigate.": ["Initial %d result loaded. Type to filter all available results. Use up and down arrow keys to navigate.", "Initial %d results loaded. Type to filter all available results. Use up and down arrow keys to navigate."], "Scrollable section": ["Scrollable section"], "Focal point left position": ["Focal point left position"], "Focal point top position": ["Focal point top position"], "Warning notice": ["Warning notice"], "Information notice": ["Information notice"], "Error notice": ["Error notice"], "Notice": ["Notice"], "No color selected": ["No colour selected"], "Small viewport width (svw)": ["Small viewport width (svw)"], "Small viewport height (svh)": ["Small viewport height (svh)"], "Viewport smallest size in the inline direction (svi)": ["Viewport smallest size in the inline direction (svi)"], "Small viewport width or height (svi)": ["Small viewport width or height (svi)"], "Viewport smallest size in the block direction (svb)": ["Viewport smallest size in the block direction (svb)"], "Small viewport width or height (svb)": ["Small viewport width or height (svb)"], "Small viewport smallest dimension (svmin)": ["Small viewport smallest dimension (svmin)"], "Large viewport width (lvw)": ["Large viewport width (lvw)"], "Large viewport height (lvh)": ["Large viewport height (lvh)"], "Large viewport width or height (lvi)": ["Large viewport width or height (lvi)"], "Large viewport width or height (lvb)": ["Large viewport width or height (lvb)"], "Large viewport smallest dimension (lvmin)": ["Large viewport smallest dimension (lvmin)"], "Dynamic viewport width (dvw)": ["Dynamic viewport width (dvw)"], "Dynamic viewport height (dvh)": ["Dynamic viewport height (dvh)"], "Dynamic viewport width or height (dvi)": ["Dynamic viewport width or height (dvi)"], "Dynamic viewport width or height (dvb)": ["Dynamic viewport width or height (dvb)"], "Dynamic viewport smallest dimension (dvmin)": ["Dynamic viewport smallest dimension (dvmin)"], "Dynamic viewport largest dimension (dvmax)": ["Dynamic viewport largest dimension (dvmax)"], "Small viewport largest dimension (svmax)": ["Small viewport largest dimension (svmax)"], "Large viewport largest dimension (lvmax)": ["Large viewport largest dimension (lvmax)"], "Top side": ["Top side"], "Bottom side": ["Bottom side"], "Left side": ["Left side"], "Right side": ["Right side"], "Top and bottom sides": ["Top and bottom sides"], "Left and right sides": ["Left and right sides"], "Edit: %s": ["Edit: %s"], "categories\u0004All": ["All"], "authors\u0004All": ["All"], "Calendar": ["Calendar"], "Font size": ["Font size"], "Type": ["Type"], "Custom": ["Custom"], "Mixed": ["Mixed"], "Previous": ["Previous"], "%d result found, use up and down arrow keys to navigate.": ["%d result found, use up and down arrow keys to navigate.", "%d results found, use up and down arrow keys to navigate."], "No results.": ["No results."], "Reset": ["Reset"], "%d result found.": ["%d result found.", "%d results found."], "Solid": ["Solid"], "Use size preset": ["Use size preset"], "Set custom size": ["Set custom size"], "Unset": ["Unset"], "Author": ["Author"], "Categories": ["Categories"], "Done": ["Done"], "Cancel": ["Cancel"], "Left": ["Left"], "Center": ["Centre"], "Medium": ["Medium"], "Size": ["Size"], "OK": ["OK"], "Search": ["Search"], "Close": ["Close"], "Default": ["<PERSON><PERSON><PERSON>"], "No results found.": ["No results found."], "January": ["January"], "February": ["February"], "March": ["March"], "April": ["April"], "May": ["May"], "June": ["June"], "July": ["July"], "August": ["August"], "September": ["September"], "October": ["October"], "November": ["November"], "December": ["December"], "Copy": ["Copy"], "Top": ["Top"], "Top Left": ["Top Left"], "Top Right": ["Top Right"], "Bottom Left": ["Bottom Left"], "Bottom Right": ["Bottom Right"], "AM": ["AM"], "PM": ["PM"], "Next": ["Next"], "Clear": ["Clear"], "Drop files to upload": ["Drop files to upload"], "Large": ["Large"], "Style": ["Style"], "Back": ["Back"], "Custom Size": ["Custom Size"], "Year": ["Year"], "Border color": ["Border colour"], "Date": ["Date"], "Month": ["Month"], "Day": ["Day"], "Time": ["Time"], "(opens in a new tab)": ["(opens in a new tab)"], "Copied!": ["Copied!"], "Small": ["Small"]}}, "comment": {"reference": "wp-includes/js/dist/components.js"}}