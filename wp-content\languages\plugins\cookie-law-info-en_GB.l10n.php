<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2024-05-23 14:02:43+0000','plural-forms'=>'nplurals=2; plural=n != 1;','project-id-version'=>'Plugins - CookieYes &#8211; Cookie Banner for Cookie Consent (Easy to setup GDPR/CCPA Compliant Cookie Notice) - Stable (latest release)','language'=>'en_GB','messages'=>['Customize the Reject button to match the theme of your site. Insert the shortcode <strong>[cookie_reject]</strong> in <strong>Customise Cookie Bar > Cookie bar > Message</strong> to include reject button in cookie consent bar.'=>'Customise the Reject button to match the theme of your site. Insert the shortcode <strong>[cookie_reject]</strong> in <strong>Customise Cookie Bar > Cookie bar > Message</strong> to include reject button in cookie consent bar.','Customize the Accept button to match the theme of your site. Insert the shortcode [cookie_button] in Customise Cookie Bar > Cookie bar > Message to include accept button in cookie consent bar.'=>'Customise the Accept button to match the theme of your site. Insert the shortcode [cookie_button] in Customise Cookie Bar > Cookie bar > Message to include accept button in cookie consent bar.','Customize the cookie settings to match the theme of your site. Insert the shortcode <strong>[cookie_settings]</strong> in <strong>Customise Cookie Bar > Cookie bar > Message</strong> to include cookie settings within the cookie consent bar. Clicking ‘Cookie settings’ opens up a pop up window with provisions to enable/disable cookie categories.'=>'Customise the cookie settings to match the theme of your site. Insert the shortcode <strong>[cookie_settings]</strong> in <strong>Customise Cookie Bar > Cookie bar > Message</strong> to include cookie settings within the cookie consent bar. Clicking ‘Cookie settings’ opens up a pop up window with provisions to enable/disable cookie categories.','Total cookies'=>'Total cookies','Clicking “Add to cookie list” will import the discovered cookies to the <a href="%s" target="_blank">Cookie List</a> and thus display them in the cookie declaration section of your consent banner.'=>'Clicking “Add to cookie list” will import the discovered cookies to the <a href="%s" target="_blank">Cookie List</a> and then display them in the cookie declaration section of your consent banner.','Cookie scan result for your website'=>'Cookie scan result for your website','Cookie scanner'=>'Cookie scanner','No cookies found'=>'No cookies found','cookies deleted.'=>'cookies deleted.','cookies skipped.'=>'cookies skipped.','cookies added.'=>'cookies added.','You do not have sufficient permissions to access this page.'=>'You do not have sufficient permissions to access this page.','Unable to handle your request'=>'Unable to handle your request.','Abort failed'=>'Abort failed','Abort successful'=>'Abort successful','Scanning initiated successfully'=>'Scanning initiated successfully','Scanner API is temporarily down please try again later.'=>'Scanner API is temporarily down. Please try again later.','Once the scanning is complete, we will notify you by email.'=>'Once the scanning is complete, we will notify you by email.','Your website is currently being scanned for cookies. This might take from a few minutes to a few hours, depending on your website speed and the number of pages to be scanned.'=>'Your website is currently being scanned for cookies. This might take from a few minutes to a few hours, depending on your website speed and the number of pages to be scanned.','Total URLs'=>'Total URLs','Scan started at'=>'Scan started at','Abort scan'=>'Abort scan','Scan initiated...'=>'Scan initiated...','Scan aborted'=>'Scan aborted','Last scan:'=>'Last scan:','Scan failed'=>'Scan failed','Scan complete'=>'Scan complete','Last scan: %1$s'=>'Last scan: %1$s','You haven\'t performed a site scan yet.'=>'You haven\'t performed a site scan yet.','Importing....'=>'Importing...','Start import'=>'Start import','Not recommended'=>'Not recommended','Append'=>'Append','Recommended'=>'Recommended','Merge'=>'Merge','Replace old'=>'Replace old','Import options'=>'Import options','View scan result'=>'View scan result','Add to cookie list'=>'Add to the cookie list','Download cookies as CSV'=>'Download cookies as CSV','Scan again'=>'Scan again','Stop'=>'Stop','Scanning pages...'=>'Scanning pages...','Finding pages...'=>'Finding pages...','Body scripts'=>'Body scripts','Head scripts'=>'Head scripts','Category default state'=>'Category default state','Edit cookie category'=>'Edit cookie category','Add cookie category'=>'Add cookie category','Cookie Category'=>'Cookie Category','ID'=>'ID','Sensitivity'=>'Sensitivity','Cookie Name'=>'Cookie Name','Cookie Duration:'=>'Cookie Duration:','Term meta cannot be added to terms that are shared between taxonomies.'=>'Term meta cannot be added to terms that are shared between taxonomies.','Could not identify the action'=>'Could not identify the action','Invalid token format'=>'Invalid token format','Invalid json token'=>'Invalid json token','Email has already verified'=>'The email has been already verified','A email verification link has been sent to your email address. Click the link in the email to verify your account'=>'An email verification link has been sent to your email address. Click the link in the email to verify your account','A password reset message has been sent to your email address. Click the link in the email to reset your password'=>'A password reset message has been sent to your email address. Click the link in the email to reset your password','Successfully connected with CookieYes'=>'Successfully connected with CookieYes','Disconnected with cookieyes, please connect and scan again'=>'Disconnected with CookieYes, please connect and scan again','License is not activated, please activate your license and try again'=>'Licence is not activated, please activate your licence and try again','You already have an account with CookieYes.'=>'You already have an account with CookieYes.','Invalid credentials'=>'Invalid credentials','Category'=>'Category','Invalid script id'=>'Invalid script ID','Token mismatch'=>'Token mismatch','Successfully inserted'=>'Successfully inserted','Failed to insert'=>'Failed to insert','Why scan your website for cookies?'=>'Why should you scan your website for cookies?','Our cookie scanning solution lets you:'=>'Our cookie scanning solution lets you:','Discover the first-party and third-party cookies that are being used on your website ( Limited upto 100 pages ).'=>'Discover the first-party and third-party cookies being used on your website (Limited to up to 100 pages).','Identify what personal data they collect and what are the other purposes they serve.'=>'Identify what personal data they collect and what other purposes they serve.','Scan website for cookies'=>'Scan website for cookies','Invalid scan token'=>'Invalid scan token','Are you sure?'=>'Are you sure?','Scanning stopped.'=>'Scanning has stopped.','Stopping...'=>'Stopping...','Error !!! Please reload the page to see cookie list.'=>'Error!!! Please reload the page to see the cookie list.','Refreshing....'=>'Refreshing...','Added to cookie list.'=>'Added to the cookie list.','Scanning completed.'=>'Scanning completed.','Scanned'=>'Scanned','Cookie Scanner'=>'Cookie Scanner','Failed'=>'Failed','Stopped'=>'Stopped','Completed'=>'Completed','Incomplete'=>'Incomplete','Migrate cookie categories'=>'Migrate cookie categories','We\'ve sent an account verification link to the email address %s. Please click on the link given in email to verify your account with CookieYes.'=>'We\'ve sent an account verification link to the email address %s. Please click on the link in the email to verify your account with CookieYes.','Delete this website'=>'Delete this website','This action will clear all your website data from CookieYes. If you have multiple websites added to your CookieYes account, then only the data associated with this website get deleted. Otherwise, your entire account will be deleted.'=>'This action will clear all your website data from CookieYes. If you have multiple websites added to your CookieYes account, then only the data associated with this website will be deleted. Otherwise, your entire account will be deleted.','Do you really want to delete your website from CookieYes'=>'Do you really want to delete your website from CookieYes?','Connect'=>'Connect','Enter your email to create an account with CookieYes. By clicking “Connect”, your CookieYes account will be created automatically and you can start scanning your website for cookies right away!'=>'Enter your email to create an account with CookieYes. By clicking “Connect”, your CookieYes account will be created automatically and you can start scanning your website for cookies right away!','Welcome to CookieYes'=>'Welcome to CookieYes','Send password reset email'=>'Send password reset email','Email'=>'Email','Your cookie list is empty'=>'Your cookie list is empty','Unable to load cookie scanner. Scanning will not work on local servers'=>'Unable to load cookie scanner. Scanning will not work on local servers','Total estimated time (Approx)'=>'Total estimated time','Scan your website with CookieYes, our scanning solution for high-speed, accurate cookie scanning'=>'Scan your website with CookieYes, our scanning solution for high-speed and accurate cookie scanning','Could not abort the scan, please try again'=>'Could not abort the scan. Please try again','Aborting the scan...'=>'Aborting the scan...','Total Cookies found'=>'Total cookies found','Total URLs scanned'=>'Total URLs scanned','Sending...'=>'Sending...','Checking API'=>'Checking API','Thank you'=>'Thank you','Unknown'=>'Unknown','Could not fetch the URLs, please try again'=>'Could not fetch the URLs. Please try again','Verification link sent'=>'Verification link sent','If you didn\'t receive the email, click <a id=\'wt-cli-ckyes-email-resend-link\' role=\'button\'>here</a> to resend the verification email.'=>'If you didn\'t receive the verification email, click <a id=\'wt-cli-ckyes-email-resend-link\' role=\'button\'>here</a> to resend.','Reset Password'=>'Reset Password','Delete failed, please try again later'=>'Delete failed. Please try again later','Successfully deleted!'=>'Successfully deleted!','Disconnect'=>'Disconnect','Invalid request'=>'Invalid request','Connected to CookieYes'=>'Connected to CookieYes','Disconnected from CookieYes'=>'Disconnected from CookieYes','Powered by'=>'Powered by','What happens after migration?'=>' What happens after migration?','Cookie Compliance Made Easy'=>'Cookie Compliance Made Easy','Login'=>'Log in','Reset password'=>'Reset password','If you did not get the email, click “Reset password” to create a new password.'=>'If you did not receive the email, click “Reset password” to create a new password.','Please check if you have received an email with your password from CookieYes.'=>'Please check if you have received an email with your password from CookieYes.','Password'=>'Password','Pending email verification!'=>'Pending email verification!','From Right Margin'=>'From Right Margin','Enable revisit consent widget'=>'Enable revisit consent widget','Revisit consent'=>'Revisit consent','Privacy Policy'=>'Privacy Policy','We do not collect any personal data when you submit this form. It\'s your feedback that we value.'=>'We do not collect any personal data when you submit this form. It\'s your feedback that we value.','Upgrade to pro'=>'Upgrade to Pro','Name the language and the translator plugin that you are using'=>'Name the language and the translator plugin you are using','Translation issues'=>'Translation issues','Disable'=>'Disable','Enable'=>'Enable','Advanced script rendering'=>'Advanced script rendering','Status updated'=>'Status updated','Script Blocker'=>'Script Blocker','Review now'=>'Review now','Not interested'=>'Not interested','Remind me later'=>'Remind me later','Title'=>'Title','Cookie bar is currently inactive'=>'The cookie bar is currently inactive','Cookie bar is currently active'=>'The cookie bar is currently active','Inactive'=>'Inactive','Name'=>'Name','Manage Script Blocking'=>'Manage Script Blocking','Advanced script rendering is currently disabled. It should be enabled for the automatic script blocker to function. <a href="%s">Enable.</a>'=>'Advanced script rendering is currently disabled. It should be enabled for the automatic script blocker to function. <a href="%s">Enable.</a>','Script blocker is currently disabled. Enable the blocker if you want any of the below listed plugins to be auto blocked.'=>'Script blocker is currently disabled. Enable the blocker if you want any of the below-listed plugins to be auto-blocked.','Script blocker is enabled.'=>'Script blocker is enabled.','Advanced script rendering will render the blocked scripts using javascript thus eliminating the need for a page refresh. It is also optimized for caching since there is no server-side processing after obtaining the consent.'=>'Advanced script rendering will render the blocked scripts using JavaScript thus eliminating the need for a page refresh. It is also optimised for caching since there is no server-side processing after obtaining the consent.','Please make sure the cache is cleared after each plugin update especially if you have minified JS and/or CSS files.'=>'Please make sure the cache is cleared after each plugin update, especially if you have minified JS and/or CSS files.','Go to support'=>'Go to support','I rather wouldn\'t say'=>'I\'d rather not say','If you have a moment, please let us know why you are deactivating:'=>'If you have a moment, please let us know why you are deactivating:','Could you tell us more about that feature?'=>'Could you tell us more about that feature?','The plugin is great, but I need specific feature that you don\'t support'=>'The plugin is great, but I need a specific feature that you don\'t support','Which plugin?'=>'Which plugin?','I found a better plugin'=>'I found a better plugin','A conflict with another plugin or theme'=>'A conflict with another plugin or theme','Cookie bar'=>'Cookie bar','The shortcode will be represented as a checkbox with select option to record consent.'=>'The shortcode will be represented as a checkbox with a select option to record consent.','The shortcode will be represented as a link wherever used.'=>'The shortcode will be represented as a link wherever used.','Checkbox'=>'Checkbox','CCPA Text'=>'CCPA Text','Enable CCPA ?'=>'Enable CCPA?','CCPA Settings'=>'CCPA Settings','CCPA'=>'CCPA','Enable cookie bar'=>'Enable cookie bar','Position:'=>'Position:','Show cookie bar as'=>'Show cookie bar as','Confirm'=>'Confirm','Cancel'=>'Cancel','Select the type of law'=>'Select the law','GDPR'=>'GDPR','Close and Accept'=>'Accept and Close','Close the cookie bar'=>'Close the cookie bar','Close'=>'Close','If you enable this option, the category toggle button will be in the active state for cookie consent.'=>'If you enable this option, the category toggle button will be in the active state for cookie consent.','Default state'=>'Default state','Non-necessary'=>'Non-necessary','Necessary'=>'Necessary','Verdana'=>'Verdana','Trebuchet'=>'Trebuchet','Times New Roman'=>'Times New Roman','Tahoma'=>'Tahoma','Lucida'=>'Lucida','Helvetica'=>'Helvetica','Georgia, serif'=>'Georgia, serif','Arial Black'=>'Arial Black','Arial'=>'Arial','Serif'=>'Serif','Sans Serif'=>'Sans Serif','Default theme font'=>'Default theme font','Small'=>'Small','Medium'=>'Medium','Large'=>'Large','Extra Large'=>'Extra Large','Cookie Sensitivity'=>'Cookie Sensitivity','Cookie Type'=>'Cookie Type','Show less'=>'Show less','Show more'=>'Show more','Disabled'=>'Disabled','Enabled'=>'Enabled','Always Enabled'=>'Always Enabled','This is the cookie settings button rendering shortcode.'=>'This is the cookie settings button rendering shortcode.','Settings Button'=>'Settings Button','Necessary Cookie Settings'=>'Necessary Cookie Settings','Save Settings'=>'Save Settings','Privacy Overview'=>'Privacy Overview','Manage your consent.'=>'Manage your consent.','No consent given.'=>'No consent given.','Consent rejected.'=>'Consent rejected.','Your current state:'=>'Your current state:','Will print all columns by default.'=>'Will print all columns by default.','Columns available'=>'Columns available','Styles included'=>'Styles included',' to generate content for Cookie Policy page.'=>' to generate content for Cookie Policy page.','here'=>'here','Click'=>'Click','Live preview'=>'Live preview','Create Cookie Policy page'=>'Create Cookie Policy page','Update existing Cookie Policy page'=>'Update existing Cookie Policy page','Heading'=>'Heading','Add new'=>'Add new','Delete'=>'Delete','Sample content'=>'Sample content','Sample heading'=>'Sample heading','Success'=>'Success','Policy generator'=>'Policy generator','Auto reload preview'=>'Auto reload preview','Cookie Policy'=>'Cookie Policy','Error'=>'Error','Unable to handle your request.'=>'Unable to handle your request.','Setup margin for above buttons'=>'Set up margin for above buttons','Enabling this option will help us spread the word by placing a credit to CookieYes at the very end of the Cookie Policy page.'=>'Enabling this option will help us spread the word by placing a credit to CookieYes at the very end of the Cookie Policy page.','Top Left'=>'Top Left','Top Right'=>'Top Right','Bottom Left'=>'Bottom Left','Bottom Right'=>'Bottom Right','Position'=>'Position','Widget'=>'Widget','Popup'=>'Popup','Banner'=>'Banner','Add content after accepting the cookie notice.'=>'Add content after accepting the cookie notice.','The currently selected page does not exist. Please select a new page.'=>'The currently selected page does not exist. Please select a new page.','Select One'=>'Select One','Page'=>'Page','URL or Page?'=>'URL or Page?','Delete Cookies'=>'Delete Cookies','Add New'=>'Add New','Update Settings'=>'Update Settings','Message Heading'=>'Message Heading','Contact Us'=>'Contact Us','We would love to help you on any queries or issues.'=>'We would love to help you on any queries or issues.','Refer to our documentation to set and get started'=>'Refer to our documentation to set and get started','Documentation'=>'Documentation','This is the "read more" link you customise above.'=>'This is the "read more" link you customise above.','This is the cookie reject button shortcode.'=>'This is the cookie reject button shortcode.','This is the "main button" you customise above.'=>'This is the "main button" you customise above.','Help Links'=>'Help Links','Shortcodes'=>'Shortcodes','Reload after Reject button click'=>'Reload after Reject button click','This option will not work along with `Popup overlay`.'=>'This option will not work along with `Popup overlay`.','As per latest GDPR policies it is required to take an explicit consent for the cookies. Use this option with discretion especially if you serve EU'=>'As per latest GDPR policies it is required to take an explicit consent for the cookies. Use this option with discretion especially if you serve EU','seconds'=>'seconds','`Accept on scroll` will not work along with this option.'=>'`Accept on scroll` will not work along with this option.','When the popup is active, an overlay will block the user from browsing the site.'=>'When the popup is active, an overlay will block the user from browsing the site.','Add overlay?'=>'Add overlay?','Off'=>'Off','On'=>'On','Other'=>'Other','Size'=>'Size','No'=>'No','Yes'=>'Yes','Button will only link to URL if Action = Open URL'=>'Button will only link to URL if Action = Open URL','URL'=>'URL','Background colour'=>'Background colour','Link'=>'Link','Button'=>'Button','Show as'=>'Show as','Text colour'=>'Text colour','Text'=>'Text','Delete settings and reset'=>'Delete settings and reset','Help Guide'=>'Help Guide','Customise Cookie Bar'=>'Customise Cookie Bar','General'=>'General','Unable to reset settings.'=>'Unable to reset settings.','Settings reset to defaults.'=>'Settings reset to defaults.','Unable to update Settings.'=>'Unable to update Settings.','Settings updated.'=>'Settings updated.','Support'=>'Support','Auto-hide(Accept) cookie bar after delay?'=>'Auto-hide (Accept) cookie bar after delay?','Auto-hide cookie bar if the user scrolls ( Accept on Scroll )?'=>'Auto-hide cookie bar if the user scrolls (Accept on Scroll)?','Reload after Accept button click'=>'Reload after Accept button click','Add any text you like- useful if you want e.g. another language to English.'=>'Add any text you like- useful if you want e.g. another language to English.','Add New Cookie Type'=>'Add New Cookie Type','Advanced'=>'Advanced','Cookie'=>'Cookie','Cookie List'=>'Cookie List','Cookie Sensitivity: ( necessary , non-necessary )'=>'Cookie Sensitivity: (necessary , non-necessary)','Cookie Type: (persistent, session, third party )'=>'Cookie Type: (persistent, session, third party)','Description'=>'Description','Duration'=>'Duration','Edit Cookie Type'=>'Edit Cookie Type','Enable Non-necessary Cookie'=>'Enable Non-necessary Cookie','GDPR Cookie Consent'=>'GDPR Cookie Consent','Help and Support'=>'Help and Support','New Cookie Type'=>'New Cookie Type','Non-necessary Cookie Settings'=>'Non-necessary Cookie Settings','Nothing found'=>'Nothing found','Other shortcodes'=>'Other shortcodes','Print scripts before the closing body tag on the front end if above cookie settings is enabled and user has given consent.'=>'Print scripts before the closing body tag on the front end if above cookie settings is enabled and user has given consent.','Print scripts in the head tag on the front end if above cookie settings is enabled and user has given consent.'=>'Print scripts in the head tag on the front end if above cookie settings is enabled and user has given consent.','Search Cookies'=>'Search Cookies','Sometimes themes apply settings that clash with plugins. If that happens, try adjusting these settings.'=>'Sometimes themes apply settings that clash with plugins. If that happens, try adjusting these settings.','These shortcodes can be used in pages and posts on your website. It is not recommended to use these inside the cookie bar itself.'=>'These shortcodes can be used in pages and posts on your website. It is not recommended to use these inside the cookie bar itself.','This prints out a nice table of cookies, in line with the guidance given by the ICO.'=>'This prints out a nice table of cookies, in line with the guidance given by the ICO.','This script will be added right after the BODY section if the above settings is enabled and user has given consent.'=>'This script will be added right after the BODY section if the above settings is enabled and user has given consent.','This script will be added to the page HEAD section if the above settings is enabled and user has give consent.'=>'This script will be added to the page HEAD section if the above settings is enabled and user has give consent.','This shortcode will display a normal HTML link which when clicked, will delete the cookie set by Cookie Law Info (this cookie is used to remember that the cookie bar is closed).'=>'This shortcode will display a normal HTML link which when clicked, will delete the cookie set by Cookie Law Info (this cookie is used to remember that the cookie bar is closed).','Type'=>'Type','View Cookie Type'=>'View Cookie Type','Nothing found in Trash'=>'Nothing found in Bin','Accept Button'=>'Accept Button','Action'=>'Action','Animate'=>'Animate','Button Size'=>'Button Size','Cookie Bar Colour'=>'Cookie Bar Colour','Cookie bar shortcodes'=>'Cookie bar shortcodes','Customise Buttons'=>'Customise Buttons','Font'=>'Font','Footer'=>'Footer','From Left Margin'=>'From Left Margin','Header'=>'Header','Left'=>'Left','Message'=>'Message','Milliseconds until hidden'=>'Milliseconds until hidden','On hide'=>'On hide','On load'=>'On load','Reject Button'=>'Reject Button','Reload after "scroll accept" event?'=>'Reload after "scroll accept" event?','Right'=>'Right','Settings'=>'Settings','Settings Updated.'=>'Settings Updated.','Sticky'=>'Sticky','Tab Position'=>'Tab Position','Text Colour'=>'Text Colour','This button/link can be customised to either simply close the cookie bar, or follow a link. You can also customise the colours and styles, and show it as a link or a button.'=>'This button/link can be customised to either simply close the cookie bar, or follow a link. You can also customise the colours and styles, and show it as a link or a button.','You do not have sufficient permission to perform this operation'=>'You do not have sufficient permission to perform this operation','A simple way to show your website complies with the EU Cookie Law / GDPR.'=>'A simple way to show your website complies with the EU Cookie Law / GDPR.','Open URL in new window?'=>'Open URL in new window?','Specify milliseconds (not seconds)'=>'Specify milliseconds (not seconds)','Cookie ID'=>'Cookie ID','Cookie Duration'=>'Cookie Duration']];