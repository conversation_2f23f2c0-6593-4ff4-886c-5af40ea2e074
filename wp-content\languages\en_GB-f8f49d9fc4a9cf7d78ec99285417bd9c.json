{"translation-revision-date": "2025-04-06 12:51:41+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Version": ["Version"], "About": ["About"], "Outdent": ["Outdent"], "Indent": ["Indent"], "List": ["List"], "Add a featured image": ["Add a featured image"], "Descriptions": ["Descriptions"], "Link to %s": ["Link to %s"], "Navigation": ["Navigation"], "Featured image": ["Featured image"], "Include": ["Include"], "Empty": ["Empty"], "Table": ["Table"], "The description will be displayed in the menu if the current theme supports it.": ["The description will be displayed in the menu if the current theme supports it."], "Taxonomy": ["Taxonomy"], "Order by": ["Order by"], "Newest to oldest": ["Newest to oldest"], "Oldest to newest": ["Oldest to newest"], "A → Z": ["A \t Z"], "Z → A": ["Z \t A"], "Number of items": ["Number of items"], "Add block": ["Add block"], "button label\u0004Import": ["Import"], "Block has been deleted or is unavailable.": ["Block has been deleted or is unavailable."], "Thumbnails are cropped to align.": ["Thumbnails are cropped to align."], "Thumbnails are not cropped.": ["Thumbnails are not cropped."], "Crop images": ["Crop images"], "Write HTML…": ["Write HTML…"], "Display avatar": ["Display avatar"], "Number of comments": ["Number of comments"], "Display post date": ["Display post date"], "Latest Posts": ["Latest Posts"], "Convert to unordered list": ["Convert to unordered list"], "Convert to ordered list": ["Convert to ordered list"], "Outdent list item": ["Outdent list item"], "Indent list item": ["Indent list item"], "Show media on left": ["Show media on left"], "Show media on right": ["Show media on right"], "Media area": ["Media area"], "Your site doesn’t include support for the \"%s\" block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely.": ["Your site doesn’t include support for the \"%s\" block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely."], "Your site doesn’t include support for the \"%s\" block. You can leave this block intact or remove it entirely.": ["Your site doesn’t include support for the \"%s\" block. You can leave this block intact or remove it entirely."], "Showing large initial letter.": ["Showing large initial letter."], "Toggle to show a large initial letter.": ["Toggle to show a large initial letter."], "Drop cap": ["Drop cap"], "Write preformatted text…": ["Write preformatted text…"], "Separator": ["Separator"], "Shortcode": ["Shortcode"], "Write shortcode here…": ["Write shortcode here…"], "Column count": ["Column count"], "Row count": ["Row count"], "Create": ["Create"], "Edit table": ["Edit table"], "Fixed width table cells": ["Fixed width table cells"], "New Column": ["New Column"], "Muted": ["Muted"], "Playback controls": ["Playback controls"], "Link removed.": ["<PERSON> removed."], "Empty block; start writing or type forward slash to choose a block": ["Empty block; start writing or type forward slash to choose a block"], "This image has an empty alt attribute; its file name is %s": ["This image has an empty alt attribute; its file name is %s"], "This image has an empty alt attribute": ["This image has an empty alt attribute"], "content placeholder\u0004Content…": ["Content…"], "image %1$d of %2$d in gallery": ["image %1$d of %2$d in gallery"], "The excerpt is hidden.": ["The excerpt is hidden."], "The excerpt is visible.": ["The excerpt is visible."], "Hide the excerpt on the full content page": ["Hide the excerpt on the full content page"], "Label text": ["Label text"], "Add label…": ["Add label…"], "Add button text…": ["Add button text…"], "Enter URL here…": ["Enter URL here…"], "Use URL": ["Use URL"], "Edit RSS URL": ["Edit RSS URL"], "Display author": ["Display author"], "Display date": ["Display date"], "Display excerpt": ["Display excerpt"], "Max number of words in excerpt": ["Max number of words in excerpt"], "- Select -": ["- Select -"], "One.": ["One."], "Two.": ["Two."], "Three.": ["Three."], "Four.": ["Four."], "Five.": ["Five."], "Six.": ["Six."], "Code is Poetry": ["Code is Poetry"], "Move image backward": ["Move image backward"], "Move image forward": ["Move image forward"], "Write gallery caption…": ["Write gallery caption…"], "The wren<br>Earns his living<br>Noiselessly.": ["The wren<br>Earns his living<br>Noiselessly."], "— Kobayashi Issa (一茶)": ["<PERSON> <PERSON><PERSON> (一茶)"], "Mont Blanc appears—still, snowy, and serene.": ["Mont Blanc appears—still, snowy, and serene."], "Align column left": ["Align column left"], "Align column center": ["Align column centre"], "Align column right": ["Align column right"], "Insert a table for sharing data.": ["Insert a table for sharing data."], "Create Table": ["Create Table"], "Change column alignment": ["Change column alignment"], "Header section": ["Header section"], "Footer section": ["Footer section"], "Jazz Musician": ["Jazz Musician"], "Release Date": ["Release Date"], "May 7, 2019": ["7 May 2019"], "February 21, 2019": ["21 February 2019"], "December 6, 2018": ["6 December 2018"], "One of the hardest things to do in technology is disrupt yourself.": ["One of the hardest things to do in technology is disrupt yourself."], "Ordered list settings": ["Ordered list settings"], "Start value": ["Start value"], "Reverse list numbering": ["Reverse list numbering"], "In quoting others, we cite ourselves.": ["In quoting others, we cite ourselves."], "Welcome to the wonderful world of blocks…": ["Welcome to the wonderful world of blocks…"], "Play inline": ["Play inline"], "The current poster image url is %s": ["The current poster image URL is %s"], "There is no poster image currently selected": ["There is no poster image currently selected"], "Percentage Width": ["Percentage Width"], "menu": ["menu"], "Post content": ["Post Content"], "Sorting and filtering": ["Sorting and filtering"], "Enter address": ["Enter address"], "Title attribute": ["Title attribute"], "Describe the role of this image on the page.": ["Describe the role of this image on the page."], "(Note: many devices and browsers do not display this text.)": ["(Note: many devices and browsers do not display this text.)"], "Level %s. Empty.": ["Level %s. Empty."], "Level %1$s. %2$s": ["Level %1$s. %2$s"], "ADD MEDIA": ["ADD MEDIA"], "Show:": ["Show:"], "Full post": ["Full post"], "Display featured image": ["Display featured image"], "Image alignment": ["Image alignment"], "EXT. XANADU - FAINT DAWN - 1940 (MINIATURE)\nWindow, very small in the distance, illuminated.\nAll around this is an almost totally black screen. Now, as the camera moves slowly towards the window which is almost a postage stamp in the frame, other forms appear;": ["EXT. XANADU - FAINT DAWN - 1940 (MINIATURE)\nWindow, very small in the distance, illuminated.\nAll around this is an almost totally black screen. Now, as the camera moves slowly towards the window, which is almost a postage stamp in the frame, other forms appear;"], "Matt Mullenweg": ["<PERSON>"], "Header label": ["Header label"], "Footer label": ["Footer label"], "WHAT was he doing, the great god Pan,\n\tDown in the reeds by the river?\nSpreading ruin and scattering ban,\nSplashing and paddling with hoofs of a goat,\nAnd breaking the golden lilies afloat\n    With the dragon-fly on the river.": ["WHAT was he doing, the great god <PERSON>,\n\tDown in the reeds by the river?\nSpreading ruin and scattering ban,\nSplashing and paddling with hooves of a goat,\nAnd breaking the golden lilies afloat\n    With the dragonfly on the river."], "Poster image": ["Poster image"], "Social Icon": ["Social Icon"], "%s label": ["%s label"], "Link label": ["Link label"], "Briefly describe the link to help screen reader users.": ["Briefly describe the link to help screen reader users."], "Button only": ["Button only"], "Template Part": ["Template Part"], "Mobile": ["Mobile"], "Image uploaded.": ["Image uploaded."], "Edit gallery image": ["Edit gallery image"], "Display author name": ["Display author name"], "Add link to featured image": ["Add link to featured image"], "“Read more” link text": ["“Read more” link text"], "Toggle search label": ["Toggle search label"], "Change button position": ["Change button position"], "No button": ["No button"], "Button outside": ["Button outside"], "Button inside": ["<PERSON><PERSON> inside"], "Use button with icon": ["Use button with icon"], "Subtitles": ["Subtitles"], "Tracks can be subtitles, captions, chapters, or descriptions. They help make your content more accessible to a wider range of users.": ["Tracks can be subtitles, captions, chapters, or descriptions. They help make your content more accessible to a wider range of users."], "Edit %s": ["Edit %s"], "Text tracks": ["Text tracks"], "Edit track": ["Edit track"], "Title of track": ["Title of track"], "Source language": ["Source language"], "Language tag (en, fr, etc.)": ["Language tag (en, fr, etc)"], "Kind": ["Kind"], "Remove track": ["Remove track"], "Add tracks": ["Add tracks"], "Open links in new tab": ["Open links in new tab"], "Add text over image": ["Add text over image"], "Image caption text": ["Image caption text"], "Unordered": ["Unordered"], "Ordered": ["Ordered"], "List text": ["List text"], "Preformatted text": ["Preformatted text"], "Pullquote text": ["Pullquote text"], "Pullquote citation text": ["Pullquote citation text"], "Block cannot be rendered inside itself.": ["Block cannot be rendered inside itself."], "Shortcode text": ["Shortcode text"], "Header cell text": ["Header cell text"], "Body cell text": ["Body cell text"], "Footer cell text": ["Footer cell text"], "Table caption text": ["Table caption text"], "Column %d text": ["Column %d text"], "Verse text": ["Verse text"], "Write verse…": ["Write verse…"], "Video caption text": ["Video caption text"], "Wood thrush singing in Central Park, NYC.": ["Wood thrush singing in Central Park, NYC."], "Normal": ["Normal"], "Huge": ["<PERSON>ge"], "Icon color": ["Icon colour"], "Start blank": ["Start blank"], "Add quote": ["Add quote"], "Add citation": ["Add citation"], "Media width": ["Media width"], "Display login as form": ["Display login as form"], "Redirect to current URL": ["Redirect to current URL"], "Click plus to add": ["Click plus to add"], "Image width": ["Image width"], "Link image to home": ["Link image to home"], "Site tagline text": ["Site tagline text"], "Write site tagline…": ["Write site tagline…"], "Site Tagline placeholder": ["Site Tagline placeholder"], "Site title text": ["Site title text"], "Write site title…": ["Write site title…"], "Site Title placeholder": ["Site Title placeholder"], "Display settings": ["Display settings"], "Items per Page": ["Items per Page"], "Offset": ["Offset"], "Limit the pages you want to show, even if the query has more results. To show all pages use 0 (zero).": ["Limit the pages you want to show, even if the query has more results. To show all pages use 0 (zero)."], "Max page to show": ["Maximum number of pages to show"], "Exclude": ["Exclude"], "Only": ["Only"], "Inherit query from template": ["Inherit query from template"], "Toggle to use the global query context that is set with the current template, such as an archive or search. Disable to customize the settings independently.": ["Toggle to use the global query context that is set with the current template, such as an archive or search. Disable to customise the settings independently."], "WordPress contains different types of content and they are divided into collections called “Post types”. By default there are a few different ones such as blog posts and pages, but plugins could add more.": ["WordPress contains different types of content and they are divided into collections called “Post types”. By default, there are a few different ones such as blog posts and pages, but plugins could add more."], "Sticky posts": ["Sticky posts"], "Blog posts can be “stickied”, a feature that places them at the top of the front page of posts, keeping it there until new sticky posts are published.": ["Blog posts can be “stickied”, a feature that places them at the top of the front page of posts, keeping it there until new sticky posts are published."], "Filters": ["Filters"], "Keyword": ["Keyword"], "Posts List": ["Posts List"], "Display a list of your most recent posts, excluding sticky posts.": ["Display a list of your most recent posts, excluding sticky posts."], "Title & Date": ["Title and Date"], "Title & Excerpt": ["Title and Excerpt"], "Title, Date, & Excerpt": ["Title, Date, and Excerpt"], "Image, Date, & Title": ["Image, Date, and Title"], "Provided type is not supported.": ["Provided type is not supported."], "Archive title": ["Archive title"], "Archive Title": ["Archive Title"], "Display the archive title based on the queried object.": ["Display the archive title based on the queried object."], "Next page link": ["Next page link"], "Previous page link": ["Previous page link"], "An example title": ["An example title"], "No Title": ["No Title"], "Make title a link": ["Make title a link"], "Change Date": ["Change Date"], "Add \"read more\" link text": ["Add \"read more\" link text"], "Show link on new line": ["Show link on new line"], "Term items not found.": ["Term items not found."], "Remove %s": ["Remove %s"], "All gallery image sizes updated to: %s": ["All gallery image sizes updated to: %s"], "Loading options…": ["Loading options…"], "The <main> element should be used for the primary content of your document only. ": ["The <main> element should be used for the primary content of your document only. "], "%s navigation": ["%s navigation"], "Start empty": ["Start empty"], "Switch to '%s'": ["Switch to '%s'"], "Create new menu": ["Create new menu"], "Manage menus": ["Manage menus"], "Menu name": ["Menu name"], "Delete menu": ["Delete menu"], "Delete %s": ["Delete %s"], "Are you sure you want to delete this navigation menu?": ["Are you sure you want to delete this navigation menu?"], "Confirm": ["Confirm"], "Display": ["Display"], "Overlay Menu": ["Overlay menu"], "Configure overlay menu": ["Configure overlay menu"], "Collapses the navigation options in a menu icon opening an overlay.": ["Collapses the navigation options in a menu icon opening an overlay."], "Off": ["Off"], "Always": ["Always"], "Submenus": ["Submenus"], "Open on click": ["Open on click"], "Submenu & overlay text": ["Submenu and overlay text"], "Submenu & overlay background": ["Submenu and overlay background"], "Contact": ["Contact"], "Select post": ["Select post"], "Select page": ["Select page"], "Select category": ["Select category"], "Select tag": ["Select tag"], "Add link": ["Add link"], "Add submenu": ["Add submenu"], "This item is missing a link": ["This item is missing a link"], "Navigation link text": ["Navigation link text"], "Create draft post: <mark>%s</mark>": ["Create draft post: <mark>%s</mark>"], "Create draft page: <mark>%s</mark>": ["Create draft page: <mark>%s</mark>"], "navigation link preview example\u0004Example Link": ["Example Link"], "Show avatar": ["Show avatar"], "Avatar size": ["Avatar size"], "Show bio": ["Show bio"], "Post author byline text": ["Post author byline text"], "Write byline…": ["Write byline…"], "Post Author": ["Post Author"], "No comments": ["No comments"], "Post Date": ["Post Date"], "Scale option for Image dimension control\u0004Cover": ["Cover"], "Scale option for Image dimension control\u0004Contain": ["Contain"], "Scale option for Image dimension control\u0004Fill": ["Fill"], "Image is scaled and cropped to fill the entire space without being distorted.": ["Image is scaled and cropped to fill the entire space without being distorted."], "Image is scaled to fill the space without clipping nor distorting.": ["Image is scaled to fill the space without clipping or distorting."], "Image will be stretched and distorted to completely fill the space.": ["Image will be stretched and distorted to completely fill the space."], "Image scaling options\u0004Scale": ["Scale"], "Next: ": ["Next: "], "Previous: ": ["Previous: "], "Next post": ["Next post"], "Previous post": ["Previous post"], "Display the title as a link": ["Display the title as a link"], "If you have entered a custom label, it will be prepended before the title.": ["If you have entered a custom label, it will be prepended before the title."], "Include the label as part of the link": ["Include the label as part of the link"], "Displays the post link that follows the current post.": ["Displays the post link that follows the current post."], "Displays the post link that precedes the current post.": ["Displays the post link that precedes the current post."], "Enter character(s) used to separate terms.": ["Enter character(s) used to separate terms."], "A decorative arrow appended to the next and previous page link.": ["A decorative arrow appended to the next and previous page link."], "Arrow option for Query Pagination Next/Previous blocks\u0004None": ["None"], "Arrow option for Query Pagination Next/Previous blocks\u0004Arrow": ["Arrow"], "Arrow option for Query Pagination Next/Previous blocks\u0004Chevron": ["Chevron"], "Add a site logo": ["Add a site logo"], "Make title link to home": ["Make title link to home"], "Number of tags": ["Number of tags"], "Template Part \"%s\" inserted.": ["Template part \"%s\" inserted."], "Untitled Template Part": ["Untitled Template Part"], "Name and create your new %s": ["Name and create your new %s"], "Choose an existing %s or create a new one.": ["Choose an existing %s or create a new one."], "Area": ["Area"], "Default based on area (%s)": ["Default based on area (%s)"], "Term Description": ["Term description"], "Loading…": ["Loading…"], "You do not have permission to edit this Menu. Any changes made will not be saved.": ["You do not have permission to edit this menu. Any changes made will not be saved."], "You do not have permission to create Navigation Menus.": ["You do not have permission to create navigation menus."], "Site Icons are what you see in browser tabs, bookmark bars, and within the WordPress mobile apps. To use a custom icon that is different from your site logo, use the <a>Site Icon settings</a>.": ["Site icons are what you see in browser tabs, bookmark bars, and within the WordPress mobile apps. To use a custom icon that is different from your site logo, use the <a>site icon settings</a>."], "Use as Site Icon": ["Use as site icon"], "Icon": ["Icon"], "Page List: Cannot retrieve Pages.": ["Page List: cannot retrieve pages."], "Icon background": ["Icon background"], "Arrange blocks horizontally.": ["Arrange blocks horizontally."], "Stack": ["<PERSON><PERSON>"], "Arrange blocks vertically.": ["Arrange blocks vertically."], "Home link text": ["Home link text"], "Add home link": ["Add home link"], "block example\u0004Home Link": ["Home Link"], "Create from '%s'": ["Create from '%s'"], "Loading navigation block setup options…": ["Loading navigation block setup options…"], "Navigation block setup options ready.": ["Navigation block setup options ready."], "Unable to fetch classic menu \"%s\" from API.": ["Unable to fetch classic menu \"%s\" from API."], "Unable to create Navigation Menu \"%s\".": ["Unable to create Navigation Menu \"%s\"."], "Creating Navigation Menu.": ["Creating Navigation Menu."], "Navigation Menu successfully created.": ["Navigation Menu successfully created."], "Failed to create Navigation Menu.": ["Failed to create Navigation Menu."], "Classic menu importing.": ["Classic menu importing."], "Classic menu imported successfully.": ["Classic menu imported successfully."], "Classic menu import failed.": ["Classic menu import failed."], "Show icon button": ["Show icon button"], "Configure the visual appearance of the button that toggles the overlay menu.": ["Configure the visual appearance of the button that toggles the overlay menu."], "Show arrow": ["Show arrow"], "Navigation menu %s successfully deleted.": ["Navigation menu %s successfully deleted."], "This item has been deleted, or is a draft": ["This item has been deleted, or is a draft"], "Convert to Link": ["Convert to <PERSON>"], "Author Biography": ["Author Biography"], "This is the Content block, it will display all the blocks in any single post or page.": ["This is the Content block, it will display all the blocks in any single post or page."], "That might be a simple arrangement like consecutive paragraphs in a blog post, or a more elaborate composition that includes image galleries, videos, tables, columns, and any other block types.": ["That might be a simple arrangement like consecutive paragraphs in a blog post, or a more elaborate composition that includes image galleries, videos, tables, columns, and any other block types."], "If there are any Custom Post Types registered at your site, the Content block can display the contents of those entries as well.": ["If there are any Custom Post Types registered at your site, the Content block can display the contents of those entries as well."], "Link to post": ["Link to post"], "Featured image: %s": ["Featured image: %s"], "Authors": ["Authors"], "Post type": ["Post type"], "Choose a pattern for the query loop or start blank.": ["Choose a pattern for the query loop or start blank."], "Choose a pattern": ["Choose a pattern"], "Add text or blocks that will display when a query returns no results.": ["Add text or blocks that will display when a query returns no results."], "Show labels": ["Show labels"], "Smallest size": ["Smallest size"], "Largest size": ["Largest size"], "Existing template parts": ["Existing template parts"], "Choose a %s": ["Choose a %s"], "Links are disabled in the editor.": ["Links are disabled in the editor."], "Gather blocks in a container.": ["Gather blocks in a container."], "Alice.": ["<PERSON>."], "The White Rabbit.": ["The White Rabbit."], "The Cheshire Cat.": ["The Cheshire Cat."], "The Mad Hatter.": ["The Mad Hatter."], "The Queen of Hearts.": ["The Queen of Hearts."], "Import Classic Menus": ["Import Classic Menus"], "handle": ["handle"], "Author Name": ["Author name"], "Link to author archive": ["Link to author archive"], "block title\u0004Post Comment": ["Post Comment"], "To show a comment, input the comment ID.": ["To show a comment, input the comment ID."], "Post Comments Count block: post not found.": ["Post Comments Count block: post not found."], "%s comment": ["%s comment", "%s comments"], "Post Comments Link block: post not found.": ["Post Comments Link block: post not found."], "Display last modified date": ["Display last modified date"], "Prefix": ["Prefix"], "Suffix": ["Suffix"], "Parents": ["Parents"], "Taxonomies": ["Taxonomies"], "Show archive type in title": ["Show archive type in title"], "Archive type: Name": ["Archive type: Name"], "Show search term in title": ["Show search term in title"], "Search results for: “search term”": ["Search results for: “search term”"], "Search Results Title": ["Search Results Title"], "Display the search results title based on the queried object.": ["Display the search results title based on the queried object."], "Quote citation": ["Quote citation"], "Convert to static list": ["Convert to static list"], "Only include current page": ["Only include current page"], "Only including headings from the current page (if the post is paginated).": ["Only including headings from the current page (if the post is paginated)."], "Toggle to only include headings from the current page (if the post is paginated).": ["Toggle to only include headings from the current page (if the post is paginated)."], "Start adding Heading blocks to create a table of contents. Headings with HTML anchors will be linked here.": ["Start adding Heading blocks to create a table of contents. Headings with HTML anchors will be linked here."], "Search for replacements": ["Search for replacements"], "Add submenu link": ["Add submenu link"], "Group blocks together. Select a layout:": ["Group blocks together. Select a layout:"], "(no title %s)": ["(no title %s)"], "Structure for navigation menu: %s": ["Structure for navigation menu: %s"], "Untitled menu": ["Untitled menu"], "You have not yet created any menus. Displaying a list of your Pages": ["You have not yet created any menus. Displaying a list of your Pages"], "Page List: \"%s\" page has no children.": ["Page List: \"%s\" page has no children."], "Edit this menu": ["Edit this menu"], "Choose a page to show only its subpages.": ["Choose a page to show only its subpages."], "Not available for aligned text.": ["Not available for aligned text."], "Link author name to author page": ["Link author name to author page"], "A decorative arrow for the next and previous link.": ["A decorative arrow for the next and previous link."], "Arrow option for Next/Previous link\u0004None": ["None"], "Arrow option for Next/Previous link\u0004Arrow": ["Arrow"], "Arrow option for Next/Previous link\u0004Chevron": ["Chevron"], "Select widget area": ["Select widget area"], "Widget area: %s": ["Widget area: %s"], "Unable to import the following widgets: %s.": ["Unable to import the following widgets: %s."], "Import widget area": ["Import widget area"], "Select the size of the source images.": ["Select the size of the source images."], "Arrange blocks in a grid.": ["Arrange blocks in a grid."], "Max number of words": ["Max number of words"], "Post meta": ["Post meta"], "Numbering style": ["Numbering style"], "Numbers": ["Numbers"], "Uppercase letters": ["Upper case letters"], "Lowercase letters": ["Lower case letters"], "Uppercase Roman numerals": ["Upper case Roman numerals"], "Lowercase Roman numerals": ["Lower case Roman numerals"], "Choose or create a Navigation menu": ["Choose or create a Navigation menu"], "This navigation menu is empty.": ["This navigation menu is empty."], "Additional information to help clarify the purpose of the link.": ["Additional information to help clarify the purpose of the link."], "Rel attribute": ["Rel attribute"], "The relationship of the linked URL as space-separated link types.": ["The relationship of the linked URL as space-separated link types."], "Post Modified Date": ["Post Modified Date"], "Only shows if the post has been modified": ["Only shows if the post has been modified"], "Display a post's last updated date.": ["Display a post's last updated date."], "This block will display the excerpt.": ["This block will display the excerpt."], "The content is currently protected and does not have the available excerpt.": ["The content is currently protected and does not have the available excerpt."], "Excerpt text": ["Excerpt text"], "No excerpt found": ["No excerpt found"], "%d minute": ["%d minute", "%d minutes"], "<a>Add new post</a>": ["<a>Add new post</a>"], "Show label text": ["Show label text"], "Toggle off to hide the label text, e.g. \"Next Page\".": ["Toggle off to hide the label text, e.g. \"Next Page\"."], "Footnotes found in blocks within this document will be displayed here.": ["Footnotes found in blocks within this document will be displayed here."], "Footnote": ["Footnote"], "Parent": ["Parent"], "… <a>Read more<span>: %1$s</span></a>": ["… <a>Read more<span>: %1$s</span></a>"], "It appears you are trying to use the deprecated Classic block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely. Alternatively, you can refresh the page to use the Classic block.": ["It appears you are trying to use the deprecated Classic block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely. Alternatively, you can refresh the page to use the Classic block."], "It appears you are trying to use the deprecated Classic block. You can leave this block intact, or remove it entirely. Alternatively, you can refresh the page to use the Classic block.": ["It appears you are trying to use the deprecated Classic block. You can leave this block intact, or remove it entirely. Alternatively, you can refresh the page to use the Classic block."], "Navigation menu has been deleted or is unavailable. <button>Create a new menu?</button>": ["Navigation menu has been deleted or is unavailable. <button>Create a new menu?</button>"], "Navigation menu: \"%s\"": ["Navigation menu: \"%s\""], "The current menu options offer reduced accessibility for users and are not recommended. Enabling either \"Open on Click\" or \"Show arrow\" offers enhanced accessibility by allowing keyboard users to browse submenus selectively.": ["The current menu options offer reduced accessibility for users and are not recommended. Enabling either \"Open on Click\" or \"Show arrow\" offers enhanced accessibility by allowing keyboard users to browse submenus selectively."], "Overlay menu controls": ["Overlay menu controls"], "Unsaved Navigation Menu.": ["Unsaved Navigation Menu."], "Edit Page List": ["Edit Page List"], "Block: Paragraph": ["Block: Paragraph"], "Comments form disabled in editor.": ["Comments form disabled in editor."], "Modified Date": ["Modified Date"], "Browsing between pages requires a full page reload.": ["Browsing between pages requires a full page reload."], "Number of links": ["Number of links"], "Specify how many links can appear before and after the current page number. Links to the first, current and last page are always visible.": ["Specify how many links can appear before and after the current page number. Links to the first, current and last page are always visible."], "%s: Name": ["%s: Name"], "%s name": ["%s name"], "Footnotes are not supported here. Add this block to post or page content.": ["Footnotes are not supported here. Add this block to post or page content."], "Browsing between pages won't require a full page reload, unless non-compatible blocks are detected.": ["Browsing between pages won't require a full page reload, unless non-compatible blocks are detected."], "Force page reload can't be disabled because there are non-compatible blocks inside the Query block.": ["Force page reload can't be disabled because there are non-compatible blocks inside the Query block."], "Force page reload": ["Force page reload"], "If you still want to prevent full page reloads, remove that block, then disable \"Force page reload\" again in the Query Block settings.": ["If you still want to prevent full page reloads, remove that block, then disable \"Force page reload\" again in the Query Block settings."], "Currently, avoiding full page reloads is not possible when a Content block is present inside the Query block.": ["Currently, avoiding full page reloads is not possible when a Content block is present inside the Query block."], "Query block: Force page reload enabled": ["Query block: Force page reload enabled"], "Crop images to fit": ["Crop images to fit"], "Randomize order": ["Randomise order"], "Open images in new tab": ["Open images in new tab"], "Custom HTML Preview": ["Custom HTML preview"], "HTML preview is not yet fully accessible. Please switch screen reader to virtualized mode to navigate the below iFrame.": ["HTML preview is not yet fully accessible. Please switch screen reader to virtualised mode to navigate the iFrame below."], "Connected to %s": ["Connected to %s"], "Connected to dynamic data": ["Connected to dynamic data"], "Upload to Media Library": ["Upload to Media Library"], "Alternative text for an image. Block toolbar label, a low character count is preferred.\u0004Alt": ["Alt"], "Crop image to fill": ["Crop image to fill"], "Choose a block to add to your Navigation.": ["Choose a block to add to your navigation."], "Search for and add a link to your Navigation.": ["Search for and add a link to your navigation."], "Pattern \"%s\" cannot be rendered inside itself.": ["Pattern \"%s\" cannot be rendered inside itself."], "This navigation menu displays your website's pages. Editing it will enable you to add, delete, or reorder pages. However, new pages will no longer be added automatically.": ["This navigation menu displays your website's pages. Editing it will enable you to add, delete, or reorder pages. However, new pages will no longer be added automatically."], "Unfiltered": ["Unfiltered"], "Filter by taxonomy": ["Filter by taxonomy"], "Only link to posts that have the same taxonomy terms as the current post. For example the same tags or categories.": ["Only link to posts that have the same taxonomy terms as the current post. For example, the same tags or categories."], "Edit original": ["Edit original"], "Currently, avoiding full page reloads is not possible when non-interactive or non-clientNavigation compatible blocks from plugins are present inside the Query block.": ["Currently, avoiding full page reloads is not possible when non-interactive or non-clientNavigation compatible blocks from plugins are present inside the Query block."], "Excerpt": ["Excerpt"], "Submit": ["Submit"], "Alternative text": ["Alternative text"], "Show post counts": ["Show post counts"], "Calendar": ["Calendar"], "Text": ["Text"], "Show hierarchy": ["Show hierarchy"], "Unlink": ["Unlink"], "Background": ["Background"], "User": ["User"], "Display as dropdown": ["Display as dropdown"], "Square": ["Square"], "Exit fullscreen": ["Exit fullscreen"], "Upload": ["Upload"], "Video": ["Video"], "Invalid": ["Invalid"], "File": ["File"], "Tools": ["Tools"], "Auto": ["Auto"], "Previous": ["Previous"], "Minimum height": ["Minimum height"], "Embed a WordPress post.": ["Embed a WordPress post."], "(Untitled)": ["(Untitled)"], "Original": ["Original"], "Contact us": ["Contact us"], "Chapters": ["Chapters"], "English": ["English"], "Gallery": ["Gallery"], "Enter your email address.": ["Enter your email address."], "Reset": ["Reset"], "Avatar": ["Avatar"], "Options": ["Options"], "Add text…": ["Add text…"], "Classic": ["Classic Edit"], "Write code…": ["Write code…"], "Cover": ["Cover"], "Fixed background": ["Fixed background"], "Overlay": ["Overlay"], "Write title…": ["Write title…"], "Embed a tweet.": ["Embed a tweet."], "music": ["music"], "video": ["video"], "Embed a YouTube video.": ["Embed a YouTube video."], "Embed a Facebook post.": ["Embed a Facebook post."], "image": ["image"], "Embed an Instagram post.": ["Embed an Instagram post."], "post": ["post"], "blog": ["blog"], "audio": ["audio"], "Embed SoundCloud content.": ["Embed SoundCloud content."], "Embed Spotify content.": ["Embed Spotify content."], "Embed Flickr content.": ["Embed Flickr content."], "Embed a Vimeo video.": ["Embed a Vimeo video."], "Embed an Animoto video.": ["Embed an Animoto video."], "Embed Cloudup content.": ["Embed Cloudup content."], "Embed CollegeHumor content.": ["Embed CollegeHumor content."], "Embed a Dailymotion video.": ["Embed a Dailymotion video."], "Embed Imgur content.": ["Embed Imgur content."], "Embed Issuu content.": ["Embed Issuu content."], "Embed Kickstarter content.": ["Embed Kickstarter content."], "Embed Mixcloud content.": ["Embed Mixcloud content."], "Embed a Reddit thread.": ["Embed a Reddit thread."], "Embed ReverbNation content.": ["Embed ReverbNation content."], "Embed Screencast content.": ["Embed Screencast content."], "Embed Scribd content.": ["Embed Scribd content."], "Embed Slideshare content.": ["Embed Slideshare content."], "Embed SmugMug content.": ["Embed SmugMug content."], "Embed Speaker Deck content.": ["Embed Speaker Deck content."], "Embed a TED video.": ["Embed a TED video."], "Embed a Tumblr post.": ["Embed a Tumblr post."], "Embed a VideoPress video.": ["Embed a VideoPress video."], "Embed a WordPress.tv video.": ["Embed a WordPress.tv video."], "This embed will preserve its aspect ratio when the browser is resized.": ["This embed will preserve its aspect ratio when the browser is resized."], "This embed may not preserve its aspect ratio when the browser is resized.": ["This embed may not preserve its aspect ratio when the browser is resized."], "Edit URL": ["Edit URL"], "Media settings": ["Media settings"], "Resize for smaller devices": ["Resize for smaller devices"], "%s URL": ["%s URL"], "Enter URL to embed here…": ["Enter URL to embed here…"], "button label\u0004Embed": ["Embed"], "Embedded content from %s": ["Embedded content from %s"], "block title\u0004Embed": ["Embed"], "Write file name…": ["Write file name…"], "Copy URL": ["Copy URL"], "button label\u0004Download": ["Download"], "Show download button": ["Show download button"], "Drag images, upload new ones or select files from your library.": ["Drag images, upload new ones or select files from your library."], "Heading": ["Heading"], "Keep as HTML": ["Keep as HTML"], "Height in pixels": ["Height in pixels"], "Width settings": ["Width settings"], "Stack on mobile": ["Stack on mobile"], "button label\u0004Try again": ["Try again"], "button label\u0004Convert to link": ["Convert to link"], "Embed Crowdsignal (formerly Polldaddy) content.": ["Embed Crowdsignal (formerly Polldaddy) content."], "ebook": ["eBook"], "Embed Amazon Kindle content.": ["Embed Amazon Kindle content."], "Sorry, this content could not be embedded.": ["Sorry, this content could not be embedded."], "Embedded content from %s can't be previewed in the editor.": ["Embedded content from %s can't be previewed in the editor."], "Optional placeholder text": ["Optional placeholder text"], "Optional placeholder…": ["Optional placeholder…"], "Button text": ["Button text"], "Upload an image file, pick one from your media library, or add one with a URL.": ["Upload an image file, pick one from your media library, or add one with a URL."], "Upload a video file, pick one from your media library, or add one with a URL.": ["Upload a video file, pick one from your media library, or add one with a URL."], "Paste a link to the content you want to display on your site.": ["Paste a link to the content you want to display on your site."], "Learn more about embeds": ["Learn more about embeds"], "Group": ["Group"], "In a village of La Mancha, the name of which I have no desire to call to mind, there lived not long since one of those gentlemen that keep a lance in the lance-rack, an old buckler, a lean hack, and a greyhound for coursing.": ["In a village of La Mancha, the name of which I have no desire to call to mind, there lived not long since one of those gentlemen that keep a lance in the lance-rack, an old buckler, a lean hack, and a greyhound for coursing."], "Describe the purpose of the image.": ["Describe the purpose of the image."], "Clear Media": ["Clear Media"], "<strong>Snow Patrol</strong>": ["<strong>Snow Patrol</strong>"], "Attachment page": ["Attachment page"], "Open in new tab": ["Open in new tab"], "Upload a file or pick one from your media library.": ["Upload a file or pick one from your media library."], "Link rel": ["<PERSON> rel"], "Call to Action": ["Call to Action"], "Two columns; equal split": ["Two columns; equal split"], "Two columns; one-third, two-thirds split": ["Two columns; one-third, two-thirds split"], "Two columns; two-thirds, one-third split": ["Two columns; two-thirds, one-third split"], "Three columns; equal split": ["Three columns; equal split"], "Three columns; wide center column": ["Three columns; wide centre column"], "Etiam et egestas lorem. Vivamus sagittis sit amet dolor quis lobortis. Integer sed fermentum arcu, id vulputate lacus. Etiam fermentum sem eu quam hendrerit.": ["Etiam et egestas lorem. Vivamus sagittis sit amet dolor quis lobortis. Integer sed fermentum arcu, id vulputate lacus. Etiam fermentum sem eu quam hendrerit."], "Nam risus massa, ullamcorper consectetur eros fermentum, porta aliquet ligula. Sed vel mauris nec enim.": ["Nam risus massa, ullamcorper consectetur eros fermentum, porta aliquet ligula. Sed vel mauris nec enim."], "Image size": ["Image size"], "Open Media Library": ["Open Media Library"], "Embed a TikTok video.": ["Embed a TikTok video."], "Link to": ["Link to"], "podcast": ["podcast"], "// A “block” is the abstract term used\n// to describe units of markup that\n// when composed together, form the\n// content or layout of a page.\nregisterBlockType( name, settings );": ["// A “block” is the abstract term used\n// to describe units of markup that\n// when composed together, form the\n// content or layout of a page.\nregisterBlockType( name, settings );"], "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent et eros eu felis.": ["Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent et eros eu felis."], "Suspendisse commodo neque lacus, a dictum orci interdum et.": ["Suspendisse commodo neque lacus, a dictum orci interdum et."], "Media file": ["Media file"], "Patterns": ["Patterns"], "Block variations": ["Block variations"], "social": ["social"], "survey": ["survey"], "16:9": ["16:9"], "4:3": ["4:3"], "3:2": ["3:2"], "9:16": ["9:16"], "3:4": ["3:4"], "2:3": ["2:3"], "Crop": ["Crop"], "Browser default": ["Browser default"], "This column count exceeds the recommended amount and may cause visual breakage.": ["This column count exceeds the recommended amount and may cause visual breakage."], "50 / 50": ["50/50"], "33 / 33 / 33": ["33/33/33"], "25 / 50 / 25": ["25/50/25"], "Minimum height of cover": ["Minimum height of cover"], "Change content position": ["Change content position"], "Comment": ["Comment"], "Search for patterns": ["Search for patterns"], "Find out more": ["Find out more"], "100": ["100"], "One column": ["One column"], "Repeated background": ["Repeated background"], "Captions": ["Captions"], "Label": ["Label"], "Convert to blocks": ["Convert to blocks"], "Gallery caption text": ["Gallery caption text"], "Audio caption text": ["Audio caption text"], "Button width": ["Button width"], "Download button text": ["Download button text"], "Type / to choose a block": ["Type / to choose a block"], "Choose": ["<PERSON><PERSON>"], "Add caption": ["Add caption"], "Autoplay may cause usability issues for some users.": ["Autoplay may cause usability issues for some users."], "Your site does not have any posts, so there is nothing to display here at the moment.": ["Your site does not have any posts, so there is nothing to display here at the moment."], "%1$s (%2$d of %3$d)": ["%1$s (%2$d of %3$d)"], "PDF settings": ["PDF settings"], "Show inline embed": ["Show inline embed"], "Note: Most phone and tablet browsers won't display embedded PDFs.": ["Note: most phone and tablet browsers won't display embedded PDFs."], "Copied URL to clipboard.": ["Copied URL to clipboard."], "Embed of the selected PDF file.": ["Embed of the selected PDF file."], "HTML element": ["HTML element"], "Default (<div>)": ["Default (<div>)"], "noun; Audio block parameter\u0004Preload": ["Preload"], "No published posts found.": ["No published posts found."], "Show only top level categories": ["Show only top level categories"], "Drag and drop onto this block, upload, or select existing media from your library.": ["Drag and drop onto this block, upload, or select existing media from your library."], "bookmark": ["bookmark"], "Embed Pinterest pins, boards, and profiles.": ["Embed Pinterest pins, boards, and profiles."], "Embed Wolfram notebook content.": ["Embed Wolfram notebook content."], "Name of the file\u0004Armstrong_Small_Step": ["<PERSON>_Small_Step"], "Media item link option\u0004None": ["None"], "If uploading to a gallery all files need to be image formats": ["If uploading to a gallery, all files need to be image formats"], "All gallery image links updated to: %s": ["All gallery image links updated to: %s"], "All gallery images updated to open in new tab": ["All gallery images updated to open in new tab"], "All gallery images updated to not open in new tab": ["All gallery images updated to not open in new tab"], "The <header> element should represent introductory content, typically a group of introductory or navigational aids.": ["The <header> element should represent introductory content, typically a group of introductory or navigational aids."], "The <section> element should represent a standalone portion of the document that can't be better represented by another element.": ["The <section> element should represent a standalone portion of the document that can't be better represented by another element."], "The <article> element should represent a self-contained, syndicatable portion of the document.": ["The <article> element should represent a self-contained, syndicatable portion of the document."], "The <aside> element should represent a portion of a document whose content is only indirectly related to the document's main content.": ["The <aside> element should represent a portion of a document whose content is only indirectly related to the document's main content."], "The <footer> element should represent a footer for its nearest sectioning element (e.g.: <section>, <article>, <main> etc.).": ["The <footer> element should represent a footer for its nearest sectioning element (eg <section>, <article>, <main>, etc)."], "Post Title": ["Post Title"], "Arrow": ["Arrow"], "Preload value\u0004None": ["None"], "single horizontal line\u0004Row": ["Row"], "Group by:": ["Group by:"], "Week": ["Week"], "Default Avatar": ["De<PERSON><PERSON>"], "Select the avatar user to display, if it is blank it will use the post/page author.": ["Select the avatar user to display, if it is blank it will use the post/page author."], "Link to user profile": ["Link to user profile"], "Link to authors URL": ["Link to author’s URL"], "block title\u0004Comment Author": ["Comment Author"], "block title\u0004Comment Content": ["Comment Content"], "Link to comment": ["Link to comment"], "block title\u0004Comment Date": ["Comment Date"], "Older comments page link": ["Older comments page link"], "A decorative arrow appended to the next and previous comments link.": ["A decorative arrow appended to the next and previous comments link."], "Arrow option for Comments Pagination Next/Previous blocks\u0004None": ["None"], "Arrow option for Comments Pagination Next/Previous blocks\u0004Arrow": ["Arrow"], "Arrow option for Comments Pagination Next/Previous blocks\u0004Chevron": ["Chevron"], "Comments Pagination block: paging comments is disabled in the Discussion Settings": ["Comments Pagination block: paging comments is disabled in the Discussion Settings"], "Newer comments page link": ["Newer comments page link"], "Show post title": ["Show post title"], "Show comments count": ["Show comments count"], "“Post Title”": ["“Post Title”"], "A WordPress Commenter": ["A WordPress Commenter"], "says": ["says"], "January 1, 2000 at 00:00 am": ["January 1, 2000 at 00:00 am"], "Hi, this is a comment.": ["Hi, this is a comment."], "To get started with moderating, editing, and deleting comments, please visit the Comments screen in the dashboard.": ["To get started with moderating, editing, and deleting comments, please visit the Comments screen in the dashboard."], "Post Comments Form block: Comments are not enabled for this post type (%s).": ["Post Comments Form block: Comments are not enabled for this post type (%s)."], "Select the size of the source image.": ["Select the size of the source image."], "Commenter avatars come from <a>Gravatar</a>.": ["Commenter avatars come from <a>Gravatar</a>."], "action that affects the current post\u0004Enable comments": ["Enable comments"], "Post Comments Form block: Comments are not enabled.": ["Post Comments Form block: Comments are not enabled."], "Post Comments Form block: Comments are not enabled for this item.": ["Post Comments Form block: Comments are not enabled for this item."], "Show label": ["Show label"], "Show empty categories": ["Show empty categories"], "33 / 66": ["33 / 66"], "66 / 33": ["66 / 33"], "Switch to editable mode": ["Switch to editable mode"], "Avatar Settings": ["Avatar settings"], "Overlay opacity": ["Overlay opacity"], "Embed a podcast player from Pocket Casts.": ["Embed a podcast player from Pocket Casts."], "Remove caption": ["Remove caption"], "Classic Editor": ["Classic Editor"], "Grid": ["Grid"], "Resolution": ["Resolution"], "Aspect ratio": ["Aspect ratio"], "Scale option for dimensions control\u0004Contain": ["Contain"], "Scale option for dimensions control\u0004Cover": ["Cover"], "Commenter Avatar": ["Commenter Avatar"], "Reply to A WordPress Commenter": ["Reply to A WordPress Commenter"], "Leave empty if decorative.": ["Leave empty if decorative."], "Add an image or video with a text overlay.": ["Add an image or video with a text overlay."], "Type / to add a hidden block": ["Type / to add a hidden block"], "Open by default": ["Open by default"], "Write summary": ["Write summary"], "Write summary…": ["Write summary…"], "https://wordpress.org/documentation/article/embeds/": ["https://wordpress.org/documentation/article/embeds/"], "Image covers the space evenly.": ["Image covers the space evenly."], "Image is contained without distortion.": ["Image is contained without distortion."], "Enter fullscreen": ["Enter fullscreen"], "The <main> element should be used for the primary content of your document only.": ["The <main> element should be used for the primary content of your document only."], "Mark as nofollow": ["Mark as nofollow"], "Caption text": ["Caption text"], "Focal point": ["Focal point"], "Your form has been submitted successfully": ["Your form has been submitted successfully"], "There was an error submitting your form.": ["There was an error submitting your form."], "Submissions method": ["Submissions method"], "Send email": ["Send email"], "- Custom -": ["- Custom -"], "Select the method to use for form submissions. Additional options for the \"custom\" mode can be found in the \"Advanced\" section.": ["Select the method to use for form submissions. Additional options for the \"custom\" mode can be found in the \"Advanced\" section."], "Select the method to use for form submissions.": ["Select the method to use for form submissions."], "Email for form submissions": ["Email for form submissions"], "The email address where form submissions will be sent. Separate multiple email addresses with a comma.": ["The email address where form submissions will be sent. Separate multiple email addresses with a comma."], "Method": ["Method"], "Form action": ["Form action"], "The URL where the form should be submitted.": ["The URL where the form should be submitted."], "Experimental Comment form": ["Experimental comment form"], "A comment form for posts and pages.": ["A comment form for posts and pages."], "Experimental privacy request form": ["Experimental privacy request form"], "A form to request data exports and/or deletion.": ["A form to request data exports and/or deletion."], "To request an export or deletion of your personal data on this site, please fill-in the form below. You can define the type of request you wish to perform, and your email address. Once the form is submitted, you will receive a confirmation email with instructions on the next steps.": ["To request an export or deletion of your personal data on this site, please fill in the form below. You can define the type of request you wish to perform, and your email address. Once the form is submitted, you will receive a confirmation email with instructions on the next steps."], "Request data export": ["Request data export"], "Request data deletion": ["Request data deletion"], "Input settings": ["Input settings"], "Inline label": ["Inline label"], "Required": ["Required"], "Affects the \"name\" atribute of the input element, and is used as a name for the form submission results.": ["Affects the \"name\" atribute of the input element, and is used as a name for the form submission results."], "Value": ["Value"], "Empty label": ["Empty label"], "Type the label for this input": ["Type the label for this input"], "Text Input": ["Text input"], "A generic text input.": ["A generic text input."], "Textarea Input": ["Text area input"], "A textarea input to allow entering multiple lines of text.": ["A text area input to allow entering multiple lines of text."], "Checkbox Input": ["Tickbox input"], "A simple checkbox input.": ["A simple tickbox input."], "Email Input": ["Email input"], "Used for email addresses.": ["Used for email addresses."], "URL Input": ["URL input"], "Used for URLs.": ["Used for URLs."], "Telephone Input": ["Telephone input"], "Used for phone numbers.": ["Used for phone numbers."], "Number Input": ["Number input"], "A numeric input.": ["A numeric input."], "Enter the message you wish displayed for form submission error/success, and select the type of the message (success/error) from the block's options.": ["Enter the message you wish to be displayed for form submission error/success, and select the type of the message (success/error) from the block's options."], "Submission success notification": ["Submission success notification"], "Submission error notification": ["Submission error notification"], "Form Submission Success": ["Form submission success"], "Success message for form submissions.": ["Success message for form submissions."], "Your form has been submitted successfully.": ["Your form has been submitted successfully."], "Form Submission Error": ["Form submission error"], "Error/failure message for form submissions.": ["Error/failure message for form submissions."], "Add gallery caption": ["Add gallery caption"], "No posts found.": ["No posts found."], "Title": ["Title"], "Draft": ["Draft"], "Author": ["Author"], "URL": ["URL"], "Edit": ["Edit"], "Apply": ["Apply"], "Categories": ["Categories"], "Save": ["Save"], "Name": ["Name"], "Description": ["Description"], "None": ["None"], "Anonymous": ["Anonymous"], "Reply": ["Reply"], "Preview": ["Preview"], "Cancel": ["Cancel"], "Add Media": ["Add Media"], "Size": ["Size"], "Media": ["Media"], "OK": ["OK"], "Add": ["Add"], "Search": ["Search"], "Close": ["Close"], "Select": ["Select"], "(no title)": ["(no title)"], "Width": ["<PERSON><PERSON><PERSON>"], "Settings": ["Settings"], "Email": ["Email"], "Height": ["Height"], "HTML": ["HTML"], "No results found.": ["No results found."], "Leave a Reply": ["Leave a Reply"], "Post Comment": ["Post Comment"], "Select Category": ["Select Category"], "Log out": ["Log out"], "Insert row before": ["Insert row before"], "Insert row after": ["Insert row after"], "Delete row": ["Delete row"], "Insert column before": ["Insert column before"], "Insert column after": ["Insert column after"], "Code": ["Code"], "Loop": ["Loop"], "Home": ["Home"], "Image": ["Image"], "Next": ["Next"], "Menus": ["Menus"], "One response to %s": ["One response to %s"], "One response": ["One response"], "%s response": ["%s response", "%s responses"], "Preload": ["Preload"], "Link": ["Link"], "Remove": ["Remove"], "Remove image": ["Remove Image"], "Attachment Page": ["Attachment Page"], "Media File": ["Media File"], "Large": ["Large"], "Columns": ["Columns"], "Menu": ["<PERSON><PERSON>"], "Move down": ["Move down"], "Move up": ["Move up"], "Page break": ["Page break"], "Replace": ["Replace"], "Delete column": ["Delete column"], "Metadata": ["<PERSON><PERSON><PERSON>"], "Autoplay": ["Autoplay"], "Back": ["Back"], "editor button\u0004Left to right": ["Left to right"], "Display Settings": ["Display Settings"], "Year": ["Year"], "%1$s response to %2$s": ["%1$s response to %2$s", "%1$s responses to %2$s"], "Word count type. Do not translate!\u0004words": ["words"], "Read more": ["Read more"], "Search results": ["Search results"], "Table of Contents": ["Table of Contents"], "Month": ["Month"], "Day": ["Day"], "Replace image": ["Replace image"], "Edit image": ["Edit image"], "List view": ["List view"], "Grid view": ["Grid view"], "Small": ["Small"], "Select poster image": ["Select poster image"], "%1$s (%2$s)": ["%1$s (%2$s)"], "Add media": ["Add media"], "by %s": ["by %s"], "This content is password protected.": ["This content is password protected."], "%1$s: %2$s": ["%1$s: %2$s"], "PDF embed": ["PDF embed"], "Embed of %s.": ["Embed of %s."], "Next Page": ["Next Page"], "Previous Page": ["Previous Page"], "Open menu": ["Open menu"], "Close menu": ["Close menu"], "Template part has been deleted or is unavailable: %s": ["Template part has been deleted or is unavailable: %s"], "%s Avatar": ["%s avatar"], "Newer Comments": ["Newer Comments"], "Older Comments": ["Older Comments"], "Response": ["Response"], "Responses": ["Responses"], "Response to %s": ["Response to %s"], "Responses to %s": ["Responses to %s"], "Footnotes": ["Footnotes"]}}, "comment": {"reference": "wp-includes/js/dist/block-library.js"}}