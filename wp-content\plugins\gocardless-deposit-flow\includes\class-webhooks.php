<?php
/**
 * Webhook handling for GoCardless Deposit Flow
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GCDF_Webhooks {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'add_webhook_endpoint'));
        add_action('template_redirect', array($this, 'handle_webhook'));
    }

    /**
     * Add webhook endpoint
     */
    public function add_webhook_endpoint() {
        add_rewrite_rule(
            '^gocardless-webhook/?$',
            'index.php?gcdf_webhook=1',
            'top'
        );
        
        add_filter('query_vars', function($vars) {
            $vars[] = 'gcdf_webhook';
            return $vars;
        });
    }

    /**
     * Handle webhook requests
     */
    public function handle_webhook() {
        if (!get_query_var('gcdf_webhook')) {
            return;
        }

        // Only allow POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            status_header(405);
            exit('Method not allowed');
        }

        try {
            $this->process_webhook();
        } catch (Exception $e) {
            gcdf()->log('Webhook processing failed: ' . $e->getMessage(), 'error');
            status_header(500);
            exit('Webhook processing failed');
        }
    }

    /**
     * Process incoming webhook
     */
    private function process_webhook() {
        // Get raw POST data
        $payload = file_get_contents('php://input');
        
        if (empty($payload)) {
            status_header(400);
            exit('Empty payload');
        }

        // Get signature header
        $signature = $_SERVER['HTTP_WEBHOOK_SIGNATURE'] ?? '';
        
        if (empty($signature)) {
            status_header(400);
            exit('Missing signature');
        }

        // Verify signature (if webhook secret is configured)
        $settings = gcdf()->get_settings();
        $webhook_secret = $settings['webhook_secret'] ?? '';
        
        if (!empty($webhook_secret)) {
            if (!gcdf()->gocardless_api->verify_webhook_signature($payload, $signature, $webhook_secret)) {
                gcdf()->log('Invalid webhook signature', 'error');
                status_header(401);
                exit('Invalid signature');
            }
        }

        // Parse JSON payload
        $data = json_decode($payload, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            status_header(400);
            exit('Invalid JSON');
        }

        // Process events
        if (isset($data['events']) && is_array($data['events'])) {
            foreach ($data['events'] as $event) {
                $this->process_event($event);
            }
        }

        // Return success response
        status_header(200);
        echo json_encode(['status' => 'success']);
        exit;
    }

    /**
     * Process individual webhook event
     */
    private function process_event($event) {
        gcdf()->log("Processing webhook event: {$event['action']} for {$event['resource_type']}");

        try {
            switch ($event['resource_type']) {
                case 'payments':
                    $this->handle_payment_event($event);
                    break;
                    
                case 'mandates':
                    $this->handle_mandate_event($event);
                    break;
                    
                case 'payouts':
                    $this->handle_payout_event($event);
                    break;
                    
                default:
                    gcdf()->log("Unhandled webhook resource type: {$event['resource_type']}");
            }
        } catch (Exception $e) {
            gcdf()->log("Failed to process event {$event['id']}: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Handle payment events
     */
    private function handle_payment_event($event) {
        $payment_id = $event['links']['payment'] ?? '';
        
        if (empty($payment_id)) {
            gcdf()->log('Payment event missing payment ID', 'error');
            return;
        }

        $deposit = gcdf()->get_database()->get_deposit_by_payment_id($payment_id);
        
        if (!$deposit) {
            gcdf()->log("No deposit found for payment {$payment_id}");
            return;
        }

        $action = $event['action'];
        $old_status = $deposit->status;

        switch ($action) {
            case 'created':
                $new_status = 'processing';
                break;
                
            case 'submitted':
                $new_status = 'processing';
                break;
                
            case 'confirmed':
                $new_status = 'completed';
                break;
                
            case 'paid_out':
                $new_status = 'completed';
                break;
                
            case 'cancelled':
                $new_status = 'cancelled';
                break;
                
            case 'failed':
                $new_status = 'failed';
                break;
                
            case 'charged_back':
                $new_status = 'refunded';
                break;
                
            default:
                gcdf()->log("Unhandled payment action: {$action}");
                return;
        }

        // Update deposit status
        $update_data = [
            'gocardless_status' => $action,
            'status' => $new_status
        ];

        // Add error message if failed
        if ($new_status === 'failed' && isset($event['details']['description'])) {
            $update_data['error_message'] = $event['details']['description'];
        }

        gcdf()->get_database()->update_deposit($deposit->id, $update_data);

        gcdf()->log("Updated deposit {$deposit->id} from {$old_status} to {$new_status}");

        // Trigger actions based on status change
        if ($old_status !== $new_status) {
            $this->trigger_status_change_actions($deposit, $old_status, $new_status);
        }
    }

    /**
     * Handle mandate events
     */
    private function handle_mandate_event($event) {
        $mandate_id = $event['links']['mandate'] ?? '';
        $action = $event['action'];

        gcdf()->log("Mandate {$mandate_id} action: {$action}");

        // Find deposits with this mandate
        global $wpdb;
        $deposits_table = $wpdb->prefix . 'gocardless_deposits';
        
        $deposits = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$deposits_table} WHERE mandate_id = %s",
                $mandate_id
            )
        );

        foreach ($deposits as $deposit) {
            switch ($action) {
                case 'cancelled':
                case 'failed':
                case 'expired':
                    if ($deposit->status === 'processing') {
                        gcdf()->get_database()->update_deposit($deposit->id, [
                            'status' => 'failed',
                            'error_message' => "Mandate {$action}: " . ($event['details']['description'] ?? '')
                        ]);
                    }
                    break;
            }
        }
    }

    /**
     * Handle payout events
     */
    private function handle_payout_event($event) {
        // Log payout events for reference
        $payout_id = $event['links']['payout'] ?? '';
        $action = $event['action'];
        
        gcdf()->log("Payout {$payout_id} action: {$action}");
    }

    /**
     * Trigger actions based on status changes
     */
    private function trigger_status_change_actions($deposit, $old_status, $new_status) {
        switch ($new_status) {
            case 'completed':
                $this->handle_deposit_completed($deposit);
                break;
                
            case 'failed':
            case 'cancelled':
                $this->handle_deposit_failed($deposit);
                break;
                
            case 'refunded':
                $this->handle_deposit_refunded($deposit);
                break;
        }

        // Fire general status change hook
        do_action('gcdf_deposit_status_changed', $deposit, $old_status, $new_status);
    }

    /**
     * Handle completed deposit
     */
    private function handle_deposit_completed($deposit) {
        gcdf()->log("Deposit {$deposit->id} completed - triggering auction completion");

        // Fire hook for auction completion
        do_action('gcdf_deposit_completed', $deposit);

        // Send notification email
        $this->send_deposit_notification($deposit, 'completed');
    }

    /**
     * Handle failed deposit
     */
    private function handle_deposit_failed($deposit) {
        gcdf()->log("Deposit {$deposit->id} failed - keeping auction item available");

        // Fire hook for failed deposit
        do_action('gcdf_deposit_failed', $deposit);

        // Send notification email
        $this->send_deposit_notification($deposit, 'failed');
    }

    /**
     * Handle refunded deposit
     */
    private function handle_deposit_refunded($deposit) {
        gcdf()->log("Deposit {$deposit->id} refunded");

        // Fire hook for refunded deposit
        do_action('gcdf_deposit_refunded', $deposit);

        // Send notification email
        $this->send_deposit_notification($deposit, 'refunded');
    }

    /**
     * Send deposit notification email
     */
    private function send_deposit_notification($deposit, $status) {
        $user = get_userdata($deposit->user_id);
        $post = get_post($deposit->post_id);
        
        if (!$user || !$post) {
            return;
        }

        $subject = '';
        $message = '';

        switch ($status) {
            case 'completed':
                $subject = sprintf(__('Deposit Confirmed - %s', 'gocardless-deposit-flow'), $post->post_title);
                $message = sprintf(
                    __('Your deposit of %s %s for "%s" has been confirmed. The item is now secured for you.', 'gocardless-deposit-flow'),
                    $deposit->currency,
                    number_format($deposit->amount, 2),
                    $post->post_title
                );
                break;
                
            case 'failed':
                $subject = sprintf(__('Deposit Failed - %s', 'gocardless-deposit-flow'), $post->post_title);
                $message = sprintf(
                    __('Your deposit payment for "%s" has failed. The item is still available for purchase. Error: %s', 'gocardless-deposit-flow'),
                    $post->post_title,
                    $deposit->error_message ?: __('Payment processing failed', 'gocardless-deposit-flow')
                );
                break;
                
            case 'refunded':
                $subject = sprintf(__('Deposit Refunded - %s', 'gocardless-deposit-flow'), $post->post_title);
                $message = sprintf(
                    __('Your deposit of %s %s for "%s" has been refunded.', 'gocardless-deposit-flow'),
                    $deposit->currency,
                    number_format($deposit->amount, 2),
                    $post->post_title
                );
                break;
        }

        if ($subject && $message) {
            wp_mail($user->user_email, $subject, $message);
            gcdf()->log("Sent {$status} notification email to {$user->user_email}");
        }
    }

    /**
     * Get webhook URL
     */
    public static function get_webhook_url() {
        return home_url('gocardless-webhook/');
    }
}
