<?php
return ['x-generator'=>'GlotPress/4.0.0-beta.2','translation-revision-date'=>'2021-03-23 07:34:00+0000','plural-forms'=>'nplurals=1; plural=0;','project-id-version'=>'Plugins - Elementor Header &amp; Footer Builder - Stable (latest release)','language'=>'zh_CN','messages'=>['Redirect To Self Link'=>'跳转到当前页面','Allow %s products to track non-sensitive usage tracking data.'=>'允许%s产品跟踪非敏感使用情况数据。','Open Submenu'=>'打开子菜单','For Horizontal layout, this will affect on the selected breakpoint'=>'对于水平布局，此设置将影响所选的断点','Action On Menu Click'=>'单击菜单时的操作','Enable Schema Support'=>'启用配色方案支持','Search'=>'搜索','Placeholder'=>'占位符','Placeholder Color'=>'占位符颜色','Install Elementor'=>'安装 Elementor','Focus'=>'聚焦','Input'=>'输入','Type & Hit Enter'=>'输入内容然后按回','Input Box With Button'=>'带按钮的输入框','Input Box'=>'输入框','Search Box'=>'搜索框','<b>Note:</b> Archive page title will be visible on frontend.'=>'<b>注意：</b>存档页标题将在前端显示。','Activate Elementor'=>'激活Elementor','All Templates'=>'所有模板','Add locations for where this template should not appear.'=>'添加不应显示此模板的位置。','Learn More.'=>'了解更多。',' This will be applicable for all sites from the network.'=>' 这将适用于网络中的所有站点。','Usage Tracking'=>'使用追踪','No Thanks'=>'不用了，谢谢','Yes! Allow it'=>'是的！允许',' Know More.'=>' 了解更多。','This will be applicable for all sites from the network.'=>'这将适用于网络中的所有站点。','Want to help make <strong>%1s</strong> even more awesome? Allow us to collect non-sensitive diagnostic data and usage information. '=>'希望帮助<strong>%1s </strong>变得更加出色吗？允许我们收集非敏感的诊断数据和使用信息。 ','This powerful plugin allows creating a custom header, footer with Elementor and display them on selected locations. You can also create custom Elementor blocks and place them anywhere on the website with a shortcode.'=>'这个强大的插件允许您使用 Elementor 创建一个自定义页头，页脚，并在指定的位置显示他们，另外，您还可以创建自定义内容区块，并使用短代码把他们显示在网站的任何位置。','Button'=>'按钮','Show Total Price'=>'显示总价','Border Hover Color'=>'边框悬停颜色','Border'=>'边框','Typography'=>'排版','Last Menu Item'=>'最后一个菜单项','Distance'=>'距离','This will hide the items count until the cart is empty'=>'这将隐藏物品计数，直到购物车中的物品被清空','Hide Empty'=>'隐藏空值','Bubble'=>'气泡','Items Count'=>'项目数量','Bag Solid'=>'Bag Solid','Bag Medium'=>'Bag Medium','Bag Light'=>'Bag Light','Custom'=>'自定义','Type'=>'类型','Menu Cart'=>'菜单购物车','Cart'=>'购物车','XXL'=>'XXL','XL'=>'XL','Add Image'=>'添加图像','Default'=>'默认','H6'=>'H6','H5'=>'H5','H4'=>'H4','H3'=>'H3','H2'=>'H2','H1'=>'H1','HTML Tag'=>'HTML标签','Select Icon'=>'选择图标','Menu Icon'=>'菜单图标','Menu'=>'菜单','Main Menu'=>'主菜单','Full Width'=>'全宽','Color'=>'颜色','Close Icon'=>'关闭图标','Classic'=>'经典','Icon'=>'图标','Icon Color'=>'图标颜色','Icon Size'=>'图标大小','Icon Hover Color'=>'图标悬停颜色','Dropdown'=>'下拉菜单','Media File'=>'媒体文件','Submenu Icon'=>'子菜单图标','Submenu Animation'=>'子菜单动画','View'=>'查看','Title'=>'标题','Custom Image'=>'自定义图像','After Title Text'=>'标题文字之后','<strong>There are no menus in your site.</strong><br>Go to the <a href="%s" target="_blank">Menus screen</a> to create one.'=>'<strong>您的网站中没有菜单。</strong> <br>转到<a href="%s" target="_blank">“菜单”屏幕</a>以创建一个菜单。','<b>Note:</b> On desktop, below style options will apply to the submenu. On mobile, this will apply to the entire menu.'=>'<b>注意：</b>在桌面端，以下样式选项将应用于子菜单。 在移动设备上，这将应用于整个菜单。','Before Title Text'=>'标题文字之前','General'=>'常规','Site Title'=>'站点标题','Spacing'=>'间距','Lightbox'=>'灯箱','Enter caption'=>'输入标题','Style'=>'样式','Site Tagline'=>'站点副标题','Close Icon Size'=>'关闭图标大小','Close Icon Color'=>'关闭图标颜色','Menu Trigger & Close Icon'=>'菜单触发器和关闭图标','Divider'=>'分隔线','Top Distance'=>'最远距离','Dropdown Width (px)'=>'下拉宽度 (px)','Active'=>'启用','Link Hover Effect Color'=>'链接悬停效果颜色','Frame Animation'=>'帧动画','Animation'=>'动画','Text'=>'文本','Framed'=>'框架','Double Line'=>'双线','Overline'=>'排版','Underline'=>'下划线','Link Hover Effect'=>'链接悬停效果','Menu Item Top Spacing'=>'菜单项顶部间距','Row Spacing'=>'行间距','Space Between'=>'之间的间距','Vertical Padding'=>'垂直间距','Horizontal Padding'=>'水平内边距','Flyout Box Padding'=>'弹出框填充','Flyout Box Width'=>'弹出框宽度','No'=>'否','Yes'=>'是','Enable this option to stretch the Sub Menu to Full Width.'=>'启用此选项可将子菜单拉伸到全宽。','This is the alignement of menu icon on selected responsive breakpoints.'=>'这是所选响应断点的菜单图标的对齐方式。','Tablet (1025px >)'=>'平板电脑 (1025px >)','Mobile (768px >)'=>'手机 (768px >)','Breakpoint'=>'断点','Responsive'=>'自适应','Slide Up'=>'向上滑动','Plus Sign'=>'加号','Arrows'=>'箭头','Menu Items Align'=>'菜单项对齐','Hamburger Align'=>'汉堡菜单对齐','Push'=>'推出','Slide'=>'幻灯','Appear Effect'=>'显示效果','Flyout Orientation'=>'飞出方向','Justify'=>'两端对齐','Flyout'=>'飞出','Expanded'=>'展开','Vertical'=>'垂直','Horizontal'=>'水平','Layout'=>'布局','Go to the <a href="%s" target="_blank">Menus screen</a> to manage your menus.'=>'转到<a href="%s" target="_blank">菜单屏幕</a>以管理您的菜单。','Navigation Menu'=>'导航菜单','Blend Mode'=>'混合模式','Justified'=>'两端对齐','Large'=>'大号','Medium'=>'中等','Small'=>'小号','Size'=>'尺寸','Icon Spacing'=>'图标间距','Page Title'=>'页面标题','Site logo'=>'站点徽标','Site Logo'=>'站点徽标','Width'=>'宽度','Background Color'=>'背景颜色','https://your-link.com'=>'https://your-link.com','Settings'=>'设置','Select'=>'选择','Please enter'=>'请输入','Please delete'=>'请删除','Max Width'=>'最大宽度','Logged Out'=>'注销','Nope, maybe later'=>'不，以后再说','Front Page'=>'首页','Date'=>'日期','Image Size'=>'图像尺寸','Choose Default Image'=>'选择默认图像','Choose Retina Image'=>'选择视网膜图像','Logged In'=>'已登录','Loading more results…'=>'正在加载更多结果…','WooCommerce Shop Page'=>'WooCommerce商店页面','User Roles'=>'用户角色','Retina Image'=>'视网膜图像','Custom URL'=>'自定义 URL','404 Page'=>'404页','Add User Rule'=>'添加用户规则','Users'=>'用户','Display custom template based on user role.'=>'根据用户角色显示自定义模板。','Exclude On'=>'排除在','Do Not Display On'=>'不显示','Add Display Rule'=>'添加显示规则','Add locations for where this template should appear.'=>'添加此模板应出现的位置。','Display On'=>'显示在','Display Rules'=>'显示规则','Theme Support'=>'主题支持','Sometimes above methods might not work well with your theme, in this case, contact your theme author and request them to add support for the <a href="https://github.com/Nikschavan/header-footer-elementor/wiki/Adding-Header-Footer-Elementor-support-for-your-theme">plugin.</>'=>'有时候，上面的方法可能无法很好的兼容您的主题，这种情况下，请联系您的主题作者，并请求它们添加对<a href="https://github.com/Nikschavan/header-footer-elementor/wiki/Adding-Header-Footer-Elementor-support-for-your-theme">插件</a>的支持','Method 2'=>'方法二','This method replaces your theme\'s header (header.php) & footer (footer.php) template with plugin\'s custom templates.'=>'此方法用插件的自定义模板替换主题的页眉（header.php）和页脚（footer.php）模板。',' Method 1 (Recommended)'=>' 方法1（建议）','Add Theme Support'=>'添加主题支持','The same display setting is already exist in %s post/s.'=>'相同的显示设置已经存在于 %s 文章。','Add Exclusion Rule'=>'添加排除规则','All %s Archive'=>'所有 %s 归档','All %s'=>'所有 %s','Add Rule'=>'添加规则','Search pages / post / categories'=>'搜索页面/文章/类别','The results could not be loaded.'=>'结果无法加载。','Searching…'=>'正在搜索…','No results found'=>'没有找到结果','s'=>'秒','item'=>'项','You can only select'=>'您只能选择','character'=>'字符','or more characters'=>'或更多字符','Advanced'=>'高级','All'=>'所有','Specific Pages / Posts / Taxonomies, etc.'=>'指定的页面/文章/分类等。','Specific Target'=>'特定目标','Special Pages'=>'特殊页面','All Archives'=>'所有归档','All Singulars'=>'所有详情页','Entire Website'=>'整个网站','Basic'=>'基本','Author Archive'=>'作者存档','Date Archive'=>'日期存档页','Blog / Posts Page'=>'博客/文章页面','Search Page'=>'搜索结果页面','WordPress Nonce not validated.'=>'WordPress 随机数没有通过验证。','%1$s Getting started article » %2$s'=>'%1$s 入门文章» %2$s','Helpful Information'=>'有用的信息','Caption Top Spacing'=>'标题顶部间距','Padding'=>'内距','Transition Duration'=>'过渡时间','Hover Animation'=>'悬停动画','Hover'=>'悬停','Opacity'=>'不透明度','Normal'=>'常规','Border Radius'=>'边框半径','Border Color'=>'边框颜色','Border Width'=>'边框宽度','Dashed'=>'虚线','Dotted'=>'圆点','Double'=>'双实线','Solid'=>'实线','Border Style'=>'边框样式','Enter your image caption'=>'输入您的图像标题','Custom Caption'=>'自定义标题','None'=>'不显示','Caption'=>'字幕','Text Color'=>'文字颜色','Right'=>'靠右','Center'=>'居中','Left'=>'靠左','Alignment'=>'对齐','Link'=>'链接','Copyright © [hfe_current_year] [hfe_site_title] | Powered by [hfe_site_title]'=>'版权© [hfe_current_year] [hfe_site_title]  |由 [hfe_site_title] 技术支持','Copyright Text'=>'版权文本','Copyright'=>'版权','I already did'=>'我已经做了','Ok, you deserve it'=>'好的，这是你应得的','Copy this shortcode and paste it into your post, page, or text widget content.'=>'复制此短代码并将其粘贴到您的文章、页面或文本小工具内容中。','Shortcode'=>'短代码','Custom Block'=>'自定义内容块','Before Footer'=>'页脚之前','Type of Template'=>'模板类型','Select Option'=>'选择选项','Footer'=>'页脚','Header'=>'页头','Enabling this option will display this layout on pages using Elementor Canvas Template.'=>'启用此选项将使用Elementor画布模板在页面上显示此布局。','Enable Layout for Elementor Canvas Template?'=>'为Elementor画布模板启用布局?','Template %1$s is already assigned to the location %2$s'=>'模板 %1$s 已经分配到该位置 %2$s','https://www.brainstormforce.com/'=>'https://www.brainstormforce.com/','https://github.com/Nikschavan/header-footer-elementor'=>'https://github.com/Nikschavan/header-footer-elementor','Brainstorm Force, Nikhil Chavan'=>'Brainstorm Force, Nikhil Chavan','Add New'=>'添加']];