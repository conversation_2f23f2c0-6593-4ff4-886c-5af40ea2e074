# Translation of Themes - Twenty Fifteen in English (UK)
# This file is distributed under the same license as the Themes - Twenty Fifteen package.
msgid ""
msgstr ""
"PO-Revision-Date: 2020-03-31 21:28:55+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Twenty Fifteen\n"

#. Description of the theme
msgid "Our 2015 default theme is clean, blog-focused, and designed for clarity. Twenty Fifteen's simple, straightforward typography is readable on a wide variety of screen sizes, and suitable for multiple languages. We designed it using a mobile-first approach, meaning your content takes center-stage, regardless of whether your visitors arrive by smartphone, tablet, laptop, or desktop computer."
msgstr "Our 2015 default theme is clean, blog-focused, and designed for clarity. Twenty Fifteen's simple, straightforward typography is readable on a wide variety of screen sizes, and suitable for multiple languages. We designed it using a mobile-first approach, meaning your content takes centre-stage, regardless of whether your visitors arrive by smartphone, tablet, laptop, or desktop computer."

#. Theme Name of the theme
msgid "Twenty Fifteen"
msgstr "Twenty Fifteen"

#: functions.php:245
msgid "Light Blue"
msgstr "Light Blue"

#: functions.php:240
msgid "Bright Blue"
msgstr "Bright Blue"

#: functions.php:195
msgid "Light Gray"
msgstr "Light Grey"

#: functions.php:190
msgid "Dark Gray"
msgstr "Dark Grey"

#: functions.php:210
msgid "Dark Brown"
msgstr "Dark Brown"

#: functions.php:200
msgid "White"
msgstr "White"

#: functions.php:220
msgid "Light Pink"
msgstr "Light Pink"

#: functions.php:215
msgid "Medium Pink"
msgstr "Medium Pink"

#: functions.php:235
msgid "Blue Gray"
msgstr "Blue Grey"

#: functions.php:225
msgid "Dark Purple"
msgstr "Dark Purple"

#. translators: %s: Post title.
#: comments.php:31
msgctxt "comments title"
msgid "One thought on &ldquo;%s&rdquo;"
msgstr "One thought on &ldquo;%s&rdquo;"

#. translators: %s: Post title.
#: inc/template-tags.php:132
msgid "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"

#: single.php:39
msgid "Previous post:"
msgstr "Previous post:"

#: single.php:38
msgid "Previous"
msgstr "Previous"

#: single.php:36
msgid "Next post:"
msgstr "Next post:"

#: single.php:35
msgid "Next"
msgstr "Next"

#. translators: %s: Search query.
#: search.php:21
msgid "Search Results for: %s"
msgstr "Search Results for: %s"

#: inc/template-tags.php:122
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr "Full size"

#: inc/template-tags.php:110
msgctxt "Used before tag names."
msgid "Tags"
msgstr "Tags"

#: inc/template-tags.php:101
msgctxt "Used before category names."
msgid "Categories"
msgstr "Categories"

#: inc/template-tags.php:97 inc/template-tags.php:106
msgctxt "Used between list items, there is a space after the comma."
msgid ", "
msgstr ", "

#: inc/template-tags.php:91
msgctxt "Used before post author name."
msgid "Author"
msgstr "Author"

#: inc/template-tags.php:81
msgctxt "Used before publish date."
msgid "Posted on"
msgstr "Posted on"

#: inc/template-tags.php:58
msgctxt "Used before post format."
msgid "Format"
msgstr "Format"

#: inc/template-tags.php:51
msgid "Featured"
msgstr "Featured"

#: inc/template-tags.php:31
msgid "Newer Comments"
msgstr "Newer Comments"

#: inc/template-tags.php:26
msgid "Older Comments"
msgstr "Older Comments"

#: inc/template-tags.php:23
msgid "Comment navigation"
msgstr "Comment navigation"

#: inc/customizer.php:237
msgid "Blue"
msgstr "Blue"

#: functions.php:230 inc/customizer.php:226
msgid "Purple"
msgstr "Purple"

#: inc/customizer.php:215
msgid "Pink"
msgstr "Pink"

#: functions.php:205 inc/customizer.php:204
msgid "Yellow"
msgstr "Yellow"

#: inc/customizer.php:193
msgid "Dark"
msgstr "Dark"

#: inc/customizer.php:182
msgid "Default"
msgstr "Default"

#: inc/customizer.php:103
msgid "Header and Sidebar Background Color"
msgstr "Header and sidebar background colour"

#: inc/customizer.php:79 inc/customizer.php:104 inc/customizer.php:111
msgid "Applied to the header on small screens and the sidebar on wide screens."
msgstr "Applied to the header on small screens and the sidebar on wide screens."

#: inc/customizer.php:78
msgid "Header and Sidebar Text Color"
msgstr "Header and sidebar text colour"

#: inc/customizer.php:55
msgid "Base Color Scheme"
msgstr "Base colour scheme"

#. translators: %s: WordPress version.
#: inc/back-compat.php:38 inc/back-compat.php:50 inc/back-compat.php:67
msgid "Twenty Fifteen requires at least WordPress version 4.1. You are running version %s. Please upgrade and try again."
msgstr "Twenty Fifteen requires at least WordPress version 4.1. You are running version %s. Please upgrade and try again."

#: image.php:88
msgctxt "Parent post link"
msgid "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%title</span>"
msgstr "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%title</span>"

#: image.php:25
msgid "Next Image"
msgstr "Next Image"

#: image.php:25
msgid "Previous Image"
msgstr "Previous Image"

#: header.php:50
msgid "Menu and widgets"
msgstr "Menu and widgets"

#: header.php:27
msgid "Skip to content"
msgstr "Skip to content"

#: functions.php:402
msgid "collapse child menu"
msgstr "collapse child menu"

#: functions.php:401
msgid "expand child menu"
msgstr "expand child menu"

#: functions.php:321
msgctxt "Add new subset (greek, cyrillic, devanagari, vietnamese)"
msgid "no-subset"
msgstr "no-subset"

#: functions.php:313
msgctxt "Inconsolata font: on or off"
msgid "on"
msgstr "on"

#: functions.php:305
msgctxt "Noto Serif font: on or off"
msgid "on"
msgstr "on"

#: functions.php:297
msgctxt "Noto Sans font: on or off"
msgid "on"
msgstr "on"

#: functions.php:270
msgid "Add widgets here to appear in your sidebar."
msgstr "Add widgets here to appear in your sidebar."

#: functions.php:268
msgid "Widget Area"
msgstr "Widget Area"

#: functions.php:87
msgid "Social Links Menu"
msgstr "Social Links Menu"

#: functions.php:86
msgid "Primary Menu"
msgstr "Primary Menu"

#. translators: %s: WordPress
#: footer.php:33
msgid "Proudly powered by %s"
msgstr "Proudly powered by %s"

#: content-none.php:36
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."

#: content-none.php:31
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Sorry, but nothing matched your search terms. Please try again with some different keywords."

#. translators: %s: Post editor URL.
#: content-none.php:25
msgid "Ready to publish your first post? <a href=\"%s\">Get started here</a>."
msgstr "Ready to publish your first post? <a href=\"%s\">Get started here</a>."

#: content-none.php:15
msgid "Nothing Found"
msgstr "Nothing Found"

#: content-link.php:60 content.php:61 image.php:74 content-search.php:28
#: content-search.php:33 content-page.php:37
msgid "Edit"
msgstr "Edit"

#: content-link.php:39 content.php:41 image.php:61 content-page.php:26
msgid "Pages:"
msgstr "Pages:"

#. translators: %s: Post title.
#: content-link.php:32 content.php:34 inc/template-tags.php:252
msgid "Continue reading %s"
msgstr "Continue reading %s"

#: comments.php:71
msgid "Comments are closed."
msgstr "Comments are closed."

#. translators: 1: Number of comments, 2: Post title.
#: comments.php:35
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] "%1$s thought on &ldquo;%2$s&rdquo;"
msgstr[1] "%1$s thoughts on &ldquo;%2$s&rdquo;"

#. translators: %s: Author display name.
#: author-bio.php:36
msgid "View all posts by %s"
msgstr "View all posts by %s"

#: author-bio.php:12
msgid "Published by"
msgstr "Published by"

#: content-link.php:43 index.php:50 content.php:45 archive.php:53 search.php:48
#: image.php:65 content-page.php:30
msgid "Page"
msgstr "Page"

#: index.php:49 archive.php:52 search.php:47
msgid "Next page"
msgstr "Next page"

#: index.php:48 archive.php:51 search.php:46
msgid "Previous page"
msgstr "Previous page"

#: 404.php:21
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "It looks like nothing was found at this location. Maybe try a search?"

#: 404.php:17
msgid "Oops! That page can&rsquo;t be found."
msgstr "Oops! That page can&rsquo;t be found."

#. Author of the theme
msgid "the WordPress team"
msgstr "the WordPress team"

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentyfifteen/"
msgstr "https://wordpress.org/themes/twentyfifteen/"

#. Author URI of the theme
#: footer.php:30
msgid "https://wordpress.org/"
msgstr "https://wordpress.org/"