/*! This file is auto-generated */
!function(c,l,m){"use strict";function u(e){return(e=(e=l.sanitize.stripTagsAndEncodeText(e=e||"")).toString().trim())||c.Menus.data.l10n.unnamed}wpNavMenu.originalInit=wpNavMenu.init,wpNavMenu.options.menuItemDepthPerLevel=20,wpNavMenu.options.sortableItems="> .customize-control-nav_menu_item",wpNavMenu.options.targetTolerance=10,wpNavMenu.init=function(){this.jQueryExtensions()},c.Menus=c.Menus||{},c.Menus.data={itemTypes:[],l10n:{},settingTransport:"refresh",phpIntMax:0,defaultSettingValues:{nav_menu:{},nav_menu_item:{}},locationSlugMappedToName:{}},"undefined"!=typeof _wpCustomizeNavMenusSettings&&m.extend(c.Menus.data,_wpCustomizeNavMenusSettings),c.Menus.generatePlaceholderAutoIncrementId=function(){return-Math.ceil(c.Menus.data.phpIntMax*Math.random())},c.Menus.AvailableItemModel=Backbone.Model.extend(m.extend({id:null},c.Menus.data.defaultSettingValues.nav_menu_item)),c.Menus.AvailableItemCollection=Backbone.Collection.extend({model:c.Menus.AvailableItemModel,sort_key:"order",comparator:function(e){return-e.get(this.sort_key)},sortByField:function(e){this.sort_key=e,this.sort()}}),c.Menus.availableMenuItems=new c.Menus.AvailableItemCollection(c.Menus.data.availableMenuItems),c.Menus.insertAutoDraftPost=function(n){var i=m.Deferred(),e=l.ajax.post("customize-nav-menus-insert-auto-draft",{"customize-menus-nonce":c.settings.nonce["customize-menus"],wp_customize:"on",customize_changeset_uuid:c.settings.changeset.uuid,params:n});return e.done(function(t){t.post_id&&(c("nav_menus_created_posts").set(c("nav_menus_created_posts").get().concat([t.post_id])),"page"===n.post_type&&(c.section.has("static_front_page")&&c.section("static_front_page").activate(),c.control.each(function(e){"dropdown-pages"===e.params.type&&e.container.find('select[name^="_customize-dropdown-pages-"]').append(new Option(n.post_title,t.post_id))})),i.resolve(t))}),e.fail(function(e){var t=e||"";void 0!==e.message&&(t=e.message),console.error(t),i.rejectWith(t)}),i.promise()},c.Menus.AvailableMenuItemsPanelView=l.Backbone.View.extend({el:"#available-menu-items",events:{"input #menu-items-search":"debounceSearch","focus .menu-item-tpl":"focus","click .menu-item-tpl":"_submit","click #custom-menu-item-submit":"_submitLink","keypress #custom-menu-item-name":"_submitLink","click .new-content-item .add-content":"_submitNew","keypress .create-item-input":"_submitNew",keydown:"keyboardAccessible"},selected:null,currentMenuControl:null,debounceSearch:null,$search:null,$clearResults:null,searchTerm:"",rendered:!1,pages:{},sectionContent:"",loading:!1,addingNew:!1,initialize:function(){var n=this;c.panel.has("nav_menus")&&(this.$search=m("#menu-items-search"),this.$clearResults=this.$el.find(".clear-results"),this.sectionContent=this.$el.find(".available-menu-items-list"),this.debounceSearch=_.debounce(n.search,500),_.bindAll(this,"close"),m("#customize-controls, .customize-section-back").on("click keydown",function(e){var t=m(e.target).is(".item-delete, .item-delete *"),e=m(e.target).is(".add-new-menu-item, .add-new-menu-item *");!m("body").hasClass("adding-menu-items")||t||e||n.close()}),this.$clearResults.on("click",function(){n.$search.val("").trigger("focus").trigger("input")}),this.$el.on("input","#custom-menu-item-name.invalid, #custom-menu-item-url.invalid",function(){m(this).removeClass("invalid")}),c.panel("nav_menus").container.on("expanded",function(){n.rendered||(n.initList(),n.rendered=!0)}),this.sectionContent.on("scroll",function(){var e=n.$el.find(".accordion-section.open .available-menu-items-list").prop("scrollHeight"),t=n.$el.find(".accordion-section.open").height();!n.loading&&m(this).scrollTop()>.75*e-t&&(e=m(this).data("type"),t=m(this).data("object"),"search"===e?n.searchTerm&&n.doSearch(n.pages.search):n.loadItems([{type:e,object:t}]))}),c.previewer.bind("url",this.close),n.delegateEvents())},search:function(e){var t=m("#available-menu-items-search"),n=m("#available-menu-items .accordion-section").not(t);e&&this.searchTerm!==e.target.value&&(""===e.target.value||t.hasClass("open")?""===e.target.value&&(t.removeClass("open"),n.show(),this.$clearResults.removeClass("is-visible")):(n.fadeOut(100),t.find(".accordion-section-content").slideDown("fast"),t.addClass("open"),this.$clearResults.addClass("is-visible")),this.searchTerm=e.target.value,this.pages.search=1,this.doSearch(1))},doSearch:function(t){var e,n=this,i=m("#available-menu-items-search"),a=i.find(".accordion-section-content"),o=l.template("available-menu-item");if(n.currentRequest&&n.currentRequest.abort(),!(t<0)){if(1<t)i.addClass("loading-more"),a.attr("aria-busy","true"),l.a11y.speak(c.Menus.data.l10n.itemsLoadingMore);else if(""===n.searchTerm)return a.html(""),void l.a11y.speak("");i.addClass("loading"),n.loading=!0,e=c.previewer.query({excludeCustomizedSaved:!0}),_.extend(e,{"customize-menus-nonce":c.settings.nonce["customize-menus"],wp_customize:"on",search:n.searchTerm,page:t}),n.currentRequest=l.ajax.post("search-available-menu-items-customizer",e),n.currentRequest.done(function(e){1===t&&a.empty(),i.removeClass("loading loading-more"),a.attr("aria-busy","false"),i.addClass("open"),n.loading=!1,e=new c.Menus.AvailableItemCollection(e.items),n.collection.add(e.models),e.each(function(e){a.append(o(e.attributes))}),e.length<20?n.pages.search=-1:n.pages.search=n.pages.search+1,e&&1<t?l.a11y.speak(c.Menus.data.l10n.itemsFoundMore.replace("%d",e.length)):e&&1===t&&l.a11y.speak(c.Menus.data.l10n.itemsFound.replace("%d",e.length))}),n.currentRequest.fail(function(e){e.message&&(a.empty().append(m('<li class="nothing-found"></li>').text(e.message)),l.a11y.speak(e.message)),n.pages.search=-1}),n.currentRequest.always(function(){i.removeClass("loading loading-more"),a.attr("aria-busy","false"),n.loading=!1,n.currentRequest=null})}},initList:function(){var t=this;_.each(c.Menus.data.itemTypes,function(e){t.pages[e.type+":"+e.object]=0}),t.loadItems(c.Menus.data.itemTypes)},loadItems:function(e,t){var i=this,a=[],o={},s=l.template("available-menu-item"),t=_.isString(e)&&_.isString(t)?[{type:e,object:t}]:e;_.each(t,function(e){var t,n=e.type+":"+e.object;-1!==i.pages[n]&&((t=m("#available-menu-items-"+e.type+"-"+e.object)).find(".accordion-section-title").addClass("loading"),o[n]=t,a.push({object:e.object,type:e.type,page:i.pages[n]}))}),0!==a.length&&(i.loading=!0,e=c.previewer.query({excludeCustomizedSaved:!0}),_.extend(e,{"customize-menus-nonce":c.settings.nonce["customize-menus"],wp_customize:"on",item_types:a}),(t=l.ajax.post("load-available-menu-items-customizer",e)).done(function(e){var n;_.each(e.items,function(e,t){0===e.length?(0===i.pages[t]&&o[t].find(".accordion-section-title").addClass("cannot-expand").removeClass("loading").find(".accordion-section-title > button").prop("tabIndex",-1),i.pages[t]=-1):("post_type:page"!==t||o[t].hasClass("open")||o[t].find(".accordion-section-title > button").trigger("click"),e=new c.Menus.AvailableItemCollection(e),i.collection.add(e.models),n=o[t].find(".available-menu-items-list"),e.each(function(e){n.append(s(e.attributes))}),i.pages[t]+=1)})}),t.fail(function(e){"undefined"!=typeof console&&console.error&&console.error(e)}),t.always(function(){_.each(o,function(e){e.find(".accordion-section-title").removeClass("loading")}),i.loading=!1}))},itemSectionHeight:function(){var e=window.innerHeight,t=this.$el.find(".accordion-section:not( #available-menu-items-search ) .accordion-section-content"),n=this.$el.find('.accordion-section:not( #available-menu-items-search ) .available-menu-items-list:not(":only-child")'),e=e-(46*(1+t.length)+14);120<e&&e<290&&(t.css("max-height",e),n.css("max-height",e-60))},select:function(e){this.selected=m(e),this.selected.siblings(".menu-item-tpl").removeClass("selected"),this.selected.addClass("selected")},focus:function(e){this.select(m(e.currentTarget))},_submit:function(e){"keypress"===e.type&&13!==e.which&&32!==e.which||this.submit(m(e.currentTarget))},submit:function(e){var t;(e=e||this.selected)&&this.currentMenuControl&&(this.select(e),t=m(this.selected).data("menu-item-id"),t=this.collection.findWhere({id:t}))&&(this.currentMenuControl.addItemToMenu(t.attributes),m(e).find(".menu-item-handle").addClass("item-added"))},_submitLink:function(e){"keypress"===e.type&&13!==e.which||this.submitLink()},submitLink:function(){var e,t=m("#custom-menu-item-name"),n=m("#custom-menu-item-url"),i=n.val().trim();this.currentMenuControl&&(e=/^((\w+:)?\/\/\w.*|\w+:(?!\/\/$)|\/|\?|#)/,""===t.val()?t.addClass("invalid"):e.test(i)?(e={title:t.val(),url:i,type:"custom",type_label:c.Menus.data.l10n.custom_label,object:"custom"},this.currentMenuControl.addItemToMenu(e),n.val("").attr("placeholder","https://"),t.val("")):n.addClass("invalid"))},_submitNew:function(e){"keypress"===e.type&&13!==e.which||this.addingNew||(e=m(e.target).closest(".accordion-section"),this.submitNew(e))},submitNew:function(n){var i=this,a=n.find(".create-item-input"),e=a.val(),t=n.find(".available-menu-items-list"),o=t.data("type"),s=t.data("object"),r=t.data("type_label");this.currentMenuControl&&"post_type"===o&&(""===a.val().trim()?(a.addClass("invalid"),a.focus()):(a.removeClass("invalid"),n.find(".accordion-section-title").addClass("loading"),i.addingNew=!0,a.attr("disabled","disabled"),c.Menus.insertAutoDraftPost({post_title:e,post_type:s}).done(function(e){var t,e=new c.Menus.AvailableItemModel({id:"post-"+e.post_id,title:a.val(),type:o,type_label:r,object:s,object_id:e.post_id,url:e.url});i.currentMenuControl.addItemToMenu(e.attributes),c.Menus.availableMenuItemsPanel.collection.add(e),t=n.find(".available-menu-items-list"),(e=m(l.template("available-menu-item")(e.attributes))).find(".menu-item-handle:first").addClass("item-added"),t.prepend(e),t.scrollTop(),a.val("").removeAttr("disabled"),i.addingNew=!1,n.find(".accordion-section-title").removeClass("loading")})))},open:function(e){var t,n=this;this.currentMenuControl=e,this.itemSectionHeight(),c.section.has("publish_settings")&&c.section("publish_settings").collapse(),m("body").addClass("adding-menu-items"),t=function(){n.close(),m(this).off("click",t)},m("#customize-preview").on("click",t),_(this.currentMenuControl.getMenuItemControls()).each(function(e){e.collapseForm()}),this.$el.find(".selected").removeClass("selected"),this.$search.trigger("focus")},close:function(e){(e=e||{}).returnFocus&&this.currentMenuControl&&this.currentMenuControl.container.find(".add-new-menu-item").focus(),this.currentMenuControl=null,this.selected=null,m("body").removeClass("adding-menu-items"),m("#available-menu-items .menu-item-handle.item-added").removeClass("item-added"),this.$search.val("").trigger("input")},keyboardAccessible:function(e){var t=13===e.which,n=27===e.which,i=9===e.which&&e.shiftKey,a=m(e.target).is(this.$search);t&&!this.$search.val()||(a&&i?(this.currentMenuControl.container.find(".add-new-menu-item").focus(),e.preventDefault()):n&&this.close({returnFocus:!0}))}}),c.Menus.MenusPanel=c.Panel.extend({attachEvents:function(){c.Panel.prototype.attachEvents.call(this);var t=this.container.find(".panel-meta"),n=t.find(".customize-help-toggle"),i=t.find(".customize-panel-description"),a=m("#screen-options-wrap"),o=t.find(".customize-screen-options-toggle");o.on("click keydown",function(e){if(!c.utils.isKeydownButNotEnterEvent(e))return e.preventDefault(),i.not(":hidden")&&(i.slideUp("fast"),n.attr("aria-expanded","false")),"true"===o.attr("aria-expanded")?(o.attr("aria-expanded","false"),t.removeClass("open"),t.removeClass("active-menu-screen-options"),a.slideUp("fast")):(o.attr("aria-expanded","true"),t.addClass("open"),t.addClass("active-menu-screen-options"),a.slideDown("fast")),!1}),n.on("click keydown",function(e){c.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),"true"===o.attr("aria-expanded")&&(o.attr("aria-expanded","false"),n.attr("aria-expanded","true"),t.addClass("open"),t.removeClass("active-menu-screen-options"),a.slideUp("fast"),i.slideDown("fast")))})},ready:function(){var e=this;e.container.find(".hide-column-tog").on("click",function(){e.saveManageColumnsState()}),c.section("menu_locations",function(e){e.headContainer.prepend(l.template("nav-menu-locations-header")(c.Menus.data))})},saveManageColumnsState:_.debounce(function(){var e=this;e._updateHiddenColumnsRequest&&e._updateHiddenColumnsRequest.abort(),e._updateHiddenColumnsRequest=l.ajax.post("hidden-columns",{hidden:e.hidden(),screenoptionnonce:m("#screenoptionnonce").val(),page:"nav-menus"}),e._updateHiddenColumnsRequest.always(function(){e._updateHiddenColumnsRequest=null})},2e3),checked:function(){},unchecked:function(){},hidden:function(){return m(".hide-column-tog").not(":checked").map(function(){var e=this.id;return e.substring(0,e.length-5)}).get().join(",")}}),c.Menus.MenuSection=c.Section.extend({initialize:function(e,t){c.Section.prototype.initialize.call(this,e,t),this.deferred.initSortables=m.Deferred()},ready:function(){var e,t,n=this;if(void 0===n.params.menu_id)throw new Error("params.menu_id was not defined");n.active.validate=function(){return!!c.has(n.id)&&!!c(n.id).get()},n.populateControls(),n.navMenuLocationSettings={},n.assignedLocations=new c.Value([]),c.each(function(e,t){t=t.match(/^nav_menu_locations\[(.+?)]/);t&&(n.navMenuLocationSettings[t[1]]=e).bind(function(){n.refreshAssignedLocations()})}),n.assignedLocations.bind(function(e){n.updateAssignedLocationsInSectionTitle(e)}),n.refreshAssignedLocations(),c.bind("pane-contents-reflowed",function(){n.contentContainer.parent().length&&(n.container.find(".menu-item .menu-item-reorder-nav button").attr({tabindex:"0","aria-hidden":"false"}),n.container.find(".menu-item.move-up-disabled .menus-move-up").attr({tabindex:"-1","aria-hidden":"true"}),n.container.find(".menu-item.move-down-disabled .menus-move-down").attr({tabindex:"-1","aria-hidden":"true"}),n.container.find(".menu-item.move-left-disabled .menus-move-left").attr({tabindex:"-1","aria-hidden":"true"}),n.container.find(".menu-item.move-right-disabled .menus-move-right").attr({tabindex:"-1","aria-hidden":"true"}))}),t=function(){var e="field-"+m(this).val()+"-active";n.contentContainer.toggleClass(e,m(this).prop("checked"))},(e=c.panel("nav_menus").contentContainer.find(".metabox-prefs:first").find(".hide-column-tog")).each(t),e.on("click",t)},populateControls:function(){var e,t=this,n=t.id+"[name]",i=c.control(n);i||(i=new c.controlConstructor.nav_menu_name(n,{type:"nav_menu_name",label:c.Menus.data.l10n.menuNameLabel,section:t.id,priority:0,settings:{default:t.id}}),c.control.add(i),i.active.set(!0)),(n=c.control(t.id))||(n=new c.controlConstructor.nav_menu(t.id,{type:"nav_menu",section:t.id,priority:998,settings:{default:t.id},menu_id:t.params.menu_id}),c.control.add(n),n.active.set(!0)),i=t.id+"[locations]",c.control(i)||(i=new c.controlConstructor.nav_menu_locations(i,{section:t.id,priority:999,settings:{default:t.id},menu_id:t.params.menu_id}),c.control.add(i.id,i),n.active.set(!0)),i=t.id+"[auto_add]",(n=c.control(i))||(n=new c.controlConstructor.nav_menu_auto_add(i,{type:"nav_menu_auto_add",label:"",section:t.id,priority:1e3,settings:{default:t.id}}),c.control.add(n),n.active.set(!0)),i=t.id+"[delete]",(e=c.control(i))||(e=new c.Control(i,{section:t.id,priority:1001,templateId:"nav-menu-delete-button"}),c.control.add(e.id,e),e.active.set(!0),e.deferred.embedded.done(function(){e.container.find("button").on("click",function(){var e=t.params.menu_id;c.Menus.getMenuControl(e).setting.set(!1)})}))},refreshAssignedLocations:function(){var n=this.params.menu_id,i=[];_.each(this.navMenuLocationSettings,function(e,t){e()===n&&i.push(t)}),this.assignedLocations.set(i)},updateAssignedLocationsInSectionTitle:function(e){var n=this.container.find(".accordion-section-title:first");n.find(".menu-in-location").remove(),_.each(e,function(e){var t=m('<span class="menu-in-location"></span>'),e=c.Menus.data.locationSlugMappedToName[e];t.text(c.Menus.data.l10n.menuLocation.replace("%s",e)),n.append(t)}),this.container.toggleClass("assigned-to-menu-location",0!==e.length)},onChangeExpanded:function(e,t){var n,i=this;e&&(wpNavMenu.menuList=i.contentContainer,wpNavMenu.targetList=wpNavMenu.menuList,m("#menu-to-edit").removeAttr("id"),wpNavMenu.menuList.attr("id","menu-to-edit").addClass("menu"),_.each(c.section(i.id).controls(),function(e){"nav_menu_item"===e.params.type&&e.actuallyEmbed()}),t.completeCallback&&(n=t.completeCallback),t.completeCallback=function(){"resolved"!==i.deferred.initSortables.state()&&(wpNavMenu.initSortables(),i.deferred.initSortables.resolve(wpNavMenu.menuList),c.control("nav_menu["+String(i.params.menu_id)+"]").reflowMenuItems()),_.isFunction(n)&&n()}),c.Section.prototype.onChangeExpanded.call(i,e,t)},highlightNewItemButton:function(){c.utils.highlightButton(this.contentContainer.find(".add-new-menu-item"),{delay:2e3})}}),c.Menus.createNavMenu=function(e){var t=c.Menus.generatePlaceholderAutoIncrementId(),n="nav_menu["+String(t)+"]";return c.create(n,n,{},{type:"nav_menu",transport:c.Menus.data.settingTransport,previewer:c.previewer}).set(m.extend({},c.Menus.data.defaultSettingValues.nav_menu,{name:e||""})),c.section.add(new c.Menus.MenuSection(n,{panel:"nav_menus",title:u(e),customizeAction:c.Menus.data.l10n.customizingMenus,priority:10,menu_id:t}))},c.Menus.NewMenuSection=c.Section.extend({attachEvents:function(){var t=this,e=t.container,n=t.contentContainer,i=/^nav_menu\[/;function a(){var t;e.find(".add-new-menu-notice").prop("hidden",(t=0,c.each(function(e){i.test(e.id)&&!1!==e.get()&&(t+=1)}),0<t))}function o(e){i.test(e.id)&&(e.bind(a),a())}t.headContainer.find(".accordion-section-title").replaceWith(l.template("nav-menu-create-menu-section-title")),e.on("click",".customize-add-menu-button",function(){t.expand()}),n.on("keydown",".menu-name-field",function(e){13===e.which&&t.submit()}),n.on("click","#customize-new-menu-submit",function(e){t.submit(),e.stopPropagation(),e.preventDefault()}),c.each(o),c.bind("add",o),c.bind("removed",function(e){i.test(e.id)&&(e.unbind(a),a())}),a(),c.Section.prototype.attachEvents.apply(t,arguments)},ready:function(){this.populateControls()},populateControls:function(){var e=this,t=e.id+"[name]",n=c.control(t);n||(n=new c.controlConstructor.nav_menu_name(t,{label:c.Menus.data.l10n.menuNameLabel,description:c.Menus.data.l10n.newMenuNameDescription,section:e.id,priority:0}),c.control.add(n.id,n),n.active.set(!0)),t=e.id+"[locations]",(n=c.control(t))||(n=new c.controlConstructor.nav_menu_locations(t,{section:e.id,priority:1,menu_id:"",isCreating:!0}),c.control.add(t,n),n.active.set(!0)),t=e.id+"[submit]",(n=c.control(t))||(n=new c.Control(t,{section:e.id,priority:1,templateId:"nav-menu-submit-new-button"}),c.control.add(t,n),n.active.set(!0))},submit:function(){var t,e=this.contentContainer,n=e.find(".menu-name-field").first(),i=n.val();i?(t=c.Menus.createNavMenu(i),n.val(""),n.removeClass("invalid"),e.find(".assigned-menu-location input[type=checkbox]").each(function(){var e=m(this);e.prop("checked")&&(c("nav_menu_locations["+e.data("location-id")+"]").set(t.params.menu_id),e.prop("checked",!1))}),l.a11y.speak(c.Menus.data.l10n.menuAdded),t.focus({completeCallback:function(){t.highlightNewItemButton()}})):(n.addClass("invalid"),n.focus())},selectDefaultLocation:function(e){var t=c.control(this.id+"[locations]"),n={};null!==e&&(n[e]=!0),t.setSelections(n)}}),c.Menus.MenuLocationControl=c.Control.extend({initialize:function(e,t){var n=e.match(/^nav_menu_locations\[(.+?)]/);this.themeLocation=n[1],c.Control.prototype.initialize.call(this,e,t)},ready:function(){var n=this,i=/^nav_menu\[(-?\d+)]/;n.setting.validate=function(e){return""===e?0:parseInt(e,10)},n.container.find(".create-menu").on("click",function(){var e=c.section("add_menu");e.selectDefaultLocation(this.dataset.locationId),e.focus()}),n.container.find(".edit-menu").on("click",function(){var e=n.setting();c.section("nav_menu["+e+"]").focus()}),n.setting.bind("change",function(){var e=0!==n.setting();n.container.find(".create-menu").toggleClass("hidden",e),n.container.find(".edit-menu").toggleClass("hidden",!e)}),c.bind("add",function(e){var t=e.id.match(i);t&&!1!==e()&&(t=t[1],e=new Option(u(e().name),t),n.container.find("select").append(e))}),c.bind("remove",function(e){var e=e.id.match(i);e&&(e=parseInt(e[1],10),n.setting()===e&&n.setting.set(""),n.container.find("option[value="+e+"]").remove())}),c.bind("change",function(e){var t=e.id.match(i);t&&(t=parseInt(t[1],10),!1===e()?(n.setting()===t&&n.setting.set(""),n.container.find("option[value="+t+"]").remove()):n.container.find("option[value="+t+"]").text(u(e().name)))})}}),c.Menus.MenuItemControl=c.Control.extend({initialize:function(e,t){var n=this;n.expanded=new c.Value(!1),n.expandedArgumentsQueue=[],n.expanded.bind(function(e){var t=n.expandedArgumentsQueue.shift(),t=m.extend({},n.defaultExpandedArguments,t);n.onChangeExpanded(e,t)}),c.Control.prototype.initialize.call(n,e,t),n.active.validate=function(){var e=c.section(n.section()),e=!!e&&e.active();return e}},embed:function(){var e=this.section();e&&((e=c.section(e))&&e.expanded()||c.settings.autofocus.control===this.id)&&this.actuallyEmbed()},actuallyEmbed:function(){"resolved"!==this.deferred.embedded.state()&&(this.renderContent(),this.deferred.embedded.resolve())},ready:function(){if(void 0===this.params.menu_item_id)throw new Error("params.menu_item_id was not defined");this._setupControlToggle(),this._setupReorderUI(),this._setupUpdateUI(),this._setupRemoveUI(),this._setupLinksUI(),this._setupTitleUI()},_setupControlToggle:function(){var i=this;this.container.find(".menu-item-handle").on("click",function(e){e.preventDefault(),e.stopPropagation();var t=i.getMenuControl(),n=m(e.target).is(".item-delete, .item-delete *"),e=m(e.target).is(".add-new-menu-item, .add-new-menu-item *");!m("body").hasClass("adding-menu-items")||n||e||c.Menus.availableMenuItemsPanel.close(),t.isReordering||t.isSorting||i.toggleForm()})},_setupReorderUI:function(){var o=this,e=l.template("menu-item-reorder-nav");o.container.find(".item-controls").after(e),o.container.find(".menu-item-reorder-nav").find(".menus-move-up, .menus-move-down, .menus-move-left, .menus-move-right").on("click",function(){var e=m(this),t=(o.params.depth=o.getDepth(),e.focus(),e.is(".menus-move-up")),n=e.is(".menus-move-down"),i=e.is(".menus-move-left"),a=e.is(".menus-move-right");t?o.moveUp():n?o.moveDown():i?(o.moveLeft(),1===o.params.depth?o.container.find(".is-submenu").hide():o.container.find(".is-submenu").show()):a&&(o.moveRight(),o.params.depth+=1,0===o.params.depth?o.container.find(".is-submenu").hide():o.container.find(".is-submenu").show()),e.focus()})},_setupUpdateUI:function(){var e,s=this,t=s.setting();s.elements={},s.elements.url=new c.Element(s.container.find(".edit-menu-item-url")),s.elements.title=new c.Element(s.container.find(".edit-menu-item-title")),s.elements.attr_title=new c.Element(s.container.find(".edit-menu-item-attr-title")),s.elements.target=new c.Element(s.container.find(".edit-menu-item-target")),s.elements.classes=new c.Element(s.container.find(".edit-menu-item-classes")),s.elements.xfn=new c.Element(s.container.find(".edit-menu-item-xfn")),s.elements.description=new c.Element(s.container.find(".edit-menu-item-description")),_.each(s.elements,function(n,i){n.bind(function(e){n.element.is("input[type=checkbox]")&&(e=e?n.element.val():"");var t=s.setting();t&&t[i]!==e&&((t=_.clone(t))[i]=e,s.setting.set(t))}),t&&("classes"!==i&&"xfn"!==i||!_.isArray(t[i])?n.set(t[i]):n.set(t[i].join(" ")))}),s.setting.bind(function(n,i){var e,t=s.params.menu_item_id,a=[],o=[];!1===n?(e=c.control("nav_menu["+String(i.nav_menu_term_id)+"]"),s.container.remove(),_.each(e.getMenuItemControls(),function(e){i.menu_item_parent===e.setting().menu_item_parent&&e.setting().position>i.position?a.push(e):e.setting().menu_item_parent===t&&o.push(e)}),_.each(a,function(e){var t=_.clone(e.setting());t.position+=o.length,e.setting.set(t)}),_.each(o,function(e,t){var n=_.clone(e.setting());n.position=i.position+t,n.menu_item_parent=i.menu_item_parent,e.setting.set(n)}),e.debouncedReflowMenuItems()):(_.each(n,function(e,t){s.elements[t]&&s.elements[t].set(n[t])}),s.container.find(".menu-item-data-parent-id").val(n.menu_item_parent),n.position===i.position&&n.menu_item_parent===i.menu_item_parent||s.getMenuControl().debouncedReflowMenuItems())}),s.setting.notifications.bind("add",e=function(){s.elements.url.element.toggleClass("invalid",s.setting.notifications.has("invalid_url"))}),s.setting.notifications.bind("removed",e)},_setupRemoveUI:function(){var r=this;r.container.find(".item-delete").on("click",function(){var e,t,n,i=!0,a=0,o=r.params.original_item_id,s=r.getMenuControl().$sectionContent.find(".menu-item");m("body").hasClass("adding-menu-items")||(i=!1),n=r.container.nextAll(".customize-control-nav_menu_item:visible").first(),t=r.container.prevAll(".customize-control-nav_menu_item:visible").first(),e=(n.length?n.find(!1===i?".item-edit":".item-delete"):t.length?t.find(!1===i?".item-edit":".item-delete"):r.container.nextAll(".customize-control-nav_menu").find(".add-new-menu-item")).first(),_.each(s,function(e){m(e).is(":visible")&&(e=e.getAttribute("id").match(/^customize-control-nav_menu_item-(-?\d+)$/,""))&&(e=parseInt(e[1],10),e=c.control("nav_menu_item["+String(e)+"]"))&&o==e.params.original_item_id&&a++}),a<=1&&((n=m("#menu-item-tpl-"+r.params.original_item_id)).removeClass("selected"),n.find(".menu-item-handle").removeClass("item-added")),r.container.slideUp(function(){r.setting.set(!1),l.a11y.speak(c.Menus.data.l10n.itemDeleted),e.focus()}),r.setting.set(!1)})},_setupLinksUI:function(){this.container.find("a.original-link").on("click",function(e){e.preventDefault(),c.previewer.previewUrl(e.target.toString())})},_setupTitleUI:function(){var i;this.container.find(".edit-menu-item-title").on("blur",function(){m(this).val(m(this).val().trim())}),i=this.container.find(".menu-item-title"),this.setting.bind(function(e){var t,n;e&&(e.title=e.title||"",n=(t=e.title.trim())||e.original_title||c.Menus.data.l10n.untitled,e._invalid&&(n=c.Menus.data.l10n.invalidTitleTpl.replace("%s",n)),t||e.original_title?i.text(n).removeClass("no-title"):i.text(n).addClass("no-title"))})},getDepth:function(){var e=this,t=e.setting(),n=0;if(!t)return 0;for(;t&&t.menu_item_parent&&(n+=1,e=c.control("nav_menu_item["+t.menu_item_parent+"]"));)t=e.setting();return n},renderContent:function(){var e,t=this,n=t.setting();t.params.title=n.title||"",t.params.depth=t.getDepth(),t.container.data("item-depth",t.params.depth),e=["menu-item","menu-item-depth-"+String(t.params.depth),"menu-item-"+n.object,"menu-item-edit-inactive"],n._invalid?(e.push("menu-item-invalid"),t.params.title=c.Menus.data.l10n.invalidTitleTpl.replace("%s",t.params.title)):"draft"===n.status&&(e.push("pending"),t.params.title=c.Menus.data.pendingTitleTpl.replace("%s",t.params.title)),t.params.el_classes=e.join(" "),t.params.item_type_label=n.type_label,t.params.item_type=n.type,t.params.url=n.url,t.params.target=n.target,t.params.attr_title=n.attr_title,t.params.classes=_.isArray(n.classes)?n.classes.join(" "):n.classes,t.params.xfn=n.xfn,t.params.description=n.description,t.params.parent=n.menu_item_parent,t.params.original_title=n.original_title||"",t.container.addClass(t.params.el_classes),c.Control.prototype.renderContent.call(t)},getMenuControl:function(){var e=this.setting();return e&&e.nav_menu_term_id?c.control("nav_menu["+e.nav_menu_term_id+"]"):null},expandControlSection:function(){var e=this.container.closest(".accordion-section");e.hasClass("open")||e.find(".accordion-section-title:first").trigger("click")},_toggleExpanded:c.Section.prototype._toggleExpanded,expand:c.Section.prototype.expand,expandForm:function(e){this.expand(e)},collapse:c.Section.prototype.collapse,collapseForm:function(e){this.collapse(e)},toggleForm:function(e,t){(e=void 0===e?!this.expanded():e)?this.expand(t):this.collapse(t)},onChangeExpanded:function(e,t){var n,i=this,a=this.container,o=a.find(".menu-item-settings:first");void 0===e&&(e=!o.is(":visible")),o.is(":visible")===e?t&&t.completeCallback&&t.completeCallback():e?(c.control.each(function(e){i.params.type===e.params.type&&i!==e&&e.collapseForm()}),n=function(){a.removeClass("menu-item-edit-inactive").addClass("menu-item-edit-active"),i.container.trigger("expanded"),t&&t.completeCallback&&t.completeCallback()},a.find(".item-edit").attr("aria-expanded","true"),o.slideDown("fast",n),i.container.trigger("expand")):(n=function(){a.addClass("menu-item-edit-inactive").removeClass("menu-item-edit-active"),i.container.trigger("collapsed"),t&&t.completeCallback&&t.completeCallback()},i.container.trigger("collapse"),a.find(".item-edit").attr("aria-expanded","false"),o.slideUp("fast",n))},focus:function(e){var t=this,n=(e=e||{}).completeCallback,i=function(){t.expandControlSection(),e.completeCallback=function(){t.container.find(".menu-item-settings").find("input, select, textarea, button, object, a[href], [tabindex]").filter(":visible").first().focus(),n&&n()},t.expandForm(e)};c.section.has(t.section())?c.section(t.section()).expand({completeCallback:i}):i()},moveUp:function(){this._changePosition(-1),l.a11y.speak(c.Menus.data.l10n.movedUp)},moveDown:function(){this._changePosition(1),l.a11y.speak(c.Menus.data.l10n.movedDown)},moveLeft:function(){this._changeDepth(-1),l.a11y.speak(c.Menus.data.l10n.movedLeft)},moveRight:function(){this._changeDepth(1),l.a11y.speak(c.Menus.data.l10n.movedRight)},_changePosition:function(e){var t,n=this,i=_.clone(n.setting()),a=[];if(1!==e&&-1!==e)throw new Error("Offset changes by 1 are only supported.");if(n.setting()){if(_(n.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===i.menu_item_parent&&a.push(e.setting)}),a.sort(function(e,t){return e().position-t().position}),-1===(t=_.indexOf(a,n.setting)))throw new Error("Expected setting to be among siblings.");0===t&&e<0||t===a.length-1&&0<e||((t=a[t+e])&&t.set(m.extend(_.clone(t()),{position:i.position})),i.position+=e,n.setting.set(i))}},_changeDepth:function(e){if(1!==e&&-1!==e)throw new Error("Offset changes by 1 are only supported.");var t,n,i=this,a=_.clone(i.setting()),o=[];if(_(i.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===a.menu_item_parent&&o.push(e)}),o.sort(function(e,t){return e.setting().position-t.setting().position}),-1===(t=_.indexOf(o,i)))throw new Error("Expected control to be among siblings.");-1===e?a.menu_item_parent&&(n=c.control("nav_menu_item["+a.menu_item_parent+"]"),_(o).chain().slice(t).each(function(e,t){e.setting.set(m.extend({},e.setting(),{menu_item_parent:i.params.menu_item_id,position:t}))}),_(i.getMenuControl().getMenuItemControls()).each(function(e){var t;e.setting().menu_item_parent===n.setting().menu_item_parent&&e.setting().position>n.setting().position&&(t=_.clone(e.setting()),e.setting.set(m.extend(t,{position:t.position+1})))}),a.position=n.setting().position+1,a.menu_item_parent=n.setting().menu_item_parent,i.setting.set(a)):1===e&&0!==t&&(a.menu_item_parent=o[t-1].params.menu_item_id,a.position=0,_(i.getMenuControl().getMenuItemControls()).each(function(e){e.setting().menu_item_parent===a.menu_item_parent&&(a.position=Math.max(a.position,e.setting().position))}),a.position+=1,i.setting.set(a))}}),c.Menus.MenuNameControl=c.Control.extend({ready:function(){var e,n=this;n.setting&&(e=n.setting(),n.nameElement=new c.Element(n.container.find(".menu-name-field")),n.nameElement.bind(function(e){var t=n.setting();t&&t.name!==e&&((t=_.clone(t)).name=e,n.setting.set(t))}),e&&n.nameElement.set(e.name),n.setting.bind(function(e){e&&n.nameElement.set(e.name)}))}}),c.Menus.MenuLocationsControl=c.Control.extend({ready:function(){var d=this;d.container.find(".assigned-menu-location").each(function(){function t(e){var t=c("nav_menu["+String(e)+"]");e&&t&&t()?n.find(".theme-location-set").show().find("span").text(u(t().name)):n.find(".theme-location-set").hide()}var n=m(this),e=n.find("input[type=checkbox]"),i=new c.Element(e),a=c("nav_menu_locations["+e.data("location-id")+"]"),o=""===d.params.menu_id,s=o?_.noop:function(e){i.set(e)},r=o?_.noop:function(e){a.set(e?d.params.menu_id:0)};s(a.get()===d.params.menu_id),e.on("change",function(){r(this.checked)}),a.bind(function(e){s(e===d.params.menu_id),t(e)}),t(a.get())})},setSelections:function(i){this.container.find(".menu-location").each(function(e,t){var n=t.dataset.locationId;t.checked=n in i&&i[n]})}}),c.Menus.MenuAutoAddControl=c.Control.extend({ready:function(){var n=this,e=n.setting();n.active.validate=function(){var e=c.section(n.section()),e=!!e&&e.active();return e},n.autoAddElement=new c.Element(n.container.find("input[type=checkbox].auto_add")),n.autoAddElement.bind(function(e){var t=n.setting();t&&t.name!==e&&((t=_.clone(t)).auto_add=e,n.setting.set(t))}),e&&n.autoAddElement.set(e.auto_add),n.setting.bind(function(e){e&&n.autoAddElement.set(e.auto_add)})}}),c.Menus.MenuControl=c.Control.extend({ready:function(){var t,n,i=this,a=c.section(i.section()),o=i.params.menu_id,e=i.setting();if(void 0===this.params.menu_id)throw new Error("params.menu_id was not defined");i.active.validate=function(){var e=!!a&&a.active();return e},i.$controlSection=a.headContainer,i.$sectionContent=i.container.closest(".accordion-section-content"),this._setupModel(),c.section(i.section(),function(e){e.deferred.initSortables.done(function(e){i._setupSortable(e)})}),this._setupAddition(),this._setupTitle(),e&&(t=u(e.name),c.control.each(function(e){e.extended(c.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&(e.container.find(".nav-menu-widget-form-controls:first").show(),e.container.find(".nav-menu-widget-no-menus-message:first").hide(),0===(n=e.container.find("select")).find("option[value="+String(o)+"]").length)&&n.append(new Option(t,o))}),(e=m("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").show(),e.find(".nav-menu-widget-no-menus-message:first").hide(),0===(n=e.find(".widget-inside select:first")).find("option[value="+String(o)+"]").length)&&n.append(new Option(t,o)),_.defer(function(){i.updateInvitationVisibility()})},_setupModel:function(){var n=this,i=n.params.menu_id;n.setting.bind(function(e){var t;!1===e?n._handleDeletion():(t=u(e.name),c.control.each(function(e){e.extended(c.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&e.container.find("select").find("option[value="+String(i)+"]").text(t)}))})},_setupSortable:function(e){var a=this;if(!e.is(a.$sectionContent))throw new Error("Unexpected menuList.");e.on("sortstart",function(){a.isSorting=!0}),e.on("sortstop",function(){setTimeout(function(){var e=a.$sectionContent.sortable("toArray"),t=[],n=0,i=10;a.isSorting=!1,a.$sectionContent.scrollLeft(0),_.each(e,function(e){var e=e.match(/^customize-control-nav_menu_item-(-?\d+)$/,"");e&&(e=parseInt(e[1],10),e=c.control("nav_menu_item["+String(e)+"]"))&&t.push(e)}),_.each(t,function(e){var t;!1!==e.setting()&&(t=_.clone(e.setting()),n+=1,i+=1,t.position=n,e.priority(i),t.menu_item_parent=parseInt(e.container.find(".menu-item-data-parent-id").val(),10),t.menu_item_parent||(t.menu_item_parent=0),e.setting.set(t))})})}),a.isReordering=!1,this.container.find(".reorder-toggle").on("click",function(){a.toggleReordering(!a.isReordering)})},_setupAddition:function(){var t=this;this.container.find(".add-new-menu-item").on("click",function(e){t.$sectionContent.hasClass("reordering")||(m("body").hasClass("adding-menu-items")?(m(this).attr("aria-expanded","false"),c.Menus.availableMenuItemsPanel.close(),e.stopPropagation()):(m(this).attr("aria-expanded","true"),c.Menus.availableMenuItemsPanel.open(t)))})},_handleDeletion:function(){var e,n=this.params.menu_id,i=0,t=c.section(this.section()),a=function(){t.container.remove(),c.section.remove(t.id)};t&&t.expanded()?t.collapse({completeCallback:function(){a(),l.a11y.speak(c.Menus.data.l10n.menuDeleted),c.panel("nav_menus").focus()}}):a(),c.each(function(e){/^nav_menu\[/.test(e.id)&&!1!==e()&&(i+=1)}),c.control.each(function(e){var t;e.extended(c.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&((t=e.container.find("select")).val()===String(n)&&t.prop("selectedIndex",0).trigger("change"),e.container.find(".nav-menu-widget-form-controls:first").toggle(0!==i),e.container.find(".nav-menu-widget-no-menus-message:first").toggle(0===i),e.container.find("option[value="+String(n)+"]").remove())}),(e=m("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").toggle(0!==i),e.find(".nav-menu-widget-no-menus-message:first").toggle(0===i),e.find("option[value="+String(n)+"]").remove()},_setupTitle:function(){var d=this;d.setting.bind(function(e){var t,n,i,a,o,s,r;e&&(t=c.section(d.section()),n=d.params.menu_id,i=t.headContainer.find(".accordion-section-title"),a=t.contentContainer.find(".customize-section-title h3"),o=t.headContainer.find(".menu-in-location"),s=a.find(".customize-action"),r=u(e.name),i.text(r),o.length&&o.appendTo(i),a.text(r),s.length&&s.prependTo(a),c.control.each(function(e){/^nav_menu_locations\[/.test(e.id)&&e.container.find("option[value="+n+"]").text(r)}),t.contentContainer.find(".customize-control-checkbox input").each(function(){m(this).prop("checked")&&m(".current-menu-location-name-"+m(this).data("location-id")).text(r)}))})},toggleReordering:function(e){var t=this.container.find(".add-new-menu-item"),n=this.container.find(".reorder-toggle"),i=this.$sectionContent.find(".item-title");(e=Boolean(e))!==this.$sectionContent.hasClass("reordering")&&(this.isReordering=e,this.$sectionContent.toggleClass("reordering",e),this.$sectionContent.sortable(this.isReordering?"disable":"enable"),this.isReordering?(t.attr({tabindex:"-1","aria-hidden":"true"}),n.attr("aria-label",c.Menus.data.l10n.reorderLabelOff),l.a11y.speak(c.Menus.data.l10n.reorderModeOn),i.attr("aria-hidden","false")):(t.removeAttr("tabindex aria-hidden"),n.attr("aria-label",c.Menus.data.l10n.reorderLabelOn),l.a11y.speak(c.Menus.data.l10n.reorderModeOff),i.attr("aria-hidden","true")),e)&&_(this.getMenuItemControls()).each(function(e){e.collapseForm()})},getMenuItemControls:function(){var t=[],n=this.params.menu_id;return c.control.each(function(e){"nav_menu_item"===e.params.type&&e.setting()&&n===e.setting().nav_menu_term_id&&t.push(e)}),t},reflowMenuItems:function(){var e=this.getMenuItemControls(),a=function(n){var t=[],i=n.currentParent;_.each(n.menuItemControls,function(e){i===e.setting().menu_item_parent&&t.push(e)}),t.sort(function(e,t){return e.setting().position-t.setting().position}),_.each(t,function(t){n.currentAbsolutePosition+=1,t.priority.set(n.currentAbsolutePosition),t.container.hasClass("menu-item-depth-"+String(n.currentDepth))||(_.each(t.container.prop("className").match(/menu-item-depth-\d+/g),function(e){t.container.removeClass(e)}),t.container.addClass("menu-item-depth-"+String(n.currentDepth))),t.container.data("item-depth",n.currentDepth),n.currentDepth+=1,n.currentParent=t.params.menu_item_id,a(n),--n.currentDepth,n.currentParent=i}),t.length&&(_(t).each(function(e){e.container.removeClass("move-up-disabled move-down-disabled move-left-disabled move-right-disabled"),0===n.currentDepth?e.container.addClass("move-left-disabled"):10===n.currentDepth&&e.container.addClass("move-right-disabled")}),t[0].container.addClass("move-up-disabled").addClass("move-right-disabled").toggleClass("move-down-disabled",1===t.length),t[t.length-1].container.addClass("move-down-disabled").toggleClass("move-up-disabled",1===t.length))};a({menuItemControls:e,currentParent:0,currentDepth:0,currentAbsolutePosition:0}),this.updateInvitationVisibility(e),this.container.find(".reorder-toggle").toggle(1<e.length)},debouncedReflowMenuItems:_.debounce(function(){this.reflowMenuItems.apply(this,arguments)},0),addItemToMenu:function(e){var t,n,i,a=0,o=10,s=e.id||"";return _.each(this.getMenuItemControls(),function(e){!1!==e.setting()&&(o=Math.max(o,e.priority()),0===e.setting().menu_item_parent)&&(a=Math.max(a,e.setting().position))}),a+=1,o+=1,delete(e=m.extend({},c.Menus.data.defaultSettingValues.nav_menu_item,e,{nav_menu_term_id:this.params.menu_id,original_title:e.title,position:a})).id,i=c.Menus.generatePlaceholderAutoIncrementId(),t="nav_menu_item["+String(i)+"]",n={type:"nav_menu_item",transport:c.Menus.data.settingTransport,previewer:c.previewer},(n=c.create(t,t,{},n)).set(e),e=new c.controlConstructor.nav_menu_item(t,{type:"nav_menu_item",section:this.id,priority:o,settings:{default:t},menu_item_id:i,original_item_id:s}),c.control.add(e),n.preview(),this.debouncedReflowMenuItems(),l.a11y.speak(c.Menus.data.l10n.itemAdded),e},updateInvitationVisibility:function(e){e=e||this.getMenuItemControls();this.container.find(".new-menu-item-invitation").toggle(0===e.length)}}),m.extend(c.controlConstructor,{nav_menu_location:c.Menus.MenuLocationControl,nav_menu_item:c.Menus.MenuItemControl,nav_menu:c.Menus.MenuControl,nav_menu_name:c.Menus.MenuNameControl,nav_menu_locations:c.Menus.MenuLocationsControl,nav_menu_auto_add:c.Menus.MenuAutoAddControl}),m.extend(c.panelConstructor,{nav_menus:c.Menus.MenusPanel}),m.extend(c.sectionConstructor,{nav_menu:c.Menus.MenuSection,new_menu:c.Menus.NewMenuSection}),c.bind("ready",function(){c.Menus.availableMenuItemsPanel=new c.Menus.AvailableMenuItemsPanelView({collection:c.Menus.availableMenuItems}),c.bind("saved",function(e){(e.nav_menu_updates||e.nav_menu_item_updates)&&c.Menus.applySavedData(e)}),c.state("changesetStatus").bind(function(e){"publish"===e&&(c("nav_menus_created_posts")._value=[])}),c.previewer.bind("focus-nav-menu-item-control",c.Menus.focusMenuItemControl)}),c.Menus.applySavedData=function(e){var u={},r={};_(e.nav_menu_updates).each(function(n){var e,t,i,a,o,s,r,d;if("inserted"===n.status){if(!n.previous_term_id)throw new Error("Expected previous_term_id");if(!n.term_id)throw new Error("Expected term_id");if(e="nav_menu["+String(n.previous_term_id)+"]",!c.has(e))throw new Error("Expected setting to exist: "+e);if(i=c(e),!c.section.has(e))throw new Error("Expected control to exist: "+e);if(o=c.section(e),!(s=i.get()))throw new Error("Did not expect setting to be empty (deleted).");s=m.extend(_.clone(s),n.saved_value),u[n.previous_term_id]=n.term_id,a="nav_menu["+String(n.term_id)+"]",t=c.create(a,a,s,{type:"nav_menu",transport:c.Menus.data.settingTransport,previewer:c.previewer}),(d=o.expanded())&&o.collapse(),a=new c.Menus.MenuSection(a,{panel:"nav_menus",title:s.name,customizeAction:c.Menus.data.l10n.customizingMenus,type:"nav_menu",priority:o.priority.get(),menu_id:n.term_id}),c.section.add(a),c.control.each(function(e){var t;e.extended(c.controlConstructor.widget_form)&&"nav_menu"===e.params.widget_id_base&&(t=(e=e.container.find("select")).find("option[value="+String(n.previous_term_id)+"]"),e.find("option[value="+String(n.term_id)+"]").prop("selected",t.prop("selected")),t.remove())}),i.callbacks.disable(),i.set(!1),i.preview(),t.preview(),i._dirty=!1,o.container.remove(),c.section.remove(e),r=0,c.each(function(e){/^nav_menu\[/.test(e.id)&&!1!==e()&&(r+=1)}),(s=m("#available-widgets-list .widget-tpl:has( input.id_base[ value=nav_menu ] )")).find(".nav-menu-widget-form-controls:first").toggle(0!==r),s.find(".nav-menu-widget-no-menus-message:first").toggle(0===r),s.find("option[value="+String(n.previous_term_id)+"]").remove(),l.customize.control.each(function(e){/^nav_menu_locations\[/.test(e.id)&&e.container.find("option[value="+String(n.previous_term_id)+"]").remove()}),c.each(function(e){var t=c.state("saved").get();/^nav_menu_locations\[/.test(e.id)&&e.get()===n.previous_term_id&&(e.set(n.term_id),e._dirty=!1,c.state("saved").set(t),e.preview())}),d&&a.expand()}else if("updated"===n.status){if(t="nav_menu["+String(n.term_id)+"]",!c.has(t))throw new Error("Expected setting to exist: "+t);i=c(t),_.isEqual(n.saved_value,i.get())||(o=c.state("saved").get(),i.set(n.saved_value),i._dirty=!1,c.state("saved").set(o))}}),_(e.nav_menu_item_updates).each(function(e){e.previous_post_id&&(r[e.previous_post_id]=e.post_id)}),_(e.nav_menu_item_updates).each(function(e){var t,n,i,a,o,s;if("inserted"===e.status){if(!e.previous_post_id)throw new Error("Expected previous_post_id");if(!e.post_id)throw new Error("Expected post_id");if(t="nav_menu_item["+String(e.previous_post_id)+"]",!c.has(t))throw new Error("Expected setting to exist: "+t);if(i=c(t),!c.control.has(t))throw new Error("Expected control to exist: "+t);if(o=c.control(t),!(s=i.get()))throw new Error("Did not expect setting to be empty (deleted).");if((s=_.clone(s)).menu_item_parent<0){if(!r[s.menu_item_parent])throw new Error("inserted ID for menu_item_parent not available");s.menu_item_parent=r[s.menu_item_parent]}u[s.nav_menu_term_id]&&(s.nav_menu_term_id=u[s.nav_menu_term_id]),n="nav_menu_item["+String(e.post_id)+"]",a=c.create(n,n,s,{type:"nav_menu_item",transport:c.Menus.data.settingTransport,previewer:c.previewer}),s=new c.controlConstructor.nav_menu_item(n,{type:"nav_menu_item",menu_id:e.post_id,section:"nav_menu["+String(s.nav_menu_term_id)+"]",priority:o.priority.get(),settings:{default:n},menu_item_id:e.post_id}),o.container.remove(),c.control.remove(t),c.control.add(s),i.callbacks.disable(),i.set(!1),i.preview(),a.preview(),i._dirty=!1,s.container.toggleClass("menu-item-edit-inactive",o.container.hasClass("menu-item-edit-inactive"))}}),_.each(e.widget_nav_menu_updates,function(e,t){t=c(t);t&&(t._value=e,t.preview())})},c.Menus.focusMenuItemControl=function(e){e=c.Menus.getMenuItemControl(e);e&&e.focus()},c.Menus.getMenuControl=function(e){return c.control("nav_menu["+e+"]")},c.Menus.getMenuItemControl=function(e){return c.control("nav_menu_item["+e+"]")}}(wp.customize,wp,jQuery);