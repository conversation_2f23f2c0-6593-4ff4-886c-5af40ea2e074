{"translation-revision-date": "2024-12-16 16:53:45+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Effects": ["Effects"], "Order": ["Order"], "Transform": ["Transform"], "Text Stroke": ["Text Stroke"], "Bottom Right": ["Bottom Right"], "Bottom Left": ["Bottom Left"], "Top Right": ["Top Right"], "Top Left": ["Top Left"], "Word Spacing": ["Word Spacing"], "Dynamic Tags": ["Dynamic Tags"], "Auto": ["Auto"], "Hidden": ["Hidden"], "Overflow": ["Overflow"], "Fixed": ["Fixed"], "Absolute": ["Absolute"], "Position": ["Position"], "End": ["End"], "Start": ["Start"], "Z-Index": ["Z-Index"], "Edit %s": ["Edit %s"], "Background": ["Background"], "General": ["General"], "Spacing": ["Spacing"], "Direction": ["Direction"], "Display": ["Display"], "Settings": ["Settings"], "Border Color": ["Border Colour"], "Center": ["Centre"], "Border Width": ["Border Width"], "Color": ["Colour"], "Border": ["Border"], "Size": ["Size"], "Alignment": ["Alignment"], "Border Radius": ["Border Radius"], "Stretch": ["<PERSON><PERSON><PERSON>"], "Height": ["Height"], "Layout": ["Layout"], "CSS Classes": ["CSS Classes"], "Margin": ["<PERSON><PERSON>"], "Width": ["<PERSON><PERSON><PERSON>"], "Padding": ["Padding"], "Text Color": ["Text Colour"], "Typography": ["Typography"], "Column": ["Column"], "Style": ["Style"], "Dashed": ["Dashed"], "Dotted": ["Dotted"], "Custom": ["Custom"], "Double": ["Double"], "Solid": ["Solid"], "None": ["None"], "Left": ["Left"], "Bottom": ["Bottom"], "Right": ["Right"], "Top": ["Top"], "Justify": ["Justify"]}}, "comment": {"reference": "assets/js/packages/editor-editing-panel/editor-editing-panel.js"}}