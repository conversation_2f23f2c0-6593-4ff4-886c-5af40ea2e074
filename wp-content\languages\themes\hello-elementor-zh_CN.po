# Translation of Themes - Hello Elementor in Chinese (China)
# This file is distributed under the same license as the Themes - Hello Elementor package.
msgid ""
msgstr ""
"PO-Revision-Date: 2020-05-25 06:01:03+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/3.0.0-alpha.2\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Hello Elementor\n"

#. Theme Name of the theme
msgid "Hello Elementor"
msgstr "您好 Elementor"

#. Description of the theme
msgid "A plain-vanilla & lightweight theme for Elementor page builder"
msgstr "Elementor 页面生成器的简单主题和轻量级主题"

#: includes/admin-functions.php:47
msgid "Hello theme is a lightweight starter theme. We recommend you use it together with Elementor Page Builder plugin, they work perfectly together!"
msgstr "Hello Theme主题是一个轻量级的入门主题。我们建议您将它与Elementor Page Builder 插件一起使用，它们可以完美地协同工作！"

#: includes/admin-functions.php:40 includes/customizer/elementor-upsell.php:65
#: includes/customizer/elementor-upsell.php:68
msgid "Activate Elementor"
msgstr "启用Elementor"

#: includes/admin-functions.php:49 includes/customizer/elementor-upsell.php:48
msgid "Install Elementor"
msgstr "安装Elementor"

#: includes/admin-functions.php:140
msgid "Thanks for installing Hello Theme!"
msgstr "感谢您安装Hello Theme！"

#: includes/admin-functions.php:38
msgid "Hello theme is a lightweight starter theme designed to work perfectly with Elementor Page Builder plugin."
msgstr "Hello Theme 主题是一个轻量级的入门主题，旨在与Elementor Page Builder 插件完美配合。"

#: includes/admin-functions.php:142
msgid "Learn more about Elementor"
msgstr "了解有关Elementor的更多信息"

#: template-parts/404.php:15
msgid "The page can&rsquo;t be found."
msgstr "有点尴尬诶！该页无法显示。"

#: template-parts/404.php:19
msgid "It looks like nothing was found at this location."
msgstr "看起来在这个位置找不到任何东西。"

#: template-parts/single.php:26
msgid "Tagged "
msgstr "标签 "

#: template-parts/dynamic-footer.php:33 template-parts/dynamic-header.php:36
#: template-parts/header.php:31
msgid "Home"
msgstr "主页"

#: template-parts/search.php:16
msgid "Search results for: "
msgstr "搜索结果： "

#: template-parts/search.php:32
msgid "It seems we can't find what you're looking for."
msgstr "看来我们找不到您要找的东西了。"

#. Translators: HTML arrow
#: template-parts/archive.php:46 template-parts/search.php:44
msgid "%s older"
msgstr "较旧：%s"

#. Translators: HTML arrow
#: template-parts/archive.php:48 template-parts/search.php:46
msgid "newer %s"
msgstr "较新：%s"

#: comments.php:27
msgctxt "comments title"
msgid "One Response"
msgstr "一个回复"

#. translators: 1: number of comments
#: comments.php:31
msgctxt "comments title"
msgid "%1$s Response"
msgid_plural "%1$s Responses"
msgstr[0] "%1$s 回复"

#: comments.php:67
msgid "Comments are closed."
msgstr "评论被关闭。"

#. Theme URI of the theme
msgid "https://elementor.com/hello-theme/?utm_source=wp-themes&utm_campaign=theme-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/hello-theme/?utm_source=wp-themes&utm_campaign=theme-uri&utm_medium=wp-dash"

#. Author URI of the theme
msgid "https://elementor.com/?utm_source=wp-themes&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-themes&utm_campaign=author-uri&utm_medium=wp-dash"

#. Author of the theme
msgid "Elementor Team"
msgstr "Elementor 团队"