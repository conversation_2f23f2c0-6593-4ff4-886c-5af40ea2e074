# Translation of Themes - Twenty Sixteen in Chinese (China)
# This file is distributed under the same license as the Themes - Twenty Sixteen package.
msgid ""
msgstr ""
"PO-Revision-Date: 2018-12-21 00:24:01+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Twenty Sixteen\n"

#. Theme Name of the theme
msgid "Twenty Sixteen"
msgstr "Twenty Sixteen"

#. Description of the theme
msgid "Twenty Sixteen is a modernized take on an ever-popular WordPress layout — the horizontal masthead with an optional right sidebar that works perfectly for blogs and websites. It has custom color options with beautiful default color schemes, a harmonious fluid grid using a mobile-first approach, and impeccable polish in every detail. Twenty Sixteen will make your WordPress look beautiful everywhere."
msgstr "Twenty Sixteen是一个流行的WordPress布局的现代化实现——横式页首以及一个可选用的右侧边栏，完全适用于博客及网站。它有美丽的预设色彩调色盘、和谐的流动式网格设计、以移动设备为优先的设计，每个细节都极尽完美。Twenty Sixteen将使您的WordPress大放异彩。"

#: functions.php:202
msgid "Bright Red"
msgstr "亮红色"

#: functions.php:197
msgid "Dark Red"
msgstr "暗红色"

#: functions.php:187
msgid "Dark Brown"
msgstr "暗棕色"

#: functions.php:177
msgid "Bright Blue"
msgstr "亮蓝色"

#: functions.php:182
msgid "Light Blue"
msgstr "浅蓝色"

#: functions.php:172
msgid "Blue Gray"
msgstr "蓝灰色"

#: functions.php:167
msgid "White"
msgstr "白色"

#: functions.php:162
msgid "Light Gray"
msgstr "浅灰色"

#: functions.php:152
msgid "Dark Gray"
msgstr "暗灰色"

#: functions.php:157
msgid "Medium Gray"
msgstr "灰色"

#: functions.php:192
msgid "Medium Brown"
msgstr "棕色"

#. translators: %s: Name of current post
#: template-parts/content.php:53 template-parts/content-page.php:39
#: template-parts/content-search.php:28 template-parts/content-search.php:43
#: template-parts/content-single.php:47 image.php:88
msgid "Edit<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "编辑<span class=\"screen-reader-text\">“%s”</span>"

#. translators: %s: Name of current post
#: template-parts/content.php:29 inc/template-tags.php:194
msgid "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "继续阅读<span class=\"screen-reader-text\">“%s”</span>"

#: inc/customizer.php:345
msgid "Red"
msgstr "红"

#. translators: %s: post title
#: comments.php:31
msgctxt "comments title"
msgid "One thought on &ldquo;%s&rdquo;"
msgstr "《%s》有一个想法"

#: searchform.php:16
msgctxt "submit button"
msgid "Search"
msgstr "搜索"

#: searchform.php:14
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr "搜索…"

#: searchform.php:13
msgctxt "label"
msgid "Search for:"
msgstr "搜索："

#: footer.php:17
msgid "Footer Primary Menu"
msgstr "页脚主菜单"

#: footer.php:30
msgid "Footer Social Links Menu"
msgstr "页脚社交链接菜单"

#: functions.php:267
msgid "Add widgets here to appear in your sidebar."
msgstr "将挂件加入此处来在侧边栏中显示。"

#: template-parts/content.php:14
msgid "Featured"
msgstr "特色"

#: template-parts/content-none.php:28
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "我们可能无法找到您需要的内容。或许搜索功能可以帮到您。"

#: template-parts/content-none.php:23
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "抱歉，没有符合您搜索条件的结果。请换其它关键词再试。"

#: template-parts/content-none.php:19
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "准备好发布第一篇文章了？<a href=\"%1$s\">从这里开始</a>。"

#: template-parts/content-none.php:13
msgid "Nothing Found"
msgstr "未找到"

#: template-parts/biography.php:33
msgid "View all posts by %s"
msgstr "查看%s的所有文章"

#: template-parts/biography.php:28
msgid "Author:"
msgstr "作者："

#: single.php:42
msgid "Previous post:"
msgstr "上篇文章："

#: single.php:39
msgid "Next post:"
msgstr "下篇文章："

#: single.php:41
msgid "Previous"
msgstr "上一篇"

#: single.php:38
msgid "Next"
msgstr "下一篇"

#: search.php:18
msgid "Search Results for: %s"
msgstr "%s的搜索结果"

#: inc/template-tags.php:112
msgctxt "Used before tag names."
msgid "Tags"
msgstr "标签"

#: inc/template-tags.php:103
msgctxt "Used before category names."
msgid "Categories"
msgstr "分类"

#: inc/template-tags.php:99 inc/template-tags.php:108
msgctxt "Used between list items, there is a space after the comma."
msgid ", "
msgstr "、"

#: inc/template-tags.php:83
msgctxt "Used before publish date."
msgid "Posted on"
msgstr "发布于"

#: inc/template-tags.php:40
msgctxt "Used before post format."
msgid "Format"
msgstr "格式"

#: inc/template-tags.php:52
msgid "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr "<span class=\"screen-reader-text\">于%s</span>留下评论"

#: inc/template-tags.php:26
msgctxt "Used before post author name."
msgid "Author"
msgstr "作者"

#: functions.php:207 inc/customizer.php:355
msgid "Yellow"
msgstr "黄色"

#: inc/customizer.php:335
msgid "Gray"
msgstr "灰"

#: inc/customizer.php:325
msgid "Dark"
msgstr "暗色"

#: inc/customizer.php:315
msgid "Default"
msgstr "默认"

#: inc/customizer.php:221
msgid "Main Text Color"
msgstr "主文字颜色"

#: inc/customizer.php:242
msgid "Secondary Text Color"
msgstr "副文字颜色"

#: inc/customizer.php:200
msgid "Link Color"
msgstr "链接颜色"

#: inc/customizer.php:176
msgid "Page Background Color"
msgstr "页面背景颜色"

#: single.php:31 image.php:107
msgctxt "Parent post link"
msgid "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%title</span>"
msgstr "<span class=\"meta-nav\">发布于</span><span class=\"post-title\">%title</span>"

#: inc/customizer.php:153
msgid "Base Color Scheme"
msgstr "基础配色方案"

#: inc/back-compat.php:41 inc/back-compat.php:54 inc/back-compat.php:72
msgid "Twenty Sixteen requires at least WordPress version 4.4. You are running version %s. Please upgrade and try again."
msgstr "Twenty Sixteen要求WordPress 4.4版或以上，您在运行%s版。请升级并重试。"

#: image.php:77
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr "原始尺寸"

#: template-parts/content.php:36 template-parts/content-page.php:24
#: template-parts/content-single.php:26 image.php:58
msgid "Pages:"
msgstr "页码："

#: image.php:26
msgid "Next Image"
msgstr "下一图片"

#: image.php:25
msgid "Previous Image"
msgstr "上一图片"

#: header.php:50
msgid "Menu"
msgstr "菜单"

#: header.php:28
msgid "Skip to content"
msgstr "跳至内容"

#: functions.php:407
msgid "expand child menu"
msgstr "展开子菜单"

#: functions.php:408
msgid "collapse child menu"
msgstr "折叠子菜单"

#: functions.php:277
msgid "Content Bottom 1"
msgstr "底部内容1"

#: functions.php:279 functions.php:291
msgid "Appears at the bottom of the content on posts and pages."
msgstr "在页面与文章内容的底部出现。"

#: functions.php:289
msgid "Content Bottom 2"
msgstr "底部内容2"

#. translators: If there are characters in your language that are not supported
#. by Merriweather, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:317
msgctxt "Merriweather font: on or off"
msgid "on"
msgstr "off"

#. translators: If there are characters in your language that are not supported
#. by Montserrat, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:322
msgctxt "Montserrat font: on or off"
msgid "on"
msgstr "off"

#. translators: If there are characters in your language that are not supported
#. by Inconsolata, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:327
msgctxt "Inconsolata font: on or off"
msgid "on"
msgstr "off"

#: functions.php:265
msgid "Sidebar"
msgstr "侧边栏"

#: functions.php:93 header.php:67
msgid "Social Links Menu"
msgstr "社交网络链接菜单"

#: functions.php:92 header.php:54
msgid "Primary Menu"
msgstr "主菜单"

#: footer.php:61
msgid "Proudly powered by %s"
msgstr "自豪地采用%s"

#: comments.php:71
msgid "Comments are closed."
msgstr "评论已关闭。"

#. translators: 1: number of comments, 2: post title
#: comments.php:35
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] "《%2$s》有%1$s个想法"

#: archive.php:53 template-parts/content.php:40
#: template-parts/content-page.php:28 template-parts/content-single.php:30
#: search.php:41 index.php:50 image.php:62
msgid "Page"
msgstr "页"

#: archive.php:52 search.php:40 index.php:49
msgid "Next page"
msgstr "下一页"

#: archive.php:51 search.php:39 index.php:48
msgid "Previous page"
msgstr "上一页"

#: 404.php:21
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "这儿似乎什么都没有，试试搜索？"

#: 404.php:17
msgid "Oops! That page can&rsquo;t be found."
msgstr "有点尴尬诶！该页无法显示。"

#. Author of the theme
msgid "the WordPress team"
msgstr "WordPress团队"

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentysixteen/"
msgstr "https://wordpress.org/themes/twentysixteen/"

#. Author URI of the theme
#: footer.php:60
msgid "https://wordpress.org/"
msgstr "https://cn.wordpress.org/"
