{"mc_conf_version": "2", "ipheader": {"hdr": "HTTP_X_FORWARDED_FOR", "pos": 0}, "brandname": "Bot Protection", "confkey": "3bbe07d4da64f64e04137579129dc188", "fw": {"mode": 3, "reqprofilingmode": 2, "bypasslevel": 2, "cookiemode": 1, "loggingmode": 2, "admincookiemode": 1, "iswpusercookieenabled": true, "wpusercapstoconsider": {"manage_options": 28, "unfiltered_html": 56, "edit_others_posts": 41, "upload_files": 58, "edit_posts": 61, "read": 63, "manage_network": 3, "edit_users": 22, "create_users": 14, "promote_users": 29, "delete_users": 17, "manage_woocommerce": 101}, "wpfruleinitmode": 1, "ipcookiemode": 2, "customroles": [], "cookiekey": "ae1767cbb47971f1adfee113d229d75b", "cookiepath": "/", "cookiedomain": "", "rulesmode": 1, "cansetcachepreventioncookie": false, "isgeoblocking": false, "logconfig": {"reqprofilingmode": 2, "loggingmode": 2, "except": {"cookies": ["wordpress_sec_47bfcd6d24b7c813bdb3569b8edf5c24", "wordpress_logged_in_47bfcd6d24b7c813bdb3569b8edf5c24", "wordpress_47bfcd6d24b7c813bdb3569b8edf5c24", "wp-postpass_47bfcd6d24b7c813bdb3569b8edf5c24", "wp-resetpass-47bfcd6d24b7c813bdb3569b8edf5c24"], "headers": ["<PERSON><PERSON>"], "post": ["password", "passwd", "pwd"]}}}, "lp": {"ptplug": "malcare", "mode": 3, "captchalimit": 10, "tempblocklimit": 15, "blockalllimit": 100, "failedlogingap": 1800, "successlogingap": 3600, "allblockedgap": 1800}, "time": 1710502076}