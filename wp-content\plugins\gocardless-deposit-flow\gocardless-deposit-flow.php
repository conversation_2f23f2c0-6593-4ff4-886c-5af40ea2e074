<?php
/**
 * Plugin Name: GoCardless Deposit Flow
 * Plugin URI: https://shakilahamed.com
 * Description: Integrates GoCardless deposit payment flow with auction Buy Now functionality. Requires £200 deposit before completing purchase.
 * Version: 1.0.1
 * Author: <PERSON><PERSON><PERSON> ahamed
 * Author URI: https://shakilahamed.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: gocardless-deposit-flow
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GCDF_VERSION', '1.0.1');
define('GCDF_PLUGIN_FILE', __FILE__);
define('GCDF_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('GCDF_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GCDF_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main GoCardless Deposit Flow Plugin Class
 */
class GoCardless_Deposit_Flow {

    /**
     * Single instance of the plugin
     */
    private static $instance = null;

    /**
     * Plugin components
     */
    public $admin;
    public $frontend;
    public $database;
    public $gocardless_api;
    public $webhooks;
    public $auction_integration;

    /**
     * Component initialization flags
     */
    private $components_initialized = false;

    /**
     * Get single instance of the plugin
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
        // Don't initialize components here - do it lazily
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('GoCardless_Deposit_Flow', 'uninstall'));

        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('init', array($this, 'init'));
        add_action('admin_notices', array($this, 'admin_notices'));
    }

    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Load Composer autoloader if available
        if (file_exists(GCDF_PLUGIN_DIR . 'vendor/autoload.php')) {
            require_once GCDF_PLUGIN_DIR . 'vendor/autoload.php';
        }

        // Load plugin classes
        require_once GCDF_PLUGIN_DIR . 'includes/class-database.php';
        require_once GCDF_PLUGIN_DIR . 'includes/class-admin.php';
        require_once GCDF_PLUGIN_DIR . 'includes/class-frontend.php';
        require_once GCDF_PLUGIN_DIR . 'includes/class-gocardless-api.php';
        require_once GCDF_PLUGIN_DIR . 'includes/class-webhooks.php';
        require_once GCDF_PLUGIN_DIR . 'includes/class-auction-integration.php';
    }

    /**
     * Initialize plugin components (lazy loading)
     */
    private function init_components() {
        if ($this->components_initialized) {
            return;
        }

        $this->database = new GCDF_Database();
        $this->admin = new GCDF_Admin();
        $this->frontend = new GCDF_Frontend();
        $this->gocardless_api = new GCDF_GoCardless_API();
        $this->webhooks = new GCDF_Webhooks();
        $this->auction_integration = new GCDF_Auction_Integration();

        $this->components_initialized = true;
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Initialize components only when needed
        $this->init_components();

        // Check if we're on the correct post type/page
        if (is_admin()) {
            return;
        }

        // Initialize frontend components
        $this->frontend->init();
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'gocardless-deposit-flow',
            false,
            dirname(GCDF_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Initialize database component for activation
        $this->database = new GCDF_Database();
        
        // Create database tables
        $this->database->create_tables();

        // Set default options
        $this->set_default_options();

        // Flush rewrite rules
        flush_rewrite_rules();

        // Log activation
        error_log('GoCardless Deposit Flow Plugin activated');
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();

        // Log deactivation
        error_log('GoCardless Deposit Flow Plugin deactivated');
    }

    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        $database = new GCDF_Database();
        $database->drop_tables();

        // Remove plugin options
        delete_option('gcdf_settings');
        delete_option('gcdf_version');

        // Log uninstall
        error_log('GoCardless Deposit Flow Plugin uninstalled');
    }

    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_settings = array(
            'gocardless_environment' => 'sandbox',
            'gocardless_access_token' => '',
            'deposit_amount' => 200.00,
            'currency' => 'GBP',
            'terms_page_id' => 0,
            'success_redirect_url' => '',
            'cancel_redirect_url' => '',
            'require_deposit_all' => true,
            'enable_logging' => true,
        );

        add_option('gcdf_settings', $default_settings);
        add_option('gcdf_version', GCDF_VERSION);
    }

    /**
     * Get plugin settings
     */
    public function get_settings() {
        return get_option('gcdf_settings', array());
    }

    /**
     * Update plugin settings
     */
    public function update_settings($settings) {
        return update_option('gcdf_settings', $settings);
    }

    /**
     * Log messages if logging is enabled
     */
    public function log($message, $level = 'info') {
        $settings = $this->get_settings();
        
        if (!empty($settings['enable_logging'])) {
            $log_message = sprintf(
                '[%s] [%s] %s',
                current_time('Y-m-d H:i:s'),
                strtoupper($level),
                $message
            );
            error_log($log_message);
        }
    }

    /**
     * Check if plugin is properly configured
     */
    public function is_configured() {
        $settings = $this->get_settings();
        return !empty($settings['gocardless_access_token']);
    }

    /**
     * Get deposit amount
     */
    public function get_deposit_amount() {
        $settings = $this->get_settings();
        return floatval($settings['deposit_amount'] ?? 200.00);
    }

    /**
     * Get currency
     */
    public function get_currency() {
        $settings = $this->get_settings();
        return $settings['currency'] ?? 'GBP';
    }

    /**
     * Show admin notices
     */
    public function admin_notices() {
        // Only show to admins
        if (!current_user_can('manage_options')) {
            return;
        }

        // Check if plugin is configured
        if (!$this->is_configured()) {
            $settings_url = admin_url('admin.php?page=gocardless-deposit-flow');
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>GoCardless Deposit Flow:</strong> ';
            echo sprintf(__('Plugin activated but not configured. <a href="%s">Configure settings</a> to start accepting deposits.', 'gocardless-deposit-flow'), $settings_url);
            echo '</p>';
            echo '</div>';
        }
    }

    /**
     * Get database component
     */
    public function get_database() {
        if (!$this->components_initialized) {
            $this->init_components();
        }
        return $this->database;
    }

    /**
     * Get admin component
     */
    public function get_admin() {
        if (!$this->components_initialized) {
            $this->init_components();
        }
        return $this->admin;
    }

    /**
     * Get frontend component
     */
    public function get_frontend() {
        if (!$this->components_initialized) {
            $this->init_components();
        }
        return $this->frontend;
    }

    /**
     * Get GoCardless API component
     */
    public function get_gocardless_api() {
        if (!$this->components_initialized) {
            $this->init_components();
        }
        return $this->gocardless_api;
    }

    /**
     * Get webhooks component
     */
    public function get_webhooks() {
        if (!$this->components_initialized) {
            $this->init_components();
        }
        return $this->webhooks;
    }

    /**
     * Get auction integration component
     */
    public function get_auction_integration() {
        if (!$this->components_initialized) {
            $this->init_components();
        }
        return $this->auction_integration;
    }
}

/**
 * Initialize the plugin
 */
function gcdf_init() {
    return GoCardless_Deposit_Flow::get_instance();
}

// Start the plugin
gcdf_init();

/**
 * Helper function to get plugin instance
 */
function gcdf() {
    return GoCardless_Deposit_Flow::get_instance();
}
