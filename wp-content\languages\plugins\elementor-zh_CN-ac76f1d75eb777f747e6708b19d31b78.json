{"translation-revision-date": "2025-06-09 04:34:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Div Block": ["DIV 块"], "Add new layout element": ["添加新的布局元素"], "Save your changes before moving on because the current document and the one you’re moving to are separate site parts.": ["在继续之前保存您的更改，因为当前文档和您要移动到的文档是不同的站点部分。"], "You are leaving to a separate site part.": ["您将离开到一个单独的站点部分。"], "Don't leave": ["不要离开"], "Save & leave": ["保存并离开"], "Floating Bars": ["浮动条"], "Copy and share Link": ["复制并分享链接"], "Link copied!": ["链接已复制！"], "Floating Buttons": ["悬浮按钮"], "The default cache status for this element:": ["该元素的默认缓存状态："], "Activating cache improves loading times by storing a static version of this element.": ["激活缓存可以通过存储该元素的静态版本来缩短加载时间。"], "Add-ons": ["扩展"], "Scrolling Effects": ["滚动效果"], "Mouse Effects": ["鼠标效果"], "Make any element on your page sticky and<br />keep them in sight at the top or bottom of<br />the screen.": ["使页面上的任何元素具有粘性，并<br />将它们保持在屏幕顶部或底部的可见位置<br />。"], "Add a Mouse Track or 3d Tilt effect with<br />Elementor Pro.": ["使用<br />Elementor Pro 添加鼠标轨迹或 3d 倾斜效果。"], "Get Scrolling Effects such as <br /> vertical/horizontal scroll, transparency,<br /> and more with Elementor Pro.": ["使用 Elementor Pro 获得滚动效果，例如<br />垂直/水平滚动、透明度<br />等等。"], "Sticky": ["<PERSON>y"], "Upgrade to Elementor Pro Advanced to get the Display Conditions Feature as well as additional professional and ecommerce widgets": ["升级到 Elementor Pro Advanced 以获取显示条件功能以及其他专业和电子商务小部件"], "To upload them anyway, ask the site administrator to enable unfiltered file uploads.": ["无论如何要上传它们，请要求站点管理员启用未过滤的文件上传。"], "This is because JSON files may pose a security risk.": ["这是因为 JSON 文件可能会带来安全风险。"], "Sorry, you can't upload that file yet": ["抱歉，您还无法上传该文件"], "Display Conditions": ["显示条件"], "Upgrade now": ["现在升级"], "Rename": ["重命名"], "Uploading JSON files from unknown sources can be harmful and put your site at risk. For maximum safety, only install JSON files from trusted sources.": ["从未知来源上传 JSON 文件可能有害，并使您的网站面临风险。为了获得最大的安全性，仅安装来自可信来源的 JSON 文件。"], "Warning: JSON files may be unsafe": ["警告：JSON 文件可能不安全"], "Clear gallery": ["清除画廊"], "Check out Global Colors": ["查看全局颜色"], "Collapse all elements": ["折叠所有元素"], "Save time by applying Global Fonts to change the style of multiple elements at once. Click %s to see what Global Fonts you already have.": ["通过应用全局字体一次更改多个元素的样式可以节省时间。单击 %s 查看您已有的全局字体。"], "Check out Global Fonts": ["查看全局字体"], "Got it!": ["知道了！"], "Save time by applying Global Colors to change the style of multiple elements at once. Click %s to see what Global Colors you already have.": ["通过应用全局颜色一次更改多个元素的样式可以节省时间。单击 %s 查看您已有的全局颜色。"], "Expand all elements": ["展开所有元素"], "Grid": ["网格"], "Flexbox": ["Flexbox (弹性盒)"], "Paste from other site": ["从其他网站粘贴"], "Paste style": ["粘贴样式"], "Make sure that both sites are updated to last version of Elementor and have enabled the features relevant to the copied element before trying again.": ["请确保两个站点均已更新至 Elementor 的最新版本，并已启用与复制的元素相关的功能，然后再重试。"], "To paste the element from your other site.": ["从其他站点粘贴元素。"], "Do not show this message again": ["不要再显示此消息"], "An error occurred.": ["发生错误。"], "Selected": ["已选择"], "Decide Later": ["稍后再决定"], "Item #%d": ["项目 #%d"], "New options for \"Exit to...\"": ["“退出到...”的新选项"], "Upgrade Now": ["立即升级"], "Upgrade": ["升级"], "Direction Row": ["方向行"], "Direction Column": ["方向栏"], "This will override the design, layout, and other settings of the %s you’re working on.": ["这将覆盖您正在处理的%s的设计、布局和其他设置。"], "Apply the settings of this %s too?": ["也应用这个 %s 的设置吗？"], "Don’t apply": ["不应用"], "Import Without Enabling": ["在不激活的情况下进行内部化"], "Enable and Import": ["启用和进口"], "If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported.": ["如果你不允许上传未经过滤的文件，在上传的模板中使用的任何SVG或JSON(包括lottie)文件将不会被导入。"], "Container": ["容器"], "Add New Container": ["添加新容器 (同层级)"], "Delete %d items": ["删除 %d 项目"], "Remove from Favorites": ["从收藏夹中移除"], "Add to Favorites": ["加入收藏"], "Connect & Activate": ["连接服务器并激活"], "That didn’t work. Try duplicating one kind of element at a time.": ["那不起作用。尝试一次复制一种元素。"], "That didn’t work. Try copying one kind of element at a time.": ["那不起作用。尝试一次复制一种元素。"], "Reload Now": ["重新加载"], "You have made modifications to the list of Active Breakpoints. For these changes to take effect, you need to reload Elementor Editor.": ["您已对活动断点列表进行了修改。为了使这些更改生效，您需要重新加载 Elementor 编辑器。"], "Reload Elementor Editor": ["重新加载 Elementor 编辑器"], "Got it": ["知道了"], "Select a color from any image, or from an element whose color you've manually defined.": ["从任何图像或您手动定义颜色的元素中选择颜色。"], "Seems like your kit was deleted, please create new one or try restore it from trash.": ["您的套件似乎已被删除，请创建新套件或尝试从垃圾箱中恢复它。"], "Your site doesn't have a default kit": ["您的网站没有默认套件"], "Imported": ["已导入"], "Recreate Kit": ["重建套件"], "Color Sampler": ["颜色采样器"], "Please try again.": ["请重试。"], "Learn more": ["了解更多"], "color": ["颜色"], "font": ["字体"], "Delete Global %s": ["删除全局%s"], "Font": ["字体"], "Would you like to exit?": ["您要退出吗？"], "Item #%s": ["项目#%s"], "Invalid Global Color": ["无效的全局颜色"], "Cannot load editor": ["无法加载编辑器"], "Would you like to save the changes you've made?": ["要保存所做的更改吗？"], "Save Changes": ["保存更改"], "Item": ["项目"], "Landing Pages": ["着陆页面"], "Global Colors": ["全局颜色"], "Add Color": ["添加颜色"], "Your changes have been updated.": ["您的更改已更新。"], "Additional Settings": ["附加设置"], "Design System": ["设计系统"], "New Item": ["新项目"], "Add Style": ["添加样式"], "Navigate From Page": ["导航至其他页面"], "User Preferences": ["用户首选项"], "Site Settings": ["站点设置"], "Are you sure you want to create a new Global Font setting?": ["是否确实要创建新的全局字体设置？"], "Please note that the same exact color already exists in your Global Colors list. Are you sure you want to create it?": ["请注意，您的\"全局颜色\"列表中已存在完全相同的颜色。是否确实要创建它？"], "Are you sure you want to create a new Global Color?": ["是否确实要创建新的全局颜色？"], "Global Fonts help you work smarter. Save a Typography, and use it anywhere throughout your site. Access and edit your Global Fonts by clicking the Manage button.": ["全局字体可帮助您更智能地工作。保存排版，并在网站的任何地方使用它。通过单击\"管理\"按钮访问和编辑全局字体。"], "Create New Global Font": ["创建新的全局字体"], "Manage Global Fonts": ["管理全局字体"], "New Typography Setting": ["新的排版设置"], "Global Colors help you work smarter. Save a color, and use it anywhere throughout your site. Access and edit your global colors by clicking the Manage button.": ["全局颜色可帮助您更智能地工作。保存颜色，并在网站的任意位置使用。单击\"管理\"按钮访问和编辑全局颜色。"], "Create New Global Color": ["创建新全局颜色"], "Manage Global Colors": ["管理全局颜色"], "New Global Color": ["新全局颜色"], "You're about to delete a Global %1$s. Note that if it's being used anywhere on your site, it will inherit a default %1$s.": ["您即将删除全局 %1$s。请注意，如果它在您网站上的任何位置使用，它将继承默认的 %1$s。"], "System %s can't be deleted": ["无法删除系统 %s"], "Enable Unfiltered File Uploads": ["启用未过滤的文件上传"], "Dynamic Content": ["动态内容"], "Back": ["返回"], "Settings Reset": ["设置重置"], "Theme Style": ["主题风格"], "Create more personalized and dynamic sites by populating data from various sources with dozens of dynamic tags to choose from.": ["通过使用数十种动态标记填充来自各种来源的数据来创建更多个性化和动态的站点。"], "Enabled": ["已启用"], "More": ["更多"], "Connect to Template Library": ["连接到模板库"], "Pasted": ["贴上"], "Access this template and our entire library by creating a free personal account": ["通过创建免费的个人帐户访问此模板和我们的整个模板库"], "Get Started": ["开始使用"], "Become a Pro user to upload unlimited font icon folders to your website.": ["成为专业用户上传无限字体图标文件夹到您的网站。"], "Upload": ["上传"], "My Libraries": ["我的模板库"], "Icon Library": ["图标库"], "Elementor's New Icon Library": ["Elementor 的新图标库"], "Click here for preview debug": ["点击这里预览调试"], "Elementor v2.6 includes an upgrade from Font Awesome 4 to 5. In order to continue using icons, be sure to click \"Update\".": ["Elementor v2.6 包括从 Font Awesome 4 到 5 的升级。为了继续使用图标，请务必单击“更新”。"], "Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.": ["在启用未过滤文件上传之前，请注意此类文件存在安全风险。 Elementor 确实运行了一个过程来删除可能的恶意代码，但使用此类文件时仍然存在风险。"], "Note: Flexbox Changes": ["注意: Flexbox 变化"], "Elementor 2.5 introduces key changes to the layout using CSS Flexbox. Your existing pages might have been affected, please review your page before publishing.": ["Elementor 2.5 使用 CSS Flexbox 对布局进行了重大更改。您现有的页面可能已受到影响，请在发布前检查您的页面。"], "Custom Positioning": ["自定义定位"], "Continue": ["继续"], "Theme Builder": ["主题生成器"], "Keyboard Shortcuts": ["键盘快捷键"], "Undo": ["撤销"], "Connected successfully.": ["连接成功。"], "Connect": ["连接"], "Create": ["创建"], "Finder": ["搜索"], "Navigator": ["导航器"], "Inner Section": ["内部区段"], "Style Reset": ["样式重置"], "Style Pasted": ["样式已粘贴"], "All Content": ["所有内容"], "Reset style": ["重置样式"], "Got It": ["知道了"], "Copy": ["复制"], "Copy All Content": ["复制所有内容"], "Save as a global": ["保存为全局模块"], "Delete %s": ["删除%s"], "Pages": ["网页"], "Dynamic": ["动态"], "Category": ["分类"], "Blocks": ["模块"], "Connection Lost": ["连接中断"], "Unknown Error": ["未知的错误"], "Saving has been disabled until you’re reconnected.": ["保存已被禁用，直到您重新连接。"], "Server Error": ["服务器错误"], "Publish": ["发布"], "Hide Panel": ["隐藏面板"], "View All Revisions": ["查看所有修订"], "Submit": ["提交"], "Update": ["更新"], "Proceed Anyway": ["仍要继续"], "Your browser isn't compatible": ["您的浏览器不兼容"], "No Results Found": ["未找到结果"], "Please make sure your search is spelled correctly or try a different words.": ["请确保您的搜索拼写正确或尝试不同的单词。"], "No Favorite Templates": ["没有收藏的模板"], "You can mark any pre-designed template as a favorite.": ["您可以将任何预设的模板标记为最爱。"], "Show Panel": ["显示面板"], "New": ["新的"], "This is just a draft. Play around and when you're done - click update.": ["这只是一个草稿。当您完成时 - 点击更新。"], "Duplicate": ["复制到此对象下"], "Your browser isn't compatible with all of Elementor's editing features. We recommend you switch to another browser like Chrome or Firefox.": ["您的浏览器不兼容 Elementor 的所有编辑功能。我们建议您切换到其他浏览器，例如 Chrome 或 Firefox。"], "Have a look": ["点击查看"], "Type Here": ["在此输入"], "Actions": ["操作"], "History": ["历史"], "Edited": ["已编辑"], "Editing Started": ["开始编辑"], "Revisions": ["修订"], "Moved": ["移动"], "Added": ["已添加"], "Add %s": ["添加%s"], "Duplicate %s": ["复制 %s"], "Edit %s": ["编辑 %s"], "Enable": ["启用"], "Disabled": ["已停用"], "Template": ["模板"], "Back to Editor": ["返回编辑器"], "Global": ["全局"], "Delete All Content": ["删除所有内容"], "Color Picker": ["颜色选择器"], "Attention: We are going to DELETE ALL CONTENT from this page. Are you sure you want to do that?": ["注意！我们将要 “清空” 此页面的所有内容，确定要这么做吗？"], "Page": ["页面"], "Close": ["关闭"], "Templates": ["模板"], "Insert": ["插入"], "The following error(s) occurred while processing the request:": ["处理请求时发生了以下错误："], "Haven’t Saved Templates Yet?": ["还没有保存模板吗？"], "Library": ["模版库"], "Your designs will be available for export and reuse on any page or website": ["您的设计可以导出并在任何页面或站点上重用。"], "This is where your templates should be. Design it. Save it. Reuse it.": ["这是你的模板应该放在的地方。设计它、保存它、重复使用它。"], "Learn More": ["了解更多"], "Global Fonts": ["全局字体"], "Paste": ["粘贴"], "Sorry, the content area was not found in your page.": ["抱歉, 在页面中找不到内容区域."], "You must call 'the_content' function in the current template, in order for Elementor to work on this page.": ["您必须在当前模板中调用“the_content”函数，Elementor 才能在此页面上工作。"], "Insert Media": ["插入媒体"], "%s Images Selected": ["%s 个图像已选择"], "No Images Selected": ["未选择图像"], "Are you sure you want to clear this gallery?": ["您确定要清除该画廊吗？"], "Elements": ["元素"], "Settings": ["设置"], "Typography": ["排版"], "Take Over": ["接管"], "Cancel": ["取消"], "Custom": ["自定义"], "Apply": ["应用"], "Preview": ["预览"], "Save": ["保存"], "Structure": ["结构"], "Style": ["样式"], "Please note: All unsaved changes will be lost.": ["请注意：所有未保存的更改都将丢失。"], "Layout": ["布局"], "Advanced": ["高级设置"], "Color": ["颜色"], "Content": ["内容"], "Go Back": ["返回"], "Delete": ["删除"], "Default": ["默认"], "Discard": ["舍弃"], "View Page": ["查看页面"], "Removed": ["删除"], "Inactive": ["未启用"], "Exit": ["退出"], "Active": ["启用"], "View": ["视图"], "Clear": ["清除"], "%s has taken over and is currently editing. Do you want to take over this page editing?": ["%s 已接管并当前正在编辑。您想接管此页面编辑吗？"], "Add New Column": ["新建列"], "Section": ["板块"]}}, "comment": {"reference": "assets/js/editor.js"}}