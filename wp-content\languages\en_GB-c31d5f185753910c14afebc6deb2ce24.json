{"translation-revision-date": "2025-04-06 12:51:41+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Restore": ["Rest<PERSON>"], "View": ["View"], "Install": ["Install"], "Actions": ["Actions"], "Blog title": ["Blog title"], "Palette": ["Palette"], "List": ["List"], "No page found": ["No page found"], "Add new page": ["Add new page"], "Learn more": ["Learn more"], "Words": ["Words"], "Navigation": ["Navigation"], "Featured image": ["Featured image"], "Status": ["Status"], "Footer": ["Footer"], "Table": ["Table"], "Trash": ["Bin"], "Bulk actions": ["Bulk actions"], "Archive: %s": ["Archive: %s"], "Detach": ["<PERSON><PERSON>"], "Hide": ["<PERSON>de"], "Are you sure you want to delete \"%s\"?": ["Are you sure you want to delete \"%s\"?"], "Pending": ["Pending"], "Category": ["Category"], "Blocks styles": ["Blocks styles"], "Copy Error": ["<PERSON><PERSON>"], "The editor has encountered an unexpected error.": ["The editor has encountered an unexpected error."], "Saving": ["Saving"], "Create password": ["Create password"], "Use a secure password": ["Use a secure password"], "Visible to everyone.": ["Visible to everyone."], "Only visible to site admins and editors.": ["Only visible to site admins and editors."], "Headings": ["Headings"], "Editor top bar": ["Editor top bar"], "Visual editor": ["Visual editor"], "Code editor": ["Code editor"], "Editor": ["Editor"], "noun\u0004View": ["View"], "Access all block and document tools in a single place": ["Access all block and document tools in a single place"], "Spotlight mode": ["Spotlight mode"], "Focus on one block at a time": ["Focus on one block at a time"], "Global shortcuts": ["Global shortcuts"], "Save your changes.": ["Save your changes."], "Show or hide the Settings sidebar.": ["Show or hide the Settings sidebar."], "Navigate to the next part of the editor.": ["Navigate to the next part of the editor."], "Navigate to the previous part of the editor.": ["Navigate to the previous part of the editor."], "Switch between visual editor and code editor.": ["Switch between visual editor and code editor."], "Selection shortcuts": ["Selection shortcuts"], "Block shortcuts": ["Block shortcuts"], "Change the block type after adding a new paragraph.": ["Change the block type after adding a new paragraph."], "Forward-slash": ["Forward-slash"], "Text formatting": ["Text formatting"], "Make the selected text bold.": ["Make the selected text bold."], "Make the selected text italic.": ["Make the selected text italic."], "Underline the selected text.": ["Underline the selected text."], "Convert the selected text into a link.": ["Convert the selected text into a link."], "Remove a link.": ["Remove a link."], "Editor publish": ["Editor publish"], "Editor content": ["Editor content"], "Discussion": ["Discussion"], "Close plugin": ["Close plugin"], "Unpin from toolbar": ["Unpin from toolbar"], "Pin to toolbar": ["Pin to toolbar"], "Close Settings": ["Close Settings"], "Editor settings": ["Editor settings"], "Editing code": ["Editing code"], "Exit code editor": ["Exit code editor"], "Visual editor selected": ["Visual editor selected"], "Code editor selected": ["Code editor selected"], "Create": ["Create"], "Scheduled": ["Scheduled"], "Type text or HTML": ["Type text or HTML"], "Start writing with text or HTML": ["Start writing with text or HTML"], "Top toolbar": ["Top toolbar"], "Top toolbar activated": ["Top toolbar activated"], "Top toolbar deactivated": ["Top toolbar deactivated"], "Spotlight mode activated": ["Spotlight mode activated"], "Spotlight mode deactivated": ["Spotlight mode deactivated"], "Display these keyboard shortcuts.": ["Display these keyboard shortcuts."], "All content copied.": ["All content copied."], "Slug": ["Slug"], "Editor footer": ["Editor footer"], "Keyboard shortcuts": ["Keyboard shortcuts"], "Welcome Guide": ["Welcome Guide"], "Get started": ["Get started"], "Buttons": ["Buttons"], "Template Part": ["Template Part"], "Open save panel": ["Open save panel"], "Design": ["Design"], "Color palettes": ["Colour palettes"], "Preferences": ["Preferences"], "Block Library": ["Block Library"], "All templates": ["All templates"], "Custom Template": ["Custom Template"], "Create custom template": ["Create custom template"], "No Title": ["No Title"], "Featured Image": ["Featured Image"], "Displays latest posts written by a single author.": ["Displays latest posts written by a single author."], "Remove %s": ["Remove %s"], "Open List View": ["Open list view"], "Area": ["Area"], "An error occurred while creating the site export.": ["An error occurred while creating the site export."], "An error occurred while deleting the template.": ["An error occurred while deleting the template."], "This template is not revertable.": ["This template is not revertible."], "The editor has encountered an unexpected error. Please reload.": ["The editor has encountered an unexpected error. Please reload."], "Template reverted.": ["Template reverted."], "Template revert failed. Please reload.": ["Template revert failed. Please reload."], "Loading…": ["Loading…"], "Clear customizations": ["Clear customisations"], "Areas": ["Areas"], "Customize the appearance of specific blocks for the whole site.": ["Customise the appearance of specific blocks for the whole site."], "Customize the appearance of specific blocks and for the whole site.": ["Customise the appearance of specific blocks and for the whole site."], "Aa": ["Aa"], "Manage the typography settings for different elements.": ["Manage the typography settings for different elements."], "Elements": ["Elements"], "Manage the fonts used on the site.": ["Manage the fonts used on the site."], "Manage the fonts and typography used on the links.": ["Manage the fonts and typography used on the links."], "%d color": ["%d colour", "%d colours"], "Add custom colors": ["Add custom colours"], "Manage palettes and the default color of different global elements on the site.": ["Manage palettes and the default colour of different global elements on the site."], "Custom colors are empty! Add some colors to create your own color palette.": ["Custom colours are empty! Add some colours to create your own colour palette."], "Custom gradients are empty! Add some gradients to create your own palette.": ["Custom gradients are empty! Add some gradients to create your own palette."], "Palettes are used to provide default color options for blocks and various design tools. Here you can edit the colors with their labels.": ["Palettes are used to provide default colour options for blocks and various design tools. Here you can edit the colours with their labels."], "Reset to defaults": ["Reset to defaults"], "Template part created.": ["Template part created."], "Drag to resize": ["Drag to resize"], "Use left and right arrow keys to resize the canvas.": ["Use left and right arrow keys to resize the canvas."], "Welcome to the site editor": ["Welcome to the site editor"], "Edit your site": ["Edit your site"], "Design everything on your site — from the header right down to the footer — using blocks.": ["Design everything on your site – from the header right down to the footer – using blocks."], "Click <StylesIconImage /> to start designing your blocks, and choose your typography, layout, and colors.": ["Click <StylesIconImage /> to start designing your blocks, and choose your typography, layout, and colours."], "styles": ["styles"], "Welcome to Styles": ["Welcome to styles"], "Tweak your site, or give it a whole new look! Get creative — how about a new color palette for your buttons, or choosing a new font? Take a look at what you can do here.": ["Tweak your site, or give it a whole new look! Get creative – how about a new colour palette for your buttons, or choosing a new font? Take a look at what you can do here."], "Set the design": ["Set the design"], "You can customize your site as much as you like with different colors, typography, and layouts. Or if you prefer, just leave it up to your theme to handle!": ["You can customise your site as much as you like with different colours, typography, and layouts. Or if you prefer, just leave it up to your theme to handle!"], "Personalize blocks": ["Personalise blocks"], "You can adjust your blocks to ensure a cohesive experience across your site — add your unique colors to a branded Button block, or adjust the Heading block to your preferred size.": ["You can adjust your blocks to ensure a cohesive experience across your site – add your unique colours to a branded button block, or adjust the heading block to your preferred size."], "New to block themes and styling your site?": ["New to block themes and styling your site?"], "https://wordpress.org/documentation/article/styles-overview/": ["https://wordpress.org/documentation/article/styles-overview/"], "Here’s a detailed guide to learn how to make the most of it.": ["Here’s a detailed guide to learn how to make the most of it."], "You attempted to edit an item that doesn't exist. Perhaps it was deleted?": ["You attempted to edit an item that doesn't exist. Perhaps it was deleted?"], "An error occurred while creating the template.": ["An error occurred while creating the template."], "An error occurred while creating the template part.": ["An error occurred while creating the template part."], "An error occurred while renaming the template.": ["An error occurred while renaming the template."], "Replace template": ["Replace template"], "An error occurred while reverting the template parts.": ["An error occurred while reverting the template parts."], "Added by": ["Added by"], "Saving failed.": ["Saving failed."], "Site updated.": ["Site updated."], "site exporter menu item\u0004Export": ["Export"], "Choose a pattern": ["Choose a pattern"], "Insert a link to a post or page.": ["Insert a link to a post or page."], "The \"%s\" plugin has encountered an error and cannot be rendered.": ["The \"%s\" plugin has encountered an error and cannot be rendered."], "\"%s\" deleted.": ["\"%s\" deleted."], "Download your theme with updated templates and styles.": ["Download your theme with updated templates and styles."], "Browse styles": ["Browse styles"], "Search for blocks": ["Search for blocks"], "Choose a different style combination for the theme styles.": ["Choose a different style combination for the theme styles."], "This Navigation Menu is empty.": ["This Navigation Menu is empty."], "View options": ["View options"], "Strikethrough the selected text.": ["Strikethrough the selected text."], "Make the selected text inline code.": ["Make the selected text inline code."], "Copy all blocks": ["Copy all blocks"], "Use the template as supplied by the theme.": ["Use the template as supplied by the theme."], "Describe the template, e.g. \"Post with sidebar\". A custom template can be manually applied to any post or page.": ["Describe the template, e.g. \"Post with sidebar\". A custom template can be manually applied to any post or page."], "Add template": ["Add template"], "Delete template: %s": ["Delete template: %s"], "Summary": ["Summary"], "Manage all template parts": ["Manage all template parts"], "Zoom-out View": ["Zoom-out View"], "View site": ["View site"], "Select heading level": ["Select heading level"], "Typography styles": ["Typography styles"], "Colors styles": ["Colours styles"], "Layout styles": ["Layout styles"], "%s block styles": ["%s block styles"], "Typography %s styles": ["Typography %s styles"], "Manage the fonts and typography used on headings.": ["Manage the fonts and typography used on headings."], "Manage the fonts and typography used on buttons.": ["Manage the fonts and typography used on buttons."], "Create template part": ["Create template part"], "Archive: %1$s (%2$s)": ["Archive: %1$s (%2$s)"], "Displays an archive with the latest posts of type: %s.": ["Displays an archive with the latest posts of type: %s."], "Single item: %s": ["Single item: %s"], "Single item: %1$s (%2$s)": ["Single item: %1$s (%2$s)"], "Displays a single item: %s.": ["Displays a single item: %s."], "Displays taxonomy: %s.": ["Displays taxonomy: %s."], "Search Authors": ["Search Authors"], "No authors found.": ["No authors found."], "All Authors": ["All authors"], "Suggestions list": ["Suggestions list"], "Add template: %s": ["Add template: %s"], "Select whether to create a single template for all items or a specific one.": ["Select whether to create a single template for all items or a specific one."], "For all items": ["For all items"], "For a specific item": ["For a specific item"], "This template will be used only for the specific item chosen.": ["This template will be used only for the specific item chosen."], "\"%s\" successfully created.": ["\"%s\" successfully created."], "Custom template": ["Custom template"], "Time to read": ["Time to read"], "(no title %s)": ["(no title %s)"], "Toggle distraction free mode.": ["Toggle distraction-free mode."], "Transform heading to paragraph.": ["Transform heading to paragraph."], "Transform paragraph to heading.": ["Transform paragraph to heading."], "Convert the current heading to a paragraph.": ["Convert the current heading to a paragraph."], "Convert the current paragraph or heading to a heading of level 1 to 6.": ["Convert the current paragraph or heading to a heading of level 1 to 6."], "Distraction free": ["Distraction free"], "Write with calmness": ["Write with calmness"], "Distraction free mode activated": ["Distraction-free mode activated"], "Distraction free mode deactivated": ["Distraction-free mode deactivated"], "%s styles applied.": ["%s styles applied."], "Apply this block’s typography, spacing, dimensions, and color styles to all %s blocks.": ["Apply this block’s typography, spacing, dimensions, and colour styles to all %s blocks."], "Apply globally": ["Apply globally"], "Loading templates…": ["Loading templates…"], "No templates found": ["No templates found"], "Manage all templates": ["Manage all templates"], "Template parts": ["Template parts"], "This is a custom template that can be applied manually to any Post or Page.": ["This is a custom template that can be applied manually to any Post or Page."], "All template parts": ["All Template Parts"], "Style Variations": ["Style Variations"], "Add your own CSS to customize the appearance and layout of your site.": ["Add your own CSS to customise the appearance and layout of your site."], "Randomize colors": ["Randomise colours"], "Choose a variation to change the look of the site.": ["Choose a variation to change the look of the site."], "Code Is Poetry": ["Code Is Poetry"], "Style Book": ["Style Book"], "Close Style Book": ["Close Style Book"], "Open %s styles in Styles panel": ["Open %s styles in Styles panel"], "CSS": ["CSS"], "Styles actions": ["Styles actions"], "template\u0004Customized": ["Customised"], "template part\u0004Customized": ["Customised"], "Template part": ["Template part"], "Save site, content, and template changes": ["Save site, content, and template changes"], "Customize the appearance of your website using the block editor.": ["Customise the appearance of your website using the block editor."], "Express the layout of your site with templates.": ["Express the layout of your site with templates."], "This is the %s template part.": ["This is the %s template part."], "Create new templates, or reset any customizations made to the templates supplied by your theme.": ["Create new templates, or reset any customisations made to the templates supplied by your theme."], "Create new template parts, or reset any customizations made to the template parts supplied by your theme.": ["Create new template parts, or reset any customisations made to the template parts supplied by your theme."], "A custom template can be manually applied to any post or page.": ["A custom template can be manually applied to any post or page."], "Examples of blocks in the %s category": ["Examples of blocks in the %s category"], "Show block tools": ["Show block tools"], "Hide block tools": ["Hide block tools"], "Manage all of my patterns": ["Manage all of my patterns"], "Toggle settings sidebar": ["Toggle settings sidebar"], "Toggle block inspector": ["Toggle block inspector"], "Toggle top toolbar": ["Toggle top toolbar"], "Go to the Dashboard": ["Go to the Dashboard"], "Global styles revisions list": ["Global styles revisions list"], "Examples of blocks": ["Examples of blocks"], "Last modified": ["Last modified"], "Edit styles": ["Edit styles"], "Looking for template parts? Find them in \"Patterns\".": ["Looking for template parts? Find them in \"Patterns\"."], "E.g. %s": ["E.g. %s"], "Select what the new template should apply to:": ["Select what the new template should apply to:"], "\"%s\" reverted.": ["\"%s\" reverted."], "Are you sure you want to delete %d template parts?": ["Are you sure you want to delete %d template parts?"], "Set the Posts Page title. Appears in search results, and when the page is shared on social media.": ["Set the Posts Page title. Appears in search results, and when the page is shared on social media."], "Posts per page": ["Posts per page"], "Set the default number of posts to display on blog pages, including categories and tags. Some templates may override this setting.": ["Set the default number of posts to display on blog pages, including categories and tags. Some templates may override this setting."], "<time>%s</time>": ["<time>%s</time>"], "template\u0004(Customized)": ["(Customised)"], "An error occurred while renaming the pattern.": ["An error occurred while renaming the pattern."], "Create pattern": ["Create pattern"], "This pattern cannot be edited.": ["This pattern cannot be edited."], "Manage what patterns are available when editing the site.": ["Manage what patterns are available when editing the site."], "Loading patterns…": ["Loading patterns…"], "No template parts or patterns found": ["No template parts or patterns found"], "Go to %s": ["Go to %s"], "This is the %s pattern.": ["This is the %s pattern."], "Syncing": ["Syncing"], "Navigation title": ["Navigation title"], "Are you sure you want to delete this Navigation menu?": ["Are you sure you want to delete this Navigation menu?"], "Navigation menus are a curated collection of blocks that allow visitors to get around your site.": ["Navigation menus are a curated collection of blocks that allow visitors to get around your site."], "Navigation Menu missing.": ["Navigation Menu missing."], "Deleted Navigation menu": ["Deleted Navigation menu"], "Unable to delete Navigation menu (%s).": ["Unable to delete Navigation menu (%s)."], "Renamed Navigation menu": ["Renamed Navigation menu"], "Unable to rename Navigation menu (%s).": ["Unable to rename Navigation menu (%s)."], "%s (Copy)": ["%s (Copy)"], "Duplicated Navigation menu": ["Duplicated Navigation menu"], "Unable to duplicate Navigation menu (%s).": ["Unable to duplicate Navigation menu (%s)."], "No Navigation Menus found.": ["No Navigation Menus found."], "Manage your Navigation menus.": ["Manage your Navigation menus."], "Activate & Save": ["Activate & Save"], "Review %d change…": ["Review %d change…", "Review %d changes…"], "No title": ["No title"], "An error occurred while creating the page.": ["An error occurred while creating the page."], "Draft a new page": ["Draft a new page"], "Create draft": ["Create draft"], "Loading pages…": ["Loading pages…"], "Manage all pages": ["Manage all pages"], "Published <time>%s</time>": ["Published <time>%s</time>"], "Scheduled: <time>%s</time>": ["Scheduled: <time>%s</time>"], "Parent": ["Parent"], "Unknown": ["Unknown"], "%s mins": ["%s mins"], "< 1 min": ["< 1 min"], "\"%s\" moved to the Trash.": ["\"%s\" moved to the Trash."], "An error occurred while moving the page to the trash.": ["An error occurred while moving the page to the bin."], "Manage the fonts and typography used on captions.": ["Manage the fonts and typography used on captions."], "Close revisions": ["Close revisions"], "Close Styles": ["Close Styles"], "Not ready to publish.": ["Not ready to publish."], "Waiting for review before publishing.": ["Waiting for review before publishing."], "Publish automatically on a chosen date.": ["Publish automatically on a chosen date."], "Change status": ["Change status"], "An error occurred while updating the status": ["An error occurred while updating the status"], "Hide this page behind a password": ["Hide this page behind a password"], "Last edited %s": ["Last edited %s"], "Editor Canvas": ["Editor <PERSON><PERSON>"], "Back to page": ["Back to page"], "Editing a page": ["Editing a page"], "Continue": ["Continue"], "It’s now possible to edit page content in the site editor. To customise other parts of the page like the header and footer switch to editing the template using the settings sidebar.": ["It’s now possible to edit page content in the site editor. To customise other parts of the page like the header and footer switch to editing the template using the settings sidebar."], "Editing a template": ["Editing a template"], "Note that the same template can be used by multiple pages, so any changes made here may affect other pages on the site. To switch back to editing the page content click the ‘Back’ button in the toolbar.": ["Note that the same template can be used by multiple pages, so any changes made here may affect other pages on the site. To switch back to editing the page content click the ‘Back’ button in the toolbar."], "Fallback content": ["Fallback content"], "Query Loop displays a list of posts or pages.": ["Query Loop displays a list of posts or pages."], "Post Content displays the content of a post or page.": ["Post Content displays the content of a post or page."], "Post Template displays each post or page in a Query Loop.": ["Post Template displays each post or page in a Query Loop."], "Open Navigation": ["Open Navigation"], "View site (opens in a new tab)": ["View site (opens in a new tab)"], "Open command palette": ["Open command palette"], "Use left and right arrow keys to resize the canvas. Hold shift to resize in larger increments.": ["Use left and right arrow keys to resize the canvas. Hold shift to resize in larger increments."], "Save panel": ["Save panel"], "Open styles": ["Open styles"], "Learn about styles": ["Learn about styles"], "Reset template part: %s": ["Reset template part: %s"], "Delete template part: %s": ["Delete template part: %s"], "An error occurred while deleting the pattern.": ["An error occurred while deleting the pattern."], "Press Enter to edit, or Delete to delete the pattern.": ["Press Enter to edit, or Delete to delete the pattern."], "Are you sure you want to clear these customizations?": ["Are you sure you want to clear these customisations?"], "Empty pattern": ["Empty pattern"], "Editing this pattern will also update anywhere it is used": ["Editing this pattern will also update anywhere it is used"], "Patterns that are kept in sync across the site.": ["Patterns that are kept in sync across the site."], "Patterns that can be changed freely without affecting the site.": ["Patterns that can be changed freely without affecting the site."], "Patterns content": ["Patterns content"], "Allow comments on new posts": ["Allow comments on new posts"], "Unsaved changes by %s": ["Unsaved changes by %s"], "Changes saved by %1$s on %2$s": ["Changes saved by %1$s on %2$s"], "Changes will apply to new posts only. Individual posts may override these settings.": ["Changes will apply to new posts only. Individual posts may override these settings."], "All patterns": ["All patterns"], "Distraction free off.": ["Distraction free off."], "Distraction free on.": ["Distraction free on."], "Editor preferences": ["Editor preferences"], "Hide block breadcrumbs": ["Hide block breadcrumbs"], "Show block breadcrumbs": ["Show block breadcrumbs"], "Breadcrumbs hidden.": ["Breadcrumbs hidden."], "Breadcrumbs visible.": ["Breadcrumbs visible."], "Template renamed.": ["Template renamed."], "Template part renamed.": ["Template part renamed."], "An error occurred while renaming the template part.": ["An error occurred while renaming the template part."], "An error occurred while reverting the template part.": ["An error occurred while reverting the template part."], "A list of all patterns from all sources.": ["A list of all patterns from all sources."], "Import pattern from JSON": ["Import pattern from JSON"], "Imported \"%s\" from JSON.": ["Imported \"%s\" from JSON."], "%s (removed)": ["%s (removed)"], "Customized": ["Customised"], "Add your own CSS to customize the appearance of the %s block. You do not need to include a CSS selector, just add the property and value.": ["Add your own CSS to customise the appearance of the %s block. You do not need to include a CSS selector, just add the property and value."], "font weight\u0004Extra-light": ["Extra-light"], "font weight\u0004Normal": ["Normal"], "font weight\u0004Semi-bold": ["Semi-bold"], "font weight\u0004Extra-bold": ["Extra-bold"], "font style\u0004Normal": ["Normal"], "%1$s/%2$s variants active": ["%1$s/%2$s variants active"], "Fonts were installed successfully.": ["Fonts were installed successfully."], "Font family uninstalled successfully.": ["<PERSON>ont family uninstalled successfully."], "Choose font variants. Keep in mind that too many variants could make your site slower.": ["Choose font variants. Keep in mind that too many variants could make your site slower."], "Theme Fonts": ["Theme Fonts"], "To install fonts from Google you must give permission to connect directly to Google servers. The fonts you install will be downloaded from Google and stored on your site. Your site will then use these locally-hosted fonts.": ["To install fonts from Google you must give permission to connect directly to Google servers. The fonts you install will be downloaded from Google and stored on your site. Your site will then use these locally-hosted fonts."], "You can alternatively upload files directly on the Upload tab.": ["You can alternatively upload files directly on the Upload tab."], "Allow access to Google Fonts": ["Allow access to Google Fonts"], "Select font variants to install.": ["Select font variants to install."], "Font name…": ["Font name…"], "No fonts found. Try with a different search term": ["No fonts found. Try with a different search term"], "Upload font": ["Upload font"], "Install Fonts": ["Install Fonts"], "Fonts": ["Fonts"], "Manage fonts": ["Manage fonts"], "Reset the styles to the theme defaults": ["Reset the styles to the theme defaults"], "Default styles": ["Default styles"], "Any unsaved changes will be lost when you apply this revision.": ["Any unsaved changes will be lost when you apply this revision."], "Choose a template": ["Choose a template"], "Replace the contents of this template with another.": ["Replace the contents of this template with another."], "Customize CSS": ["Customise CSS"], "Style revisions": ["Style revisions"], "Open code editor": ["Open code editor"], "Pattern renamed.": ["Pattern renamed."], "\"%s\" duplicated.": ["\"%s\" duplicated."], "Theme & plugin patterns cannot be edited.": ["Theme & plugin patterns cannot be edited."], "Empty template part": ["Empty template part"], "Export as JSON": ["Export as JSON"], "An error occurred while reverting the template.": ["An error occurred while reverting the template."], "Option that shows all synchronized patterns\u0004Synced": ["Synced"], "Option that shows all patterns that are not synchronized\u0004Not synced": ["Not synced"], "header landmark area\u0004Header": ["Header"], "Exit Distraction Free": ["Exit distraction free"], "Enter Distraction Free ": ["Enter distraction free"], "Toggle spotlight": ["Toggle spotlight"], "Spotlight off.": ["Spotlight off."], "Spotlight on.": ["Spotlight on."], "Close List View": ["Close list view"], "List View off.": ["List view off."], "List View on.": ["List view on."], "Top toolbar off.": ["Top toolbar off."], "Top toolbar on.": ["Top toolbar on."], "Blocks from synced patterns that can have overriden content.": ["Blocks from synced patterns that can have overridden content."], "Templates deleted.": ["Templates deleted."], "Template parts deleted.": ["Template parts deleted."], "An error occurred while deleting the template part.": ["An error occurred while deleting the template part."], "An error occurred while deleting the template parts.": ["An error occurred while deleting the template parts."], "An error occurred while deleting the templates: %s": ["An error occurred while deleting the templates: %s"], "An error occurred while deleting the template parts: %s": ["An error occurred while deleting the template parts: %s"], "Some errors occurred while deleting the templates: %s": ["Some errors occurred while deleting the templates: %s"], "Some errors occurred while deleting the template parts: %s": ["Some errors occurred while deleting the template parts: %s"], "Style Revisions": ["Style revisions"], "Text that indicates that the pattern is not synchronized\u0004Not synced": ["Not synced"], "Text that indicates that the pattern is synchronized\u0004Synced": ["Synced"], "Deselect item: %s": ["Deselect item: %s"], "Select item: %s": ["Select item: %s"], "Select a new item": ["Select a new item"], "Deselect item": ["Deselect item"], "Edit %d item": ["Edit %d item", "Edit %d items"], "Bulk edit": ["Bulk edit"], "Add filter": ["Add filter"], "Deselect all": ["Deselect all"], "No results": ["No results"], "View details": ["View details"], "Is": ["Is"], "Is not": ["Is not"], "Sort ascending": ["Sort ascending"], "Sort descending": ["Sort descending"], "Manage templates": ["Manage templates"], "Manage template parts": ["Manage template parts"], "Activating %s": ["Activating %s"], "Activate %s & Save": ["Activate %s and save"], "Activate %s": ["Activate %s"], "Browse and manage pages.": ["Browse and manage pages."], "All pages": ["All pages"], "Drafts": ["Drafts"], "My view": ["My view"], "New view": ["New view"], "Add new view": ["Add new view"], "Rename view": ["Rename view"], "Custom Views": ["Custom views"], "Manage pages": ["Manage pages"], "Saving your changes will change your active theme from %1$s to %2$s.": ["Saving your changes will change your active theme from %1$s to %2$s."], "Reset styles": ["Reset styles"], "Edit template: %s": ["Edit template: %s"], "Rename pattern": ["Rename pattern"], "Duplicate pattern": ["Duplicate pattern"], "%d variant": ["%d variant", "%d variants"], "Installed Fonts": ["Installed fonts"], "There was an error uninstalling the font family. ": ["There was an error uninstalling the font family."], "Are you sure you want to delete \"%s\" font and all its variants and assets?": ["Are you sure you want to delete \"%s\" font and all its variants and assets?"], "Connect to Google Fonts": ["Connect to Google Fonts"], "font categories\u0004All": ["All"], "Error installing the fonts, could not be downloaded.": ["Error installing the fonts, could not be downloaded."], "Revoke access to Google Fonts": ["Revoke access to Google Fonts"], "paging\u0004Page <CurrentPageControl /> of %s": ["Page <CurrentPageControl /> of %s"], "Current page": ["Current page"], "No fonts found to install.": ["No fonts found to install."], "Uploaded fonts appear in your library and can be used in your theme. Supported formats: .ttf, .otf, .woff, and .woff2.": ["Uploaded fonts appear in your library and can be used in your theme. Supported formats: .ttf, .otf, .woff, and .woff2."], "No fonts installed.": ["No fonts installed."], "Add fonts": ["Add fonts"], "heading levels\u0004All": ["All"], "Changes saved by %1$s on %2$s. This revision matches current editor styles.": ["Changes saved by %1$s on %2$s. This revision matches current editor styles."], "(Unsaved)": ["(Unsaved)"], "These styles are already applied to your site.": ["These styles are already applied to your site."], "Pagination Navigation": ["Pagination navigation"], "Revisions (%s)": ["Revisions (%s)"], "Click on previously saved styles to preview them. To restore a selected version to the editor, hit \"Apply.\" When you're ready, use the Save button to save your changes.": ["Click on previously saved styles to preview them. To restore a selected version to the editor, hit \"Apply.\" When you're ready, use the “Save” button to save your changes."], "Global Styles pagination navigation": ["Global styles pagination navigation"], "%1$s ‹ %2$s ‹ Editor — WordPress": ["%1$s ‹ %2$s ‹ Editor – WordPress"], "%1$s ‹ %2$s": ["%1$s ‹ %2$s"], "Items per page": ["Items per page"], "Fields": ["Fields"], "Sort by": ["Sort by"], "Search items": ["Search items"], "<Span1>%1$s </Span1><Span2>is %2$s</Span2>": ["<Span1>%1$s </Span1><Span2>is %2$s</Span2>"], "<Span1>%1$s </Span1><Span2>is not %2$s</Span2>": ["<Span1>%1$s </Span1><Span2>is not %2$s</Span2>"], "Unknown status for %1$s": ["Unknown status for %1$s"], "Conditions": ["Conditions"], "Filter by: %1$s": ["Filter by: %1$s"], "Reset filters": ["Reset filters"], "Are you sure you want to delete %d pages?": ["Are you sure you want to delete %d pages?"], "Pages moved to the Trash.": ["Pages moved to the bin."], "An error occurred while moving the post to the trash.": ["An error occurred while moving the post to the bin."], "An error occurred while moving the posts to the trash.": ["An error occurred while moving the posts to the bin."], "An error occurred while moving the posts to the trash: %s": ["An error occurred while moving the posts to the bin: %s"], "Some errors occurred while moving the pages to the trash: %s": ["Some errors occurred while moving the pages to the bin: %s"], "Permanently delete": ["Permanently delete"], "\"%s\" permanently deleted.": ["\"%s\" permanently deleted."], "The posts were permanently deleted.": ["The posts were permanently deleted."], "An error occurred while permanently deleting the post.": ["An error occurred while permanently deleting the post."], "An error occurred while permanently deleting the posts.": ["An error occurred while permanently deleting the posts."], "An error occurred while permanently deleting the posts: %s": ["An error occurred while permanently deleting the posts: %s"], "Some errors occurred while permanently deleting the posts: %s": ["Some errors occurred while permanently deleting the posts: %s"], "%d posts have been restored.": ["%d posts have been restored."], "\"%s\" has been restored.": ["\"%s\" has been restored."], "View revisions": ["View revisions"], "The patterns were deleted.": ["The patterns were deleted."], "Some errors occurred while deleting the patterns: %s": ["Some errors occurred while deleting the patterns: %s"], "Are you sure you want to delete %d patterns?": ["Are you sure you want to delete %d patterns?"], "action label\u0004Duplicate": ["Duplicate"], "action label\u0004Duplicate pattern": ["Duplicate pattern"], "action label\u0004Duplicate template part": ["Duplicate template part"], "An error occurred while deleting the pattern category.": ["An error occurred while deleting the pattern category."], "Are you sure you want to delete the category \"%s\"? The patterns will not be deleted.": ["Are you sure you want to delete the category \"%s\"? The patterns will not be deleted."], "Action menu for %s pattern category": ["Action menu for %s pattern category"], "Sync Status": ["Sync status"], "%s items reverted.": ["%s items reverted."], "Delete %d item?": ["Delete %d item?", "Delete %d items?"], "Delete \"%s\"?": ["Delete \"%s\"?"], "Empty template": ["Empty template"], "No description.": ["No description."], "An error occurred while deleting the templates.": ["An error occurred while deleting the templates."], "Reset template: %s": ["Reset template: %s"], "An error occurred while restoring the posts.": ["An error occurred while restoring the posts."], "An error occurred while restoring the post.": ["An error occurred while restoring the post."], "An error occurred while deleting the patterns.": ["An error occurred while deleting the patterns."], "An error occurred while deleting the patterns: %s": ["An error occurred while deleting the patterns: %s"], "An error occurred while reverting the templates.": ["An error occurred while reverting the templates."], "There was an error installing fonts.": ["There was an error installing fonts."], "Font library\u0004Library": ["Library"], "No": ["No"], "Delete": ["Delete"], "Content": ["Content"], "Links": ["Links"], "Yes": ["Yes"], "Theme": ["Theme"], "Page title": ["Page title"], "Text": ["Text"], "Custom": ["Custom"], "User": ["User"], "Styles": ["Styles"], "Upload": ["Upload"], "Saved": ["Saved"], "Block": ["Block"], "Tools": ["Tools"], "List View": ["List View"], "Blocks": ["Blocks"], "Reset": ["Reset"], "Duplicate": ["Duplicate"], "%d result found.": ["%d result found.", "%d results found."], "Options": ["Options"], "Skip": ["<PERSON><PERSON>"], "Typography": ["Typography"], "Patterns": ["Patterns"], "Captions": ["Captions"], "More": ["More"], "Duotone": ["Duotone"], "Layout": ["Layout"], "Navigate to the previous view": ["Navigate to the previous view"], "Rename": ["<PERSON><PERSON>"], "font style\u0004Italic": ["Italic"], "font weight\u0004Thin": ["Thin"], "font weight\u0004Light": ["Light"], "font weight\u0004Medium": ["Medium"], "font weight\u0004Bold": ["Bold"], "font weight\u0004Black": ["Black"], "H1": ["H1"], "H2": ["H2"], "H3": ["H3"], "H4": ["H4"], "H5": ["H5"], "H6": ["H6"], "https://wordpress.org/documentation/article/site-editor/": ["https://wordpress.org/documentation/article/site-editor/"], "Pattern": ["Pattern"], "Grid": ["Grid"], "My patterns": ["My patterns"], "First page": ["First page"], "paging\u0004%1$s of %2$s": ["%1$s of %2$s"], "Last page": ["Last page"], "No results found": ["No results found"], "Activate": ["Activate"], "Title": ["Title"], "Published": ["Published"], "Pending Review": ["Pending Review"], "Draft": ["Draft"], "Author": ["Author"], "Edit": ["Edit"], "Apply": ["Apply"], "Publish": ["Publish"], "Revisions": ["Revisions"], "Undo": ["Undo"], "Categories": ["Categories"], "Advanced": ["Advanced"], "Save": ["Save"], "Name": ["Name"], "Description": ["Description"], "None": ["None"], "Plugins": ["Plugins"], "Preview": ["Preview"], "Cancel": ["Cancel"], "Password": ["Password"], "Private": ["Private"], "Update": ["Update"], "Search": ["Search"], "Uncategorized": ["Uncategorised"], "Pages": ["Pages"], "Close": ["Close"], "(no title)": ["(no title)"], "Help": ["Help"], "Settings": ["Settings"], "Default": ["<PERSON><PERSON><PERSON>"], "Select all": ["Select all"], "Next page": ["Next page"], "Previous page": ["Previous page"], "Template": ["Template"], "Remove": ["Remove"], "Colors": ["Colours"], "Clear": ["Clear"], "Deselect": ["Deselect"], "Move down": ["Move down"], "Move up": ["Move up"], "Back": ["Back"], "Site Icon": ["Site Icon"], "Word count type. Do not translate!\u0004words": ["words"], "Additional CSS": ["Additional CSS"], "Learn more about CSS": ["Learn more about CSS"], "%s item": ["%s item", "%s items"], "Date": ["Date"], "(opens in a new tab)": ["(opens in a new tab)"], "Move to Trash": ["Move to Bin"], "Details": ["Details"], "Password protected": ["Password protected"], "%1$s (%2$s)": ["%1$s (%2$s)"], "Templates": ["Templates"], "Sorry, you are not allowed to upload this file type.": ["Sorry, you are not allowed to upload this file type."], "Template Parts": ["Template Parts"]}}, "comment": {"reference": "wp-includes/js/dist/edit-site.js"}}