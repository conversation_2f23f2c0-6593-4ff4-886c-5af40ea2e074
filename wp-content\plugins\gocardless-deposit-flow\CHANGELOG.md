# Changelog

All notable changes to the GoCardless Deposit Flow plugin will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.1] - 2025-08-22

### Fixed
- **Memory exhaustion error during plugin activation** - Resolved fatal error that prevented plugin activation
- **Infinite loop issues in auction integration hooks** - Fixed circular dependency causing memory leaks
- **Component initialization timing** - Prevented premature component loading during plugin bootstrap

### Improved
- **Lazy loading implementation** - All plugin components now initialize only when needed
- **Memory usage optimization** - Reduced memory footprint by deferring component instantiation
- **Component access control** - Added getter methods for better encapsulation

### Changed
- **Component initialization pattern** - Moved from immediate to lazy loading
- **Database component access** - Updated all references to use `get_database()` method
- **Admin component access** - Updated all references to use `get_admin()` method
- **Frontend component access** - Updated all references to use `get_frontend()` method
- **API component access** - Updated all references to use `get_gocardless_api()` method
- **Webhooks component access** - Updated all references to use `get_webhooks()` method
- **Auction integration access** - Updated all references to use `get_auction_integration()` method

### Technical Details
- Added `$components_initialized` flag to prevent double initialization
- Implemented `init_components()` method with lazy loading logic
- Created getter methods for all plugin components
- Updated activation method to properly initialize database component
- Modified all class files to use new getter methods instead of direct property access

## [1.0.0] - 2025-08-22

### Added
- Initial plugin release
- GoCardless API integration for bank transfer payments
- Modal interface for deposit payment flow
- Auction system integration with "Buy Now" interception
- Real-time webhook support for payment status updates
- Comprehensive admin dashboard with settings and statistics
- Security measures including nonce verification and input sanitization
- Database tables for deposit tracking
- Custom post meta for deposit requirements
- Terms and conditions integration
- Error handling and logging system
- Multi-currency support (default: GBP)
- Responsive design for mobile compatibility

### Features
- **Deposit Requirement**: £200 default deposit before vehicle purchase
- **Payment Flow**: Seamless GoCardless bank transfer integration
- **Auction Integration**: Automatic auction completion after deposit
- **Admin Controls**: Complete settings management and monitoring
- **Webhook Processing**: Real-time payment status updates
- **Security**: Comprehensive validation and access controls
