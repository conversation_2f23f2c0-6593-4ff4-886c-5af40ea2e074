# Translation of Plugins - All-in-One WP Migration and Backup - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - All-in-One WP Migration and Backup - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-01-20 16:27:07+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - All-in-One WP Migration and Backup - Stable (latest release)\n"

#: lib/view/reset/index.php:41
msgid "Reset Hub Demo"
msgstr "Reset Hub Demo"

#: lib/view/reset/index.php:41
msgid "Upgrade to Premium "
msgstr "Upgrade to Premium"

#: lib/view/reset/index.php:39
msgid "Please note, the features displayed below are part of an image showcasing the potential of Reset Hub in its Premium version. To activate and enjoy these advanced features, <a href=\"https://servmask.com/products/unlimited-extension\" title=\"Upgrade to Premium\" target=\"_blank\">upgrade to Premium now</a>! Elevate your website management experience with these exclusive functionalities and priority support."
msgstr "Please note, the features displayed below are part of an image showcasing the potential of Reset Hub in its Premium version. To activate and enjoy these advanced features, <a href=\"https://servmask.com/products/unlimited-extension\" title=\"Upgrade to Premium\" target=\"_blank\">upgrade to Premium now</a>! Elevate your website management experience with these exclusive functionalities and priority support."

#: lib/view/reset/index.php:37
msgid "Experience Full Functionality with Premium!"
msgstr "Experience full functionality with Premium!"

#: lib/view/reset/index.php:36
msgid "Star"
msgstr "Star"

#: lib/controller/class-ai1wm-main-controller.php:672
#: lib/controller/class-ai1wm-main-controller.php:673
msgid "Reset Hub"
msgstr "Reset Hub"

#: lib/view/main/premium-badge.php:33
msgid "Premium"
msgstr "Premium"

#: lib/view/schedules/index.php:111
msgid "Leverage the simplicity of Dropbox for your backup needs. Direct your scheduled backups to be stored in Dropbox. It's secure, straightforward, and keeps your backups at your fingertips."
msgstr "Leverage the simplicity of Dropbox for your backup needs. Direct your scheduled backups to be stored in Dropbox. It's secure, straightforward, and keeps your backups at your fingertips."

#: lib/view/schedules/index.php:163
msgid "Tailor your backup schedules to fit the complexity of your WordPress Multisite. Choose to export the entire network or only a selection of subsites according to your requirements. Effortless management for even the most intricate site networks."
msgstr "Tailor your backup schedules to fit the complexity of your WordPress Multisite. Choose to export the entire network or only a selection of subsites according to your requirements. Effortless management for even the most intricate site networks."

#: lib/view/schedules/index.php:150
msgid "We've got you covered with an array of supported storage providers. Whether you prefer Box, Amazon S3, WebDav or something else, you can choose the one that fits your needs best. Secure your backups exactly where you want them."
msgstr "We've got you covered with an array of supported storage providers. Whether you prefer Box, Amazon S3, WebDAV, or something else, you can choose the one that fits your needs best. Secure your backups exactly where you want them."

#: lib/view/schedules/index.php:137
msgid "Enjoy the flexibility of FTP storage. Direct your scheduled backups to your own FTP server. You'll have full control over your data, providing you with a versatile and private storage solution."
msgstr "Enjoy the flexibility of FTP storage. Direct your scheduled backups to your own FTP server. You'll have full control over your data, providing you with a versatile and private storage solution."

#: lib/view/schedules/index.php:124
msgid "Harness the power of OneDrive for your backups. Set up your scheduled backups to be saved directly in your OneDrive. It's secure, integrated with your Microsoft account, and keeps your data readily accessible."
msgstr "Harness the power of OneDrive for your backups. Set up your scheduled backups to be saved directly in your OneDrive. It's secure, integrated with your Microsoft account, and keeps your data readily accessible."

#: lib/view/schedules/index.php:98
msgid "Benefit from the robustness of Google Drive. Schedule your backups to be saved directly to your Google Drive account. Simple, secure, and integrated into a platform you already use."
msgstr "Benefit from the robustness of Google Drive. Schedule your backups to be saved directly to your Google Drive account. Simple, secure, and integrated into a platform you already use."

#: lib/view/schedules/index.php:85
msgid "Manage your storage effectively with our flexible retention settings. Decide how many backups you want to keep at a time. Old backups are automatically cleared, keeping your storage neat and efficient."
msgstr "Manage your storage effectively with our flexible retention settings. Decide how many backups you want to keep at a time. Old backups are automatically cleared, keeping your storage neat and efficient."

#: lib/view/schedules/index.php:77 lib/view/schedules/index.php:82
msgid "Retention settings"
msgstr "Retention settings"

#: lib/view/schedules/index.php:72
msgid "Stay informed, not overwhelmed. Tailor your notification preferences to get updates that matter to you. Whether it's the status of each backup, or just critical alerts, control what you want to be notified about."
msgstr "Stay informed, not overwhelmed. Tailor your notification preferences to get updates that matter to you. Whether it's the status of each backup, or just critical alerts, control about what you want to be notified."

#: lib/view/schedules/index.php:64 lib/view/schedules/index.php:69
msgid "Notification settings"
msgstr "Notification settings"

#: lib/view/schedules/index.php:59
msgid "Never worry about forgetting to back up your site again. Choose from various scheduling options, from daily to monthly, and we'll automate the rest. Backups happen like clockwork, giving you peace of mind and a solid safety net"
msgstr "Never worry about forgetting to back up your site again. Choose from various scheduling options, from daily to monthly, and we'll automate the rest. Backups happen like clockwork, giving you peace of mind and a solid safety net"

#: lib/view/schedules/index.php:56 lib/view/schedules/index.php:69
#: lib/view/schedules/index.php:82 lib/view/schedules/index.php:95
#: lib/view/schedules/index.php:108 lib/view/schedules/index.php:121
#: lib/view/schedules/index.php:134 lib/view/schedules/index.php:147
#: lib/view/schedules/index.php:160
msgid "Enable this feature"
msgstr "Enable this feature"

#: lib/view/schedules/index.php:51
msgid "Backup scheduler"
msgstr "Backup scheduler"

#: lib/view/schedules/index.php:45 lib/view/schedules/index.php:155
#: lib/view/schedules/index.php:160
msgid "Multisite Schedules"
msgstr "Multisite Schedules"

#: lib/view/schedules/index.php:44 lib/view/schedules/index.php:142
#: lib/view/schedules/index.php:147
msgid "More Storage Providers"
msgstr "More Storage Providers"

#: lib/view/schedules/index.php:43 lib/view/schedules/index.php:129
#: lib/view/schedules/index.php:134
msgid "FTP Storage"
msgstr "FTP Storage"

#: lib/view/schedules/index.php:42 lib/view/schedules/index.php:116
#: lib/view/schedules/index.php:121
msgid "OneDrive Storage"
msgstr "OneDrive Storage"

#: lib/view/schedules/index.php:41 lib/view/schedules/index.php:103
#: lib/view/schedules/index.php:108
msgid "Dropbox Storage"
msgstr "Dropbox Storage"

#: lib/view/schedules/index.php:40 lib/view/schedules/index.php:90
#: lib/view/schedules/index.php:95
msgid "Google Drive Storage"
msgstr "Google Drive Storage"

#: lib/view/schedules/index.php:39
msgid "Retention Settings"
msgstr "Retention Settings"

#: lib/view/schedules/index.php:38
msgid "Notification Settings"
msgstr "Notification Settings"

#: lib/view/schedules/index.php:37 lib/view/schedules/index.php:56
msgid "Backup Scheduler"
msgstr "Backup Scheduler"

#: lib/controller/class-ai1wm-main-controller.php:681
#: lib/controller/class-ai1wm-main-controller.php:682
msgid "Schedules"
msgstr "Schedules"

#: lib/model/import/class-ai1wm-import-check-encryption.php:61
msgid "Backup is encrypted. Please provide decryption password: "
msgstr "Backup is encrypted. Please provide decryption password: "

#: lib/view/export/advanced-settings.php:66
msgid "Password-protect and encrypt backups"
msgstr "Password-protect and encrypt backups"

#: lib/view/export/advanced-settings.php:53
msgid "A password is required"
msgstr "A password is required"

#: lib/view/export/advanced-settings.php:46
msgid "Encrypt this backup with a password"
msgstr "Encrypt this backup with a password"

#: lib/model/import/class-ai1wm-import-check-encryption.php:50
msgid "Importing an encrypted backup is not supported on this server. The process cannot continue. <a href=\"https://help.servmask.com/knowledgebase/unable-to-encrypt-and-decrypt-backups/\" target=\"_blank\">Technical details</a>"
msgstr "Importing an encrypted backup is not supported on this server. The process cannot continue. <a href=\"https://help.servmask.com/knowledgebase/unable-to-encrypt-and-decrypt-backups/\" target=\"_blank\">Technical details</a>"

#: lib/controller/class-ai1wm-main-controller.php:797
#: lib/view/export/advanced-settings.php:58
msgid "The passwords do not match"
msgstr "The passwords do not match"

#: lib/controller/class-ai1wm-main-controller.php:796
#: lib/view/export/advanced-settings.php:56
msgid "Repeat the password"
msgstr "Repeat the password"

#: lib/controller/class-ai1wm-main-controller.php:794
msgid "Submit"
msgstr "Submit"

#: lib/controller/class-ai1wm-main-controller.php:792
msgid "The backup is encrypted"
msgstr "The backup is encrypted"

#: lib/view/backups/backups-list.php:131
msgid "List"
msgstr "List"

#: lib/view/backups/backups-list.php:129
msgid "Show backup content"
msgstr "Show backup content"

#: lib/view/backups/backups-list.php:122
msgid "Downloading is not possible because backups directory is not accessible."
msgstr "Downloading is not possible because the backups directory is not accessible."

#: lib/controller/class-ai1wm-main-controller.php:838
msgid "Reading..."
msgstr "Reading..."

#: lib/controller/class-ai1wm-main-controller.php:837
msgid "List the content of the backup"
msgstr "List the content of the backup"

#: lib/controller/class-ai1wm-main-controller.php:835
msgid "Error"
msgstr "Error"

#: lib/view/backups/backups-list.php:95
msgid "More"
msgstr "More"

#. translators: 1: Number of backups.
#: lib/controller/class-ai1wm-main-controller.php:771
msgid "You have %d backups"
msgstr "You have %d backups"

#. translators: 1: Number of backups.
#. translators: Numbers of backups.
#: lib/controller/class-ai1wm-main-controller.php:769
#: lib/view/main/backups.php:33
msgid "You have %d backup"
msgid_plural "You have %d backups"
msgstr[0] "You have %d backup"
msgstr[1] "You have %d backups"

#. translators: 1: Error message.
#: lib/view/updater/error.php:33
msgid "Error: %s"
msgstr "Error: %s"

#: lib/view/main/contact-support.php:33
msgid "Contact Support"
msgstr "Contact Support"

#: lib/model/import/class-ai1wm-import-permalinks.php:38
msgid "Retrieving WordPress permalinks settings..."
msgstr "Retrieving WordPress permalinks settings..."

#: lib/view/import/avada.php:46
msgid "» <a class=\"ai1wm-no-underline\" href=\"https://theme-fusion.com/documentation/avada/installation-maintenance/important-update-information/#clear-caches\" target=\"_blank\">Reset Avada Fusion Builder cache</a>.<br />"
msgstr "» <a class=\"ai1wm-no-underline\" href=\"https://theme-fusion.com/documentation/avada/installation-maintenance/important-update-information/#clear-caches\" target=\"_blank\">Reset Avada Fusion Builder cache</a>.<br />"

#: lib/view/import/avada.php:33
msgid "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a><br />"
msgstr "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a><br />"

#: lib/view/main/translate.php:33
msgid "Translate"
msgstr "Translate"

#: lib/controller/class-ai1wm-main-controller.php:789
msgid "I have enough disk space"
msgstr "I have enough disk space"

#: lib/view/backups/backups-list.php:48 lib/view/backups/index.php:51
msgid "Refreshing backup list..."
msgstr "Refreshing backup list..."

#: lib/view/import/button-file.php:33
msgid "To choose a file please go inside the link and click on the browse button."
msgstr "To choose a file please go inside the link and click on the browse button."

#: lib/controller/class-ai1wm-main-controller.php:787
msgid "Finish"
msgstr "Finish"

#: lib/model/import/class-ai1wm-import-validate.php:102
msgid "The archive file appears to be corrupted. Follow <a href=\"https://help.servmask.com/knowledgebase/corrupted-archive/\" target=\"_blank\">this article</a> for possible fixes."
msgstr "The archive file appears to be corrupted. Follow <a href=\"https://help.servmask.com/knowledgebase/corrupted-archive/\" target=\"_blank\">this article</a> for possible fixes."

#: lib/view/import/import-buttons.php:41
msgid "Drag & Drop a backup to import it"
msgstr "Drag & Drop a backup to import it"

#: lib/controller/class-ai1wm-main-controller.php:829
msgid "Backup restore requires the Unlimited extension. <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Get it here</a>"
msgstr "Backup restore requires the Unlimited extension. <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Get it here</a>"

#: lib/controller/class-ai1wm-main-controller.php:274
msgid "All-in-One WP Migration Command"
msgstr "All-in-One WP Migration Command"

#: lib/controller/class-ai1wm-main-controller.php:760
#: lib/model/export/class-ai1wm-export-init.php:45
msgid "Preparing to export..."
msgstr "Preparing to export..."

#. translators: Human time diff
#: lib/view/backups/backups-list.php:84
msgid "%s ago"
msgstr "%s ago"

#: lib/controller/class-ai1wm-main-controller.php:756
msgid "Your purchase ID is invalid. Please <a href=\"mailto:<EMAIL>\">contact us</a>."
msgstr "Your purchase ID is invalid. Please <a href=\"mailto:<EMAIL>\">contact us</a>."

#: lib/controller/class-ai1wm-main-controller.php:828
msgid "Are you sure you want to delete this backup?"
msgstr "Are you sure you want to delete this backup?"

#: lib/controller/class-ai1wm-main-controller.php:791
msgid "Please do not close this browser window or your import will fail"
msgstr "Please do not close this browser window or your import will fail"

#: lib/controller/class-ai1wm-main-controller.php:790
msgid "Continue"
msgstr "Continue"

#: lib/controller/class-ai1wm-main-controller.php:788
msgid "Proceed"
msgstr "Proceed"

#: lib/controller/class-ai1wm-main-controller.php:786
msgid "Stop import"
msgstr "Stop import"

#: lib/controller/class-ai1wm-main-controller.php:767
msgid "Stop export"
msgstr "Stop export"

#: lib/controller/class-ai1wm-main-controller.php:766
#: lib/controller/class-ai1wm-main-controller.php:785
msgid "Close"
msgstr "Close"

#: lib/model/export/class-ai1wm-export-config.php:38
msgid "Preparing configuration..."
msgstr "Preparing configuration..."

#. translators: Progress.
#: lib/model/export/class-ai1wm-export-config-file.php:63
#: lib/model/export/class-ai1wm-export-config-file.php:99
msgid "Archiving configuration...<br />%d%% complete"
msgstr "Archiving configuration...<br />%d%% complete"

#. translators: Progress.
#: lib/model/import/class-ai1wm-import-validate.php:90
#: lib/model/import/class-ai1wm-import-validate.php:162
msgid "Unpacking archive...<br />%d%% complete"
msgstr "Unpacking archive...<br />%d%% complete"

#. translators: Progress.
#: lib/model/export/class-ai1wm-export-database-file.php:69
#: lib/model/export/class-ai1wm-export-database-file.php:105
msgid "Archiving database...<br />%d%% complete"
msgstr "Archiving database...<br />%d%% complete"

#. translators: Progress.
#: lib/model/import/class-ai1wm-import-database.php:81
#: lib/model/import/class-ai1wm-import-database.php:1037
msgid "Restoring database...<br />%d%% complete"
msgstr "Restoring database...<br />%d%% complete"

#: lib/controller/class-ai1wm-main-controller.php:777
msgid "Preparing to import..."
msgstr "Preparing to import..."

#: lib/controller/class-ai1wm-main-controller.php:1314
msgid "Monthly"
msgstr "Monthly"

#: lib/controller/class-ai1wm-main-controller.php:1310
msgid "Weekly"
msgstr "Weekly"

#: lib/model/import/class-ai1wm-import-mu-plugins.php:37
msgid "Activating mu-plugins..."
msgstr "Activating mu-plugins..."

#: lib/model/import/class-ai1wm-import-blogs.php:37
msgid "Preparing blogs..."
msgstr "Preparing blogs..."

#: lib/view/updater/modal.php:57
msgid "There is an update available. To update, you must enter your"
msgstr "There is an update available. To update, you must enter your"

#: lib/view/updater/modal.php:50
msgid "Discard"
msgstr "Discard"

#: lib/view/updater/modal.php:48
msgid "Save"
msgstr "Save"

#: lib/view/updater/modal.php:44
msgid "here"
msgstr "here"

#: lib/view/updater/modal.php:43
msgid "Don't have a Purchase ID? You can find your Purchase ID"
msgstr "Don't have a Purchase ID? You can find your Purchase ID"

#: lib/view/updater/modal.php:39 lib/view/updater/modal.php:58
msgid "Purchase ID"
msgstr "Purchase ID"

#: lib/view/updater/modal.php:36
msgid "To update your plugin/extension to the latest version, please fill your Purchase ID below."
msgstr "To update your plugin/extension to the latest version, please fill your Purchase ID below."

#: lib/view/updater/modal.php:35
msgid "Enter your Purchase ID"
msgstr "Enter your Purchase ID"

#: lib/controller/class-ai1wm-main-controller.php:755
#: lib/view/updater/check.php:33
msgid "Check for updates"
msgstr "Check for updates"

#. Author of the plugin
#: all-in-one-wp-migration.php
msgid "ServMask"
msgstr "ServMask"

#. Plugin URI of the plugin
#. Author URI of the plugin
#: all-in-one-wp-migration.php
msgid "https://servmask.com/"
msgstr "https://servmask.com/"

#: lib/view/import/index.php:39
msgid "Import Site"
msgstr "Import Site"

#: lib/view/import/import-buttons.php:45
msgid "Import From"
msgstr "Import From"

#: lib/view/export/index.php:39
msgid "Export Site"
msgstr "Export Site"

#: lib/view/export/help-section.php:52
msgid "Once the file is successfully downloaded on your computer, you can import it to any of your WordPress sites."
msgstr "Once the file is successfully downloaded on your computer, you can import it to any of your WordPress sites."

#: lib/view/export/help-section.php:48
msgid "Press \"Export\" button and the site archive file will pop up in your browser."
msgstr "Press \"Export\" button and the site archive file will pop up in your browser."

#: lib/view/export/help-section.php:44
msgid "In the advanced settings section you can configure more precisely the way of exporting."
msgstr "In the advanced settings section you can configure more precisely the way of exporting."

#: lib/view/export/find-replace.php:54
msgid "Add"
msgstr "Add"

#: lib/view/export/find-replace.php:41
msgid "in the database"
msgstr "in the database"

#: lib/view/export/find-replace.php:40
msgid "<another-text>"
msgstr "<another-text>"

#: lib/view/export/find-replace.php:39 lib/view/export/find-replace.php:47
msgid "Replace with"
msgstr "Replace with"

#: lib/view/export/find-replace.php:38
msgid "<text>"
msgstr "<text>"

#: lib/view/export/button-file.php:33 lib/view/import/button-file.php:34
msgid "File"
msgstr "File"

#: lib/view/export/advanced-settings.php:38
msgid "(click to expand)"
msgstr "(click to expand)"

#: lib/view/export/advanced-settings.php:37
msgid "Advanced options"
msgstr "Advanced options"

#: lib/view/common/share-buttons.php:73
msgid "Tweet"
msgstr "Tweet"

#: lib/view/common/leave-feedback.php:69
msgid "Cancel"
msgstr "Cancel"

#: lib/view/common/leave-feedback.php:72
msgid "Send"
msgstr "Send"

#: lib/controller/class-ai1wm-main-controller.php:795
#: lib/view/export/advanced-settings.php:51
msgid "Enter a password"
msgstr "Enter a password"

#: lib/view/common/sidebar-right.php:41
msgid "Leave Feedback"
msgstr "Leave Feedback"

#: lib/view/backups/index.php:59
msgid "Create backup"
msgstr "Create backup"

#: lib/view/backups/backups-list.php:136 lib/view/backups/backups-list.php:138
msgid "Delete"
msgstr "Delete"

#: lib/view/backups/backups-list.php:101 lib/view/backups/backups-list.php:103
msgid "Restore"
msgstr "Restore"

#: lib/view/backups/backups-list.php:109 lib/view/backups/backups-list.php:111
#: lib/view/backups/backups-list.php:114 lib/view/backups/backups-list.php:116
#: lib/view/backups/backups-list.php:124
msgid "Download"
msgstr "Download"

#: lib/view/backups/backups-list.php:40
msgid "Size"
msgstr "Size"

#: lib/view/backups/backups-list.php:39
msgid "Date"
msgstr "Date"

#: lib/view/backups/backups-list.php:38
msgid "Name"
msgstr "Name"

#: lib/model/import/class-ai1wm-import-done.php:365
#: lib/model/import/class-ai1wm-import-done.php:367
#: lib/model/import/class-ai1wm-import-done.php:369
msgid "Your site has been imported successfully!"
msgstr "Your site has been imported successfully!"

#: lib/model/export/class-ai1wm-export-download.php:37
msgid "Renaming export file..."
msgstr "Renaming export file..."

#: lib/controller/class-ai1wm-main-controller.php:662
#: lib/controller/class-ai1wm-main-controller.php:663
#: lib/view/backups/index.php:39
msgid "Backups"
msgstr "Backups"

#: lib/controller/class-ai1wm-main-controller.php:653
#: lib/controller/class-ai1wm-main-controller.php:654
msgid "Import"
msgstr "Import"

#: lib/controller/class-ai1wm-main-controller.php:644
#: lib/controller/class-ai1wm-main-controller.php:645
msgid "Export"
msgstr "Export"

#: lib/view/main/multisite-notice.php:43
msgid "Get multisite"
msgstr "Get multisite"