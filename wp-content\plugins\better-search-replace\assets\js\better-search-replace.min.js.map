{"version": 3, "file": "better-search-replace.min.js", "sources": ["better-search-replace.js"], "names": ["$", "bsr_update_progress_bar", "percentage", "speed", "animate", "width", "search_replace_submit", "bsr_error_wrap", "toggle_tooltip", "icon", "bubble", "next", "position", "not", "hide", "parent", "nodeName", "offset", "hasClass", "css", "left", "top", "height", "toggle", "on", "e", "preventDefault", "val", "data", "serialize", "replace", "html", "addClass", "before", "append", "bsr_object_vars", "processing", "bsr_process_step", "action", "step", "page", "ajax", "type", "url", "endpoint", "bsr_ajax_nonce", "ajax_nonce", "bsr_step", "bsr_page", "bsr_data", "dataType", "success", "response", "message", "remove", "next_action", "removeClass", "window", "location", "fail", "unknown", "show", "console", "log", "no_tables", "no_search", "slider", "value", "page_size", "range", "min", "max", "slide", "event", "ui", "text", "$iframeBody", "contents", "find", "this", "stopPropagation", "appendTo", "setTimeout", "$settings_saved_notice", "$bsr_notices", "length", "prependTo", "j<PERSON><PERSON><PERSON>"], "mappings": "CAAWA,IAuGV,SAASC,EAAyBC,EAAYC,GACxB,KAAA,IAATA,IACXA,EAAQ,KAETH,EAAG,eAAgB,EAAEI,QAAQ,CAC5BC,MAAOH,CACR,EAAGC,CAAM,CACV,CAxGA,IAiEKG,EACAC,EA2DL,SAASC,EAAgBC,GACxB,IACIC,GAASD,EADFT,EAAGS,CAAK,GACDE,KAAK,EAKnBC,GAFJZ,EAAG,iBAAkB,EAAEa,IAAKH,CAAO,EAAEI,KAAK,EAE3BL,EAAKG,SAAS,GAEM,OAA9BH,EAAKM,OAAO,EAAE,GAAGC,WACrBJ,EAAWH,EAAKQ,OAAO,GAGnBP,EAAOQ,SAAU,MAAO,EAC5BR,EAAOS,IAAI,CACVC,KAAUR,EAASQ,KAAOV,EAAOL,MAAM,EAAII,EAAKJ,MAAM,EAAI,GAAO,KACjEgB,IAAST,EAASS,IAAMZ,EAAKa,OAAO,EAAI,EAAI,GAAO,IACpD,CAAC,EACUZ,EAAOQ,SAAU,QAAS,EACrCR,EAAOS,IAAK,CACXC,KAAYR,EAASQ,KAAOV,EAAOL,MAAM,EAAI,EAAM,EAAM,KACzDgB,IAAST,EAASS,IAAMZ,EAAKa,OAAO,EAAI,GAAO,IAChD,CAAE,EAEFZ,EAAOS,IAAK,CACXC,KAAUR,EAASQ,KAAOX,EAAKJ,MAAM,EAAI,GAAO,KAChDgB,IAAST,EAASS,IAAMZ,EAAKa,OAAO,EAAI,EAAI,GAAO,IACpD,CAAE,EAGHZ,EAAOa,OAAO,CACf,CA3FKjB,EAAwBN,EAAG,aAAc,EACzCO,EAAiBP,EAAG,iBAAkB,EAC1CM,EAAsBkB,GAAI,QAAS,SAAUC,GAE5CA,EAAEC,eAAe,EAEVpB,EAAsBY,SAAU,iBAAkB,IAEjDlB,EAAG,aAAc,EAAE2B,IAAI,EAEhB3B,EAAG,mBAAoB,EAAE2B,IAAI,GAItCC,EADO5B,EAAG,kBAAmB,EAAE6B,UAAU,EAC7BC,QAAQ,OAAQ,iBAAkB,EAElDvB,EAAewB,KAAK,EAAE,EAAEjB,KAAK,EAC7BR,EAAsB0B,SAAU,8BAA+B,EAC/DhC,EAAG,kBAAmB,EAAEiC,OAAO,+JAA+J,EAC9LjC,EAAE,oBAAoB,EAAEkC,OAAQ,0CAA4CC,gBAAgBC,WAAa,MAAO,EA5EpH,SAASC,EAAkBC,EAAQC,EAAMC,EAAMZ,GAE9C5B,EAAEyC,KAAK,CACNC,KAAM,OACNC,IAAKR,gBAAgBS,SAAWN,EAChCV,KAAM,CACLiB,eAAiBV,gBAAgBW,WACjCR,OAAQA,EACRS,SAAUR,EACVS,SAAUR,EACVS,SAAUrB,CACX,EACAsB,SAAU,OACVC,QAAS,SAAUC,GAGc,KAAA,IAApBA,EAASC,UACpBrD,EAAE,kBAAkB,EAAEsD,OAAO,EAC7BtD,EAAE,oBAAoB,EAAEkC,OAAQ,0CAA4CkB,EAASC,QAAU,MAAO,GAGlG,QAAUD,EAASb,MAEvBtC,EAAyB,MAAO,EAGI,KAAA,IAAxBmD,EAASG,aACpBtD,EAAyB,KAAM,CAAE,EACjCoC,EAAkBe,EAASG,YAAa,EAAG,EAAGH,EAASH,QAAS,IAEhEjD,EAAE,sBAAsB,EAAEsD,OAAO,EACjCtD,EAAE,eAAe,EAAEwD,YAAY,8BAA+B,EAC9DC,OAAOC,SAAWN,EAAST,OAI5B1C,EAAyBmD,EAASlD,UAAW,EAC7CmC,EAAkBC,EAAQc,EAASb,KAAMa,EAASZ,KAAMY,EAASH,QAAS,EAG5E,CACD,CAAC,EAAEU,KAAK,SAAUP,GACjBpD,EAAE,sBAAsB,EAAEsD,OAAO,EACjCtD,EAAE,eAAe,EAAEwD,YAAY,8BAA+B,EAC9DxD,EAAE,iBAAiB,EAAE+B,KAAM,yBAA2BI,gBAAgByB,QAAU,YAAa,EAAEC,KAAK,EAC/FJ,OAAOK,SAAWL,OAAOK,QAAQC,KACrCD,QAAQC,IAAIX,CAAQ,CAEtB,CAAC,CAEF,EA2BsB,yBAA0B,EAAG,EAAGxB,CAAK,GATvDrB,EAAewB,KAAM,yBAA2BI,gBAAgB6B,UAAY,YAAa,EAAEH,KAAK,EAFhGtD,EAAewB,KAAM,yBAA2BI,gBAAgB8B,UAAY,YAAa,EAAEJ,KAAK,EAgBnG,CAAC,EAoBD7D,EAAE,uBAAuB,EAAEkE,OAAO,CACjCC,MAAOhC,gBAAgBiC,UACvBC,MAAO,MACPC,IAAK,IACLC,IAAK,IACLhC,KAAM,IACNiC,MAAO,SAAUC,EAAOC,GACvB1E,EAAE,sBAAsB,EAAE2E,KAAMD,EAAGP,KAAM,EACzCnE,EAAE,gBAAgB,EAAE2B,IAAK+C,EAAGP,KAAM,CACnC,CACD,CAAC,EAsCFnE,EAAE,MAAM,EAAEwB,GAAG,yBAA0B,WACtC,IAAIoD,EAAc5E,EAAG,mBAAoB,EAAE6E,SAAS,EAAEC,KAAM,MAAO,EAEnEF,EAAYpD,GAAI,YAAa,WAAY,SAAUC,GAClDA,EAAEC,eAAe,EACjBkD,EAAYE,KAAM,iBAAkB,EAAEhE,KAAK,EAC3CN,EAAgBuE,IAAK,EACrBtD,EAAEuD,gBAAgB,CACnB,CAAC,EAEDJ,EAAYpD,GAAI,aAAc,KAAM,SAAUC,GAC7CmD,EAAYE,KAAM,iBAAkB,EAAEhE,KAAK,CAC5C,CAAC,CACF,CAAC,EAEDd,EAAG,MAAO,EAAEwB,GAAI,YAAa,WAAY,SAAUC,GAClDjB,EAAgBuE,IAAK,CACtB,CAAE,EAEF/E,EAAG,MAAO,EAAEwB,GAAI,aAAc,WAAY,SAAUC,GACnDzB,EAAG,iBAAkB,EAAEc,KAAK,CAC7B,CAAE,EAEFd,EAAG,gBAAiB,EAClBiF,SAAS,uBAAwB,EACjC9D,IAAK,UAAW,OAAQ,EAE1B+D,WAAW,WACV,IAAMC,EAAyBnF,EAAG,iCAAkC,EACpE,IAAMoF,EAAepF,EAAG,cAAe,GAElCmF,EAAuBE,QAAUD,EAAaC,UAClDrF,EAAG,gDAAiD,EAAEsF,UAAW,SAAU,EAC3EH,EAAuBG,UAAW,6BAA8B,EAAEnE,IAAK,UAAW,OAAQ,EAC1FiE,EAAaE,UAAW,6BAA8B,EAAEnE,IAAK,UAAW,OAAQ,GAGjFnB,EAAG,6CAA8C,EAAEwB,GAAI,QAAS,SAAWC,GACnE2D,EAAaC,QACnBrF,EAAG,6BAA8B,EAAEsD,OAAO,CAE5C,CAAC,CACF,EAAG,EAAE,CAIL,GAAGiC,MAAO"}