{"translation-revision-date": "2024-12-16 16:53:45+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "This result includes an Elementor Pro widget that's not available with your current plan. Upgrade to use all the widgets in this result.": ["This result includes an Elementor Pro widget that's not available with your current plan. Upgrade to use all the widgets in this result."], "Luxurious theme with rich colors discussing": ["Luxurious theme with rich colours discussing"], "Earthy tones and organic shapes featuring": ["Earthy tones and organic shapes featuring"], "Professional look with clean lines for": ["Professional look with clean lines for"], "Futuristic design with neon accents about": ["Futuristic design with neon accents about"], "Replace Background": ["Replace Background"], "Remove Background": ["Remove Background"], "Image preview": ["Image preview"], "Expand": ["Expand"], "Seems like the prompt contains words that could generate harmful content. Write a different prompt to continue.": ["Seems like the prompt contains words that could generate harmful content. Write a different prompt to continue."], "We were unable to generate that prompt.": ["We were unable to generate that prompt."], "Reconnect": ["Reconnect"], "Create with AI": ["Create with AI"], "Copy prompt": ["Copy prompt"], "Spark your imagination with images generated by our community": ["Spark your imagination with images generated by our community"], "Hold tight, painting dreams might take a moment.": ["Hold tight, painting dreams might take a moment."], "Bringing your vision to life...": ["Bringing your vision to life..."], "Use Image": ["Use Image"], "Output": ["Output"], "Upscale to": ["Upscale to"], "Make an image larger and improve it’s resolution.": ["Make an image larger and improve it’s resolution."], "Original image size": ["Original image size"], "Position image in it’s new size to generate content around the edges.": ["Position image in it’s new size to generate content around the edges."], "Generate": ["Generate"], "Describe what you want to generate in the marked area (English only)": ["Describe what you want to generate in the marked area (English only)"], "Mark an area and edit it with a prompt.": ["Mark an area and edit it with a prompt."], "Create new versions of the original image.": ["Create new versions of the original image."], "Stay tuned! More incredible AI tools are coming your way soon.": ["Stay tuned! More incredible AI tools are coming your way soon."], "Edit with AI": ["Edit with AI"], "Generate with a prompt": ["Generate with a prompt"], "Variations": ["Variations"], "Resize": ["Resize"], "Generative Fill": ["Generative Fill"], "Expand Image": ["Expand Image"], "Generate images": ["Generate images"], "Generate again": ["Generate again"], "Generate images by selecting the desired type and style, and entering a prompt.": ["Generate images by selecting the desired type and style, and entering a prompt."], "Imagine anything create everything": ["Imagine anything create everything"], "Aspect ratio": ["Aspect ratio"], "Image type": ["Image type"], "Enhance prompt": ["Enhance prompt"], "Reference strength": ["Reference strength"], "describe your image": ["describe your image"], "Wood": ["<PERSON>"], "Stone": ["Stone"], "Origami": ["Origami"], "Digital": ["Digital"], "Clay": ["<PERSON>"], "3D": ["3D"], "Watercolor": ["Watercolor"], "Pencil Drawing": ["Pencil Drawing"], "Oil Painting": ["Oil Painting"], "Line Art": ["Line Art"], "Doodle": ["Doodle"], "Handmade": ["Handmade"], "Neon Punk": ["Neon Punk"], "Low Poly": ["Low Poly"], "Pixel Art": ["Pixel Art"], "Vector": ["Vector"], "Isometric": ["Isometric"], "Fantasy Art": ["Fantasy Art"], "Comic Book": ["Comic Book"], "Cinematic": ["Cinematic"], "Cartoon": ["Cartoon"], "Anime": ["Anime"], "Digital Art": ["Digital Art"], "Bokeh": ["Bokeh"], "Neon": ["Neon"], "Mosaic": ["Mosaic"], "Gradient": ["Gradient"], "Floral": ["<PERSON><PERSON>"], "Long Exposure": ["Long Exposure"], "Portrait": ["Portrait"], "Macro": ["Macro"], "Landscape": ["Landscape"], "Photographic": ["Photographic"], "Yes, leave": ["Yes, leave"], "Images will be gone forever and we won’t be able to recover them.": ["Images will be gone forever and we won’t be able to recover them."], "Leave Elementor AI?": ["Leave Elementor AI?"], "Upgrade Elementor AI": ["Upgrade Elementor AI"], "Code with AI": ["Code with AI"], "Write with AI": ["Write with AI"], "search": ["search"], "Vietnamese": ["Vietnamese"], "Turkish": ["Turkish"], "Thai": ["Thai"], "Swedish": ["Swedish"], "Spanish": ["Spanish"], "Russian": ["Russian"], "Portuguese": ["Portuguese"], "Polish": ["Polish"], "Persian": ["Persian"], "Korean": ["Korean"], "Japanese": ["Japanese"], "Italian": ["Italian"], "Indonesian": ["Indonesian"], "Hungarian": ["Hungarian"], "Hebrew": ["Hebrew"], "Greek": ["Greek"], "German": ["German"], "French": ["French"], "Finnish": ["Finnish"], "English": ["English"], "Dutch": ["Dutch"], "Danish": ["Danish"], "Czech": ["Czech"], "Chinese": ["Chinese"], "Arabic": ["Arabic"], "AI": ["AI"], "Use text": ["Use text"], "Generate text": ["Generate text"], "Translate to": ["Translate to"], "Change tone": ["Change tone"], "Upgrade": ["Upgrade"], "Get Elementor AI": ["Get Elementor AI"], "Let’s do it": ["Let’s do it"], "Show me how": ["Show me how"], "Try Again": ["Try Again"], "Privacy Policy": ["Privacy Policy"], "Terms of Service": ["Terms of Service"], "Loading...": ["Loading..."], "Clear all": ["Clear all"], "Beta": ["Beta"], "Product": ["Product"], "Back": ["Back"], "Get Started": ["Get Started"], "Redo": ["Redo"], "Undo": ["Undo"], "Connect": ["Connect"], "Zoom": ["Zoom"], "Pro": ["Pro"], "Current": ["Current"], "Restore": ["Rest<PERSON>"], "Remove": ["Remove"], "History": ["History"], "Go Pro": ["Go Pro"], "Insert": ["Insert"], "Close": ["Close"], "Background": ["Background"], "Background Color": ["Background Colour"], "Square": ["Square"], "Images": ["Images"], "Edit": ["Edit"], "Text": ["Text"], "Style": ["Style"], "Cancel": ["Cancel"], "Preview": ["Preview"], "Loading": ["Loading"], "None": ["None"]}}, "comment": {"reference": "assets/js/ai.js"}}