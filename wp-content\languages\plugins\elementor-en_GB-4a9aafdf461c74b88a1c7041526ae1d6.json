{"translation-revision-date": "2024-12-16 16:53:45+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported.": ["If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported."], "Import Without Enabling": ["Import Without Enabling"], "Enable and Import": ["Enable and Import"], "The file exceeds the maximum upload size for this site.": ["The file exceeds the maximum upload size for this site."], "Uploading...": ["Uploading..."], "Got it": ["Got it"], "Enable Unfiltered File Uploads": ["Enable Unfiltered File Uploads"], "Unable to connect": ["Unable to connect"], "Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.": ["Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files."], "Finder": ["Finder"], "Enable": ["Enable"], "Cancel": ["Cancel"]}}, "comment": {"reference": "assets/js/common.js"}}