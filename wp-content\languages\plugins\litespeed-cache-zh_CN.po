# Translation of Plugins - LiteSpeed Cache - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - LiteSpeed Cache - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-10-31 10:25:59+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - LiteSpeed Cache - Stable (latest release)\n"

#: tpl/page_optm/settings_html.tpl.php:75
msgid "Preconnecting speeds up future loads from a given origin."
msgstr "预连接可加快未来来自特定原产地的负载。"

#: thirdparty/woocommerce.content.tpl.php:71
msgid "If your theme does not use JS to update the mini cart, you must enable this option to display the correct cart contents."
msgstr "如果您的主题不使用 JS 更新迷你购物车，则必须启用此选项才能显示正确的购物车内容。"

#: thirdparty/woocommerce.content.tpl.php:70
msgid "Generate a separate vary cache copy for the mini cart when the cart is not empty."
msgstr "当购物车不是空的时候，为迷你购物车生成一个单独的不同缓存副本。"

#: thirdparty/woocommerce.content.tpl.php:62
msgid "Vary for Mini Cart"
msgstr "因迷你推车而异"

#: src/lang.cls.php:161
msgid "DNS Preconnect"
msgstr "DNS 预连接"

#: src/doc.cls.php:39
msgid "This setting is %1$s for certain qualifying requests due to %2$s!"
msgstr "由于 %2$s，对于某些符合条件的请求，此设置为 %1$s！"

#: tpl/page_optm/settings_tuning.tpl.php:43
msgid "Listed JS files or inline JS code will be delayed."
msgstr "列出的 JS 文件或内联 JS 代码将被延迟。"

#: tpl/crawler/map.tpl.php:58
msgid "URL Search"
msgstr "URL 搜索"

#: src/lang.cls.php:163
msgid "JS Delayed Includes"
msgstr "联署材料延迟包括"

#: src/cloud.cls.php:1453
msgid "Your domain_key has been temporarily blocklisted to prevent abuse. You may contact support at QUIC.cloud to learn more."
msgstr "您的 domain_key 已被暂时列入黑名单以防止滥用。您可以联系 QUIC.cloud 的支持人员以了解更多信息。"

#: src/cloud.cls.php:1448
msgid "Cloud server refused the current request due to unpulled images. Please pull the images first."
msgstr "由于未提取图像，云服务器拒绝了当前请求。请先提取图像。"

#: tpl/crawler/summary.tpl.php:110
msgid "Current server load"
msgstr "当前服务器负载"

#: src/img-optm.cls.php:876
msgid "Started async image optimization request"
msgstr "开始异步图像优化请求"

#: src/crawler.cls.php:229
msgid "Started async crawling"
msgstr "开始异步爬行"

#: src/conf.cls.php:514
msgid "Saving option failed. IPv4 only for %s."
msgstr "保存选项失败。仅 IPv4 适用于 %s。"

#: src/cloud.cls.php:1460
msgid "Cloud server refused the current request due to rate limiting. Please try again later."
msgstr "由于速率限制，云服务器拒绝了当前请求。请稍后再试。"

#: tpl/img_optm/summary.tpl.php:298
msgid "Maximum image post id"
msgstr "最大图像帖子 ID"

#: tpl/img_optm/summary.tpl.php:297 tpl/img_optm/summary.tpl.php:372
msgid "Current image post id position"
msgstr "当前图像帖子 ID 位置"

#: src/lang.cls.php:26
msgid "Images ready to request"
msgstr "图片可随时索取"

#: tpl/dash/dashboard.tpl.php:384 tpl/general/online.tpl.php:31
#: tpl/img_optm/summary.tpl.php:54 tpl/img_optm/summary.tpl.php:56
#: tpl/page_optm/settings_css.tpl.php:109
#: tpl/page_optm/settings_css.tpl.php:246
#: tpl/page_optm/settings_media.tpl.php:192
#: tpl/page_optm/settings_vpi.tpl.php:59
msgid "Redetect"
msgstr "重新检测"

#. translators: %1$s: Socket name, %2$s: Host field title, %3$s: Example socket
#. path
#. translators: %1$s: Socket name, %2$s: Port field title, %3$s: Port value
#: tpl/cache/settings_inc.object.tpl.php:107
#: tpl/cache/settings_inc.object.tpl.php:146
msgid "If you are using a %1$s socket, %2$s should be set to %3$s"
msgstr "如果使用 %1$s 插座，%2$s 应设置为 %3$s"

#: src/root.cls.php:197
msgid "All QUIC.cloud service queues have been cleared."
msgstr "已清除所有 QUIC.cloud 服务队列。"

#. translators: %s: The type of the given cache key.
#: src/object.lib.php:519
msgid "Cache key must be integer or non-empty string, %s given."
msgstr "缓存键必须是整数或非空字符串，%s 已给出。"

#: src/object.lib.php:517
msgid "Cache key must not be an empty string."
msgstr "缓存键不能为空字符串。"

#: src/lang.cls.php:172
msgid "JS Deferred / Delayed Excludes"
msgstr "联署材料推迟/延迟 不包括"

#: src/doc.cls.php:168
msgid "The queue is processed asynchronously. It may take time."
msgstr "队列是异步处理的。这可能需要时间。"

#: src/cloud.cls.php:1179
msgid "In order to use QC services, need a real domain name, cannot use an IP."
msgstr "要使用 QC 服务，需要一个真实的域名，不能使用 IP。"

#: tpl/presets/standard.tpl.php:195
msgid "Restore Settings"
msgstr "恢复设置"

#: tpl/presets/standard.tpl.php:193
msgid "This will restore the backup settings created %1$s before applying the %2$s preset. Any changes made since then will be lost. Do you want to continue?"
msgstr "这将恢复应用 %2$s 预设前创建的 %1$s 备份设置。此后所做的任何更改都将丢失。要继续吗？"

#: tpl/presets/standard.tpl.php:189
msgid "Backup created %1$s before applying the %2$s preset"
msgstr "在应用 %2$s 预置之前创建的备份 %1$s"

#: tpl/presets/standard.tpl.php:178
msgid "Applied the %1$s preset %2$s"
msgstr "应用预设 %1$s %2$s"

#: tpl/presets/standard.tpl.php:175
msgid "Restored backup settings %1$s"
msgstr "恢复备份设置 %1$s"

#: tpl/presets/standard.tpl.php:173
msgid "Error: Failed to apply the settings %1$s"
msgstr "错误：应用设置失败 %1$s"

#: tpl/presets/standard.tpl.php:163
msgid "History"
msgstr "历史"

#: tpl/presets/standard.tpl.php:152
msgid "unknown"
msgstr "未知"

#: tpl/presets/standard.tpl.php:133
msgid "Apply Preset"
msgstr "应用预设"

#: tpl/presets/standard.tpl.php:131
msgid "This will back up your current settings and replace them with the %1$s preset settings. Do you want to continue?"
msgstr "这将备份您当前的设置，并用 %1$s 预设设置取而代之。要继续吗？"

#: tpl/presets/standard.tpl.php:121
msgid "Who should use this preset?"
msgstr "谁应该使用此预置？"

#: tpl/presets/standard.tpl.php:96
msgid "Use an official LiteSpeed-designed Preset to configure your site in one click. Try no-risk caching essentials, extreme optimization, or something in between."
msgstr "使用 LiteSpeed 官方设计的预设，一键配置您的网站。尝试无风险缓存要领、极端优化或两者之间的其他方法。"

#: tpl/presets/standard.tpl.php:92
msgid "LiteSpeed Cache Standard Presets"
msgstr "LiteSpeed 缓存标准预设"

#: tpl/presets/standard.tpl.php:85
msgid "A Domain Key is required to use this preset. Enables the maximum level of optimizations for improved page speed scores."
msgstr "使用此预设需要域密钥。启用最大程度的优化，以提高页面速度分数。"

#: tpl/presets/standard.tpl.php:84
msgid "This preset almost certainly will require testing and exclusions for some CSS, JS and Lazy Loaded images. Pay special attention to logos, or HTML-based slider images."
msgstr "此预设几乎肯定需要对某些 CSS、JS 和懒加载图像进行测试和排除。请特别注意徽标或基于 HTML 的滑块图像。"

#: tpl/presets/standard.tpl.php:81
msgid "Inline CSS added to Combine"
msgstr "为组合添加内联 CSS"

#: tpl/presets/standard.tpl.php:80
msgid "Inline JS added to Combine"
msgstr "在组合中添加内联 JS"

#: tpl/presets/standard.tpl.php:79
msgid "JS Delayed"
msgstr "联署材料延迟"

#: tpl/presets/standard.tpl.php:78
msgid "Viewport Image Generation"
msgstr "视口图像生成"

#: tpl/presets/standard.tpl.php:77
msgid "Lazy Load for Images"
msgstr "懒加载图像"

#: tpl/presets/standard.tpl.php:76
msgid "Everything in Aggressive, Plus"
msgstr "一切都在进取，加"

#: tpl/presets/standard.tpl.php:74
msgid "Extreme"
msgstr "极高"

#: tpl/presets/standard.tpl.php:69
msgid "This preset might work out of the box for some websites, but be sure to test! Some CSS or JS exclusions may be necessary in Page Optimization > Tuning."
msgstr "该预设可能对某些网站有效，但请务必进行测试！可能需要在页面优化 > 调整中排除一些 CSS 或 JS。"

#: tpl/presets/standard.tpl.php:66
msgid "Lazy Load for Iframes"
msgstr "框架的懒加载"

#: tpl/presets/standard.tpl.php:65
msgid "Removed Unused CSS for Users"
msgstr "为用户删除未使用的 CSS"

#: tpl/presets/standard.tpl.php:64
msgid "Asynchronous CSS Loading with Critical CSS"
msgstr "使用关键 CSS 异步加载 CSS"

#: tpl/presets/standard.tpl.php:63
msgid "CSS & JS Combine"
msgstr "CSS 与 JS 结合"

#: tpl/presets/standard.tpl.php:62
msgid "Everything in Advanced, Plus"
msgstr "高级版中的所有内容，以及"

#: tpl/presets/standard.tpl.php:60
msgid "Aggressive"
msgstr "积极"

#: tpl/presets/standard.tpl.php:56 tpl/presets/standard.tpl.php:70
msgid "A Domain Key is required to use this preset. Includes many optimizations known to improve page speed scores."
msgstr "使用此预设需要域密钥。包含许多已知的优化功能，可提高页面速度得分。"

#: tpl/presets/standard.tpl.php:55
msgid "This preset is good for most websites, and is unlikely to cause conflicts. Any CSS or JS conflicts may be resolved with Page Optimization > Tuning tools."
msgstr "此预设适用于大多数网站，不太可能导致冲突。任何 CSS 或 JS 冲突都可以通过页面优化 > 调整工具来解决。"

#: tpl/presets/standard.tpl.php:50
msgid "Remove Query Strings from Static Files"
msgstr "删除静态文件中的查询字符串"

#: tpl/presets/standard.tpl.php:48
msgid "DNS Prefetch for static files"
msgstr "静态文件的 DNS 预取"

#: tpl/presets/standard.tpl.php:47
msgid "JS Defer for both external and inline JS"
msgstr "针对外部和内联 JS 的 JS 延迟"

#: tpl/presets/standard.tpl.php:45
msgid "CSS, JS and HTML Minification"
msgstr "CSS、JS 和 HTML 最小化"

#: tpl/presets/standard.tpl.php:44
msgid "Guest Mode and Guest Optimization"
msgstr "访客模式和访客优化"

#: tpl/presets/standard.tpl.php:43
msgid "Everything in Basic, Plus"
msgstr "基础版中的所有内容，Plus"

#: tpl/presets/standard.tpl.php:41
msgid "Advanced (Recommended)"
msgstr "高级（推荐）"

#: tpl/presets/standard.tpl.php:37
msgid "A Domain Key is required to use this preset. Includes optimizations known to improve site score in page speed measurement tools."
msgstr "使用此预设需要域密钥。包括已知可提高网站在页面速度测量工具中得分的优化。"

#: tpl/presets/standard.tpl.php:36
msgid "This low-risk preset introduces basic optimizations for speed and user experience. Appropriate for enthusiastic beginners."
msgstr "该低风险预设介绍了对速度和用户体验的基本优化。适合热心的初学者。"

#: tpl/presets/standard.tpl.php:33
msgid "Mobile Cache"
msgstr "移动缓存"

#: tpl/presets/standard.tpl.php:31
msgid "Everything in Essentials, Plus"
msgstr "所有必需品，外加"

#: tpl/presets/standard.tpl.php:25
msgid "A Domain Key is not required to use this preset. Only basic caching features are enabled."
msgstr "使用此预设不需要域密钥。仅启用基本缓存功能。"

#: tpl/presets/standard.tpl.php:24
msgid "This no-risk preset is appropriate for all websites. Good for new users, simple websites, or cache-oriented development."
msgstr "这种无风险预设适用于所有网站。适合新用户、简单网站或面向缓存的开发。"

#: tpl/presets/standard.tpl.php:20
msgid "Higher TTL"
msgstr "更高的 TTL"

#: tpl/presets/standard.tpl.php:19
msgid "Default Cache"
msgstr "默认缓存"

#: tpl/presets/standard.tpl.php:17
msgid "Essentials"
msgstr "必备"

#: tpl/presets/entry.tpl.php:23
msgid "LiteSpeed Cache Configuration Presets"
msgstr "LiteSpeed 缓存配置预设"

#: tpl/presets/entry.tpl.php:16
msgid "Standard Presets"
msgstr "标准预置"

#: tpl/page_optm/settings_tuning_css.tpl.php:52
msgid "Listed CSS files will be excluded from UCSS and saved to inline."
msgstr "列出的 CSS 文件将从 UCSS 中排除，并保存为内联文件。"

#: src/lang.cls.php:144
msgid "UCSS File Excludes and Inline"
msgstr "UCSS 文件排除和内联"

#: src/lang.cls.php:143
msgid "UCSS Selector Allowlist"
msgstr "UCSS 选择器允许列表"

#: src/admin-display.cls.php:122
msgid "Presets"
msgstr "预设"

#: tpl/dash/dashboard.tpl.php:310
msgid "Partner Benefits Provided by"
msgstr "合作伙伴提供的福利"

#: tpl/toolbox/log_viewer.tpl.php:35
msgid "LiteSpeed Logs"
msgstr "LiteSpeed 日志"

#: tpl/toolbox/log_viewer.tpl.php:28
msgid "Crawler Log"
msgstr "履带日志"

#: tpl/toolbox/log_viewer.tpl.php:23
msgid "Purge Log"
msgstr "清除日志"

#: tpl/toolbox/settings-debug.tpl.php:164
msgid "Prevent writing log entries that include listed strings."
msgstr "防止写入包含列出字符串的日志条目。"

#: tpl/toolbox/settings-debug.tpl.php:27
msgid "View Site Before Cache"
msgstr "查看缓存前的网站"

#: tpl/toolbox/settings-debug.tpl.php:23
msgid "View Site Before Optimization"
msgstr "查看优化前的网站"

#: tpl/toolbox/settings-debug.tpl.php:19
msgid "Debug Helpers"
msgstr "调试助手"

#: tpl/page_optm/settings_vpi.tpl.php:122
msgid "Enable Viewport Images auto generation cron."
msgstr "启用自动生成视口图像的 cron。"

#: tpl/page_optm/settings_vpi.tpl.php:39
msgid "This enables the page's initial screenful of imagery to be fully displayed without delay."
msgstr "这样，页面的初始画面就能立即完全显示出来。"

#: tpl/page_optm/settings_vpi.tpl.php:38
msgid "The Viewport Images service detects which images appear above the fold, and excludes them from lazy load."
msgstr "Viewport Images 服务会检测哪些图片出现在折叠上方，并将其排除在懒加载之外。"

#: tpl/page_optm/settings_vpi.tpl.php:37
msgid "When you use Lazy Load, it will delay the loading of all images on a page."
msgstr "使用 \"懒加载 \"时，页面上所有图片的加载时间都会延迟。"

#: tpl/page_optm/settings_media.tpl.php:257
msgid "Use %1$s to bypass remote image dimension check when %2$s is ON."
msgstr "当 %2$s 打开时，使用 %1$s 可绕过远程图像尺寸检查。"

#: tpl/page_optm/entry.tpl.php:20
msgid "VPI"
msgstr "VPI"

#: tpl/general/settings.tpl.php:72 tpl/page_optm/settings_media.tpl.php:251
#: tpl/page_optm/settings_vpi.tpl.php:44
msgid "%s must be turned ON for this setting to work."
msgstr "必须打开 %s 才能使用此设置。"

#: tpl/dash/dashboard.tpl.php:755
msgid "Viewport Image"
msgstr "视口图像"

#: thirdparty/litespeed-check.cls.php:74 thirdparty/litespeed-check.cls.php:122
msgid "Please consider disabling the following detected plugins, as they may conflict with LiteSpeed Cache:"
msgstr "请考虑禁用以下检测到的插件，因为它们可能与 LiteSpeed Cache 冲突："

#: src/metabox.cls.php:34
msgid "Mobile"
msgstr "手机"

#: src/metabox.cls.php:32
msgid "Disable VPI"
msgstr "禁用 VPI"

#: src/metabox.cls.php:31
msgid "Disable Image Lazyload"
msgstr "禁用图像懒加载"

#: src/metabox.cls.php:30
msgid "Disable Cache"
msgstr "禁用缓存"

#: src/lang.cls.php:263
msgid "Debug String Excludes"
msgstr "调试字符串排除"

#: src/lang.cls.php:204
msgid "Viewport Images Cron"
msgstr "视口图像 Cron"

#: src/lang.cls.php:203 src/metabox.cls.php:33 src/metabox.cls.php:34
#: tpl/page_optm/settings_vpi.tpl.php:23
msgid "Viewport Images"
msgstr "视口图像"

#: src/lang.cls.php:54
msgid "Alias is in use by another QUIC.cloud account."
msgstr "另一个 QUIC.cloud 账户正在使用别名。"

#: src/lang.cls.php:52
msgid "Unable to automatically add %1$s as a Domain Alias for main %2$s domain."
msgstr "无法自动添加 %1$s 作为主 %2$s 域的域别名。"

#: src/lang.cls.php:47
msgid "Unable to automatically add %1$s as a Domain Alias for main %2$s domain, due to potential CDN conflict."
msgstr "由于潜在的 CDN 冲突，无法自动添加 %1$s 作为主 %2$s 域的域别名。"

#: src/error.cls.php:200
msgid "You cannot remove this DNS zone, because it is still in use. Please update the domain's nameservers, then try to delete this zone again, otherwise your site will become inaccessible."
msgstr "您无法删除此 DNS 区域，因为它仍在使用中。请更新域名服务器，然后再尝试删除此区域，否则您的网站将无法访问。"

#: src/error.cls.php:103
msgid "The site is not a valid alias on QUIC.cloud."
msgstr "该网站不是 QUIC.cloud 上的有效别名。"

#: tpl/page_optm/settings_localization.tpl.php:141
msgid "Please thoroughly test each JS file you add to ensure it functions as expected."
msgstr "请彻底测试您添加的每个 JS 文件，确保其功能符合预期。"

#: tpl/page_optm/settings_localization.tpl.php:108
msgid "Please thoroughly test all items in %s to ensure they function as expected."
msgstr "请彻底测试 %s 中的所有项目，确保其功能符合预期。"

#: tpl/page_optm/settings_tuning_css.tpl.php:100
msgid "Use %1$s to bypass UCSS for the pages which page type is %2$s."
msgstr "对于页面类型为 %2$s 的页面，使用 %1$s 可绕过 UCSS。"

#: tpl/page_optm/settings_tuning_css.tpl.php:99
msgid "Use %1$s to generate one single UCSS for the pages which page type is %2$s while other page types still per URL."
msgstr "使用 %1$s 为页面类型为 %2$s 的页面生成一个单一的 UCSS，其他页面类型仍按 URL 生成。"

#: tpl/page_optm/settings_css.tpl.php:86
msgid "Filter %s available for UCSS per page type generation."
msgstr "每个页面类型生成的 UCSS 可用过滤器 %s。"

#: tpl/general/settings_inc.guest.tpl.php:45
#: tpl/general/settings_inc.guest.tpl.php:48
msgid "Guest Mode failed to test."
msgstr "访客模式测试失败。"

#: tpl/general/settings_inc.guest.tpl.php:42
msgid "Guest Mode passed testing."
msgstr "访客模式通过测试。"

#: tpl/general/settings_inc.guest.tpl.php:35
msgid "Testing"
msgstr "测试"

#: tpl/general/settings_inc.guest.tpl.php:34
msgid "Guest Mode testing result"
msgstr "访客模式测试结果"

#: tpl/crawler/blacklist.tpl.php:85
msgid "Not blocklisted"
msgstr "未列入封锁名单"

#: tpl/cache/settings_inc.cache_mobile.tpl.php:25
msgid "Learn more about when this is needed"
msgstr "进一步了解何时需要"

#: src/purge.cls.php:344
msgid "Cleaned all localized resource entries."
msgstr "清理了所有本地化资源条目。"

#: tpl/toolbox/entry.tpl.php:24
msgid "View .htaccess"
msgstr "查看 .htaccess"

#: tpl/toolbox/edit_htaccess.tpl.php:63 tpl/toolbox/edit_htaccess.tpl.php:81
msgid "You can use this code %1$s in %2$s to specify the htaccess file path."
msgstr "您可以在 %2$s 中使用此代码 %1$s 来指定 htaccess 文件路径。"

#: tpl/toolbox/edit_htaccess.tpl.php:62 tpl/toolbox/edit_htaccess.tpl.php:80
msgid "PHP Constant %s is supported."
msgstr "支持 PHP 常量 %s。"

#: tpl/toolbox/edit_htaccess.tpl.php:58 tpl/toolbox/edit_htaccess.tpl.php:76
msgid "Default path is"
msgstr "默认路径为"

#: tpl/toolbox/edit_htaccess.tpl.php:46
msgid ".htaccess Path"
msgstr ".htaccess 路径"

#: tpl/general/settings.tpl.php:49
msgid "Please read all warnings before enabling this option."
msgstr "启用此选项前，请阅读所有警告。"

#: tpl/toolbox/purge.tpl.php:83
msgid "This will delete all generated unique CSS files"
msgstr "这将删除所有生成的唯一 CSS 文件"

#: tpl/toolbox/beta_test.tpl.php:74
msgid "In order to avoid an upgrade error, you must be using %1$s or later before you can upgrade to %2$s versions."
msgstr "为了避免升级错误，您必须在使用 %1$s 或更高版本后才能升级到 %2$s 版本。"

#: tpl/toolbox/beta_test.tpl.php:67
msgid "Use latest GitHub Dev/Master commit"
msgstr "使用最新的 GitHub Dev/Master 提交"

#: tpl/toolbox/beta_test.tpl.php:67
msgid "Press the %s button to use the most recent GitHub commit. Master is for release candidate & Dev is for experimental testing."
msgstr "按 %s 按钮可使用最新的 GitHub 提交。Master 用于候选发布版本，Dev 用于实验测试。"

#: tpl/toolbox/beta_test.tpl.php:62
msgid "Downgrade not recommended. May cause fatal error due to refactored code."
msgstr "不建议降级。由于代码重构，可能会导致致命错误。"

#: tpl/page_optm/settings_tuning.tpl.php:144
msgid "Only optimize pages for guest (not logged in) visitors. If turned this OFF, CSS/JS/CCSS files will be doubled by each user group."
msgstr "只为访客（未登录）优化页面。如果关闭此选项，CSS/JS/CCSS 文件将按每个用户组加倍。"

#: tpl/page_optm/settings_tuning.tpl.php:106
msgid "Listed JS files or inline JS code will not be optimized by %s."
msgstr "列出的 JS 文件或内联 JS 代码将不会被 %s 优化。"

#: tpl/page_optm/settings_tuning_css.tpl.php:92
msgid "Listed URI will not generate UCSS."
msgstr "列出的 URI 不会生成 UCSS。"

#: tpl/page_optm/settings_tuning_css.tpl.php:74
msgid "The selector must exist in the CSS. Parent classes in the HTML will not work."
msgstr "选择器必须存在于 CSS 中。HTML 中的父类不起作用。"

#: tpl/page_optm/settings_tuning_css.tpl.php:70
#: tpl/page_optm/settings_tuning_css.tpl.php:145
msgid "Wildcard %s supported."
msgstr "支持通配符 %s。"

#: tpl/page_optm/settings_media_exc.tpl.php:34
msgid "Useful for above-the-fold images causing CLS (a Core Web Vitals metric)."
msgstr "适用于导致 CLS（核心网络活力指标）的折叠上方图像。"

#: tpl/page_optm/settings_media.tpl.php:246
msgid "Set an explicit width and height on image elements to reduce layout shifts and improve CLS (a Core Web Vitals metric)."
msgstr "在图像元素上设置明确的宽度和高度，以减少布局偏移并提高 CLS（Core Web Vitals 指标）。"

#: tpl/page_optm/settings_media.tpl.php:139
msgid "Changes to this setting do not apply to already-generated LQIPs. To regenerate existing LQIPs, please %s first from the admin bar menu."
msgstr "对该设置的更改不适用于已生成的 LQIP。要重新生成现有的 LQIP，请先从管理栏菜单中选择 %s。"

#: tpl/page_optm/settings_js.tpl.php:79
msgid "Deferring until page is parsed or delaying till interaction can help reduce resource contention and improve performance causing a lower FID (Core Web Vitals metric)."
msgstr "推迟页面解析或推迟交互时间有助于减少资源争用，提高性能，从而降低 FID（核心网络生命指数指标）。"

#: tpl/page_optm/settings_js.tpl.php:77
msgid "Delayed"
msgstr "延迟"

#: tpl/page_optm/settings_js.tpl.php:52
msgid "JS error can be found from the developer console of browser by right clicking and choosing Inspect."
msgstr "在浏览器的开发人员控制台中单击右键并选择 \"检查\"，即可发现 JS 错误。"

#: tpl/page_optm/settings_js.tpl.php:51 tpl/page_optm/settings_js.tpl.php:85
msgid "This option may result in a JS error or layout issue on frontend pages with certain themes/plugins."
msgstr "使用某些主题/插件时，该选项可能会导致前台页面出现 JS 错误或布局问题。"

#: tpl/page_optm/settings_html.tpl.php:147
msgid "This will also add a preconnect to Google Fonts to establish a connection earlier."
msgstr "这还将为 Google 字体添加一个预连接，以便更早地建立连接。"

#: tpl/page_optm/settings_html.tpl.php:91
msgid "Delay rendering off-screen HTML elements by its selector."
msgstr "通过选择器延迟渲染屏幕外的 HTML 元素。"

#: tpl/page_optm/settings_css.tpl.php:312
msgid "Disable this option to generate CCSS per Post Type instead of per page. This can save significant CCSS quota, however it may result in incorrect CSS styling if your site uses a page builder."
msgstr "禁用此选项可按 \"帖子类型 \"而不是按页面生成 CCSS。这可以节省大量 CCSS 配额，但如果您的网站使用页面生成器，则可能导致 CSS 样式不正确。"

#: tpl/page_optm/settings_css.tpl.php:228
msgid "This option is bypassed due to %s option."
msgstr "由于使用了 %s 选项，该选项被绕过。"

#: tpl/page_optm/settings_css.tpl.php:222
msgid "Elements with attribute %s in HTML code will be excluded."
msgstr "在 HTML 代码中带有属性 %s 的元素将被排除在外。"

#: tpl/page_optm/settings_css.tpl.php:215
msgid "Use QUIC.cloud online service to generate critical CSS and load remaining CSS asynchronously."
msgstr "使用 QUIC.cloud 在线服务生成关键 CSS 并异步加载剩余 CSS。"

#: tpl/page_optm/settings_css.tpl.php:179
msgid "This option will automatically bypass %s option."
msgstr "该选项将自动绕过 %s 选项。"

#: tpl/page_optm/settings_css.tpl.php:176
msgid "Inline UCSS to reduce the extra CSS file loading. This option will not be automatically turned on for %1$s pages. To use it on %1$s pages, please set it to ON."
msgstr "内联 UCSS 以减少额外 CSS 文件的加载。对于 %1$s 页面，此选项不会自动开启。要在 %1$s 页面上使用，请将其设置为 \"开启\"。"

#: tpl/page_optm/settings_css.tpl.php:153
#: tpl/page_optm/settings_css.tpl.php:158
#: tpl/page_optm/settings_css.tpl.php:290
#: tpl/page_optm/settings_css.tpl.php:295
#: tpl/page_optm/settings_vpi.tpl.php:100
#: tpl/page_optm/settings_vpi.tpl.php:105
msgid "Run %s Queue Manually"
msgstr "手动运行 %s 队列"

#: tpl/page_optm/settings_css.tpl.php:91
msgid "This option is bypassed because %1$s option is %2$s."
msgstr "由于 %1$s 选项为 %2$s，因此绕过了该选项。"

#: tpl/page_optm/settings_css.tpl.php:84
msgid "Automatic generation of unique CSS is in the background via a cron-based queue."
msgstr "通过基于 cron 的队列在后台自动生成独特的 CSS。"

#: tpl/page_optm/settings_css.tpl.php:82
msgid "This will drop the unused CSS on each page from the combined file."
msgstr "这将从合并文件中删除每个页面上未使用的 CSS。"

#: tpl/page_optm/entry.tpl.php:18 tpl/page_optm/settings_html.tpl.php:17
msgid "HTML Settings"
msgstr "HTML 设置"

#: tpl/inc/in_upgrading.php:15
msgid "LiteSpeed cache plugin upgraded. Please refresh the page to complete the configuration data upgrade."
msgstr "LiteSpeed 缓存插件已升级。请刷新页面以完成配置数据升级。"

#: tpl/general/settings_tuning.tpl.php:62
msgid "Listed IPs will be considered as Guest Mode visitors."
msgstr "列出的 IP 将被视为访客模式访客。"

#: tpl/general/settings_tuning.tpl.php:40
msgid "Listed User Agents will be considered as Guest Mode visitors."
msgstr "列出的用户代理将被视为访客模式游客。"

#: tpl/general/settings_inc.guest.tpl.php:27
msgid "This option can help to correct the cache vary for certain advanced mobile or tablet visitors."
msgstr "此选项可帮助纠正某些高级手机或平板电脑访问者的缓存变化。"

#: tpl/general/settings_inc.guest.tpl.php:26
msgid "Guest Mode provides an always cacheable landing page for an automated guest's first time visit, and then attempts to update cache varies via AJAX."
msgstr "访客模式为自动访客的首次访问提供一个始终可缓存的登陆页面，然后尝试通过 AJAX 更新缓存变化。"

#: tpl/general/settings.tpl.php:104
msgid "Please make sure this IP is the correct one for visiting your site."
msgstr "请确保该 IP 是访问您网站的正确 IP。"

#: tpl/general/settings.tpl.php:103
msgid "the auto-detected IP may not be accurate if you have an additional outgoing IP set, or you have multiple IPs configured on your server."
msgstr "如果您设置了额外的外发 IP，或在服务器上配置了多个 IP，自动检测到的 IP 可能不准确。"

#: tpl/general/settings.tpl.php:86
msgid "You need to turn %s on and finish all WebP generation to get maximum result."
msgstr "您需要打开 %s 并完成所有 WebP 生成，以获得最佳效果。"

#: tpl/general/settings.tpl.php:79
msgid "You need to turn %s on to get maximum result."
msgstr "您需要打开 %s 以获得最大效果。"

#: tpl/general/settings.tpl.php:48
msgid "This option enables maximum optimization for Guest Mode visitors."
msgstr "该选项可最大限度地优化访客模式访客。"

#: tpl/dash/dashboard.tpl.php:54 tpl/dash/dashboard.tpl.php:81
#: tpl/dash/dashboard.tpl.php:520 tpl/dash/dashboard.tpl.php:597
#: tpl/dash/dashboard.tpl.php:624 tpl/dash/dashboard.tpl.php:668
#: tpl/dash/dashboard.tpl.php:712 tpl/dash/dashboard.tpl.php:756
#: tpl/dash/dashboard.tpl.php:800 tpl/dash/dashboard.tpl.php:847
msgid "More"
msgstr "更多"

#: tpl/dash/dashboard.tpl.php:300
msgid "Remaining Daily Quota"
msgstr "每日剩余配额"

#: tpl/crawler/summary.tpl.php:246
msgid "Successfully Crawled"
msgstr "成功爬行"

#: tpl/crawler/summary.tpl.php:245
msgid "Already Cached"
msgstr "已缓存"

#: tpl/crawler/settings.tpl.php:59
msgid "The crawler will use your XML sitemap or sitemap index. Enter the full URL to your sitemap here."
msgstr "爬虫将使用您的 XML 网站地图或网站地图索引。请在此处输入网站地图的完整 URL。"

#: tpl/cdn/cf.tpl.php:48
msgid "Optional when API token used."
msgstr "使用 API 令牌时为可选项。"

#: tpl/cdn/cf.tpl.php:40
msgid "Recommended to generate the token from Cloudflare API token template \"WordPress\"."
msgstr "建议从 Cloudflare API 令牌模板 \"WordPress \"生成令牌。"

#: tpl/cdn/cf.tpl.php:35
msgid "Global API Key / API Token"
msgstr "全球应用程序接口密钥/应用程序接口令牌"

#: tpl/cache/settings_inc.object.tpl.php:47
msgid "Use external object cache functionality."
msgstr "使用外部对象缓存功能。"

#: tpl/cache/settings_inc.cache_mobile.tpl.php:24
msgid "Serve a separate cache copy for mobile visitors."
msgstr "为移动访客提供单独的缓存副本。"

#: thirdparty/woocommerce.content.tpl.php:25
msgid "By default, the My Account, Checkout, and Cart pages are automatically excluded from caching. Misconfiguration of page associations in WooCommerce settings may cause some pages to be erroneously excluded."
msgstr "默认情况下，\"我的账户\"、\"结账 \"和 \"购物车 \"页面会自动从缓存中排除。WooCommerce 设置中的页面关联配置错误可能会导致某些页面被错误地排除在外。"

#: src/purge.cls.php:272
msgid "Cleaned all Unique CSS files."
msgstr "清理了所有独特的 CSS 文件。"

#: src/lang.cls.php:202
msgid "Add Missing Sizes"
msgstr "添加缺失的尺寸"

#: src/lang.cls.php:177
msgid "Optimize for Guests Only"
msgstr "仅为访客优化"

#: src/lang.cls.php:173
msgid "Guest Mode JS Excludes"
msgstr "访客模式 JS 排除"

#: src/lang.cls.php:153
msgid "CCSS Per URL"
msgstr "CCSS 每个 URL"

#: src/lang.cls.php:150
msgid "HTML Lazy Load Selectors"
msgstr "HTML 懒加载选择器"

#: src/lang.cls.php:145
msgid "UCSS URI Excludes"
msgstr "UCSS URI 不包括"

#: src/lang.cls.php:142
msgid "UCSS Inline"
msgstr "UCSS 内联"

#: src/lang.cls.php:102
msgid "Guest Optimization"
msgstr "访客优化"

#: src/lang.cls.php:101
msgid "Guest Mode"
msgstr "访客模式"

#: src/lang.cls.php:88
msgid "Guest Mode IPs"
msgstr "访客模式 IP"

#: src/lang.cls.php:87
msgid "Guest Mode User Agents"
msgstr "访客模式用户代理"

#: src/error.cls.php:119
msgid "Online node needs to be redetected."
msgstr "需要重新检测在线节点。"

#: src/error.cls.php:115
msgid "The current server is under heavy load."
msgstr "当前服务器负载过重。"

#: src/doc.cls.php:71
msgid "Please see %s for more details."
msgstr "详情请参见 %s。"

#: src/doc.cls.php:55
msgid "This setting will regenerate crawler list and clear the disabled list!"
msgstr "此设置将重新生成爬虫列表并清除禁用列表！"

#: src/gui.cls.php:82
msgid "%1$s %2$s files left in queue"
msgstr "%1$s %2$s 队列中剩余的文件"

#: src/crawler.cls.php:144
msgid "Crawler disabled list is cleared! All crawlers are set to active! "
msgstr "爬虫禁用列表已清除！所有爬虫都设置为激活状态！ "

#: src/cloud.cls.php:1468
msgid "Redetected node"
msgstr "重新检测节点"

#: src/cloud.cls.php:1018
msgid "No available Cloud Node after checked server load."
msgstr "检查服务器负载后，没有可用的云节点。"

#: src/lang.cls.php:158
msgid "Localization Files"
msgstr "本地化文件"

#: cli/purge.cls.php:234
msgid "Purged!"
msgstr "已清除！"

#: tpl/page_optm/settings_localization.tpl.php:130
msgid "Resources listed here will be copied and replaced with local URLs."
msgstr "此处列出的资源将被复制并替换为本地 URL。"

#: tpl/toolbox/beta_test.tpl.php:50
msgid "Use latest GitHub Master commit"
msgstr "使用最新的GitHub主提交"

#: tpl/toolbox/beta_test.tpl.php:46
msgid "Use latest GitHub Dev commit"
msgstr "使用最新的GitHub开发提交"

#: src/crawler-map.cls.php:371
msgid "No valid sitemap parsed for crawler."
msgstr "没有为爬虫解析出有效的网站地图。"

#: src/lang.cls.php:140
msgid "CSS Combine External and Inline"
msgstr "CSS结合外部和内联"

#: tpl/page_optm/settings_css.tpl.php:193
msgid "Include external CSS and inline CSS in combined file when %1$s is also enabled. This option helps maintain the priorities of CSS, which should minimize potential errors caused by CSS Combine."
msgstr "当%1$s也启用时，在组合文件中包括外部CSS和内联CSS。此选项有助于保持CSS的优先级，从而将CSS合并导致的潜在错误降至最低。"

#: tpl/page_optm/settings_css.tpl.php:45
msgid "Minify CSS files and inline CSS code."
msgstr "缩小CSS文件并内联CSS代码。"

#: tpl/cache/settings-excludes.tpl.php:32
#: tpl/page_optm/settings_tuning_css.tpl.php:78
#: tpl/page_optm/settings_tuning_css.tpl.php:153
msgid "Predefined list will also be combined w/ the above settings"
msgstr "预定义列表也将与上述设置相结合"

#: tpl/page_optm/entry.tpl.php:22
msgid "Localization"
msgstr "本地化"

#: tpl/page_optm/settings_js.tpl.php:66
msgid "Include external JS and inline JS in combined file when %1$s is also enabled. This option helps maintain the priorities of JS execution, which should minimize potential errors caused by JS Combine."
msgstr "当%1$s也被启用时，在合并文件中包含外部JS和内联JS。这个选项有助于保持JS执行的优先级，这应该可以最大限度地减少JS Combine引起的潜在错误。"

#: tpl/page_optm/settings_js.tpl.php:47
msgid "Combine all local JS files into a single file."
msgstr "将所有本地JS文件合并为一个文件。"

#: tpl/page_optm/settings_tuning.tpl.php:85
msgid "Listed JS files or inline JS code will not be deferred or delayed."
msgstr "列出的 JS 文件或内联 JS 代码不会被推迟或延迟。"

#: src/data.upgrade.func.php:238
msgid "Click here to settings"
msgstr "点击此处进行设置"

#: src/data.upgrade.func.php:236
msgid "JS Defer"
msgstr "JS延迟"

#: src/data.upgrade.func.php:231
msgid "LiteSpeed Cache upgraded successfully. NOTE: Due to changes in this version, the settings %1$s and %2$s have been turned OFF. Please turn them back on manually and verify that your site layout is correct, and you have no JS errors."
msgstr ""
"LiteSpeed Cache升级成功。注意：由于此版本中的更改，设置%1$s和%2$s已关闭。\n"
"请手动重新打开它们，并验证您的站点布局是否正确，并且没有JS错误。"

#: src/lang.cls.php:148
msgid "JS Combine External and Inline"
msgstr "JS结合外部和内联"

#: src/admin-display.cls.php:519 tpl/banner/new_version.php:114
#: tpl/banner/score.php:142 tpl/banner/slack.php:49
msgid "Dismiss"
msgstr "忽略"

#: tpl/cache/settings-esi.tpl.php:103
msgid "The latest data file is"
msgstr "最新的数据文件是"

#: tpl/cache/settings-esi.tpl.php:102
msgid "The list will be merged with the predefined nonces in your local data file."
msgstr "该列表将与您本地数据文件中预定义的随机数合并。"

#: tpl/page_optm/settings_css.tpl.php:59
msgid "Combine CSS files and inline CSS code."
msgstr "合并CSS文件和内联CSS代码。"

#: tpl/page_optm/settings_js.tpl.php:33
msgid "Minify JS files and inline JS codes."
msgstr "缩小JS文件和内联JS代码。"

#: src/admin-display.cls.php:1030
msgid "This setting is overwritten by the Network setting"
msgstr "此设置将被网络设置覆盖"

#: src/lang.cls.php:191
msgid "LQIP Excludes"
msgstr "LQIP排除"

#: tpl/page_optm/settings_media_exc.tpl.php:132
msgid "These images will not generate LQIP."
msgstr "这些图像不会生成LQIP。"

#: tpl/toolbox/import_export.tpl.php:70
msgid "Are you sure you want to reset all settings back to the default settings?"
msgstr "你确定要将所有设置恢复到默认设置吗？"

#: tpl/page_optm/settings_html.tpl.php:188
msgid "This option will remove all %s tags from HTML."
msgstr "这个选项将删除HTML中所有%s标签。"

#: tpl/general/online.tpl.php:31
msgid "Are you sure you want to clear all cloud nodes?"
msgstr "你确定你要清除所有云节点？"

#: src/lang.cls.php:175 tpl/presets/standard.tpl.php:52
msgid "Remove Noscript Tags"
msgstr "移除 Noscript 标签"

#: src/error.cls.php:107
msgid "The site is not registered on QUIC.cloud."
msgstr "该网站未在QUIC.cloud上注册。"

#: src/error.cls.php:46 tpl/crawler/settings.tpl.php:123
#: tpl/crawler/settings.tpl.php:144 tpl/crawler/summary.tpl.php:218
msgid "Click here to set."
msgstr "单击此处进行设置。"

#: src/lang.cls.php:157
msgid "Localize Resources"
msgstr "本地化资源"

#: tpl/cache/settings_inc.browser.tpl.php:26
msgid "Setting Up Custom Headers"
msgstr "设置自定义标题"

#: tpl/toolbox/purge.tpl.php:92
msgid "This will delete all localized resources"
msgstr "这将删除所有本地化的资源"

#: src/gui.cls.php:628 src/gui.cls.php:810 tpl/toolbox/purge.tpl.php:91
msgid "Localized Resources"
msgstr "本地化资源"

#: tpl/page_optm/settings_localization.tpl.php:135
msgid "Comments are supported. Start a line with a %s to turn it into a comment line."
msgstr "支持评论。以%s开始一行，将其转换为注释行。"

#: tpl/page_optm/settings_localization.tpl.php:131
msgid "HTTPS sources only."
msgstr "仅HTTPS源。"

#: tpl/page_optm/settings_localization.tpl.php:104
msgid "Localize external resources."
msgstr "本地化外部资源。"

#: tpl/page_optm/settings_localization.tpl.php:27
msgid "Localization Settings"
msgstr "本地化设置"

#: tpl/page_optm/settings_css.tpl.php:81
msgid "Use QUIC.cloud online service to generate unique CSS."
msgstr "使用QUIC.cloud在线服务生成唯一的CSS。"

#: src/lang.cls.php:141
msgid "Generate UCSS"
msgstr "生成UCSS"

#: tpl/dash/dashboard.tpl.php:667 tpl/toolbox/purge.tpl.php:82
msgid "Unique CSS"
msgstr "独特的 CSS"

#: tpl/toolbox/purge.tpl.php:118
msgid "Purge the cache entries created by this plugin except for Critical CSS & Unique CSS & LQIP caches"
msgstr "清除此插件创建的缓存条目，关键 CSS、唯一 CSS 和 LQIP 缓存除外"

#: tpl/toolbox/report.tpl.php:58
msgid "LiteSpeed Report"
msgstr "LiteSpeed报告"

#: tpl/img_optm/summary.tpl.php:224
msgid "Image Thumbnail Group Sizes"
msgstr "图像缩略图组大小"

#. translators: %s: LiteSpeed Web Server version
#: tpl/cache/settings_inc.cache_dropquery.tpl.php:27
msgid "Ignore certain query strings when caching. (LSWS %s required)"
msgstr "缓存时忽略某些查询字符串。 （需要LSWS%s）"

#: tpl/cache/settings-purge.tpl.php:116
msgid "For URLs with wildcards, there may be a delay in initiating scheduled purge."
msgstr "对于带有通配符的URL，初始化计划清除可能会有所延迟。"

#: tpl/cache/settings-purge.tpl.php:92
msgid "By design, this option may serve stale content. Do not enable this option, if that is not OK with you."
msgstr "根据设计，此选项可能会提供过时的内容。 如果您不满意，请不要启用此选项。"

#: src/lang.cls.php:128
msgid "Serve Stale"
msgstr "服务过期"

#: src/admin-display.cls.php:1028
msgid "This setting is overwritten by the primary site setting"
msgstr "此设置被主站点设置覆盖"

#: src/img-optm.cls.php:1151
msgid "One or more pulled images does not match with the notified image md5"
msgstr "一个或多个拉取的图像与通知的图像MD5不匹配"

#: src/img-optm.cls.php:1072
msgid "Some optimized image file(s) has expired and was cleared."
msgstr "某些优化的图像文件已过期并被清除。"

#: src/error.cls.php:76
msgid "You have too many requested images, please try again in a few minutes."
msgstr "您请求的图片过多，请在几分钟后重试。"

#: src/img-optm.cls.php:1087
msgid "Pulled WebP image md5 does not match the notified WebP image md5."
msgstr "拉动的WebP图像md5与通知的WebP图像md5不匹配。"

#: tpl/inc/admin_footer.php:19
msgid "Read LiteSpeed Documentation"
msgstr "阅读LiteSpeed文档"

#: src/error.cls.php:97
msgid "There is proceeding queue not pulled yet. Queue info: %s."
msgstr "有进行中的队列还没有拉开。队列信息： %s."

#: tpl/page_optm/settings_localization.tpl.php:89
msgid "Specify how long, in seconds, Gravatar files are cached."
msgstr "指定Gravatar文件缓存的时间（以秒为单位）。"

#: src/img-optm.cls.php:604
msgid "Cleared %1$s invalid images."
msgstr "已清除%1$s无效图像。"

#: tpl/general/entry.tpl.php:31
msgid "LiteSpeed Cache General Settings"
msgstr "LiteSpeed缓存常规设置"

#: tpl/toolbox/purge.tpl.php:110
msgid "This will delete all cached Gravatar files"
msgstr "这将删除所有缓存的Gravatar文件"

#: tpl/toolbox/settings-debug.tpl.php:150
msgid "Prevent any debug log of listed pages."
msgstr "防止列出页面的任何调试日志。"

#: tpl/toolbox/settings-debug.tpl.php:136
msgid "Only log listed pages."
msgstr "仅记录列出的页面。"

#: tpl/toolbox/settings-debug.tpl.php:108
msgid "Specify the maximum size of the log file."
msgstr "指定日志文件的最大大小。"

#: tpl/toolbox/settings-debug.tpl.php:59
msgid "To prevent filling up the disk, this setting should be OFF when everything is working."
msgstr "为避免填满磁盘，一切正常时，此设置应为OFF。"

#: tpl/toolbox/beta_test.tpl.php:70
msgid "Press the %s button to stop beta testing and go back to the current release from the WordPress Plugin Directory."
msgstr "按%s按钮停止beta测试，然后从WordPress插件目录返回当前版本。"

#: tpl/toolbox/beta_test.tpl.php:54 tpl/toolbox/beta_test.tpl.php:70
msgid "Use latest WordPress release version"
msgstr "使用最新的WordPress发布版本"

#: tpl/toolbox/beta_test.tpl.php:54
msgid "OR"
msgstr "或"

#: tpl/toolbox/beta_test.tpl.php:37
msgid "Use this section to switch plugin versions. To beta test a GitHub commit, enter the commit URL in the field below."
msgstr "使用此部分切换插件版本。要测试GitHub提交，请在下面的字段中输入提交URL。"

#: tpl/toolbox/import_export.tpl.php:71
msgid "Reset Settings"
msgstr "重置设置"

#: tpl/toolbox/entry.tpl.php:41
msgid "LiteSpeed Cache Toolbox"
msgstr "LiteSpeed缓存工具箱"

#: tpl/toolbox/entry.tpl.php:35
msgid "Beta Test"
msgstr "Beta测试"

#: tpl/toolbox/entry.tpl.php:34
msgid "Log View"
msgstr "日志查看"

#: tpl/toolbox/entry.tpl.php:33 tpl/toolbox/settings-debug.tpl.php:31
msgid "Debug Settings"
msgstr "调试设置"

#: tpl/toolbox/heartbeat.tpl.php:103
msgid "Turn ON to control heartbeat in backend editor."
msgstr "在后端编辑器中打开以控制心跳。"

#: tpl/toolbox/heartbeat.tpl.php:73
msgid "Turn ON to control heartbeat on backend."
msgstr "开启控制后台的心跳。"

#: tpl/toolbox/heartbeat.tpl.php:58 tpl/toolbox/heartbeat.tpl.php:88
#: tpl/toolbox/heartbeat.tpl.php:118
msgid "Set to %1$s to forbid heartbeat on %2$s."
msgstr "设置为%1$s在%2$s禁止心跳。"

#: tpl/toolbox/heartbeat.tpl.php:57 tpl/toolbox/heartbeat.tpl.php:87
#: tpl/toolbox/heartbeat.tpl.php:117
msgid "WordPress valid interval is %s seconds."
msgstr "WordPress的有效间隔为%s秒。"

#: tpl/toolbox/heartbeat.tpl.php:56 tpl/toolbox/heartbeat.tpl.php:86
#: tpl/toolbox/heartbeat.tpl.php:116
msgid "Specify the %s heartbeat interval in seconds."
msgstr "指定%s心跳间隔（以秒为单位）。"

#: tpl/toolbox/heartbeat.tpl.php:43
msgid "Turn ON to control heartbeat on frontend."
msgstr "开启控制前端的心跳。"

#: tpl/toolbox/heartbeat.tpl.php:26
msgid "Disable WordPress interval heartbeat to reduce server load."
msgstr "禁用WordPress间隔心跳以减少服务器负载。"

#: tpl/toolbox/heartbeat.tpl.php:19
msgid "Heartbeat Control"
msgstr "心跳控制"

#: tpl/toolbox/report.tpl.php:127
msgid "provide more information here to assist the LiteSpeed team with debugging."
msgstr "在此处提供更多信息，以帮助LiteSpeed团队进行调试。"

#: tpl/toolbox/report.tpl.php:126
msgid "Optional"
msgstr "可选"

#: tpl/toolbox/report.tpl.php:100 tpl/toolbox/report.tpl.php:102
msgid "Generate Link for Current User"
msgstr "为当前用户生成链接"

#: tpl/toolbox/report.tpl.php:96
msgid "Passwordless Link"
msgstr "无密码链接"

#: tpl/toolbox/report.tpl.php:75
msgid "System Information"
msgstr "系统信息"

#: tpl/toolbox/report.tpl.php:52
msgid "Go to plugins list"
msgstr "转到插件列表"

#: tpl/toolbox/report.tpl.php:51
msgid "Install DoLogin Security"
msgstr "安装 DoLogin Security"

#: tpl/general/settings.tpl.php:102
msgid "Check my public IP from"
msgstr "检查我的公共IP"

#: tpl/general/settings.tpl.php:102
msgid "Your server IP"
msgstr "您的服务器 IP"

#: tpl/general/settings.tpl.php:101
msgid "Enter this site's IP address to allow cloud services directly call IP instead of domain name. This eliminates the overhead of DNS and CDN lookups."
msgstr "输入此站点的IP地址，以允许云服务直接调用IP而不是域名。 这消除了DNS和CDN查找的开销。"

#: tpl/crawler/settings.tpl.php:31
msgid "This will enable crawler cron."
msgstr "这将启用爬虫cron。"

#: tpl/crawler/settings.tpl.php:17
msgid "Crawler General Settings"
msgstr "爬虫常规设置"

#: tpl/crawler/blacklist.tpl.php:53
msgid "Remove from Blocklist"
msgstr "从阻止列表中删除"

#: tpl/crawler/blacklist.tpl.php:22
msgid "Empty blocklist"
msgstr "空拦截列表"

#: tpl/crawler/blacklist.tpl.php:21
msgid "Are you sure to delete all existing blocklist items?"
msgstr "您确定要删除所有现有的拦截列表项目吗？"

#: tpl/crawler/blacklist.tpl.php:86 tpl/crawler/map.tpl.php:103
msgid "Blocklisted due to not cacheable"
msgstr "因无法缓存而被屏蔽"

#: tpl/crawler/map.tpl.php:89
msgid "Add to Blocklist"
msgstr "添加到封锁清单"

#: tpl/crawler/blacklist.tpl.php:42 tpl/crawler/map.tpl.php:78
msgid "Operation"
msgstr "操作"

#: tpl/crawler/map.tpl.php:52
msgid "Sitemap Total"
msgstr "站点地图总计"

#: tpl/crawler/map.tpl.php:48
msgid "Sitemap List"
msgstr "站点地图列表"

#: tpl/crawler/map.tpl.php:32
msgid "Refresh Crawler Map"
msgstr "刷新爬虫地图"

#: tpl/crawler/map.tpl.php:29
msgid "Clean Crawler Map"
msgstr "清理爬虫地图"

#: tpl/crawler/blacklist.tpl.php:27 tpl/crawler/entry.tpl.php:16
msgid "Blocklist"
msgstr "阻止清单"

#: tpl/crawler/entry.tpl.php:15
msgid "Map"
msgstr "地图"

#: tpl/crawler/entry.tpl.php:14
msgid "Summary"
msgstr "概要"

#: tpl/crawler/map.tpl.php:63 tpl/crawler/map.tpl.php:102
msgid "Cache Miss"
msgstr "缓存未命中"

#: tpl/crawler/map.tpl.php:62 tpl/crawler/map.tpl.php:101
msgid "Cache Hit"
msgstr "缓存命中"

#: tpl/crawler/summary.tpl.php:244
msgid "Waiting to be Crawled"
msgstr "等待被爬虫"

#: tpl/crawler/blacklist.tpl.php:87 tpl/crawler/map.tpl.php:64
#: tpl/crawler/map.tpl.php:104 tpl/crawler/summary.tpl.php:199
#: tpl/crawler/summary.tpl.php:247
msgid "Blocklisted"
msgstr "屏蔽列表"

#: tpl/crawler/summary.tpl.php:194
msgid "Miss"
msgstr "未命中"

#: tpl/crawler/summary.tpl.php:189
msgid "Hit"
msgstr "命中"

#: tpl/crawler/summary.tpl.php:184
msgid "Waiting"
msgstr "等待中"

#: tpl/crawler/summary.tpl.php:155
msgid "Running"
msgstr "运行"

#: tpl/crawler/settings.tpl.php:177
msgid "Use %1$s in %2$s to indicate this cookie has not been set."
msgstr "在%2$s中使用%1$s表示尚未设置此Cookie。"

#: src/admin-display.cls.php:211
msgid "Add new cookie to simulate"
msgstr "添加新的cookie来模拟"

#: src/admin-display.cls.php:210
msgid "Remove cookie simulation"
msgstr "删除Cookie模拟"

#. translators: %s: Current mobile agents in htaccess
#: tpl/cache/settings_inc.cache_mobile.tpl.php:51
msgid "Htaccess rule is: %s"
msgstr "Htaccess 规则是: %s"

#. translators: %s: LiteSpeed Cache menu label
#: tpl/cache/more_settings_tip.tpl.php:27
msgid "More settings available under %s menu"
msgstr "%s菜单下有更多可用设置"

#: tpl/cache/settings_inc.browser.tpl.php:63
msgid "The amount of time, in seconds, that files will be stored in browser cache before expiring."
msgstr "过期之前，文件将存储在浏览器缓存中的时间（以秒为单位）。"

#: tpl/cache/settings_inc.browser.tpl.php:25
msgid "OpenLiteSpeed users please check this"
msgstr "OpenLiteSpeed用户请检查此"

#: tpl/cache/settings_inc.browser.tpl.php:17
msgid "Browser Cache Settings"
msgstr "浏览器缓存设置"

#: tpl/cache/settings-cache.tpl.php:158
msgid "Paths containing these strings will be forced to public cached regardless of no-cacheable settings."
msgstr "包含这些字符串的路径将被强制公开缓存，而不顾不可缓存设置如何。"

#: tpl/cache/settings-cache.tpl.php:49
msgid "With QUIC.cloud CDN enabled, you may still be seeing cache headers from your local server."
msgstr "启用QUIC.cloud CDN后，您可能仍会从本地服务器看到缓存头。"

#: tpl/cache/settings-esi.tpl.php:114
msgid "An optional second parameter may be used to specify cache control. Use a space to separate"
msgstr "可选的第二个参数可用于指定缓存控制。 用空格隔开"

#: tpl/cache/settings-esi.tpl.php:112
msgid "The above nonces will be converted to ESI automatically."
msgstr "以上随机数将自动转换为ESI。"

#: tpl/cache/entry.tpl.php:25 tpl/cache/entry_network.tpl.php:20
msgid "Browser"
msgstr "浏览器"

#: tpl/cache/entry.tpl.php:24 tpl/cache/entry_network.tpl.php:19
msgid "Object"
msgstr "对象"

#. translators: %1$s: Object cache name, %2$s: Port number
#: tpl/cache/settings_inc.object.tpl.php:128
#: tpl/cache/settings_inc.object.tpl.php:137
msgid "Default port for %1$s is %2$s."
msgstr "%1$s的默认端口是%2$s。"

#: tpl/cache/settings_inc.object.tpl.php:33
msgid "Object Cache Settings"
msgstr "对象缓存设置"

#: tpl/cache/settings-ttl.tpl.php:111
msgid "Specify an HTTP status code and the number of seconds to cache that page, separated by a space."
msgstr "指定HTTP状态代码和缓存该页面的秒数，以空格分隔。"

#: tpl/cache/settings-ttl.tpl.php:59
msgid "Specify how long, in seconds, the front page is cached."
msgstr "指定首页缓存多长时间（以秒为单位）。"

#: tpl/cache/entry.tpl.php:17 tpl/cache/settings-ttl.tpl.php:15
msgid "TTL"
msgstr "TTL"

#: tpl/cache/settings-purge.tpl.php:86
msgid "If ON, the stale copy of a cached page will be shown to visitors until a new cache copy is available. Reduces the server load for following visits. If OFF, the page will be dynamically generated while visitors wait."
msgstr "如果开启，则将向访问者显示已缓存页面的陈旧副本，直到有新的缓存副本可用为止。 减少后续访问的服务器负载。 如果设置为关闭，则会在访客等待时动态生成页面。"

#: tpl/page_optm/settings_css.tpl.php:339
msgid "Swap"
msgstr "交换"

#: tpl/page_optm/settings_css.tpl.php:338
msgid "Set this to append %1$s to all %2$s rules before caching CSS to specify how fonts should be displayed while being downloaded."
msgstr "将此选项设置为在缓存CSS之前将%1$s附加到所有%2$s规则，以指定下载时字体的显示方式。"

#: tpl/page_optm/settings_localization.tpl.php:67
msgid "Avatar list in queue waiting for update"
msgstr "队列中的头像列表等待更新"

#: tpl/page_optm/settings_localization.tpl.php:54
msgid "Refresh Gravatar cache by cron."
msgstr "通过cron刷新Gravatar缓存。"

#: tpl/page_optm/settings_localization.tpl.php:41
msgid "Accelerates the speed by caching Gravatar (Globally Recognized Avatars)."
msgstr "通过缓存Gravatar（全球公认的头像）来加快速度。"

#: tpl/page_optm/settings_localization.tpl.php:40
msgid "Store Gravatar locally."
msgstr "将Gravatar存储在本地。"

#: tpl/page_optm/settings_localization.tpl.php:22
msgid "Failed to create Avatar table. Please follow <a %s>Table Creation guidance from LiteSpeed Wiki</a> to finish setup."
msgstr "无法创建头像表。 请按照 <a %s>LiteSpeed Wiki中的表创建指南</a> 完成设置。"

#: tpl/page_optm/settings_media.tpl.php:154
msgid "LQIP requests will not be sent for images where both width and height are smaller than these dimensions."
msgstr "对于宽度和高度均小于这些尺寸的图像，不会发送LQIP请求。"

#: tpl/page_optm/settings_media.tpl.php:152
msgid "pixels"
msgstr "像素"

#: tpl/page_optm/settings_media.tpl.php:136
msgid "Larger number will generate higher resolution quality placeholder, but will result in larger files which will increase page size and consume more points."
msgstr "数字越大，生成的占位符分辨率越高，但文件也会越大，从而增加页面大小并消耗更多的点数。"

#: tpl/page_optm/settings_media.tpl.php:135
msgid "Specify the quality when generating LQIP."
msgstr "指定生成LQIP时的质量。"

#: tpl/page_optm/settings_media.tpl.php:121
msgid "Keep this off to use plain color placeholders."
msgstr "请勿使用纯色占位符。"

#: tpl/page_optm/settings_media.tpl.php:120
msgid "Use QUIC.cloud LQIP (Low Quality Image Placeholder) generator service for responsive image previews while loading."
msgstr "在加载时，使用QUIC.cloud LQIP（低质量图像占位符）生成器服务进行响应的图像预览。"

#: tpl/page_optm/settings_media.tpl.php:105
msgid "Specify the responsive placeholder SVG color."
msgstr "指定响应式占位符SVG颜色。"

#: tpl/page_optm/settings_media.tpl.php:91
msgid "Variables %s will be replaced with the configured background color."
msgstr "变量%s将被替换为配置的背景颜色。"

#: tpl/page_optm/settings_media.tpl.php:90
msgid "Variables %s will be replaced with the corresponding image properties."
msgstr "变量%s将替换为相应的图像属性。"

#: tpl/page_optm/settings_media.tpl.php:89
msgid "It will be converted to a base64 SVG placeholder on-the-fly."
msgstr "它将即时转换为base64 SVG占位符。"

#: tpl/page_optm/settings_media.tpl.php:88
msgid "Specify an SVG to be used as a placeholder when generating locally."
msgstr "指定在本地生成时用作占位符的SVG。"

#: tpl/page_optm/settings_media_exc.tpl.php:118
msgid "Prevent any lazy load of listed pages."
msgstr "防止延迟加载列出的页面。"

#: tpl/page_optm/settings_media_exc.tpl.php:104
msgid "Iframes having these parent class names will not be lazy loaded."
msgstr "具有这些父类名称的iframe不会被延迟加载。"

#: tpl/page_optm/settings_media_exc.tpl.php:89
msgid "Iframes containing these class names will not be lazy loaded."
msgstr "包含这些类名称的iframe不会被延迟加载。"

#: tpl/page_optm/settings_media_exc.tpl.php:75
msgid "Images having these parent class names will not be lazy loaded."
msgstr "具有这些父类名称的图像将不会被延迟加载。"

#: tpl/page_optm/entry.tpl.php:31
msgid "LiteSpeed Cache Page Optimization"
msgstr "LiteSpeed缓存页面优化"

#: tpl/page_optm/entry.tpl.php:21 tpl/page_optm/settings_media_exc.tpl.php:17
msgid "Media Excludes"
msgstr "媒体排除"

#: tpl/page_optm/entry.tpl.php:16 tpl/page_optm/settings_css.tpl.php:30
msgid "CSS Settings"
msgstr "CSS 设置"

#: tpl/page_optm/settings_css.tpl.php:339
msgid "%s is recommended."
msgstr "建议使用%s。"

#: tpl/page_optm/settings_js.tpl.php:77
msgid "Deferred"
msgstr "递延"

#: tpl/page_optm/settings_css.tpl.php:336
msgid "Default"
msgstr "默认"

#: tpl/page_optm/settings_html.tpl.php:61
msgid "This can improve the page loading speed."
msgstr "这样可以提高页面加载速度。"

#: tpl/page_optm/settings_html.tpl.php:60
msgid "Automatically enable DNS prefetching for all URLs in the document, including images, CSS, JavaScript, and so forth."
msgstr "自动为文档中的所有 URL（包括图像、CSS、JavaScript 等）启用 DNS 预取。"

#: tpl/banner/new_version_dev.tpl.php:30
msgid "New developer version %s is available now."
msgstr "新的开发版本%s现在可用。"

#: tpl/banner/new_version_dev.tpl.php:22
msgid "New Developer Version Available!"
msgstr "新的开发版本可用！"

#: tpl/banner/cloud_news.tpl.php:51 tpl/banner/cloud_promo.tpl.php:73
msgid "Dismiss this notice"
msgstr "忽略此通知"

#: tpl/banner/cloud_promo.tpl.php:61
msgid "Tweet this"
msgstr "推特分享"

#: tpl/banner/cloud_promo.tpl.php:45
msgid "Tweet preview"
msgstr "微博预览"

#: tpl/banner/cloud_promo.tpl.php:40
#: tpl/page_optm/settings_tuning_css.tpl.php:69
#: tpl/page_optm/settings_tuning_css.tpl.php:144
msgid "Learn more"
msgstr "了解更多"

#: tpl/banner/cloud_promo.tpl.php:22
msgid "You just unlocked a promotion from QUIC.cloud!"
msgstr "你刚刚解锁了QUIC.cloud的促销活动！"

#: tpl/page_optm/settings_media.tpl.php:271
msgid "The image compression quality setting of WordPress out of 100."
msgstr "WordPress的图像压缩质量设置（满分100）。"

#: tpl/img_optm/entry.tpl.php:17 tpl/img_optm/entry.tpl.php:22
#: tpl/img_optm/network_settings.tpl.php:19 tpl/img_optm/settings.tpl.php:19
msgid "Image Optimization Settings"
msgstr "图像优化设置"

#: tpl/img_optm/summary.tpl.php:377
msgid "Are you sure to destroy all optimized images?"
msgstr "您确定要销毁所有优化的图像吗？"

#: tpl/img_optm/summary.tpl.php:360
msgid "Use Optimized Files"
msgstr "使用优化文件"

#: tpl/img_optm/summary.tpl.php:359
msgid "Switch back to using optimized images on your site"
msgstr "切换回使用您网站上的优化图像"

#: tpl/img_optm/summary.tpl.php:356
msgid "Use Original Files"
msgstr "使用原始文件"

#: tpl/img_optm/summary.tpl.php:355
msgid "Use original images (unoptimized) on your site"
msgstr "在您的网站上使用原始图片（未经优化）"

#: tpl/img_optm/summary.tpl.php:350
msgid "You can quickly switch between using original (unoptimized versions) and optimized image files. It will affect all images on your website, both regular and webp versions if available."
msgstr "您可以在使用原始（未优化的版本）和优化的图像文件之间快速切换。 它将影响您网站上的所有图像，包括常规版本和webp版本（如果可用）。"

#: tpl/img_optm/summary.tpl.php:347
msgid "Optimization Tools"
msgstr "优化工具"

#: tpl/img_optm/summary.tpl.php:305
msgid "Rescan New Thumbnails"
msgstr "重新扫描新缩略图"

#: tpl/img_optm/summary.tpl.php:293
msgid "What is an image group?"
msgstr "什么是图像组？"

#: tpl/img_optm/summary.tpl.php:241
msgid "Delete all backups of the original images"
msgstr "删除原始图像的所有备份"

#: tpl/img_optm/summary.tpl.php:217
msgid "Calculate Backups Disk Space"
msgstr "计算备份磁盘空间"

#: tpl/img_optm/summary.tpl.php:108
msgid "Optimization Status"
msgstr "优化状态"

#: tpl/img_optm/summary.tpl.php:69
msgid "Current limit is"
msgstr "当前限制为"

#: tpl/img_optm/summary.tpl.php:68
msgid "To make sure our server can communicate with your server without any issues and everything works fine, for the few first requests the number of image groups allowed in a single request is limited."
msgstr "为确保我们的服务器能与您的服务器顺利通信，并确保一切运行正常，对于少数几个首次请求，单个请求中允许的图像组数量是有限的。"

#: tpl/img_optm/summary.tpl.php:63
msgid "You can request a maximum of %s images at once."
msgstr "您一次最多可以请求 %s张图片。"

#: tpl/img_optm/summary.tpl.php:58
msgid "Optimize images with our QUIC.cloud server"
msgstr "使用我们的QUIC.cloud服务器优化图像"

#: tpl/db_optm/settings.tpl.php:46
msgid "Revisions newer than this many days will be kept when cleaning revisions."
msgstr "清理修订版本时，将保留比这几天新的修订版本。"

#: tpl/db_optm/settings.tpl.php:44
msgid "Day(s)"
msgstr "天"

#: tpl/db_optm/settings.tpl.php:32
msgid "Specify the number of most recent revisions to keep when cleaning revisions."
msgstr "指定清理修订时要保留的最新修订的数量。"

#: tpl/db_optm/entry.tpl.php:24
msgid "LiteSpeed Cache Database Optimization"
msgstr "LiteSpeed缓存数据库优化"

#: tpl/db_optm/entry.tpl.php:17 tpl/db_optm/settings.tpl.php:19
msgid "DB Optimization Settings"
msgstr "数据库优化设置"

#: tpl/db_optm/manage.tpl.php:185
msgid "Option Name"
msgstr "选项名称"

#: tpl/db_optm/manage.tpl.php:171
msgid "Database Summary"
msgstr "数据库摘要"

#: tpl/db_optm/manage.tpl.php:149
msgid "We are good. No table uses MyISAM engine."
msgstr "我们很好。 没有表使用MyISAM引擎。"

#: tpl/db_optm/manage.tpl.php:141
msgid "Convert to InnoDB"
msgstr "转换为InnoDB"

#: tpl/db_optm/manage.tpl.php:126
msgid "Tool"
msgstr "工具"

#: tpl/db_optm/manage.tpl.php:125
msgid "Engine"
msgstr "引擎"

#: tpl/db_optm/manage.tpl.php:124
msgid "Table"
msgstr "数据表"

#: tpl/db_optm/manage.tpl.php:116
msgid "Database Table Engine Converter"
msgstr "数据库表引擎转换器"

#: tpl/db_optm/manage.tpl.php:66
msgid "Clean revisions older than %1$s day(s), excluding %2$s latest revisions"
msgstr "清理%1$s天之前的修订版本，%2$s 最新版本除外"

#: tpl/dash/dashboard.tpl.php:87 tpl/dash/dashboard.tpl.php:806
msgid "Currently active crawler"
msgstr "当前活动的爬虫"

#: tpl/dash/dashboard.tpl.php:84 tpl/dash/dashboard.tpl.php:803
msgid "Crawler(s)"
msgstr "爬虫"

#: tpl/crawler/map.tpl.php:77 tpl/dash/dashboard.tpl.php:80
#: tpl/dash/dashboard.tpl.php:799
msgid "Crawler Status"
msgstr "爬虫状态"

#: tpl/dash/dashboard.tpl.php:648 tpl/dash/dashboard.tpl.php:692
#: tpl/dash/dashboard.tpl.php:736 tpl/dash/dashboard.tpl.php:780
msgid "Force cron"
msgstr "强制 cron"

#: tpl/dash/dashboard.tpl.php:645 tpl/dash/dashboard.tpl.php:689
#: tpl/dash/dashboard.tpl.php:733 tpl/dash/dashboard.tpl.php:777
msgid "Requests in queue"
msgstr "队列中的请求"

#: tpl/dash/dashboard.tpl.php:59 tpl/dash/dashboard.tpl.php:602
msgid "Private Cache"
msgstr "私有缓存"

#: tpl/dash/dashboard.tpl.php:58 tpl/dash/dashboard.tpl.php:601
msgid "Public Cache"
msgstr "公共缓存"

#: tpl/dash/dashboard.tpl.php:53 tpl/dash/dashboard.tpl.php:596
msgid "Cache Status"
msgstr "缓存状态"

#: tpl/dash/dashboard.tpl.php:571
msgid "Last Pull"
msgstr "最后一拉"

#: tpl/dash/dashboard.tpl.php:519 tpl/img_optm/entry.tpl.php:16
msgid "Image Optimization Summary"
msgstr "图像优化摘要"

#: tpl/dash/dashboard.tpl.php:511
msgid "Refresh page score"
msgstr "刷新页面分数"

#: tpl/dash/dashboard.tpl.php:382 tpl/img_optm/summary.tpl.php:54
#: tpl/page_optm/settings_css.tpl.php:109
#: tpl/page_optm/settings_css.tpl.php:246
#: tpl/page_optm/settings_media.tpl.php:192
#: tpl/page_optm/settings_vpi.tpl.php:59
msgid "Are you sure you want to redetect the closest cloud server for this service?"
msgstr "您确定要重新检测与此服务器最近的云服务器吗？"

#: tpl/dash/dashboard.tpl.php:446
msgid "Refresh page load time"
msgstr "刷新页面加载时间"

#: tpl/dash/dashboard.tpl.php:353 tpl/general/online.tpl.php:128
msgid "Go to QUIC.cloud dashboard"
msgstr "转到QUIC.cloud仪表盘"

#: tpl/dash/dashboard.tpl.php:206 tpl/dash/dashboard.tpl.php:711
#: tpl/dash/network_dash.tpl.php:39
msgid "Low Quality Image Placeholder"
msgstr "低质量图像占位符"

#: tpl/dash/dashboard.tpl.php:182
msgid "Sync data from Cloud"
msgstr "从云同步数据"

#: tpl/dash/dashboard.tpl.php:179
msgid "QUIC.cloud Service Usage Statistics"
msgstr "QUIC.cloud 服务使用情况统计"

#: tpl/dash/dashboard.tpl.php:292 tpl/dash/network_dash.tpl.php:119
msgid "Total images optimized in this month"
msgstr "本月优化的总图像"

#: tpl/dash/dashboard.tpl.php:291 tpl/dash/network_dash.tpl.php:118
msgid "Total Usage"
msgstr "总用量"

#: tpl/dash/dashboard.tpl.php:273 tpl/dash/network_dash.tpl.php:111
msgid "Pay as You Go Usage Statistics"
msgstr "按量付费使用统计"

#: tpl/dash/dashboard.tpl.php:270 tpl/dash/network_dash.tpl.php:108
msgid "PAYG Balance"
msgstr "现收余额"

#: tpl/dash/network_dash.tpl.php:107
msgid "Pay as You Go"
msgstr "随用随付"

#: tpl/dash/dashboard.tpl.php:258 tpl/dash/network_dash.tpl.php:95
msgid "Usage"
msgstr "用法"

#: tpl/dash/dashboard.tpl.php:258 tpl/dash/network_dash.tpl.php:95
msgid "Fast Queue Usage"
msgstr "快速队列使用"

#: tpl/dash/dashboard.tpl.php:205 tpl/dash/network_dash.tpl.php:38
msgid "CDN Bandwidth"
msgstr "CDN带宽"

#: tpl/dash/entry.tpl.php:29
msgid "LiteSpeed Cache Dashboard"
msgstr "LiteSpeed缓存仪表盘"

#: tpl/dash/entry.tpl.php:21
msgid "Network Dashboard"
msgstr "网络仪表盘"

#: tpl/general/online.tpl.php:51
msgid "No cloud services currently in use"
msgstr "目前没有使用云服务"

#: tpl/general/online.tpl.php:31
msgid "Click to clear all nodes for further redetection."
msgstr "单击以清除所有节点，以便进一步重新检测。"

#: tpl/general/online.tpl.php:30
msgid "Current Cloud Nodes in Service"
msgstr "当前服务中的云节点"

#: tpl/cdn/qc.tpl.php:126 tpl/cdn/qc.tpl.php:133 tpl/dash/dashboard.tpl.php:359
#: tpl/general/online.tpl.php:153
msgid "Link to QUIC.cloud"
msgstr "链接到QUIC.cloud"

#: tpl/general/entry.tpl.php:17 tpl/general/entry.tpl.php:23
#: tpl/general/network_settings.tpl.php:19 tpl/general/settings.tpl.php:24
msgid "General Settings"
msgstr "常规设置"

#: tpl/cdn/other.tpl.php:136
msgid "Specify which HTML element attributes will be replaced with CDN Mapping."
msgstr "指定将用CDN映射替换哪些HTML元素属性。"

#: src/admin-display.cls.php:233
msgid "Add new CDN URL"
msgstr "新增CDN URL"

#: src/admin-display.cls.php:232
msgid "Remove CDN URL"
msgstr "删除CDN URL"

#: tpl/cdn/cf.tpl.php:102
msgid "To enable the following functionality, turn ON Cloudflare API in CDN Settings."
msgstr "要启用以下功能，请在CDN设置中打开Cloudflare API。"

#: tpl/cdn/entry.tpl.php:14
msgid "QUIC.cloud"
msgstr "QUIC.cloud"

#: thirdparty/woocommerce.content.tpl.php:17
msgid "WooCommerce Settings"
msgstr "WooCommerce 设置"

#: src/doc.cls.php:155
msgid "Current Online Server IPs"
msgstr "当前在线服务器ip"

#: src/doc.cls.php:154
msgid "Before generating key, please verify all IPs on this list are allowlisted"
msgstr "生成密钥前，请确认此列表中的所有 IP 均已列入允许列表"

#: src/doc.cls.php:153
msgid "For online services to work correctly, you must allowlist all %s server IPs."
msgstr "要使在线服务正常工作，必须允许列出所有 %s 服务器 IP。"

#: src/gui.cls.php:638 src/gui.cls.php:820
#: tpl/page_optm/settings_media.tpl.php:139 tpl/toolbox/purge.tpl.php:100
msgid "LQIP Cache"
msgstr "LQIP 缓存"

#: src/admin-settings.cls.php:274 src/admin-settings.cls.php:308
msgid "Options saved."
msgstr "选项已保存。"

#: src/img-optm.cls.php:1729
msgid "Removed backups successfully."
msgstr "成功删除了备份。"

#: src/img-optm.cls.php:1637
msgid "Calculated backups successfully."
msgstr "计算备份成功。"

#: src/img-optm.cls.php:1571
msgid "Rescanned %d images successfully."
msgstr "成功重新扫描 %d 幅图像。"

#: src/img-optm.cls.php:1509 src/img-optm.cls.php:1571
msgid "Rescanned successfully."
msgstr "重新扫描成功。"

#: src/img-optm.cls.php:1444
msgid "Destroy all optimization data successfully."
msgstr "成功销毁所有优化数据。"

#: src/img-optm.cls.php:1343
msgid "Cleaned up unfinished data successfully."
msgstr "成功清除未完成的数据。"

#: src/img-optm.cls.php:962
msgid "Pull Cron is running"
msgstr "拉动 Cron 正在运行"

#: src/img-optm.cls.php:686
msgid "No valid image found by Cloud server in the current request."
msgstr "云服务器在当前请求中找不到有效的图像。"

#: src/img-optm.cls.php:661
msgid "No valid image found in the current request."
msgstr "在当前请求中找不到有效的图像。"

#: src/img-optm.cls.php:343
msgid "Pushed %1$s to Cloud server, accepted %2$s."
msgstr "已将%1$s推送至云端服务器，已接受%2$s。"

#: src/lang.cls.php:266
msgid "Revisions Max Age"
msgstr "修订最长时间"

#: src/lang.cls.php:265
msgid "Revisions Max Number"
msgstr "修订版本最大数量"

#: src/lang.cls.php:262
msgid "Debug URI Excludes"
msgstr "调试URI排除"

#: src/lang.cls.php:261
msgid "Debug URI Includes"
msgstr "调试URI包括"

#: src/lang.cls.php:241
msgid "HTML Attribute To Replace"
msgstr "要替换的HTML属性"

#: src/lang.cls.php:235
msgid "Use CDN Mapping"
msgstr "使用CDN映射"

#: src/lang.cls.php:233
msgid "Editor Heartbeat TTL"
msgstr "编辑心跳TTL"

#: src/lang.cls.php:232
msgid "Editor Heartbeat"
msgstr "编辑心跳"

#: src/lang.cls.php:231
msgid "Backend Heartbeat TTL"
msgstr "后端心跳TTL"

#: src/lang.cls.php:230
msgid "Backend Heartbeat Control"
msgstr "后端心跳控制"

#: src/lang.cls.php:229
msgid "Frontend Heartbeat TTL"
msgstr "前端心跳TTL"

#: src/lang.cls.php:228
msgid "Frontend Heartbeat Control"
msgstr "前端心跳控制"

#: tpl/toolbox/edit_htaccess.tpl.php:71
msgid "Backend .htaccess Path"
msgstr "后端.htaccess路径"

#: tpl/toolbox/edit_htaccess.tpl.php:53
msgid "Frontend .htaccess Path"
msgstr "前端.htaccess路径"

#: src/lang.cls.php:218
msgid "ESI Nonces"
msgstr "ESI随机数"

#: src/lang.cls.php:214
msgid "WordPress Image Quality Control"
msgstr "WordPress图像质量控制"

#: src/lang.cls.php:206
msgid "Auto Request Cron"
msgstr "自动请求计划"

#: src/lang.cls.php:200
msgid "Generate LQIP In Background"
msgstr "在后台生成LQIP"

#: src/lang.cls.php:198
msgid "LQIP Minimum Dimensions"
msgstr "LQIP最小尺寸"

#: src/lang.cls.php:197
msgid "LQIP Quality"
msgstr "LQIP质量"

#: src/lang.cls.php:196
msgid "LQIP Cloud Generator"
msgstr "LQIP云生成器"

#: src/lang.cls.php:195
msgid "Responsive Placeholder SVG"
msgstr "响应式占位符SVG"

#: src/lang.cls.php:194
msgid "Responsive Placeholder Color"
msgstr "响应式占位符颜色"

#: src/lang.cls.php:192
msgid "Basic Image Placeholder"
msgstr "基本图像占位符"

#: src/lang.cls.php:190
msgid "Lazy Load URI Excludes"
msgstr "延迟加载URI排除"

#: src/lang.cls.php:189
msgid "Lazy Load Iframe Parent Class Name Excludes"
msgstr "延迟加载iframe父类名称排除项"

#: src/lang.cls.php:188
msgid "Lazy Load Iframe Class Name Excludes"
msgstr "延迟加载iFrame类名排除"

#: src/lang.cls.php:187
msgid "Lazy Load Image Parent Class Name Excludes"
msgstr "延迟加载图像父类名称排除"

#: src/lang.cls.php:182
msgid "Gravatar Cache TTL"
msgstr "Gravatar 缓存 TTL"

#: src/lang.cls.php:181
msgid "Gravatar Cache Cron"
msgstr "Gravatar 缓存 Cron"

#: src/gui.cls.php:648 src/gui.cls.php:830 src/lang.cls.php:180
#: tpl/presets/standard.tpl.php:49 tpl/toolbox/purge.tpl.php:109
msgid "Gravatar Cache"
msgstr "Gravatar 缓存"

#: src/lang.cls.php:160
msgid "DNS Prefetch Control"
msgstr "DNS Prefetch Control"

#: src/lang.cls.php:155 tpl/presets/standard.tpl.php:46
msgid "Font Display Optimization"
msgstr "字体显示优化"

#: src/lang.cls.php:132
msgid "Force Public Cache URIs"
msgstr "强制公共缓存URI"

#: src/lang.cls.php:103
msgid "Notifications"
msgstr "通知"

#: src/lang.cls.php:97
msgid "Default HTTP Status Code Page TTL"
msgstr "默认HTTP状态代码页TTL"

#: src/lang.cls.php:96
msgid "Default REST TTL"
msgstr "默认 REST TTL"

#: src/lang.cls.php:90
msgid "Enable Cache"
msgstr "启用缓存"

#: src/cloud.cls.php:233 src/cloud.cls.php:285 src/lang.cls.php:86
msgid "Server IP"
msgstr "服务器IP"

#: src/lang.cls.php:25
msgid "Images not requested"
msgstr "未请求图像"

#: src/cloud.cls.php:1994
msgid "Sync credit allowance with Cloud Server successfully."
msgstr "成功地与云服务器同步信用额度。"

#: src/cloud.cls.php:1614
msgid "Failed to communicate with QUIC.cloud server"
msgstr "与QUIC.cloud服务器通信失败"

#: src/cloud.cls.php:1537
msgid "Good news from QUIC.cloud server"
msgstr "来自QUIC.cloud服务器的好消息"

#: src/cloud.cls.php:1521 src/cloud.cls.php:1529
msgid "Message from QUIC.cloud server"
msgstr "来自QUIC.cloud服务器的消息"

#: src/cloud.cls.php:1228
msgid "Please try after %1$s for service %2$s."
msgstr "请在%1$s后尝试使用服务%2$s。"

#: src/cloud.cls.php:1084
msgid "No available Cloud Node."
msgstr "没有可用的云节点。"

#: src/cloud.cls.php:967 src/cloud.cls.php:980 src/cloud.cls.php:1018
#: src/cloud.cls.php:1084 src/cloud.cls.php:1225
msgid "Cloud Error"
msgstr "云错误"

#: src/data.cls.php:220
msgid "The database has been upgrading in the background since %s. This message will disappear once upgrade is complete."
msgstr "自%s起，数据库一直在后台升级。 升级完成后，此消息将消失。"

#: src/media.cls.php:403
msgid "Restore from backup"
msgstr "从备份还原"

#: src/media.cls.php:388
msgid "No backup of unoptimized WebP file exists."
msgstr "不存在未优化的WebP文件的备份。"

#: src/media.cls.php:374
msgid "WebP file reduced by %1$s (%2$s)"
msgstr "WebP文件减少了 %1$s (%2$s)"

#: src/media.cls.php:366
msgid "Currently using original (unoptimized) version of WebP file."
msgstr "当前使用WebP文件的原始（未优化）版本。"

#: src/media.cls.php:359
msgid "Currently using optimized version of WebP file."
msgstr "当前正在使用WebP文件的优化版本。"

#: src/media.cls.php:337
msgid "Orig"
msgstr "原件"

#: src/media.cls.php:335
msgid "(no savings)"
msgstr "(no savings)"

#: src/media.cls.php:335
msgid "Orig %s"
msgstr "原件 %s"

#: src/media.cls.php:334
msgid "Congratulation! Your file was already optimized"
msgstr "恭喜您您的文件已经优化"

#: src/media.cls.php:329
msgid "No backup of original file exists."
msgstr "不存在原始文件的备份。"

#: src/media.cls.php:329 src/media.cls.php:387
msgid "Using optimized version of file. "
msgstr "使用文件的优化版本。"

#: src/media.cls.php:322
msgid "Orig saved %s"
msgstr "比原文件节省 %s"

#: src/media.cls.php:318
msgid "Original file reduced by %1$s (%2$s)"
msgstr "原始文件减少了%1$s (%2$s)"

#: src/media.cls.php:312 src/media.cls.php:367
msgid "Click to switch to optimized version."
msgstr "单击以切换到优化版本。"

#: src/media.cls.php:312
msgid "Currently using original (unoptimized) version of file."
msgstr "当前使用文件的原始（未优化）版本。"

#: src/media.cls.php:311 src/media.cls.php:363
msgid "(non-optm)"
msgstr "(non-optm)"

#: src/media.cls.php:308 src/media.cls.php:360
msgid "Click to switch to original (unoptimized) version."
msgstr "单击以切换到原始（未优化）版本。"

#: src/media.cls.php:308
msgid "Currently using optimized version of file."
msgstr "当前使用文件的优化版本。"

#: src/media.cls.php:307 src/media.cls.php:330 src/media.cls.php:356
#: src/media.cls.php:389
msgid "(optm)"
msgstr "(optm)"

#: src/placeholder.cls.php:141
msgid "LQIP image preview for size %s"
msgstr "LQIP图片预览的大小为%s"

#: src/placeholder.cls.php:84
msgid "LQIP"
msgstr "LQIP"

#: src/crawler.cls.php:1409
msgid "Previously existed in blocklist"
msgstr "以前存在于拦截列表中"

#: src/crawler.cls.php:1406
msgid "Manually added to blocklist"
msgstr "手动添加到拦截列表"

#: src/htaccess.cls.php:328
msgid "Mobile Agent Rules"
msgstr "移动代理规则"

#: src/crawler-map.cls.php:376
msgid "Sitemap created successfully: %d items"
msgstr "站点地图创建成功：%d个项目"

#: src/crawler-map.cls.php:279
msgid "Sitemap cleaned successfully"
msgstr "网站地图成功清除"

#: src/admin-display.cls.php:1188
msgid "Invalid IP"
msgstr "无效的 IP"

#: src/admin-display.cls.php:1163
msgid "Value range"
msgstr "取值范围"

#: src/admin-display.cls.php:1160
msgid "Smaller than"
msgstr "小于"

#: src/admin-display.cls.php:1158
msgid "Larger than"
msgstr "大于"

#: src/admin-display.cls.php:1152
msgid "Zero, or"
msgstr "0,或"

#: src/admin-display.cls.php:1140
msgid "Maximum value"
msgstr "最大值"

#: src/admin-display.cls.php:1137
msgid "Minimum value"
msgstr "最小值"

#: src/admin-display.cls.php:1119
msgid "Path must end with %s"
msgstr "路径必须以%s结尾"

#: src/admin-display.cls.php:1102
msgid "Invalid rewrite rule"
msgstr "无效的重写规则"

#: src/admin-display.cls.php:1033
msgid "currently set to %s"
msgstr "当前设置为 %s"

#: src/admin-display.cls.php:1026
msgid "This setting is overwritten by the PHP constant %s"
msgstr "此设置被PHP常量%s覆盖"

#: src/admin-display.cls.php:138
msgid "Toolbox"
msgstr "工具箱"

#: src/admin-display.cls.php:134
msgid "Database"
msgstr "数据库"

#: src/admin-display.cls.php:132 tpl/dash/dashboard.tpl.php:204
#: tpl/dash/network_dash.tpl.php:37 tpl/general/online.tpl.php:83
#: tpl/general/online.tpl.php:133 tpl/general/online.tpl.php:148
msgid "Page Optimization"
msgstr "页面优化"

#: src/admin-display.cls.php:120 tpl/dash/entry.tpl.php:16
msgid "Dashboard"
msgstr "仪表盘"

#: src/db-optm.cls.php:292
msgid "Converted to InnoDB successfully."
msgstr "成功转换为InnoDB。"

#: src/purge.cls.php:327
msgid "Cleaned all Gravatar files."
msgstr "清除了所有头像文件。"

#: src/purge.cls.php:310
msgid "Cleaned all LQIP files."
msgstr "清除所有LQIP文件。"

#: src/error.cls.php:207
msgid "Unknown error"
msgstr "未知错误"

#: src/error.cls.php:196
msgid "Your domain has been forbidden from using our services due to a previous policy violation."
msgstr "由于先前违反了政策，因此您的域已被禁止使用我们的服务。"

#: src/error.cls.php:191
msgid "The callback validation to your domain failed. Please make sure there is no firewall blocking our servers. Response code: "
msgstr "对您的域的回调验证失败。 请确保没有防火墙阻止我们的服务器。 响应码："

#: src/error.cls.php:186
msgid "The callback validation to your domain failed. Please make sure there is no firewall blocking our servers."
msgstr "对您的域的回调验证失败。 请确保没有防火墙阻止我们的服务器。"

#: src/error.cls.php:182
msgid "The callback validation to your domain failed due to hash mismatch."
msgstr "由于哈希不匹配，对您域的回调验证失败。"

#: src/error.cls.php:178
msgid "Your application is waiting for approval."
msgstr "您的申请正在等待批准。"

#: src/error.cls.php:172
msgid "Previous request too recent. Please try again after %s."
msgstr "之前的请求太近。请在%s后再试。"

#: src/error.cls.php:167
msgid "Previous request too recent. Please try again later."
msgstr "之前的请求太近。请稍后再试。"

#: src/error.cls.php:163
msgid "Crawler disabled by the server admin."
msgstr "爬虫被服务器管理员禁用。"

#: src/error.cls.php:135
msgid "Could not find %1$s in %2$s."
msgstr "在 %2$s中找不到%1$s ."

#: src/error.cls.php:123
msgid "Credits are not enough to proceed the current request."
msgstr "积分不足以进行当前请求。"

#: src/error.cls.php:111
msgid "The domain key is not correct. Please try to sync your domain key again."
msgstr "域密钥不正确。 请尝试再次同步您的域密钥。"

#: src/error.cls.php:92
msgid "There is proceeding queue not pulled yet."
msgstr "有进行中的队列还没有拉开。"

#: src/error.cls.php:88
msgid "Not enough parameters. Please check if the domain key is set correctly"
msgstr "参数不足。 请检查域密钥设置是否正确"

#: src/error.cls.php:84
msgid "The image list is empty."
msgstr "图像列表为空。"

#: src/task.cls.php:233
msgid "LiteSpeed Crawler Cron"
msgstr "LiteSpeed 爬虫 Cron"

#: src/task.cls.php:214
msgid "Every Minute"
msgstr "每分钟"

#: tpl/general/settings.tpl.php:119
msgid "Turn this option ON to show latest news automatically, including hotfixes, new releases, available beta versions, and promotions."
msgstr "启用此选项可以自动显示最新的新闻（其中包括修补程序、插件的新版本、与测试版、促销消息）"

#: tpl/toolbox/report.tpl.php:105
msgid "To grant wp-admin access to the LiteSpeed Support Team, please generate a passwordless link for the current logged-in user to be sent with the report."
msgstr "需要给予\"wp-admin\"对\"LiteSpeed插件\"的访问权限，请为当前登录用户生成一个无密码链接，以便与报告一起发送。"

#: tpl/toolbox/report.tpl.php:107
msgid "Please do NOT share the above passwordless link with anyone."
msgstr "请不要与任何人共享上述无密码链接。"

#: tpl/toolbox/report.tpl.php:48
msgid "To generate a passwordless link for LiteSpeed Support Team access, you must install %s."
msgstr "要生成用于LiteSpeed支持团队访问的无密码链接，必须安装%s。"

#: tpl/banner/cloud_news.tpl.php:30 tpl/banner/cloud_news.tpl.php:41
msgid "Install"
msgstr "安装"

#: tpl/cache/settings-esi.tpl.php:46
msgid "These options are only available with LiteSpeed Enterprise Web Server or QUIC.cloud CDN."
msgstr "这些选项仅适用于LiteSpeed Enterprise Web Server或QUIC.cloud CDN。"

#: tpl/banner/score.php:74 tpl/dash/dashboard.tpl.php:455
msgid "PageSpeed Score"
msgstr "\"PageSpeed\"评分"

#: tpl/banner/score.php:62 tpl/banner/score.php:96
#: tpl/dash/dashboard.tpl.php:410 tpl/dash/dashboard.tpl.php:486
msgid "Improved by"
msgstr "改进了"

#: tpl/banner/score.php:53 tpl/banner/score.php:87
#: tpl/dash/dashboard.tpl.php:402 tpl/dash/dashboard.tpl.php:478
msgid "After"
msgstr "之后"

#: tpl/banner/score.php:45 tpl/banner/score.php:79
#: tpl/dash/dashboard.tpl.php:394 tpl/dash/dashboard.tpl.php:470
msgid "Before"
msgstr "之前"

#: tpl/banner/score.php:40 tpl/dash/dashboard.tpl.php:374
msgid "Page Load Time"
msgstr "页面加载时间"

#: tpl/inc/check_cache_disabled.php:20
msgid "To use the caching functions you must have a LiteSpeed web server or be using QUIC.cloud CDN."
msgstr "要使用缓存功能，您必须具有LiteSpeed Web服务器或正在使用QUIC.cloud CDN。"

#: src/lang.cls.php:211
msgid "Preserve EXIF/XMP data"
msgstr "保留EXIF/XMP数据"

#: tpl/toolbox/beta_test.tpl.php:32
msgid "Try GitHub Version"
msgstr "试用GitHub版本"

#: tpl/cdn/other.tpl.php:112
msgid "If you turn any of the above settings OFF, please remove the related file types from the %s box."
msgstr "如果您关闭上述任何设置，请从%s框中删除相关的文件类型。"

#: src/doc.cls.php:124
msgid "Both full and partial strings can be used."
msgstr "完整和部分字符串都可以使用。"

#: tpl/page_optm/settings_media_exc.tpl.php:60
msgid "Images containing these class names will not be lazy loaded."
msgstr "包含这些类名的图像不会被延迟加载。"

#: src/lang.cls.php:186
msgid "Lazy Load Image Class Name Excludes"
msgstr "延迟加载图像类名排除"

#: tpl/cache/settings-cache.tpl.php:139 tpl/cache/settings-cache.tpl.php:164
msgid "For example, %1$s defines a TTL of %2$s seconds for %3$s."
msgstr "例如，%1$s为%3$s定义了%2$s秒的TTL。"

#: tpl/cache/settings-cache.tpl.php:136 tpl/cache/settings-cache.tpl.php:161
msgid "To define a custom TTL for a URI, add a space followed by the TTL value to the end of the URI."
msgstr "要为URI定义自定义TTL，请在URI的末尾添加空格和TTL值。"

#: tpl/banner/new_version.php:93
msgid "Maybe Later"
msgstr "稍后再说"

#: tpl/banner/new_version.php:87
msgid "Turn On Auto Upgrade"
msgstr "开启自动升级"

#: tpl/banner/new_version.php:77 tpl/banner/new_version_dev.tpl.php:41
#: tpl/toolbox/beta_test.tpl.php:77
msgid "Upgrade"
msgstr "升级"

#: tpl/banner/new_version.php:66
msgid "New release %s is available now."
msgstr "新版本%s现已发布。"

#: tpl/banner/new_version.php:58
msgid "New Version Available!"
msgstr "新版本可用！"

#: tpl/banner/score.php:112
msgid "Sure I'd love to review!"
msgstr "当然，我很乐意评论!"

#: tpl/banner/score.php:36
msgid "Thank You for Using the LiteSpeed Cache Plugin!"
msgstr "感谢您使用LiteSpeed Cache插件！"

#: src/activation.cls.php:526
msgid "Upgraded successfully."
msgstr "升级成功。"

#: src/activation.cls.php:517 src/activation.cls.php:522
msgid "Failed to upgrade."
msgstr "升级失败。"

#: src/conf.cls.php:688
msgid "Changed setting successfully."
msgstr "更改设置成功。"

#: tpl/cache/settings-esi.tpl.php:37
msgid "ESI sample for developers"
msgstr "针对开发人员的ESI示例"

#: tpl/cache/settings-esi.tpl.php:29
msgid "Replace %1$s with %2$s."
msgstr "将%1$s替换为%2$s。"

#: tpl/cache/settings-esi.tpl.php:26
msgid "You can turn shortcodes into ESI blocks."
msgstr "您可以将快捷代码转换为ESI块。"

#: tpl/cache/settings-esi.tpl.php:22
msgid "WpW: Private Cache vs. Public Cache"
msgstr "WPW：私有缓存与公共缓存"

#: tpl/page_optm/settings_html.tpl.php:132
msgid "Append query string %s to the resources to bypass this action."
msgstr "将查询字符串%s附加到资源以绕过此操作。"

#: tpl/page_optm/settings_html.tpl.php:127
msgid "Google reCAPTCHA will be bypassed automatically."
msgstr "Google reCAPTCHA将被自动绕过。"

#: tpl/crawler/settings.tpl.php:172
msgid "To crawl for a particular cookie, enter the cookie name, and the values you wish to crawl for. Values should be one per line. There will be one crawler created per cookie value, per simulated role."
msgstr "要抓取特定 cookie，请输入 cookie 名称和要抓取的值。值应每行一个。每个 cookie 值、每个模拟角色将创建一个爬虫。"

#: src/admin-display.cls.php:208 tpl/crawler/settings.tpl.php:179
msgid "Cookie Values"
msgstr "Cookie值"

#: src/admin-display.cls.php:207
msgid "Cookie Name"
msgstr "Cookie名称"

#: src/lang.cls.php:252
msgid "Cookie Simulation"
msgstr "Cookie模拟"

#: tpl/page_optm/settings_html.tpl.php:146
msgid "Use Web Font Loader library to load Google Fonts asynchronously while leaving other CSS intact."
msgstr "使用Web Font Loader库以异步方式加载Google字体，同时保持其他CSS不变。"

#: tpl/general/settings_inc.auto_upgrade.tpl.php:25
msgid "Turn this option ON to have LiteSpeed Cache updated automatically, whenever a new version is released. If OFF, update manually as usual."
msgstr "每当发布新版本时，将此选项设置为ON即可自动更新LiteSpeed缓存。 如果关闭，则照常手动更新。"

#: src/lang.cls.php:100
msgid "Automatically Upgrade"
msgstr "自动升级"

#: tpl/toolbox/settings-debug.tpl.php:74
msgid "Your IP"
msgstr "您的IP"

#: src/import.cls.php:155
msgid "Reset successfully."
msgstr "重置成功。"

#: tpl/toolbox/import_export.tpl.php:67
msgid "This will reset all settings to default settings."
msgstr "这会将所有设置重置为默认设置。"

#: tpl/toolbox/import_export.tpl.php:63
msgid "Reset All Settings"
msgstr "重置所有设置"

#: tpl/page_optm/settings_tuning_css.tpl.php:128
msgid "Separate critical CSS files will be generated for paths containing these strings."
msgstr "将为包含这些字符串的路径生成单独的关键CSS文件。"

#: src/lang.cls.php:170
msgid "Separate CCSS Cache URIs"
msgstr "单独的CCSS缓存URI"

#: tpl/page_optm/settings_tuning_css.tpl.php:114
msgid "For example, if every Page on the site has different formatting, enter %s in the box. Separate critical CSS files will be stored for every Page on the site."
msgstr "例如，如果站点上的每个页面都有不同的格式，请在框中输入%s。 网站上每个页面都将存储单独的关键CSS文件。"

#: tpl/page_optm/settings_tuning_css.tpl.php:113
msgid "List post types where each item of that type should have its own CCSS generated."
msgstr "列出文章类型，其中该类型的每个项目都应生成自己的CCSS。"

#: src/lang.cls.php:169
msgid "Separate CCSS Cache Post Types"
msgstr "单独的 CCSS 缓存帖子类型"

#: tpl/page_optm/settings_media.tpl.php:198
msgid "Size list in queue waiting for cron"
msgstr "等待cron的队列中的大小列表"

#: tpl/page_optm/settings_media.tpl.php:173
msgid "If set to %1$s, before the placeholder is localized, the %2$s configuration will be used."
msgstr "如果设置为%1$s，则在定位占位符之前，将使用%2$s配置。"

#: tpl/page_optm/settings_media.tpl.php:170
msgid "Automatically generate LQIP in the background via a cron-based queue."
msgstr "通过基于cron的队列在后台自动生成LQIP。"

#: tpl/page_optm/settings_media.tpl.php:75
msgid "This will generate the placeholder with same dimensions as the image if it has the width and height attributes."
msgstr "如果具有width和height属性，它将生成与图像尺寸相同的占位符。"

#: tpl/page_optm/settings_media.tpl.php:74
msgid "Responsive image placeholders can help to reduce layout reshuffle when images are loaded."
msgstr "响应式图像占位符可以帮助减少加载图像时的布局重新排列。"

#: src/lang.cls.php:193
msgid "Responsive Placeholder"
msgstr "响应式占位符"

#: tpl/toolbox/purge.tpl.php:101
msgid "This will delete all generated image LQIP placeholder files"
msgstr "这将删除所有生成的图像LQIP占位符文件"

#: tpl/inc/check_cache_disabled.php:31
msgid "Please enable LiteSpeed Cache in the plugin settings."
msgstr "请在插件设置中启用LiteSpeed缓存。"

#: tpl/inc/check_cache_disabled.php:25
msgid "Please enable the LSCache Module at the server level, or ask your hosting provider."
msgstr "请在服务器级别启用LSCache模块，或询问您的主机提供商。"

#: src/cloud.cls.php:1393 src/cloud.cls.php:1416
msgid "Failed to request via WordPress"
msgstr "无法通过WordPress请求"

#. Description of the plugin
#: litespeed-cache.php
msgid "High-performance page caching and site optimization from LiteSpeed"
msgstr "LiteSpeed的高性能页面缓存和站点优化"

#: src/img-optm.cls.php:2083
msgid "Reset the optimized data successfully."
msgstr "成功重置优化数据。"

#: src/gui.cls.php:893
msgid "Update %s now"
msgstr "现在更新%s"

#: src/gui.cls.php:890
msgid "View %1$s version %2$s details"
msgstr "查看%1$s版本%2$s详情"

#: src/gui.cls.php:888
msgid "<a href=\"%1$s\" %2$s>View version %3$s details</a> or <a href=\"%4$s\" %5$s target=\"_blank\">update now</a>."
msgstr "<a href=\"%1$s\" %2$s>查看版本 %3$s 详情</a> or <a href=\"%4$s\" %5$s target=\"_blank\">立即更新</a>."

#: src/gui.cls.php:868
msgid "Install %s"
msgstr "安装%s"

#: tpl/inc/check_cache_disabled.php:40
msgid "LSCache caching functions on this page are currently unavailable!"
msgstr "该页面上的LSCache缓存功能当前不可用！"

#: src/cloud.cls.php:1547
msgid "%1$s plugin version %2$s required for this action."
msgstr "此操作需要%1$s插件版本%2$s。"

#: src/cloud.cls.php:1476
msgid "We are working hard to improve your online service experience. The service will be unavailable while we work. We apologize for any inconvenience."
msgstr "我们正在努力改善您的在线服务体验。当我们工作时，服务将不可用。给您带来的不便，我们深表歉意。"

#: tpl/img_optm/settings.tpl.php:60
msgid "Automatically remove the original image backups after fetching optimized images."
msgstr "获取到优化图像后删除原始图像的备份。"

#: src/lang.cls.php:208
msgid "Remove Original Backups"
msgstr "移除备份的原始文件"

#: tpl/img_optm/settings.tpl.php:34
msgid "Automatically request optimization via cron job."
msgstr "通过cron任务进行自动优化。"

#: tpl/img_optm/summary.tpl.php:188
msgid "A backup of each image is saved before it is optimized."
msgstr "在优化前备份图像。"

#: src/img-optm.cls.php:1876
msgid "Switched images successfully."
msgstr "切换图像成功。"

#: tpl/img_optm/settings.tpl.php:81
msgid "This can improve quality but may result in larger images than lossy compression will."
msgstr "这可以提高图像品质，但是也会增加图片的体积。"

#: tpl/img_optm/settings.tpl.php:80
msgid "Optimize images using lossless compression."
msgstr "使用无损压缩优化图像。"

#: src/lang.cls.php:210
msgid "Optimize Losslessly"
msgstr "无损压缩"

#: tpl/img_optm/settings.tpl.php:47
msgid "Optimize images and save backups of the originals in the same folder."
msgstr "优化图像并将原始图像备份在相同目录中。"

#: src/lang.cls.php:207
msgid "Optimize Original Images"
msgstr "优化原始图像"

#: tpl/page_optm/settings_css.tpl.php:218
msgid "When this option is turned %s, it will also load Google Fonts asynchronously."
msgstr "将此选项设为%s时，它还将异步加载Google字体。"

#: src/purge.cls.php:253
msgid "Cleaned all Critical CSS files."
msgstr "清理了所有关键的CSS文件。"

#: tpl/page_optm/settings_css.tpl.php:325
msgid "This will inline the asynchronous CSS library to avoid render blocking."
msgstr "这将内联异步CSS库以避免渲染阻塞。"

#: src/lang.cls.php:154
msgid "Inline CSS Async Lib"
msgstr "内联CSS异步库"

#: tpl/page_optm/settings_localization.tpl.php:72
#: tpl/page_optm/settings_media.tpl.php:216
msgid "Run Queue Manually"
msgstr "手动运行队列"

#: tpl/page_optm/settings_css.tpl.php:115
#: tpl/page_optm/settings_css.tpl.php:252 tpl/page_optm/settings_vpi.tpl.php:65
msgid "URL list in %s queue waiting for cron"
msgstr "URL 列表在 %s 队列中，等待 cron"

#: tpl/page_optm/settings_css.tpl.php:103
#: tpl/page_optm/settings_css.tpl.php:240
msgid "Last requested cost"
msgstr "上次请求的成本"

#: tpl/page_optm/settings_css.tpl.php:100
#: tpl/page_optm/settings_css.tpl.php:237
#: tpl/page_optm/settings_media.tpl.php:186
#: tpl/page_optm/settings_vpi.tpl.php:53
msgid "Last generated"
msgstr "上次生成的时间"

#: tpl/page_optm/settings_media.tpl.php:178
msgid "If set to %s this is done in the foreground, which may slow down page load."
msgstr "如果设置为%s，则此操作在前台完成，这可能会减慢页面加载速度。"

#: tpl/page_optm/settings_css.tpl.php:217
msgid "Automatic generation of critical CSS is in the background via a cron-based queue."
msgstr "通过基于 cron 的队列在后台自动生成关键 CSS。"

#: tpl/page_optm/settings_css.tpl.php:213
msgid "Optimize CSS delivery."
msgstr "优化CSS传输。"

#: tpl/toolbox/purge.tpl.php:74
msgid "This will delete all generated critical CSS files"
msgstr "这将删除所有生成的关键CSS文件"

#: tpl/dash/dashboard.tpl.php:623 tpl/toolbox/purge.tpl.php:73
msgid "Critical CSS"
msgstr "Critical CSS"

#: src/doc.cls.php:66
msgid "This site utilizes caching in order to facilitate a faster response time and better user experience. Caching potentially stores a duplicate copy of every web page that is on display on this site. All cache files are temporary, and are never accessed by any third party, except as necessary to obtain technical support from the cache plugin vendor. Cache files expire on a schedule set by the site administrator, but may easily be purged by the admin before their natural expiration, if necessary. We may use QUIC.cloud services to process & cache your data temporarily."
msgstr "本网站采用缓存技术，以加快响应速度和改善用户体验。缓存可能会存储本网站上显示的每个网页的副本。所有缓存文件均为临时文件，任何第三方均不得访问，除非在必要时从缓存插件供应商处获得技术支持。缓存文件的过期时间由网站管理员设定，但在必要时，管理员可在缓存文件自然过期前轻松清除缓存文件。我们可能使用 QUIC.cloud 服务暂时处理和缓存您的数据。"

#: tpl/toolbox/heartbeat.tpl.php:28
msgid "Disabling this may cause WordPress tasks triggered by AJAX to stop working."
msgstr "禁用此功能可能会导致AJAX触发的WordPress任务停止工作。"

#: src/utility.cls.php:228
msgid "right now"
msgstr "现在"

#: src/utility.cls.php:228
msgid "just now"
msgstr "刚才"

#: tpl/img_optm/summary.tpl.php:259
msgid "Saved"
msgstr "已保存"

#: tpl/img_optm/summary.tpl.php:253
#: tpl/page_optm/settings_localization.tpl.php:61
msgid "Last ran"
msgstr "最后一次运行"

#: tpl/img_optm/settings.tpl.php:66 tpl/img_optm/summary.tpl.php:245
msgid "You will be unable to Revert Optimization once the backups are deleted!"
msgstr "一旦删除备份，您将无法还原优化！"

#: tpl/img_optm/settings.tpl.php:65 tpl/img_optm/summary.tpl.php:244
msgid "This is irreversible."
msgstr "该操作不可逆。"

#: tpl/img_optm/summary.tpl.php:265
msgid "Remove Original Image Backups"
msgstr "删除原始图像备份"

#: tpl/img_optm/summary.tpl.php:264
msgid "Are you sure you want to remove all image backups?"
msgstr "确定要删除所有的图像备份？"

#: tpl/crawler/blacklist.tpl.php:31 tpl/img_optm/summary.tpl.php:201
msgid "Total"
msgstr "总共"

#: tpl/img_optm/summary.tpl.php:198 tpl/img_optm/summary.tpl.php:256
msgid "Files"
msgstr "文件"

#: tpl/img_optm/summary.tpl.php:194
msgid "Last calculated"
msgstr "最后计算"

#: tpl/img_optm/summary.tpl.php:208
msgid "Calculate Original Image Storage"
msgstr "计算原始图像占用空间"

#: tpl/img_optm/summary.tpl.php:184
msgid "Storage Optimization"
msgstr "存储优化"

#: tpl/cdn/other.tpl.php:141 tpl/img_optm/settings.tpl.php:125
msgid "Use the format %1$s or %2$s (element is optional)."
msgstr "使用格式 %1$s 或 %2$s (元素可选)."

#: tpl/cdn/other.tpl.php:137 tpl/img_optm/settings.tpl.php:124
msgid "Only attributes listed here will be replaced."
msgstr "只有列在此处的属性会被替换。"

#: tpl/cdn/other.tpl.php:196
msgid "Only files within these directories will be pointed to the CDN."
msgstr "只有这些目录下的文件会指向CDN。"

#: src/lang.cls.php:243
msgid "Included Directories"
msgstr "包括目录"

#: tpl/cache/settings-purge.tpl.php:152
msgid "A Purge All will be executed when WordPress runs these hooks."
msgstr "当WordPress运行这些hook时会运行清除全部。"

#: src/lang.cls.php:220
msgid "Purge All Hooks"
msgstr "清除全部的Hook"

#: src/purge.cls.php:212
msgid "Purged all caches successfully."
msgstr "成功清除全部缓存。"

#: src/gui.cls.php:562 src/gui.cls.php:691 src/gui.cls.php:744
msgid "LSCache"
msgstr "LSCache"

#: src/gui.cls.php:506
msgid "Forced cacheable"
msgstr "强制缓存"

#: tpl/cache/settings-cache.tpl.php:133
msgid "Paths containing these strings will be cached regardless of no-cacheable settings."
msgstr "包括这些字串的路径会无视不缓存的设定而直接缓存。"

#: src/lang.cls.php:131
msgid "Force Cache URIs"
msgstr "强制缓存URI"

#: tpl/cache/network_settings-excludes.tpl.php:17
#: tpl/cache/settings-excludes.tpl.php:15
msgid "Exclude Settings"
msgstr "排除设定"

#: tpl/toolbox/settings-debug.tpl.php:45
msgid "This will disable LSCache and all optimization features for debug purpose."
msgstr "这会禁用LSCache和所有优化选项以便debug。"

#: src/lang.cls.php:255
msgid "Disable All Features"
msgstr "禁用全部功能"

#: src/gui.cls.php:599 src/gui.cls.php:781 tpl/toolbox/purge.tpl.php:64
msgid "Opcode Cache"
msgstr "Opcode Cache"

#: src/gui.cls.php:570 src/gui.cls.php:752 tpl/toolbox/purge.tpl.php:47
msgid "CSS/JS Cache"
msgstr "CSS/JS Cache"

#: src/gui.cls.php:849 tpl/img_optm/summary.tpl.php:176
msgid "Remove all previous unfinished image optimization requests."
msgstr "移除所有先前完成的图片优化请求。"

#: src/gui.cls.php:850 tpl/img_optm/summary.tpl.php:178
msgid "Clean Up Unfinished Data"
msgstr "清理未完成的数据"

#: tpl/banner/slack.php:40
msgid "Join Us on Slack"
msgstr "加入我们的 Slack"

#. translators: %s: Link to LiteSpeed Slack community
#: tpl/banner/slack.php:28
msgid "Join the %s community."
msgstr "加入 %s 社区。"

#: tpl/banner/slack.php:24
msgid "Want to connect with other LiteSpeed users?"
msgstr "希望和其它LiteSpeed用户保持联系？"

#: tpl/cdn/cf.tpl.php:38
msgid "Your API key / token is used to access %s APIs."
msgstr "您的 API 密钥/令牌用于访问 %s API。"

#: tpl/cdn/cf.tpl.php:47
msgid "Your Email address on %s."
msgstr "您的电子邮件地址%s。"

#: tpl/cdn/cf.tpl.php:31
msgid "Use %s API functionality."
msgstr "使用 %s API 功能。"

#: tpl/cdn/other.tpl.php:80
msgid "To randomize CDN hostname, define multiple hostnames for the same resources."
msgstr "要随机输出CDN主机名，请为相同资源定义多个主机名。"

#: tpl/inc/admin_footer.php:23
msgid "Join LiteSpeed Slack community"
msgstr "加入LiteSpeed Slack社区"

#: tpl/inc/admin_footer.php:21
msgid "Visit LSCWP support forum"
msgstr "访问LSCWP支持论坛"

#: src/lang.cls.php:28 tpl/dash/dashboard.tpl.php:560
msgid "Images notified to pull"
msgstr "图片已通知待取回"

#: tpl/img_optm/summary.tpl.php:291
msgid "What is a group?"
msgstr "什么是图片组？"

#: src/admin-display.cls.php:1256
msgid "%s image"
msgstr "%s张图像"

#: src/admin-display.cls.php:1253
msgid "%s group"
msgstr "%s个群组"

#: src/admin-display.cls.php:1244
msgid "%s images"
msgstr "%s 张图像"

#: src/admin-display.cls.php:1241
msgid "%s groups"
msgstr "%s 组"

#: src/crawler.cls.php:1235
msgid "Guest"
msgstr "游客"

#: tpl/crawler/settings.tpl.php:109
msgid "To crawl the site as a logged-in user, enter the user ids to be simulated."
msgstr "要以登录用户身份预读取网站，请输入要模拟的用户ID。"

#: src/lang.cls.php:251
msgid "Role Simulation"
msgstr "角色模拟"

#: tpl/crawler/summary.tpl.php:232
msgid "running"
msgstr "运行"

#: tpl/db_optm/manage.tpl.php:187
msgid "Size"
msgstr "大小"

#: tpl/crawler/summary.tpl.php:123 tpl/dash/dashboard.tpl.php:103
#: tpl/dash/dashboard.tpl.php:822
msgid "Ended reason"
msgstr "结束原因"

#: tpl/crawler/summary.tpl.php:116 tpl/dash/dashboard.tpl.php:97
#: tpl/dash/dashboard.tpl.php:816
msgid "Last interval"
msgstr "上次间隔"

#: tpl/crawler/summary.tpl.php:104 tpl/dash/dashboard.tpl.php:91
#: tpl/dash/dashboard.tpl.php:810
msgid "Current crawler started at"
msgstr "当前爬虫始于"

#: tpl/crawler/summary.tpl.php:97
msgid "Run time for previous crawler"
msgstr "上一个爬虫运行时间"

#: tpl/crawler/summary.tpl.php:91 tpl/crawler/summary.tpl.php:98
msgid "%d seconds"
msgstr "%d 秒"

#: tpl/crawler/summary.tpl.php:90
msgid "Last complete run time for all crawlers"
msgstr "上次完整的全部爬虫运行时间"

#: tpl/crawler/summary.tpl.php:77
msgid "Current sitemap crawl started at"
msgstr "当前站点地图爬行始于"

#. translators: %1$s: Object Cache Admin title, %2$s: OFF status
#: tpl/cache/settings_inc.object.tpl.php:278
msgid "Save transients in database when %1$s is %2$s."
msgstr "当 %1$s 为 %2$s 时保存transient到数据库。"

#: src/lang.cls.php:125
msgid "Store Transients"
msgstr "保存 Transient"

#. translators: %1$s: Cache Mobile label, %2$s: ON status, %3$s: List of Mobile
#. User Agents label
#: tpl/cache/settings_inc.cache_mobile.tpl.php:89
msgid "If %1$s is %2$s, then %3$s must be populated!"
msgstr "当 %1$s 为 %2$s时，%3$s 必须被设定！"

#: tpl/cache/more_settings_tip.tpl.php:22
#: tpl/cache/settings-excludes.tpl.php:71
#: tpl/cache/settings-excludes.tpl.php:104 tpl/cdn/other.tpl.php:79
#: tpl/crawler/settings.tpl.php:76 tpl/crawler/settings.tpl.php:86
msgid "NOTE"
msgstr "注意"

#: src/admin-display.cls.php:1210
msgid "Server variable(s) %s available to override this setting."
msgstr "服务器变量 %s 可用来覆盖本设定。"

#: src/admin-display.cls.php:1208 tpl/cache/settings-esi.tpl.php:105
#: tpl/page_optm/settings_css.tpl.php:86 tpl/page_optm/settings_css.tpl.php:221
#: tpl/page_optm/settings_html.tpl.php:131
#: tpl/page_optm/settings_media.tpl.php:256
#: tpl/page_optm/settings_media_exc.tpl.php:36
#: tpl/page_optm/settings_tuning.tpl.php:48
#: tpl/page_optm/settings_tuning.tpl.php:68
#: tpl/page_optm/settings_tuning.tpl.php:89
#: tpl/page_optm/settings_tuning.tpl.php:110
#: tpl/page_optm/settings_tuning.tpl.php:129
#: tpl/page_optm/settings_tuning_css.tpl.php:35
#: tpl/page_optm/settings_tuning_css.tpl.php:96
#: tpl/page_optm/settings_tuning_css.tpl.php:99
#: tpl/page_optm/settings_tuning_css.tpl.php:100
#: tpl/toolbox/edit_htaccess.tpl.php:61 tpl/toolbox/edit_htaccess.tpl.php:79
msgid "API"
msgstr "API"

#: src/import.cls.php:133
msgid "Imported setting file %s successfully."
msgstr "成功导入设定文件 %s 。"

#: src/import.cls.php:80
msgid "Import failed due to file error."
msgstr "由于文件错误导致导入失败。"

#: tpl/page_optm/settings_css.tpl.php:60 tpl/page_optm/settings_js.tpl.php:48
msgid "How to Fix Problems Caused by CSS/JS Optimization."
msgstr "如何修正由 CSS/JS 优化导致的问题。"

#: tpl/cache/settings-advanced.tpl.php:76
msgid "This will generate extra requests to the server, which will increase server load."
msgstr "这会产生额外的服务器请求，将增加服务器负担。"

#: tpl/cache/settings-advanced.tpl.php:71
msgid "When a visitor hovers over a page link, preload that page. This will speed up the visit to that link."
msgstr "当访问者将鼠标悬停在页面链接上时，预载该页面。这将加快访问该链接的速度。"

#: src/lang.cls.php:222
msgid "Instant Click"
msgstr "即时点击"

#: tpl/toolbox/purge.tpl.php:65
msgid "Reset the entire opcode cache"
msgstr "重置整个opcode缓存"

#: tpl/toolbox/import_export.tpl.php:59
msgid "This will import settings from a file and override all current LiteSpeed Cache settings."
msgstr "这会从文件中导入设定并覆盖所有已有的设定。"

#: tpl/toolbox/import_export.tpl.php:54
msgid "Last imported"
msgstr "上次导入"

#: tpl/toolbox/import_export.tpl.php:48
msgid "Import"
msgstr "导入"

#: tpl/toolbox/import_export.tpl.php:40
msgid "Import Settings"
msgstr "导入设定"

#: tpl/toolbox/import_export.tpl.php:36
msgid "This will export all current LiteSpeed Cache settings and save them as a file."
msgstr "这会导出所有当前的 LiteSpeed 缓存设定并存为文件。"

#: tpl/toolbox/import_export.tpl.php:31
msgid "Last exported"
msgstr "上次导出"

#: tpl/toolbox/import_export.tpl.php:25
msgid "Export"
msgstr "导出"

#: tpl/toolbox/import_export.tpl.php:19
msgid "Export Settings"
msgstr "导出设定"

#: tpl/presets/entry.tpl.php:17 tpl/toolbox/entry.tpl.php:20
msgid "Import / Export"
msgstr "导入/导出"

#: tpl/cache/settings_inc.object.tpl.php:249
msgid "Use keep-alive connections to speed up cache operations."
msgstr "使用长链接加速缓存操作。"

#: tpl/cache/settings_inc.object.tpl.php:209
msgid "Database to be used"
msgstr "要使用的数据库"

#: src/lang.cls.php:120
msgid "Redis Database ID"
msgstr "Redis 数据库 ID"

#: tpl/cache/settings_inc.object.tpl.php:196
msgid "Specify the password used when connecting."
msgstr "指定连接时使用的密码。"

#: src/lang.cls.php:119
msgid "Password"
msgstr "密码"

#. translators: %s: SASL
#: tpl/cache/settings_inc.object.tpl.php:180
msgid "Only available when %s is installed."
msgstr "仅当 %s 已安装时可用。"

#: src/lang.cls.php:118
msgid "Username"
msgstr "用户名"

#. translators: %s: Object cache name
#: tpl/cache/settings_inc.object.tpl.php:99
msgid "Your %s Hostname or IP address."
msgstr "您的 %s 主机名或者IP地址。"

#: src/lang.cls.php:114
msgid "Method"
msgstr "方法"

#: src/purge.cls.php:472
msgid "Purge all object caches successfully."
msgstr "成功清除所有对象缓存。"

#: src/purge.cls.php:459
msgid "Object cache is not enabled."
msgstr "对象缓存未激活。"

#: tpl/cache/settings_inc.object.tpl.php:262
msgid "Improve wp-admin speed through caching. (May encounter expired data)"
msgstr "通过缓存改进wp-admin的加载速度。（可能碰到过期数据）"

#: src/lang.cls.php:124
msgid "Cache WP-Admin"
msgstr "缓存 WP-Admin"

#: src/lang.cls.php:123
msgid "Persistent Connection"
msgstr "永久连接"

#: src/lang.cls.php:122
msgid "Do Not Cache Groups"
msgstr "不缓存群组"

#: tpl/cache/settings_inc.object.tpl.php:222
msgid "Groups cached at the network level."
msgstr "站点网络级别的缓存群组。"

#: src/lang.cls.php:121
msgid "Global Groups"
msgstr "全局群组"

#: tpl/cache/settings_inc.object.tpl.php:71
msgid "Connection Test"
msgstr "连接测试"

#. translators: %s: Object cache name
#: tpl/cache/settings_inc.object.tpl.php:58
#: tpl/cache/settings_inc.object.tpl.php:66
msgid "%s Extension"
msgstr "%s 扩展"

#: tpl/cache/settings_inc.object.tpl.php:52 tpl/crawler/blacklist.tpl.php:41
#: tpl/crawler/summary.tpl.php:153
msgid "Status"
msgstr "状态"

#: tpl/cache/settings_inc.object.tpl.php:164
msgid "Default TTL for cached objects."
msgstr "默认的对象缓存的TTL。"

#: src/lang.cls.php:117
msgid "Default Object Lifetime"
msgstr "默认的对象生命周期"

#: src/lang.cls.php:116
msgid "Port"
msgstr "端口"

#: src/lang.cls.php:115
msgid "Host"
msgstr "主机"

#: src/gui.cls.php:589 src/gui.cls.php:771 src/lang.cls.php:113
#: tpl/dash/dashboard.tpl.php:60 tpl/dash/dashboard.tpl.php:603
#: tpl/toolbox/purge.tpl.php:55
msgid "Object Cache"
msgstr "对象缓存"

#: tpl/cache/settings_inc.object.tpl.php:28
msgid "Failed"
msgstr "失败"

#: tpl/cache/settings_inc.object.tpl.php:25
msgid "Passed"
msgstr "通过"

#: tpl/cache/settings_inc.object.tpl.php:23
msgid "Not Available"
msgstr "不可用"

#: tpl/toolbox/purge.tpl.php:56
msgid "Purge all the object caches"
msgstr "清除全部对象缓存"

#: src/cdn/cloudflare.cls.php:259 src/cdn/cloudflare.cls.php:281
msgid "Failed to communicate with Cloudflare"
msgstr "和 Cloudflare 通讯失败"

#: src/cdn/cloudflare.cls.php:272
msgid "Communicated with Cloudflare successfully."
msgstr "和 CLoudflare 通讯成功。"

#: src/cdn/cloudflare.cls.php:169
msgid "No available Cloudflare zone"
msgstr "无可用的 Cloudflare 区域"

#: src/cdn/cloudflare.cls.php:155
msgid "Notified Cloudflare to purge all successfully."
msgstr "已成功通知 Cloudflare 清除全部。"

#: src/cdn/cloudflare.cls.php:139
msgid "Cloudflare API is set to off."
msgstr "Cloudflare API 被设置为关闭。"

#: src/cdn/cloudflare.cls.php:111
msgid "Notified Cloudflare to set development mode to %s successfully."
msgstr "已成功通知 Cloudflare 设定开发模式为 %s。"

#: tpl/cdn/cf.tpl.php:60
msgid "Once saved, it will be matched with the current list and completed automatically."
msgstr "一旦保存，它将自动匹配现有的列表并自动完成。"

#: tpl/cdn/cf.tpl.php:59
msgid "You can just type part of the domain."
msgstr "您可以输入域名的部分内容。"

#: tpl/cdn/cf.tpl.php:52
msgid "Domain"
msgstr "域名"

#: src/lang.cls.php:245
msgid "Cloudflare API"
msgstr "Cloudflare API"

#: tpl/cdn/cf.tpl.php:162
msgid "Purge Everything"
msgstr "清除全部"

#: tpl/cdn/cf.tpl.php:156
msgid "Cloudflare Cache"
msgstr "Cloudflare 缓存"

#: tpl/cdn/cf.tpl.php:151
msgid "Development Mode will be turned off automatically after three hours."
msgstr "开发模式会在三小时后自动关闭。"

#: tpl/cdn/cf.tpl.php:149
msgid "Temporarily bypass Cloudflare cache. This allows changes to the origin server to be seen in realtime."
msgstr "临时跳过 Cloudflare 缓存。这可以让原始服务器的改变实时展现。"

#: tpl/cdn/cf.tpl.php:141
msgid "Development mode will be automatically turned off in %s."
msgstr "开发模式将在 %s 后自动关闭。"

#: tpl/cdn/cf.tpl.php:137
msgid "Current status is %s."
msgstr "当前状态为 %s。"

#: tpl/cdn/cf.tpl.php:129
msgid "Current status is %1$s since %2$s."
msgstr "当前状态自 %2$s 开始为 %1$s。"

#: tpl/cdn/cf.tpl.php:119
msgid "Check Status"
msgstr "检查状态"

#: tpl/cdn/cf.tpl.php:116
msgid "Turn OFF"
msgstr "关闭"

#: tpl/cdn/cf.tpl.php:113
msgid "Turn ON"
msgstr "开启"

#: tpl/cdn/cf.tpl.php:111
msgid "Development Mode"
msgstr "开发模式"

#: tpl/cdn/cf.tpl.php:108
msgid "Cloudflare Zone"
msgstr "Cloudflare 区域"

#: tpl/cdn/cf.tpl.php:107
msgid "Cloudflare Domain"
msgstr "Cloudflare 域名"

#: src/gui.cls.php:579 src/gui.cls.php:761 tpl/cdn/cf.tpl.php:96
#: tpl/cdn/entry.tpl.php:15
msgid "Cloudflare"
msgstr "Cloudflare"

#: tpl/page_optm/settings_html.tpl.php:45
#: tpl/page_optm/settings_html.tpl.php:76
msgid "For example"
msgstr "比如"

#: tpl/page_optm/settings_html.tpl.php:44
msgid "Prefetching DNS can reduce latency for visitors."
msgstr "预读取DNS可降低访客的延迟。"

#: src/lang.cls.php:159
msgid "DNS Prefetch"
msgstr "DNS预读取"

#: tpl/page_optm/settings_media.tpl.php:43
msgid "Adding Style to Your Lazy-Loaded Images"
msgstr "增加样式到您的延迟加载图片中"

#: src/admin-display.cls.php:1074 src/admin-display.cls.php:1078
#: tpl/cdn/other.tpl.php:108
msgid "Default value"
msgstr "默认值"

#: tpl/cdn/other.tpl.php:100
msgid "Static file type links to be replaced by CDN links."
msgstr "将被CDN链接替换的静态文件类型。"

#: src/lang.cls.php:111
msgid "Drop Query String"
msgstr "丢弃 Query String"

#: tpl/cache/settings-advanced.tpl.php:57
msgid "Enable this option if you are using both HTTP and HTTPS in the same domain and are noticing cache irregularities."
msgstr "当您在同一域名下同时使用HTTP和HTTPS并注意到有缓存问题时，请激活此选项。"

#: src/lang.cls.php:221
msgid "Improve HTTP/HTTPS Compatibility"
msgstr "改善 HTTP/HTTPS 兼容性"

#: tpl/img_optm/summary.tpl.php:382
msgid "Remove all previous image optimization requests/results, revert completed optimizations, and delete all optimization files."
msgstr "移除之前的全部图片优化请求和结果，重置已完成的优化并删除全部已优化的文件。"

#: tpl/img_optm/settings.media_webp.tpl.php:34 tpl/img_optm/summary.tpl.php:378
msgid "Destroy All Optimization Data"
msgstr "销毁所有优化数据"

#: tpl/img_optm/summary.tpl.php:304
msgid "Scan for any new unoptimized image thumbnail sizes and resend necessary image optimization requests."
msgstr "扫描新的未优化图片缩略图并发送图片优化请求。"

#: tpl/img_optm/settings.tpl.php:95
msgid "This will increase the size of optimized files."
msgstr "这会增加优化后图片的体积。"

#: tpl/img_optm/settings.tpl.php:94
msgid "Preserve EXIF data (copyright, GPS, comments, keywords, etc) when optimizing."
msgstr "在优化时保留EXIF信息 ( 如版权，GPS，描述等 ) 。"

#: tpl/toolbox/log_viewer.tpl.php:46 tpl/toolbox/log_viewer.tpl.php:75
msgid "Clear Logs"
msgstr "清除日志"

#: thirdparty/woocommerce.content.tpl.php:24
msgid "To test the cart, visit the <a %s>FAQ</a>."
msgstr "要测试购物车，请访问 <a %s>FAQ</a>。"

#: src/utility.cls.php:231
msgid " %s ago"
msgstr "%s 之前"

#: src/media.cls.php:380
msgid "WebP saved %s"
msgstr "WebP 节省 %s"

#: tpl/toolbox/report.tpl.php:68
msgid "If you run into any issues, please refer to the report number in your support message."
msgstr "如果您遇到任何问题，请在客服信息里提及本报告号码。"

#: tpl/img_optm/summary.tpl.php:156
msgid "Last pull initiated by cron at %s."
msgstr "最后运行cron的时间为%s。"

#: tpl/img_optm/summary.tpl.php:93
msgid "Images will be pulled automatically if the cron job is running."
msgstr "如果计划任务在运行，图片会自动被取回。"

#: tpl/img_optm/summary.tpl.php:93
msgid "Only press the button if the pull cron job is disabled."
msgstr "仅当取回计划任务被仅用时点击本按钮。"

#: tpl/img_optm/summary.tpl.php:102
msgid "Pull Images"
msgstr "取回图片"

#: tpl/img_optm/summary.tpl.php:142
msgid "This process is automatic."
msgstr "整个过程是自动的。"

#: tpl/dash/dashboard.tpl.php:568 tpl/img_optm/summary.tpl.php:322
msgid "Last Request"
msgstr "上次请求"

#: tpl/dash/dashboard.tpl.php:545 tpl/img_optm/summary.tpl.php:319
msgid "Images Pulled"
msgstr "已取回的图片"

#: tpl/toolbox/entry.tpl.php:29
msgid "Report"
msgstr "报告"

#: tpl/toolbox/report.tpl.php:139
msgid "Send this report to LiteSpeed. Refer to this report number when posting in the WordPress support forum."
msgstr "发送本报告给LiteSpeed。在WordPress论坛发帖时您可以提到所生成的报告号码。"

#: tpl/toolbox/report.tpl.php:38
msgid "Send to LiteSpeed"
msgstr "发送给LiteSpeed"

#: src/media.cls.php:258
msgid "LiteSpeed Optimization"
msgstr "LiteSpeed 优化"

#: src/lang.cls.php:166
msgid "Load Google Fonts Asynchronously"
msgstr "异步加载Google Fonts"

#: src/lang.cls.php:98
msgid "Browser Cache TTL"
msgstr "浏览器缓存TTL"

#: src/doc.cls.php:88 src/doc.cls.php:140 tpl/dash/dashboard.tpl.php:186
#: tpl/dash/dashboard.tpl.php:845 tpl/general/online.tpl.php:81
#: tpl/general/online.tpl.php:93 tpl/general/online.tpl.php:109
#: tpl/general/online.tpl.php:114 tpl/img_optm/summary.tpl.php:59
#: tpl/inc/check_cache_disabled.php:46
msgid "Learn More"
msgstr "了解更多"

#: tpl/img_optm/summary.tpl.php:285
msgid "Image groups total"
msgstr "图片组总数"

#: src/lang.cls.php:29
msgid "Images optimized and pulled"
msgstr "已优化并抓回的图片"

#: src/lang.cls.php:27 tpl/dash/dashboard.tpl.php:551
msgid "Images requested"
msgstr "已请求的图片"

#: src/img-optm.cls.php:1973 src/img-optm.cls.php:2033
msgid "Switched to optimized file successfully."
msgstr "成功切换到已优化的文件。"

#: src/img-optm.cls.php:2027
msgid "Restored original file successfully."
msgstr "成功恢复原始文件。"

#: src/img-optm.cls.php:1997
msgid "Enabled WebP file successfully."
msgstr "启用WebP文件成功。"

#: src/img-optm.cls.php:1992
msgid "Disabled WebP file successfully."
msgstr "禁用WebP文件成功。"

#: tpl/img_optm/settings.media_webp.tpl.php:26
msgid "Significantly improve load time by replacing images with their optimized %s versions."
msgstr "通过以优化过的 %s 版本替换图片来显著的改善加载时间。"

#: tpl/cache/settings-excludes.tpl.php:135
msgid "Selected roles will be excluded from cache."
msgstr "选择的角色将不被缓存。"

#: tpl/general/entry.tpl.php:18 tpl/page_optm/entry.tpl.php:23
#: tpl/page_optm/entry.tpl.php:24
msgid "Tuning"
msgstr "调整"

#: tpl/page_optm/settings_tuning.tpl.php:156
msgid "Selected roles will be excluded from all optimizations."
msgstr "选择的角色将不做任何优化。"

#: src/lang.cls.php:178
msgid "Role Excludes"
msgstr "角色排除"

#: tpl/general/settings_tuning.tpl.php:19
#: tpl/page_optm/settings_tuning.tpl.php:29
msgid "Tuning Settings"
msgstr "调整设定"

#: tpl/cache/settings-excludes.tpl.php:106
msgid "If the tag slug is not found, the tag will be removed from the list on save."
msgstr "如果标签未被发现，标签将会在保存时从列表中移除。"

#: tpl/cache/settings-excludes.tpl.php:73
msgid "If the category name is not found, the category will be removed from the list on save."
msgstr "如果分类未被发现，分类将会在保存时从列表中移除。"

#: tpl/img_optm/summary.tpl.php:141
msgid "After the QUIC.cloud Image Optimization server finishes optimization, it will notify your site to pull the optimized images."
msgstr "在LiteSpeed图片优化服务器完成优化后，它会通知您的网站去抓取优化后的图片。"

#: tpl/dash/dashboard.tpl.php:536 tpl/img_optm/summary.tpl.php:76
#: tpl/img_optm/summary.tpl.php:89
msgid "Send Optimization Request"
msgstr "发送优化请求"

#: tpl/img_optm/summary.tpl.php:276
msgid "Image Information"
msgstr "图片信息"

#: tpl/dash/dashboard.tpl.php:542 tpl/img_optm/summary.tpl.php:316
msgid "Total Reduction"
msgstr "总共减少"

#: tpl/img_optm/summary.tpl.php:313
msgid "Optimization Summary"
msgstr "优化概要"

#: tpl/img_optm/entry.tpl.php:30
msgid "LiteSpeed Cache Image Optimization"
msgstr "LiteSpeed缓存图片优化"

#: src/admin-display.cls.php:130 src/gui.cls.php:727
#: tpl/dash/dashboard.tpl.php:203 tpl/dash/network_dash.tpl.php:36
#: tpl/general/online.tpl.php:75 tpl/general/online.tpl.php:134
#: tpl/general/online.tpl.php:149 tpl/presets/standard.tpl.php:32
msgid "Image Optimization"
msgstr "图片优化"

#: tpl/page_optm/settings_media.tpl.php:60
msgid "For example, %s can be used for a transparent placeholder."
msgstr "比如，%s可以被用作背景透明的占位符。"

#: tpl/page_optm/settings_media.tpl.php:59
msgid "By default a gray image placeholder %s will be used."
msgstr "默认一个灰色的图片占位符%s会被使用。"

#: tpl/page_optm/settings_media.tpl.php:58
msgid "This can be predefined in %2$s as well using constant %1$s, with this setting taking priority."
msgstr "这也可以通过在%2$s中预定义%1$s实现。本选项优先级较预定义高。"

#: tpl/page_optm/settings_media.tpl.php:57
msgid "Specify a base64 image to be used as a simple placeholder while images finish loading."
msgstr "指定要在图像加载完成时用作简单占位符的base64图像。"

#: tpl/page_optm/settings_media_exc.tpl.php:38
#: tpl/page_optm/settings_tuning.tpl.php:70
#: tpl/page_optm/settings_tuning.tpl.php:91
#: tpl/page_optm/settings_tuning.tpl.php:112
#: tpl/page_optm/settings_tuning_css.tpl.php:37
msgid "Elements with attribute %s in html code will be excluded."
msgstr "有属性%s的元素将被排除。"

#: tpl/cache/settings-esi.tpl.php:106
#: tpl/page_optm/settings_media_exc.tpl.php:37
#: tpl/page_optm/settings_tuning.tpl.php:49
#: tpl/page_optm/settings_tuning.tpl.php:69
#: tpl/page_optm/settings_tuning.tpl.php:90
#: tpl/page_optm/settings_tuning.tpl.php:111
#: tpl/page_optm/settings_tuning.tpl.php:130
#: tpl/page_optm/settings_tuning_css.tpl.php:36
#: tpl/page_optm/settings_tuning_css.tpl.php:97
msgid "Filter %s is supported."
msgstr "支持过滤器%s。"

#: tpl/page_optm/settings_media_exc.tpl.php:31
msgid "Listed images will not be lazy loaded."
msgstr "列出的图片将不被延迟加载。"

#: src/lang.cls.php:185
msgid "Lazy Load Image Excludes"
msgstr "延迟加载图片排除"

#: src/gui.cls.php:539
msgid "No optimization"
msgstr "无优化"

#: tpl/page_optm/settings_tuning.tpl.php:126
msgid "Prevent any optimization of listed pages."
msgstr "列出的页面将不被优化。"

#: src/lang.cls.php:176
msgid "URI Excludes"
msgstr "URI排除"

#: tpl/page_optm/settings_html.tpl.php:174
msgid "Stop loading WordPress.org emoji. Browser default emoji will be displayed instead."
msgstr "停止加载 wordpress.org 表情包。浏览器默认表情包将被显示。"

#: src/doc.cls.php:126
msgid "Both full URLs and partial strings can be used."
msgstr "完整URL和部分匹配字串都可以使用。"

#: tpl/page_optm/settings_media.tpl.php:232
msgid "Load iframes only when they enter the viewport."
msgstr "仅在Iframe进入视野时加载它们。"

#: src/lang.cls.php:201
msgid "Lazy Load Iframes"
msgstr "延迟加载Iframe"

#: tpl/page_optm/settings_media.tpl.php:39
#: tpl/page_optm/settings_media.tpl.php:233
msgid "This can improve page loading time by reducing initial HTTP requests."
msgstr "这可以通过减少初始HTTP请求数量来改善页面加载时间。"

#: tpl/page_optm/settings_media.tpl.php:38
msgid "Load images only when they enter the viewport."
msgstr "仅在图片进入视野时加载它们。"

#: src/lang.cls.php:184
msgid "Lazy Load Images"
msgstr "延迟加载图片"

#: tpl/page_optm/entry.tpl.php:19 tpl/page_optm/settings_media.tpl.php:24
msgid "Media Settings"
msgstr "多媒体设定"

#: tpl/cache/settings-esi.tpl.php:117 tpl/cache/settings-purge.tpl.php:111
#: tpl/cdn/other.tpl.php:169
msgid "Wildcard %1$s supported (match zero or more characters). For example, to match %2$s and %3$s, use %4$s."
msgstr "支持泛匹配%1$s (匹配空或多字符)。比如，要匹配%2$s和%3$s，使用%4$s。"

#: src/admin-display.cls.php:1225
msgid "To match the beginning, add %s to the beginning of the item."
msgstr "要匹配开头，在条目开头加上%s。"

#: tpl/banner/score.php:117
msgid "Maybe later"
msgstr "来日方长"

#: tpl/banner/score.php:116
msgid "I've already left a review"
msgstr "我已经留过评论了"

#: tpl/banner/slack.php:20
msgid "Welcome to LiteSpeed"
msgstr "欢迎来到LiteSpeed"

#: src/lang.cls.php:174 tpl/presets/standard.tpl.php:51
msgid "Remove WordPress Emoji"
msgstr "移除WordPress表情包"

#: src/gui.cls.php:547
msgid "More settings"
msgstr "更多设定"

#: src/gui.cls.php:528
msgid "Private cache"
msgstr "私有缓存"

#: src/gui.cls.php:517
msgid "Non cacheable"
msgstr "不可缓存"

#: src/gui.cls.php:494
msgid "Mark this page as "
msgstr "标注此页为"

#: src/gui.cls.php:470 src/gui.cls.php:485
msgid "Purge this page"
msgstr "清除此页"

#: src/lang.cls.php:156
msgid "Load JS Deferred"
msgstr "延迟加载JS"

#: tpl/page_optm/settings_tuning_css.tpl.php:167
msgid "Specify critical CSS rules for above-the-fold content when enabling %s."
msgstr "注明当激活%s时需要在页面显示时用到的关键CSS内容。"

#: src/lang.cls.php:168
msgid "Critical CSS Rules"
msgstr "关键CSS"

#: src/lang.cls.php:152 tpl/page_optm/settings_tuning_css.tpl.php:167
msgid "Load CSS Asynchronously"
msgstr "异步加载CSS"

#: tpl/page_optm/settings_html.tpl.php:161
msgid "Prevent Google Fonts from loading on all pages."
msgstr "禁止在所有页面上加载Google字体。"

#: src/lang.cls.php:167
msgid "Remove Google Fonts"
msgstr "移除Google字体"

#: tpl/page_optm/settings_css.tpl.php:214
#: tpl/page_optm/settings_html.tpl.php:175 tpl/page_optm/settings_js.tpl.php:81
msgid "This can improve your speed score in services like Pingdom, GTmetrix and PageSpeed."
msgstr "这会在诸如Pingdom、GTmetrix和PageSpeed等服务上改良您的速度评分。"

#: tpl/page_optm/settings_html.tpl.php:123
msgid "Remove query strings from internal static resources."
msgstr "从内部静态资源中删除查询字符串。"

#: src/lang.cls.php:165
msgid "Remove Query Strings"
msgstr "移除 Query Strings"

#: tpl/cache/settings_inc.exclude_useragent.tpl.php:28
msgid "user agents"
msgstr "用户代理"

#: tpl/cache/settings_inc.exclude_cookies.tpl.php:28
msgid "cookies"
msgstr "cookie"

#: tpl/cache/settings_inc.browser.tpl.php:41
msgid "Browser caching stores static files locally in the user's browser. Turn on this setting to reduce repeated requests for static files."
msgstr "浏览器缓存将静态文件本地化存储在用户浏览器。开启这个设定以降低对静态文件的重复请求。"

#: src/lang.cls.php:91 tpl/dash/dashboard.tpl.php:61
#: tpl/dash/dashboard.tpl.php:604 tpl/presets/standard.tpl.php:21
msgid "Browser Cache"
msgstr "浏览器缓存"

#: tpl/cache/settings-excludes.tpl.php:100
msgid "tags"
msgstr "标签"

#: src/lang.cls.php:136
msgid "Do Not Cache Tags"
msgstr "不缓存的标签"

#: tpl/cache/settings-excludes.tpl.php:110
msgid "To exclude %1$s, insert %2$s."
msgstr "要排除%1$s，请插入%2$s。"

#: tpl/cache/settings-excludes.tpl.php:67
msgid "categories"
msgstr "类别"

#. translators: %s: "cookies"
#. translators: %s: "user agents"
#: tpl/cache/settings-excludes.tpl.php:67
#: tpl/cache/settings-excludes.tpl.php:100
#: tpl/cache/settings_inc.exclude_cookies.tpl.php:27
#: tpl/cache/settings_inc.exclude_useragent.tpl.php:27
msgid "To prevent %s from being cached, enter them here."
msgstr "为了防止%s被缓存，请在此处输入它们。"

#: src/lang.cls.php:135
msgid "Do Not Cache Categories"
msgstr "不缓存的类别"

#: tpl/cache/settings-excludes.tpl.php:45
msgid "Query strings containing these parameters will not be cached."
msgstr "包含这些参数的查询字符串将不会被缓存。"

#: src/lang.cls.php:134
msgid "Do Not Cache Query Strings"
msgstr "不缓存的Query String"

#: tpl/cache/settings-excludes.tpl.php:30
msgid "Paths containing these strings will not be cached."
msgstr "包含这些字串的路径将不被缓存。"

#: src/lang.cls.php:133
msgid "Do Not Cache URIs"
msgstr "不缓存的URI"

#: src/admin-display.cls.php:1227 src/doc.cls.php:109
msgid "One per line."
msgstr "每行一个。"

#: tpl/cache/settings-cache.tpl.php:119
msgid "URI Paths containing these strings will NOT be cached as public."
msgstr "包含这些字串URI路径将不被存储为公开。"

#: src/lang.cls.php:110
msgid "Private Cached URIs"
msgstr "私有缓存URI"

#: tpl/cdn/other.tpl.php:210
msgid "Paths containing these strings will not be served from the CDN."
msgstr "包含这些字串的路径将不通过CDN服务。"

#: src/lang.cls.php:244
msgid "Exclude Path"
msgstr "排除路径"

#: src/lang.cls.php:240 tpl/cdn/other.tpl.php:113
msgid "Include File Types"
msgstr "包括文件类型"

#: tpl/cdn/other.tpl.php:97
msgid "Serve all JavaScript files through the CDN. This will affect all enqueued WP JavaScript files."
msgstr "将所有JavaScript文件通过CDN服务。这会影响到所有WP内嵌的JavaScript文件。"

#: src/lang.cls.php:239
msgid "Include JS"
msgstr "包含JS"

#: tpl/cdn/other.tpl.php:94
msgid "Serve all CSS files through the CDN. This will affect all enqueued WP CSS files."
msgstr "将所有CSS文件通过CDN服务。这会影响到所有WP内嵌的CSS文件。"

#: src/lang.cls.php:238
msgid "Include CSS"
msgstr "包含CSS"

#: src/lang.cls.php:237
msgid "Include Images"
msgstr "包含图片"

#: src/admin-display.cls.php:230
msgid "CDN URL to be used. For example, %s"
msgstr "要使用的CDN URL。比如%s"

#: src/lang.cls.php:236
msgid "CDN URL"
msgstr "CDN URL"

#: tpl/cdn/other.tpl.php:161
msgid "Site URL to be served through the CDN. Beginning with %1$s. For example, %2$s."
msgstr "要通过CDN服务的站内URL。以%1$s开始。例如%2$s。"

#: src/lang.cls.php:242
msgid "Original URLs"
msgstr "原始URl"

#: tpl/cdn/other.tpl.php:28
msgid "CDN Settings"
msgstr "CDN设定"

#: src/admin-display.cls.php:128
msgid "CDN"
msgstr "CDN"

#: src/admin-display.cls.php:235 src/admin-display.cls.php:940
#: src/admin-display.cls.php:966 src/admin-display.cls.php:1015
#: tpl/cache/settings-cache.tpl.php:28
#: tpl/cache/settings_inc.object.tpl.php:280 tpl/cdn/other.tpl.php:53
#: tpl/dash/dashboard.tpl.php:69 tpl/dash/dashboard.tpl.php:461
#: tpl/dash/dashboard.tpl.php:583 tpl/dash/dashboard.tpl.php:612
#: tpl/img_optm/settings.media_webp.tpl.php:22
#: tpl/page_optm/settings_css.tpl.php:91 tpl/page_optm/settings_js.tpl.php:77
#: tpl/page_optm/settings_media.tpl.php:178
#: tpl/toolbox/settings-debug.tpl.php:56
msgid "OFF"
msgstr "关闭"

#: src/admin-display.cls.php:234 src/admin-display.cls.php:939
#: src/admin-display.cls.php:966 src/admin-display.cls.php:1015
#: src/doc.cls.php:40 tpl/cache/settings-cache.tpl.php:28
#: tpl/cache/settings_inc.cache_mobile.tpl.php:91 tpl/cdn/other.tpl.php:45
#: tpl/crawler/settings.tpl.php:138 tpl/dash/dashboard.tpl.php:67
#: tpl/dash/dashboard.tpl.php:459 tpl/dash/dashboard.tpl.php:581
#: tpl/dash/dashboard.tpl.php:610 tpl/page_optm/settings_css.tpl.php:218
#: tpl/page_optm/settings_media.tpl.php:174
#: tpl/toolbox/settings-debug.tpl.php:56
msgid "ON"
msgstr "开启"

#: src/purge.cls.php:378
msgid "Notified LiteSpeed Web Server to purge CSS/JS entries."
msgstr "通知LiteSpeed服务器清除CSS/JS缓存。"

#: tpl/page_optm/settings_html.tpl.php:31
msgid "Minify HTML content."
msgstr "最小化HTML代码。"

#: src/lang.cls.php:149
msgid "HTML Minify"
msgstr "HTML最小化"

#: src/lang.cls.php:164
msgid "JS Excludes"
msgstr "JS排除"

#: src/data.upgrade.func.php:235 src/lang.cls.php:147
msgid "JS Combine"
msgstr "JS合并"

#: src/lang.cls.php:146
msgid "JS Minify"
msgstr "JS最小化"

#: src/lang.cls.php:162
msgid "CSS Excludes"
msgstr "CSS排除"

#: src/lang.cls.php:139
msgid "CSS Combine"
msgstr "CSS合并"

#: src/lang.cls.php:138
msgid "CSS Minify"
msgstr "CSS最小化"

#: tpl/page_optm/entry.tpl.php:43
msgid "Please test thoroughly when enabling any option in this list. After changing Minify/Combine settings, please do a Purge All action."
msgstr "请详细测试本处所有功能，在您正式使用它们之前。修改了最小化/合并设定后，请务必执行清除全部的操作。"

#: tpl/toolbox/purge.tpl.php:48
msgid "This will purge all minified/combined CSS/JS entries only"
msgstr "这将清除所有最小化或合并的CSS/JS缓存"

#: tpl/toolbox/purge.tpl.php:32
msgid "Purge %s Error"
msgstr "清除 %s 错误"

#: tpl/db_optm/manage.tpl.php:90
msgid "Database Optimizer"
msgstr "数据库优化"

#: tpl/db_optm/manage.tpl.php:58
msgid "Optimize all tables in your database"
msgstr "优化您数据库中的所有数据表"

#: tpl/db_optm/manage.tpl.php:57
msgid "Optimize Tables"
msgstr "优化数据表"

#: tpl/db_optm/manage.tpl.php:54
msgid "Clean all transient options"
msgstr "清理全部即时Transient内容"

#: tpl/db_optm/manage.tpl.php:53
msgid "All Transients"
msgstr "全部即时Transients内容"

#: tpl/db_optm/manage.tpl.php:50
msgid "Clean expired transient options"
msgstr "清理过期transient选项"

#: tpl/db_optm/manage.tpl.php:49
msgid "Expired Transients"
msgstr "过期Transients"

#: tpl/db_optm/manage.tpl.php:46
msgid "Clean all trackbacks and pingbacks"
msgstr "清理全部trackback和pingback"

#: tpl/db_optm/manage.tpl.php:45
msgid "Trackbacks/Pingbacks"
msgstr "Trackbacks/Pingbacks"

#: tpl/db_optm/manage.tpl.php:42
msgid "Clean all trashed comments"
msgstr "清理所有回收站的评论"

#: tpl/db_optm/manage.tpl.php:41
msgid "Trashed Comments"
msgstr "回收站评论"

#: tpl/db_optm/manage.tpl.php:38
msgid "Clean all spam comments"
msgstr "清理所有垃圾评论"

#: tpl/db_optm/manage.tpl.php:37
msgid "Spam Comments"
msgstr "垃圾评论"

#: tpl/db_optm/manage.tpl.php:34
msgid "Clean all trashed posts and pages"
msgstr "清理所有回收站的文章和页面"

#: tpl/db_optm/manage.tpl.php:33
msgid "Trashed Posts"
msgstr "回收站的文章"

#: tpl/db_optm/manage.tpl.php:30
msgid "Clean all auto saved drafts"
msgstr "清理所有自动保存的草稿"

#: tpl/db_optm/manage.tpl.php:29
msgid "Auto Drafts"
msgstr "自动保存草稿"

#: tpl/db_optm/manage.tpl.php:22
msgid "Clean all post revisions"
msgstr "清理全部文章修订记录"

#: tpl/db_optm/manage.tpl.php:21
msgid "Post Revisions"
msgstr "文章修订记录"

#: tpl/db_optm/manage.tpl.php:17
msgid "Clean All"
msgstr "清理全部"

#: src/db-optm.cls.php:242
msgid "Optimized all tables."
msgstr "已优化全部数据表。"

#: src/db-optm.cls.php:232
msgid "Clean all transients successfully."
msgstr "成功清理全部Transient。"

#: src/db-optm.cls.php:228
msgid "Clean expired transients successfully."
msgstr "成功清理过期Transient。"

#: src/db-optm.cls.php:224
msgid "Clean trackbacks and pingbacks successfully."
msgstr "成功清理trackback和pingback。"

#: src/db-optm.cls.php:220
msgid "Clean trashed comments successfully."
msgstr "成功清理回收站评论。"

#: src/db-optm.cls.php:216
msgid "Clean spam comments successfully."
msgstr "成功清理垃圾评论。"

#: src/db-optm.cls.php:212
msgid "Clean trashed posts and pages successfully."
msgstr "成功清理回收站文章和页面。"

#: src/db-optm.cls.php:208
msgid "Clean auto drafts successfully."
msgstr "成功清理自动保存的草稿。"

#: src/db-optm.cls.php:200
msgid "Clean post revisions successfully."
msgstr "成功清理文章修订记录。"

#: src/db-optm.cls.php:143
msgid "Clean all successfully."
msgstr "全部清理成功。"

#: src/lang.cls.php:93
msgid "Default Private Cache TTL"
msgstr "默认私有缓存TTL时间"

#: tpl/cache/settings-esi.tpl.php:145
msgid "If your site contains public content that certain user roles can see but other roles cannot, you can specify a Vary Group for those user roles. For example, specifying an administrator vary group allows there to be a separate publicly-cached page tailored to administrators (with “edit” links, etc), while all other user roles see the default public page."
msgstr "如果您的网站包含公开的内容，其只能被特定的用户组看到但其他不能看到，您可以设定一个Vary群组。比如，设定管理员vary群组为不同值，特定页面（如包含像编辑按钮等只有管理员能看到的按钮），将会被单独存储为公开页面，而其他用户将看到默认的公开页面。"

#: src/lang.cls.php:219 tpl/page_optm/settings_css.tpl.php:138
#: tpl/page_optm/settings_css.tpl.php:275 tpl/page_optm/settings_vpi.tpl.php:88
msgid "Vary Group"
msgstr "Vary 群组"

#: tpl/cache/settings-esi.tpl.php:85
msgid "Cache the built-in Comment Form ESI block."
msgstr "缓存内置的评论表单ESI块。"

#: src/lang.cls.php:217
msgid "Cache Comment Form"
msgstr "缓存评论表单"

#: src/lang.cls.php:216
msgid "Cache Admin Bar"
msgstr "缓存管理员条"

#: tpl/cache/settings-esi.tpl.php:59
msgid "Turn ON to cache public pages for logged in users, and serve the Admin Bar and Comment Form via ESI blocks. These two blocks will be uncached unless enabled below."
msgstr "启用则将缓存登录用户为公开缓存，而管理员条和评论表单将通过ESI块单独缓存。这两个块仅在下面的设定启用时方可缓存。"

#: tpl/cache/settings-esi.tpl.php:21
msgid "ESI allows you to designate parts of your dynamic page as separate fragments that are then assembled together to make the whole page. In other words, ESI lets you “punch holes” in a page, and then fill those holes with content that may be cached privately, cached publicly with its own TTL, or not cached at all."
msgstr "ESI允许您指定动态页面为不同区块，然后将它们合并为一个完整页面。换句话说，ESI允许您在一个页面上打洞，然后将这些洞洞用独立的缓存内容(有自己的TTL存储时间的私有或公有缓存)填充，或者用不缓存的内容填充亦可。"

#: tpl/cache/settings-esi.tpl.php:20
msgid "With ESI (Edge Side Includes), pages may be served from cache for logged-in users."
msgstr "有ESI(Edge Side Includes)相伴，登录用户也可以看到缓存的页面。"

#: tpl/esi_widget_edit.php:53
msgid "Private"
msgstr "私有"

#: tpl/esi_widget_edit.php:52
msgid "Public"
msgstr "公开"

#: tpl/cache/network_settings-purge.tpl.php:17
#: tpl/cache/settings-purge.tpl.php:15
msgid "Purge Settings"
msgstr "清除设定"

#: src/lang.cls.php:108 tpl/cache/settings_inc.cache_mobile.tpl.php:90
msgid "Cache Mobile"
msgstr "缓存手机访客"

#: tpl/toolbox/settings-debug.tpl.php:95
msgid "Advanced level will log more details."
msgstr "高级模式会记录更多细节。"

#: tpl/presets/standard.tpl.php:29 tpl/toolbox/settings-debug.tpl.php:93
msgid "Basic"
msgstr "基本"

#: tpl/crawler/settings.tpl.php:73
msgid "The maximum average server load allowed while crawling. The number of crawler threads in use will be actively reduced until average server load falls under this limit. If this cannot be achieved with a single thread, the current crawler run will be terminated."
msgstr "爬行时允许的最大化平均服务器荷载。使用中的爬虫线程的数量将会主动减少直到平均服务器荷载降到这个限制为止。如果单线程仍不能降到该限制，当前爬虫将自觉退出。"

#: src/lang.cls.php:107
msgid "Cache Login Page"
msgstr "缓存登录页面"

#: tpl/cache/settings-cache.tpl.php:89
msgid "Cache requests made by WordPress REST API calls."
msgstr "缓存由WordPress REST API发出的请求。"

#: src/lang.cls.php:106
msgid "Cache REST API"
msgstr "缓存REST API"

#: tpl/cache/settings-cache.tpl.php:76
msgid "Privately cache commenters that have pending comments. Disabling this option will serve non-cacheable pages to commenters. (LSWS %s required)"
msgstr "用私有缓存存储有待审核评论的评论者。禁用此选项会提供没有缓存的页面给评论者。(需要LSWS %s)"

#: src/lang.cls.php:105
msgid "Cache Commenters"
msgstr "缓存评论者"

#: tpl/cache/settings-cache.tpl.php:63
msgid "Privately cache frontend pages for logged-in users. (LSWS %s required)"
msgstr "用私有缓存为登录用户存储前台页面。(需要LSWS %s)"

#: src/lang.cls.php:104
msgid "Cache Logged-in Users"
msgstr "缓存登录用户"

#: tpl/cache/network_settings-cache.tpl.php:17
#: tpl/cache/settings-cache.tpl.php:15
msgid "Cache Control Settings"
msgstr "缓存控制设定"

#: tpl/cache/entry.tpl.php:20
msgid "ESI"
msgstr "ESI"

#: tpl/cache/entry.tpl.php:19 tpl/cache/entry_network.tpl.php:18
msgid "Excludes"
msgstr "例外规则"

#: tpl/cache/entry.tpl.php:18 tpl/cache/entry_network.tpl.php:17
#: tpl/toolbox/entry.tpl.php:16 tpl/toolbox/purge.tpl.php:141
msgid "Purge"
msgstr "清除规则"

#: src/admin-display.cls.php:126 tpl/cache/entry.tpl.php:16
#: tpl/cache/entry_network.tpl.php:16
msgid "Cache"
msgstr "缓存规则"

#: thirdparty/woocommerce.tab.tpl.php:3
msgid "WooCommerce"
msgstr "WooCommerce"

#: tpl/cache/settings-purge.tpl.php:132
msgid "Current server time is %s."
msgstr "当前服务器时间为%s。"

#: tpl/cache/settings-purge.tpl.php:131
msgid "Specify the time to purge the \"%s\" list."
msgstr "设定清除\"%s\"列表的时间。"

#: tpl/cache/settings-purge.tpl.php:107
msgid "Both %1$s and %2$s are acceptable."
msgstr "%1$s和%2$s都可以使用。"

#: src/lang.cls.php:130 tpl/cache/settings-purge.tpl.php:106
msgid "Scheduled Purge Time"
msgstr "计划清除时间"

#: tpl/cache/settings-purge.tpl.php:106
msgid "The URLs here (one per line) will be purged automatically at the time set in the option \"%s\"."
msgstr "这里填写的URL(一行一个)将会在设定\"%s\"的时间自动被清除。"

#: src/lang.cls.php:129 tpl/cache/settings-purge.tpl.php:131
msgid "Scheduled Purge URLs"
msgstr "计划清除URL"

#: tpl/toolbox/settings-debug.tpl.php:123
msgid "Shorten query strings in the debug log to improve readability."
msgstr "缩短Query Strings在debug日志中的长度以增强可读性。"

#: tpl/toolbox/entry.tpl.php:28
msgid "Heartbeat"
msgstr "心跳包"

#: tpl/toolbox/settings-debug.tpl.php:106
msgid "MB"
msgstr "MB"

#: src/lang.cls.php:259
msgid "Log File Size Limit"
msgstr "日志文件尺寸限制"

#: src/htaccess.cls.php:787
msgid "<p>Please add/replace the following codes into the beginning of %1$s:</p> %2$s"
msgstr "<p>请添加/替换下列代码到%1$s的开头位置:</p> %2$s"

#: src/error.cls.php:127 src/error.cls.php:151
msgid "%s file not writable."
msgstr "%s文件不可写。"

#: src/error.cls.php:147
msgid "%s file not readable."
msgstr "%s文件不可读。"

#: src/lang.cls.php:260
msgid "Collapse Query Strings"
msgstr "缩短Query Strings"

#: tpl/cache/settings-esi.tpl.php:15
msgid "ESI Settings"
msgstr "ESI设定"

#: tpl/esi_widget_edit.php:82
msgid "A TTL of 0 indicates do not cache."
msgstr "TTL为0标明不缓存。"

#: tpl/esi_widget_edit.php:81
msgid "Recommended value: 28800 seconds (8 hours)."
msgstr "推荐值：28800秒（8小时）。"

#: src/lang.cls.php:215 tpl/esi_widget_edit.php:43
msgid "Enable ESI"
msgstr "启用ESI"

#: src/lang.cls.php:253
msgid "Custom Sitemap"
msgstr "自定义站点地图"

#: tpl/toolbox/purge.tpl.php:214
msgid "Purge pages by relative or full URL."
msgstr "清除页面基于相对或完整URL。"

#: tpl/crawler/summary.tpl.php:61
msgid "The crawler feature is not enabled on the LiteSpeed server. Please consult your server admin or hosting provider."
msgstr "爬虫功能尚未在LiteSpeed服务器上启用。请咨询您的服务器管理员。"

#: tpl/cache/settings-esi.tpl.php:45 tpl/cdn/cf.tpl.php:100
#: tpl/crawler/summary.tpl.php:60 tpl/inc/check_cache_disabled.php:38
#: tpl/inc/check_if_network_disable_all.php:28
#: tpl/page_optm/settings_css.tpl.php:76 tpl/page_optm/settings_css.tpl.php:209
#: tpl/page_optm/settings_localization.tpl.php:21
msgid "WARNING"
msgstr "警告"

#: tpl/crawler/summary.tpl.php:82
msgid "The next complete sitemap crawl will start at"
msgstr "下次完整的站点地图爬行将始于"

#: src/file.cls.php:178
msgid "Failed to write to %s."
msgstr "写入%s失败。"

#: src/file.cls.php:161
msgid "Folder is not writable: %s."
msgstr "文件夹不可写: %s."

#: src/file.cls.php:153
msgid "Can not create folder: %1$s. Error: %2$s"
msgstr "无法创建文件夹:%1$s. 错误: %2$s"

#: src/file.cls.php:141
msgid "Folder does not exist: %s"
msgstr "文件夹不存在: %s"

#: src/core.cls.php:332
msgid "Notified LiteSpeed Web Server to purge the list."
msgstr "已知会LiteSpeed服务器清除列表。"

#: tpl/toolbox/settings-debug.tpl.php:73
msgid "Allows listed IPs (one per line) to perform certain actions from their browsers."
msgstr "允许列表中IP(每行一个)从它们的浏览器中执行特定操作。"

#: src/lang.cls.php:250
msgid "Server Load Limit"
msgstr "服务器荷载上限"

#: tpl/crawler/settings.tpl.php:45
msgid "Specify how long in seconds before the crawler should initiate crawling the entire sitemap again."
msgstr "设定爬虫再从网站地图起始开始爬的间隔时间。"

#: src/lang.cls.php:249
msgid "Crawl Interval"
msgstr "爬行间隔"

#. translators: %s: Example subdomain
#: tpl/cache/settings_inc.login_cookie.tpl.php:53
msgid "Then another WordPress is installed (NOT MULTISITE) at %s"
msgstr "另一份WordPress(非多站点)安装在%s"

#: tpl/cache/entry_network.tpl.php:27
msgid "LiteSpeed Cache Network Cache Settings"
msgstr "LiteSpeed缓存网络设定"

#: tpl/toolbox/purge.tpl.php:179
msgid "Select below for \"Purge by\" options."
msgstr "选择清除选项。"

#: tpl/cdn/entry.tpl.php:22
msgid "LiteSpeed Cache CDN"
msgstr "LiteSpeed缓存CDN"

#: tpl/crawler/summary.tpl.php:293
msgid "No crawler meta file generated yet"
msgstr "尚无爬虫爬过的痕迹"

#: tpl/crawler/summary.tpl.php:278
msgid "Show crawler status"
msgstr "显示爬虫状态"

#: tpl/crawler/summary.tpl.php:272
msgid "Watch Crawler Status"
msgstr "查看爬虫状态"

#: tpl/crawler/summary.tpl.php:251
msgid "Run frequency is set by the Interval Between Runs setting."
msgstr "运行频率由运行中的间隔设定。"

#: tpl/crawler/summary.tpl.php:142
msgid "Manually run"
msgstr "手动运行"

#: tpl/crawler/summary.tpl.php:141
msgid "Reset position"
msgstr "重置位置"

#: tpl/crawler/summary.tpl.php:152
msgid "Run Frequency"
msgstr "运行频率"

#: tpl/crawler/summary.tpl.php:151
msgid "Cron Name"
msgstr "Cron名字"

#: tpl/crawler/summary.tpl.php:54
msgid "Crawler Cron"
msgstr "爬虫Cron"

#: cli/crawler.cls.php:100 tpl/crawler/summary.tpl.php:47
msgid "%d minute"
msgstr "%d分钟"

#: cli/crawler.cls.php:98 tpl/crawler/summary.tpl.php:47
msgid "%d minutes"
msgstr "%d分钟"

#: cli/crawler.cls.php:91 tpl/crawler/summary.tpl.php:39
msgid "%d hour"
msgstr "%d小时"

#: cli/crawler.cls.php:89 tpl/crawler/summary.tpl.php:39
msgid "%d hours"
msgstr "%d小时"

#: tpl/crawler/map.tpl.php:40
msgid "Generated at %s"
msgstr "生成于%s"

#: tpl/crawler/entry.tpl.php:23
msgid "LiteSpeed Cache Crawler"
msgstr "LiteSpeed缓存爬虫"

#: src/admin-display.cls.php:136 src/lang.cls.php:248
msgid "Crawler"
msgstr "爬虫"

#. Plugin URI of the plugin
#: litespeed-cache.php
msgid "https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration"
msgstr "https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration"

#: src/purge.cls.php:697
msgid "Notified LiteSpeed Web Server to purge all pages."
msgstr "已通知LiteSpeed网络服务器清除页面。"

#: tpl/cache/settings-purge.tpl.php:25
msgid "All pages with Recent Posts Widget"
msgstr "所有包含最新帖部件的页面"

#: tpl/cache/settings-purge.tpl.php:24
msgid "Pages"
msgstr "独立页面"

#: tpl/toolbox/purge.tpl.php:24
msgid "This will Purge Pages only"
msgstr "这将只清除独立页面"

#: tpl/toolbox/purge.tpl.php:23
msgid "Purge Pages"
msgstr "清除独立页面"

#: src/gui.cls.php:83 tpl/inc/modal.deactivation.php:77
msgid "Cancel"
msgstr "取消"

#: tpl/crawler/summary.tpl.php:154
msgid "Activate"
msgstr "启用"

#: tpl/cdn/cf.tpl.php:44
msgid "Email Address"
msgstr "Email 地址"

#: src/gui.cls.php:869
msgid "Install Now"
msgstr "现在安装"

#: cli/purge.cls.php:133
msgid "Purged the blog!"
msgstr "清除Blog！"

#: cli/purge.cls.php:86
msgid "Purged All!"
msgstr "清除全部！"

#: src/purge.cls.php:717
msgid "Notified LiteSpeed Web Server to purge error pages."
msgstr "已通知LiteSpeed网络服务器清除错误页面。"

#: tpl/inc/show_error_cookie.php:27
msgid "If using OpenLiteSpeed, the server must be restarted once for the changes to take effect."
msgstr "如果正在使用OpenLiteSpeed，服务器必须从新启动一次以使更改生效。"

#: tpl/inc/show_error_cookie.php:18
msgid "If the login cookie was recently changed in the settings, please log out and back in."
msgstr "如果设置中登录Cookie最近有改动，请登出后从新登录。"

#: tpl/inc/show_display_installed.php:29
msgid "However, there is no way of knowing all the possible customizations that were implemented."
msgstr "然而，不可能知道所有已安装的定制可能情况。"

#: tpl/inc/show_display_installed.php:28
msgid "The LiteSpeed Cache plugin is used to cache pages - a simple way to improve the performance of the site."
msgstr "LiteSpeed缓存插件被用作缓存页面——一个简单的改善网站性能的方式。"

#: tpl/cache/settings-cache.tpl.php:45
msgid "The network admin setting can be overridden here."
msgstr "网络管理员设置可以在这里被覆盖。"

#: tpl/cache/settings-ttl.tpl.php:29
msgid "Specify how long, in seconds, public pages are cached."
msgstr "指定公共页面缓存的时间（以秒为单位）。"

#: tpl/cache/settings-ttl.tpl.php:44
msgid "Specify how long, in seconds, private pages are cached."
msgstr "指定私有页面缓存的时间（以秒为单位）。"

#: tpl/toolbox/purge.tpl.php:208
msgid "Purge pages by post ID."
msgstr "根据贴子ID清除页面。"

#: tpl/toolbox/purge.tpl.php:41
msgid "Purge the LiteSpeed cache entries created by this plugin"
msgstr "清除此插件创建的LiteSpeed缓存条目"

#: tpl/toolbox/purge.tpl.php:33
msgid "Purge %s error pages"
msgstr "清除 %s 个错误页面"

#: tpl/toolbox/purge.tpl.php:18
msgid "This will Purge Front Page only"
msgstr "这将仅清除首页"

#: tpl/toolbox/purge.tpl.php:211
msgid "Purge pages by tag name - e.g. %2$s should be used for the URL %1$s."
msgstr "根据标签名清除页面。例如：%2$s应该被用于URL %1$s。"

#: tpl/toolbox/purge.tpl.php:205
msgid "Purge pages by category name - e.g. %2$s should be used for the URL %1$s."
msgstr "根据分类名清除页面。例如：%2$s应该被用于URL %1$s。"

#: tpl/toolbox/purge.tpl.php:132
msgid "If only the WordPress site should be purged, use Purge All."
msgstr "如果只是WordPress网站需要清除，请使用清除全部。"

#: src/core.cls.php:327
msgid "Notified LiteSpeed Web Server to purge everything."
msgstr "已知会LiteSpeed网页服务器清除所有缓存。"

#: tpl/general/network_settings.tpl.php:31
msgid "Use Primary Site Configuration"
msgstr "使用主站配置"

#: tpl/general/network_settings.tpl.php:36
msgid "This will disable the settings page on all subsites."
msgstr "这会禁用所有子站点的设置页面。"

#: tpl/general/network_settings.tpl.php:35
msgid "Check this option to use the primary site's configuration for all subsites."
msgstr "选择本选项以使用主站的配置来应用到全部子站上。"

#: src/admin-display.cls.php:809 src/admin-display.cls.php:813
msgid "Save Changes"
msgstr "保存修改"

#: tpl/inc/check_if_network_disable_all.php:31
msgid "The following options are selected, but are not editable in this settings page."
msgstr "在这个设置页面中下面的选项被选择，但不可修改。"

#: tpl/inc/check_if_network_disable_all.php:30
msgid "The network admin selected use primary site configs for all subsites."
msgstr "网络管理员选择全部子站点使用首站点配置。"

#: tpl/toolbox/purge.tpl.php:127
msgid "Empty Entire Cache"
msgstr "清空整个缓存"

#: tpl/toolbox/purge.tpl.php:128
msgid "This action should only be used if things are cached incorrectly."
msgstr "仅当不能正常缓存时方可使用本操作。"

#: tpl/toolbox/purge.tpl.php:132
msgid "This may cause heavy load on the server."
msgstr "这样可能导致服务器高负荷。"

#: tpl/toolbox/purge.tpl.php:132
msgid "This will clear EVERYTHING inside the cache."
msgstr "这将清空缓存内的所有内容。"

#: src/gui.cls.php:691
msgid "LiteSpeed Cache Purge All"
msgstr "LiteSpeed缓存清除全部"

#: tpl/inc/show_display_installed.php:41
msgid "If you would rather not move at litespeed, you can deactivate this plugin."
msgstr "如果您宁愿不迁移到LiteSpeed，您可以禁用本插件。"

#: tpl/inc/show_display_installed.php:33
msgid "Create a post, make sure the front page is accurate."
msgstr "创建一个文章，确保前台页面的精确。"

#: tpl/inc/show_display_installed.php:32
msgid "Visit the site while logged out."
msgstr "登出状态下访问本站。"

#: tpl/inc/show_display_installed.php:31
msgid "Examples of test cases include:"
msgstr "测试情况示例包括："

#: tpl/inc/show_display_installed.php:30
msgid "For that reason, please test the site to make sure everything still functions properly."
msgstr "因为那个原因，请测试网站确保所有功能正常运行。"

#: tpl/inc/show_display_installed.php:27
msgid "This message indicates that the plugin was installed by the server admin."
msgstr "这则消息表明本插件由服务器管理员安装。"

#: tpl/inc/show_display_installed.php:26
msgid "LiteSpeed Cache plugin is installed!"
msgstr "LiteSpeed缓存插件已经安装！"

#: src/lang.cls.php:256 tpl/toolbox/log_viewer.tpl.php:18
msgid "Debug Log"
msgstr "Debug日志"

#: tpl/toolbox/settings-debug.tpl.php:56
msgid "Admin IP Only"
msgstr "仅管理员IP"

#: tpl/cache/settings-ttl.tpl.php:89
msgid "Specify how long, in seconds, REST calls are cached."
msgstr "指定REST调用被缓存的时间（以秒为单位）。"

#: tpl/toolbox/report.tpl.php:66
msgid "The environment report contains detailed information about the WordPress configuration."
msgstr "环境报告包含WordPress配置的所有详细信息。"

#: tpl/cache/settings_inc.login_cookie.tpl.php:36
msgid "The server will determine if the user is logged in based on the existence of this cookie."
msgstr "服务器将根据 cookie 的存在判断用户是否已登录。"

#: tpl/cache/settings-purge.tpl.php:53 tpl/cache/settings-purge.tpl.php:90
#: tpl/cache/settings-purge.tpl.php:114
#: tpl/page_optm/settings_tuning_css.tpl.php:72
#: tpl/page_optm/settings_tuning_css.tpl.php:147
msgid "Note"
msgstr "注释"

#: thirdparty/woocommerce.content.tpl.php:23
msgid "After verifying that the cache works in general, please test the cart."
msgstr "检查缓存正常工作后，请测试购物车。"

#: tpl/cache/settings_inc.purge_on_upgrade.tpl.php:25
msgid "When enabled, the cache will automatically purge when any plugin, theme or the WordPress core is upgraded."
msgstr "如果启用，当任何插件、主题或WordPress内核更新时，缓存将自动清除。"

#: src/lang.cls.php:127
msgid "Purge All On Upgrade"
msgstr "升级时清除全部"

#: thirdparty/woocommerce.content.tpl.php:33
msgid "Product Update Interval"
msgstr "产品更新间隔"

#: thirdparty/woocommerce.content.tpl.php:54
msgid "Determines how changes in product quantity and product stock status affect product pages and their associated category pages."
msgstr "决定修改产品数量和库存状态时怎样影响产品页和它们相关的分类页。"

#: thirdparty/woocommerce.content.tpl.php:41
msgid "Always purge both product and categories on changes to the quantity or stock status."
msgstr "当数量或库存状态改变时总是清除产品和分类页。"

#: thirdparty/woocommerce.content.tpl.php:40
msgid "Do not purge categories on changes to the quantity or stock status."
msgstr "当数量或库存状态改变时不要清除分类。"

#: thirdparty/woocommerce.content.tpl.php:40
msgid "Purge product only when the stock status changes."
msgstr "仅当库存状态改变时清除产品页。"

#: thirdparty/woocommerce.content.tpl.php:39
msgid "Purge product and categories only when the stock status changes."
msgstr "仅当库存状态改变时清除产品和分类页。"

#: thirdparty/woocommerce.content.tpl.php:38
msgid "Purge categories only when stock status changes."
msgstr "仅当库存状态改变时清除分类页。"

#: thirdparty/woocommerce.content.tpl.php:38
msgid "Purge product on changes to the quantity or stock status."
msgstr "当数量或库存状态改变时清除产品页。"

#: tpl/cache/settings_inc.cache_mobile.tpl.php:47
msgid "Htaccess did not match configuration option."
msgstr "Htaccess和配置内容不匹配。"

#: tpl/cache/settings-ttl.tpl.php:75 tpl/cache/settings-ttl.tpl.php:90
msgid "If this is set to a number less than 30, feeds will not be cached."
msgstr "如果设定为小于30的数，feeds将不会缓存。"

#: tpl/cache/settings-ttl.tpl.php:74
msgid "Specify how long, in seconds, feeds are cached."
msgstr "指定feeds缓存多久，单位秒。"

#: src/lang.cls.php:95
msgid "Default Feed TTL"
msgstr "默认Feed TTL"

#: src/error.cls.php:155
msgid "Failed to get %s file contents."
msgstr "获取%s文件内容失败。"

#: tpl/cache/settings-cache.tpl.php:102
msgid "Disabling this option may negatively affect performance."
msgstr "禁用该选项或会影响性能。"

#: tpl/cache/settings_inc.login_cookie.tpl.php:63
msgid "Invalid login cookie. Invalid characters found."
msgstr "无效的登录cookie。 找到无效的字符。"

#: tpl/cache/settings_inc.login_cookie.tpl.php:84
msgid "WARNING: The .htaccess login cookie and Database login cookie do not match."
msgstr "警告: .htaccess中的登录cookie信息和数据库的登录cookie内容不匹配。"

#: src/error.cls.php:139
msgid "Invalid login cookie. Please check the %s file."
msgstr "无效的登录cookie。 请检查%s文件。"

#: tpl/cache/settings_inc.login_cookie.tpl.php:57
msgid "The cache needs to distinguish who is logged into which WordPress site in order to cache correctly."
msgstr "缓存需要分辨谁登录到哪个WordPress网站以正确缓存。"

#. translators: %s: Example domain
#: tpl/cache/settings_inc.login_cookie.tpl.php:45
msgid "There is a WordPress installed for %s."
msgstr "有一份WordPress安装到%s。"

#: tpl/cache/settings_inc.login_cookie.tpl.php:41
msgid "Example use case:"
msgstr "示范使用案例："

#: tpl/cache/settings_inc.login_cookie.tpl.php:39
msgid "The cookie set here will be used for this WordPress installation."
msgstr "本处设置的cookie将用于这个WordPress安装。"

#: tpl/cache/settings_inc.login_cookie.tpl.php:38
msgid "If every web application uses the same cookie, the server may confuse whether a user is logged in or not."
msgstr "如果所有网页应用都使用同样的cookie，服务器将混淆一个用户是否已登录。"

#: tpl/cache/settings_inc.login_cookie.tpl.php:37
msgid "This setting is useful for those that have multiple web applications for the same domain."
msgstr "本设置对同一域名下有多个网页应用的情况很有用。"

#. translators: %s: Default login cookie name
#: tpl/cache/settings_inc.login_cookie.tpl.php:32
msgid "The default login cookie is %s."
msgstr "默认的登录cookie是%s。"

#: src/lang.cls.php:225
msgid "Login Cookie"
msgstr "登录Cookie"

#: tpl/toolbox/settings-debug.tpl.php:80
msgid "More information about the available commands can be found here."
msgstr "有关可用命令的更多信息，请参见此处。"

#: tpl/cache/settings-advanced.tpl.php:22
msgid "These settings are meant for ADVANCED USERS ONLY."
msgstr "这些设置仅供高级用户使用。"

#: tpl/toolbox/edit_htaccess.tpl.php:91
msgid "Current %s Contents"
msgstr "当前%s内容"

#: tpl/cache/entry.tpl.php:28 tpl/cache/entry_network.tpl.php:21
#: tpl/toolbox/settings-debug.tpl.php:93
msgid "Advanced"
msgstr "高级"

#: tpl/cache/network_settings-advanced.tpl.php:17
#: tpl/cache/settings-advanced.tpl.php:16
msgid "Advanced Settings"
msgstr "高级设置"

#: tpl/toolbox/purge.tpl.php:225
msgid "Purge List"
msgstr "清除列表"

#: tpl/toolbox/purge.tpl.php:176
msgid "Purge By..."
msgstr "清除..."

#: tpl/crawler/blacklist.tpl.php:40 tpl/crawler/map.tpl.php:76
#: tpl/toolbox/purge.tpl.php:200
msgid "URL"
msgstr "URL"

#: tpl/toolbox/purge.tpl.php:196
msgid "Tag"
msgstr "标签"

#: tpl/toolbox/purge.tpl.php:192
msgid "Post ID"
msgstr "文章ID"

#: tpl/toolbox/purge.tpl.php:188
msgid "Category"
msgstr "分类"

#: tpl/inc/show_error_cookie.php:16
msgid "NOTICE: Database login cookie did not match your login cookie."
msgstr "注意: 数据库登录cookie和您的登录cookie不匹配。"

#: src/purge.cls.php:807
msgid "Purge url %s"
msgstr "清除url %s"

#: src/purge.cls.php:773
msgid "Purge tag %s"
msgstr "清除标签%s"

#: src/purge.cls.php:744
msgid "Purge category %s"
msgstr "清除分类%s"

#: tpl/cache/settings-cache.tpl.php:42
msgid "When disabling the cache, all cached entries for this site will be purged."
msgstr "禁用缓存时，本博客的全部缓存都将被清除。"

#: tpl/cache/settings-cache.tpl.php:42 tpl/crawler/settings.tpl.php:113
#: tpl/crawler/settings.tpl.php:133 tpl/crawler/summary.tpl.php:208
#: tpl/page_optm/entry.tpl.php:42
msgid "NOTICE"
msgstr "注意"

#: src/doc.cls.php:138
msgid "This setting will edit the .htaccess file."
msgstr "这个选项将修改.htaccess文件。"

#: tpl/toolbox/edit_htaccess.tpl.php:41
msgid "LiteSpeed Cache View .htaccess"
msgstr "LiteSpeed 缓存查看 .htaccess"

#: src/error.cls.php:143
msgid "Failed to back up %s file, aborted changes."
msgstr "无法备份%s文件，中止更改。"

#: src/lang.cls.php:223
msgid "Do Not Cache Cookies"
msgstr "不缓存的Cookie"

#: src/lang.cls.php:224
msgid "Do Not Cache User Agents"
msgstr "不缓存的用户代理"

#: tpl/cache/network_settings-cache.tpl.php:30
msgid "This is to ensure compatibility prior to enabling the cache for all sites."
msgstr "这用于确保在全部站点上激活缓存前的兼容性。"

#: tpl/cache/network_settings-cache.tpl.php:24
msgid "Network Enable Cache"
msgstr "网络级激活缓存"

#: thirdparty/woocommerce.content.tpl.php:22
#: tpl/cache/settings-advanced.tpl.php:21
#: tpl/cache/settings_inc.browser.tpl.php:23 tpl/toolbox/heartbeat.tpl.php:24
#: tpl/toolbox/report.tpl.php:46
msgid "NOTICE:"
msgstr "注意："

#: tpl/cache/settings-purge.tpl.php:56
msgid "Other checkboxes will be ignored."
msgstr "其它勾选框将会被无视。"

#: tpl/cache/settings-purge.tpl.php:55
msgid "Select \"All\" if there are dynamic widgets linked to posts on pages other than the front or home pages."
msgstr "如果有动态小部件链接到除首页和主页外的文章或页面，请选择“全部”。"

#: src/lang.cls.php:109 tpl/cache/settings_inc.cache_mobile.tpl.php:92
msgid "List of Mobile User Agents"
msgstr "移动用户代理列表"

#: src/file.cls.php:167 src/file.cls.php:171
msgid "File %s is not writable."
msgstr "文件%s不可写。"

#: tpl/page_optm/entry.tpl.php:17 tpl/page_optm/settings_js.tpl.php:17
msgid "JS Settings"
msgstr "JS 设置"

#: src/gui.cls.php:710 tpl/db_optm/entry.tpl.php:13
msgid "Manage"
msgstr "管理"

#: src/lang.cls.php:94
msgid "Default Front Page TTL"
msgstr "默认首页TTL"

#: src/purge.cls.php:683
msgid "Notified LiteSpeed Web Server to purge the front page."
msgstr "已知会LiteSpeed网页服务器清除首页缓存。"

#: tpl/toolbox/purge.tpl.php:17
msgid "Purge Front Page"
msgstr "清除首页"

#: tpl/page_optm/settings_localization.tpl.php:137
#: tpl/toolbox/beta_test.tpl.php:40
msgid "Example"
msgstr "示例"

#: tpl/cache/settings-excludes.tpl.php:99
msgid "All tags are cached by default."
msgstr "所有的标签默认都会被缓存。"

#: tpl/cache/settings-excludes.tpl.php:66
msgid "All categories are cached by default."
msgstr "所有的分类默认都会被缓存。"

#: src/admin-display.cls.php:1226
msgid "To do an exact match, add %s to the end of the URL."
msgstr "若要做精确匹配，请添加“%s”到URL的结尾。"

#: src/admin-display.cls.php:1222
msgid "The URLs will be compared to the REQUEST_URI server variable."
msgstr "URL将会和服务器变量REQUEST_URI对比。"

#: tpl/cache/settings-purge.tpl.php:57
msgid "Select only the archive types that are currently used, the others can be left unchecked."
msgstr "只选择正使用中的存档类型，其它类型可留空。"

#: tpl/toolbox/report.tpl.php:122
msgid "Notes"
msgstr "提示"

#: tpl/cache/settings-cache.tpl.php:28
msgid "Use Network Admin Setting"
msgstr "使用网络管理员设定"

#: tpl/esi_widget_edit.php:54
msgid "Disable"
msgstr "取消"

#: tpl/cache/network_settings-cache.tpl.php:28
msgid "Enabling LiteSpeed Cache for WordPress here enables the cache for the network."
msgstr "在此启用Wordpress专用的LiteSpeed缓存会给整个网络激活缓存。"

#: tpl/cache/settings_inc.object.tpl.php:16
msgid "Disabled"
msgstr "不启用"

#: tpl/cache/settings_inc.object.tpl.php:15
msgid "Enabled"
msgstr "已启用"

#: src/lang.cls.php:137
msgid "Do Not Cache Roles"
msgstr "勿存我的规则设定"

#. Author URI of the plugin
#: litespeed-cache.php
msgid "https://www.litespeedtech.com"
msgstr "https://www.litespeedtech.com"

#. Author of the plugin
#: litespeed-cache.php
msgid "LiteSpeed Technologies"
msgstr "LiteSpeed Technologies"

#. Plugin Name of the plugin
#: litespeed-cache.php tpl/banner/new_version.php:57
#: tpl/banner/new_version_dev.tpl.php:21 tpl/cache/more_settings_tip.tpl.php:28
#: tpl/esi_widget_edit.php:41 tpl/inc/admin_footer.php:17
msgid "LiteSpeed Cache"
msgstr "LiteSpeed缓存"

#: src/lang.cls.php:258
msgid "Debug Level"
msgstr "Debug等级"

#: tpl/general/settings.tpl.php:72 tpl/general/settings.tpl.php:79
#: tpl/general/settings.tpl.php:86 tpl/general/settings.tpl.php:103
#: tpl/page_optm/settings_media.tpl.php:251
#: tpl/page_optm/settings_vpi.tpl.php:44
msgid "Notice"
msgstr "消息"

#: tpl/cache/settings-purge.tpl.php:31
msgid "Term archive (include category, tag, and tax)"
msgstr "类目存档(包含分类，标签和类别)"

#: tpl/cache/settings-purge.tpl.php:30
msgid "Daily archive"
msgstr "按日存档"

#: tpl/cache/settings-purge.tpl.php:29
msgid "Monthly archive"
msgstr "按月存档"

#: tpl/cache/settings-purge.tpl.php:28
msgid "Yearly archive"
msgstr "按年存档"

#: tpl/cache/settings-purge.tpl.php:27
msgid "Post type archive"
msgstr "主题类型存档"

#: tpl/cache/settings-purge.tpl.php:26
msgid "Author archive"
msgstr "作者存档"

#: tpl/cache/settings-purge.tpl.php:23
msgid "Home page"
msgstr "首页"

#: tpl/cache/settings-purge.tpl.php:22
msgid "Front page"
msgstr "静态首页"

#: tpl/cache/settings-purge.tpl.php:21
msgid "All pages"
msgstr "所有页面"

#: tpl/cache/settings-purge.tpl.php:73
msgid "Select which pages will be automatically purged when posts are published/updated."
msgstr "选择发表或更新文章时哪些页面将被清除。"

#: tpl/cache/settings-purge.tpl.php:50
msgid "Auto Purge Rules For Publish/Update"
msgstr "发表/更新时的自动清除规则"

#: src/lang.cls.php:92
msgid "Default Public Cache TTL"
msgstr "默认公开缓存的TTL"

#: src/admin-display.cls.php:1043 tpl/cache/settings_inc.object.tpl.php:162
#: tpl/crawler/settings.tpl.php:43 tpl/esi_widget_edit.php:78
msgid "seconds"
msgstr "秒"

#: src/lang.cls.php:257
msgid "Admin IPs"
msgstr "管理员 IP"

#: src/admin-display.cls.php:124
msgid "General"
msgstr "常规"

#: tpl/cache/entry.tpl.php:50
msgid "LiteSpeed Cache Settings"
msgstr "LiteSpeed缓存设置"

#: src/purge.cls.php:234
msgid "Notified LiteSpeed Web Server to purge all LSCache entries."
msgstr "已知会LiteSpeed网页服务器清除全部缓存。"

#: src/gui.cls.php:554 src/gui.cls.php:562 src/gui.cls.php:570
#: src/gui.cls.php:579 src/gui.cls.php:589 src/gui.cls.php:599
#: src/gui.cls.php:609 src/gui.cls.php:619 src/gui.cls.php:628
#: src/gui.cls.php:638 src/gui.cls.php:648 src/gui.cls.php:736
#: src/gui.cls.php:744 src/gui.cls.php:752 src/gui.cls.php:761
#: src/gui.cls.php:771 src/gui.cls.php:781 src/gui.cls.php:791
#: src/gui.cls.php:801 src/gui.cls.php:810 src/gui.cls.php:820
#: src/gui.cls.php:830 tpl/page_optm/settings_media.tpl.php:139
#: tpl/toolbox/purge.tpl.php:40 tpl/toolbox/purge.tpl.php:47
#: tpl/toolbox/purge.tpl.php:55 tpl/toolbox/purge.tpl.php:64
#: tpl/toolbox/purge.tpl.php:73 tpl/toolbox/purge.tpl.php:82
#: tpl/toolbox/purge.tpl.php:91 tpl/toolbox/purge.tpl.php:100
#: tpl/toolbox/purge.tpl.php:109 tpl/toolbox/purge.tpl.php:117
msgid "Purge All"
msgstr "清除全部"

#: src/admin-display.cls.php:291 src/gui.cls.php:718
#: tpl/crawler/entry.tpl.php:17
msgid "Settings"
msgstr "设置"