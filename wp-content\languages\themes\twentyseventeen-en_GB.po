# Translation of Themes - Twenty Seventeen in English (UK)
# This file is distributed under the same license as the Themes - Twenty Seventeen package.
msgid ""
msgstr ""
"PO-Revision-Date: 2020-03-31 21:25:42+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Twenty Seventeen\n"

#. Description of the theme
msgid "Twenty Seventeen brings your site to life with header video and immersive featured images. With a focus on business sites, it features multiple sections on the front page as well as widgets, navigation and social menus, a logo, and more. Personalize its asymmetrical grid with a custom color scheme and showcase your multimedia content with post formats. Our default theme for 2017 works great in many languages, for any abilities, and on any device."
msgstr "Twenty Seventeen brings your site to life with header video and immersive featured images. With a focus on business sites, it features multiple sections on the front page as well as widgets, navigation and social menus, a logo, and more. Personalise its asymmetrical grid with a custom colour scheme and showcase your multimedia content with post formats. Our default theme for 2017 works great in many languages, for any abilities, and on any device."

#. Theme Name of the theme
msgid "Twenty Seventeen"
msgstr "Twenty Seventeen"

#: template-parts/footer/footer-widgets.php:18
msgid "Footer"
msgstr "Footer"

#: functions.php:340
msgid "Add widgets here to appear in your sidebar on blog posts and archive pages."
msgstr "Add widgets here to appear in your sidebar on blog posts and archive pages."

#: functions.php:338 sidebar.php:18
msgid "Blog Sidebar"
msgstr "Blog Sidebar"

#: template-parts/header/site-branding.php:34
#: template-parts/navigation/navigation-top.php:31
msgid "Scroll down to content"
msgstr "Scroll down to content"

#: functions.php:181
msgctxt "Theme starter content"
msgid "Coffee"
msgstr "Coffee"

#: functions.php:177
msgctxt "Theme starter content"
msgid "Sandwich"
msgstr "Sandwich"

#: functions.php:173
msgctxt "Theme starter content"
msgid "Espresso"
msgstr "Espresso"

#: inc/custom-header.php:128
msgid "Pause background video"
msgstr "Pause background video"

#: inc/custom-header.php:127
msgid "Play background video"
msgstr "Play background video"

#. translators: %s: The section ID.
#: inc/template-tags.php:158
msgid "Front Page Section %s Placeholder"
msgstr "Front Page Section %s Placeholder"

#: inc/customizer.php:109
msgid "When the two-column layout is assigned, the page title is in one column and content is in the other."
msgstr "When the two-column layout is assigned, the page title is in one column and content is in the other."

#: single.php:34
msgid "Next Post"
msgstr "Next Post"

#: index.php:27
msgid "Posts"
msgstr "Posts"

#: inc/template-tags.php:89
msgid "Tags"
msgstr "Tags"

#: inc/template-tags.php:85
msgid "Categories"
msgstr "Categories"

#. translators: Used between list items, there is a space after the comma.
#: inc/template-tags.php:66
msgid ", "
msgstr ", "

#. translators: %s: Post date.
#: inc/template-tags.php:52
msgid "<span class=\"screen-reader-text\">Posted on</span> %s"
msgstr "<span class=\"screen-reader-text\">Posted on</span> %s"

#. translators: %s: Post author.
#: inc/template-tags.php:21
msgid "by %s"
msgstr "by %s"

#: inc/icon-functions.php:44
msgid "Please define an SVG icon filename."
msgstr "Please define an SVG icon filename."

#: inc/icon-functions.php:39
msgid "Please define default parameters in the form of an array."
msgstr "Please define default parameters in the form of an array."

#: inc/customizer.php:143
msgid "Select pages to feature in each area from the dropdowns. Add an image to a section by setting a featured image in the page editor. Empty sections will not be displayed."
msgstr "Select pages to feature in each area from the dropdowns. Add an image to a section by setting a featured image in the page editor. Empty sections will not be displayed."

#. translators: %d: The front page section number.
#: inc/customizer.php:142
msgid "Front Page Section %d Content"
msgstr "Front Page Section %d Content"

#: inc/customizer.php:112 inc/customizer.php:171
msgid "Two Column"
msgstr "Two Column"

#: inc/customizer.php:111 inc/customizer.php:170
msgid "One Column"
msgstr "One Column"

#: inc/customizer.php:106
msgid "Page Layout"
msgstr "Page Layout"

#: inc/customizer.php:89
msgid "Theme Options"
msgstr "Theme Options"

#: inc/customizer.php:64
msgid "Custom"
msgstr "Custom"

#: inc/customizer.php:62
msgid "Light"
msgstr "Light"

#: inc/customizer.php:60
msgid "Color Scheme"
msgstr "Colour Scheme"

#: inc/custom-header.php:56
msgid "Default Header Image"
msgstr "Default Header Image"

#: functions.php:362
msgid "Footer 2"
msgstr "Footer 2"

#: functions.php:352 functions.php:364
msgid "Add widgets here to appear in your footer."
msgstr "Add widgets here to appear in your footer."

#: functions.php:350
msgid "Footer 1"
msgstr "Footer 1"

#: functions.php:290
msgctxt "Libre Franklin font: on or off"
msgid "on"
msgstr "on"

#: template-parts/navigation/navigation-top.php:12 functions.php:64
#: functions.php:205
msgid "Top Menu"
msgstr "Top Menu"

#: comments.php:62
msgid "Reply"
msgstr "Reply"

#: template-parts/post/content-none.php:33
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."

#: search.php:59
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Sorry, but nothing matched your search terms. Please try again with some different keywords."

#. translators: %s: Post editor URL.
#: template-parts/post/content-none.php:27
msgid "Ready to publish your first post? <a href=\"%s\">Get started here</a>."
msgstr "Ready to publish your first post? <a href=\"%s\">Get started here</a>."

#: template-parts/post/content-none.php:17 search.php:26
msgid "Nothing Found"
msgstr "Nothing Found"

#: single.php:33
msgid "Previous Post"
msgstr "Previous Post"

#: single.php:33 comments.php:71
msgid "Previous"
msgstr "Previous"

#: single.php:34 comments.php:72
msgid "Next"
msgstr "Next"

#: searchform.php:20
msgctxt "submit button"
msgid "Search"
msgstr "Search"

#: searchform.php:19
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr "Search &hellip;"

#: searchform.php:17
msgctxt "label"
msgid "Search for:"
msgstr "Search for:"

#. translators: Search query.
#: search.php:22
msgid "Search Results for: %s"
msgstr "Search Results for: %s"

#. translators: %s: Post title.
#: template-parts/post/content.php:57 template-parts/post/content-audio.php:84
#: template-parts/post/content-image.php:61
#: template-parts/post/content-video.php:83
#: template-parts/post/content-gallery.php:71
#: template-parts/page/content-front-page.php:42
#: template-parts/page/content-front-page-panels.php:45 functions.php:392
msgid "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"

#: inc/customizer.php:63
msgid "Dark"
msgstr "Dark"

#. translators: %s: The current WordPress version.
#: inc/back-compat.php:40 inc/back-compat.php:54 inc/back-compat.php:73
msgid "Twenty Seventeen requires at least WordPress version 4.7. You are running version %s. Please upgrade and try again."
msgstr "Twenty Seventeen requires at least WordPress version 4.7. You are running version %s. Please upgrade and try again."

#. translators: %s: Post title.
#: inc/template-tags.php:117
msgid "Edit<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "Edit<span class=\"screen-reader-text\"> \"%s\"</span>"

#: template-parts/post/content.php:64 template-parts/post/content-audio.php:91
#: template-parts/post/content-image.php:68
#: template-parts/post/content-video.php:90
#: template-parts/post/content-gallery.php:78
#: template-parts/page/content-page.php:26
msgid "Pages:"
msgstr "Pages:"

#: template-parts/navigation/navigation-top.php:17
msgid "Menu"
msgstr "Menu"

#: header.php:28
msgid "Skip to content"
msgstr "Skip to content"

#: functions.php:484
msgid "Collapse child menu"
msgstr "Collapse child menu"

#: functions.php:483
msgid "Expand child menu"
msgstr "Expand child menu"

#: functions.php:65 functions.php:216
msgid "Social Links Menu"
msgstr "Social Links Menu"

#. translators: %s: WordPress
#: template-parts/footer/site-info.php:21
msgid "Proudly powered by %s"
msgstr "Proudly powered by %s"

#: footer.php:26
msgid "Footer Social Links Menu"
msgstr "Footer Social Links Menu"

#: comments.php:82
msgid "Comments are closed."
msgstr "Comments are closed."

#. translators: 1: Number of comments, 2: Post title.
#: comments.php:41
msgctxt "comments title"
msgid "%1$s Reply to &ldquo;%2$s&rdquo;"
msgid_plural "%1$s Replies to &ldquo;%2$s&rdquo;"
msgstr[0] "%1$s Reply to &ldquo;%2$s&rdquo;"
msgstr[1] "%1$s Replies to &ldquo;%2$s&rdquo;"

#. translators: %s: Post title.
#: comments.php:37
msgctxt "comments title"
msgid "One Reply to &ldquo;%s&rdquo;"
msgstr "One Reply to &ldquo;%s&rdquo;"

#: index.php:55 archive.php:51 search.php:52
msgid "Page"
msgstr "Page"

#: 404.php:21
msgid "Oops! That page can&rsquo;t be found."
msgstr "Oops! That page can&rsquo;t be found."

#: 404.php:24
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "It looks like nothing was found at this location. Maybe try a search?"

#: index.php:53 archive.php:49 search.php:50
msgid "Previous page"
msgstr "Previous page"

#: index.php:54 archive.php:50 search.php:51
msgid "Next page"
msgstr "Next page"

#. Author of the theme
msgid "the WordPress team"
msgstr "the WordPress team"

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentyseventeen/"
msgstr "https://en-gb.wordpress.org/themes/twentyseventeen/"

#. Author URI of the theme
#: template-parts/footer/site-info.php:18
msgid "https://wordpress.org/"
msgstr "https://en-gb.wordpress.org/"