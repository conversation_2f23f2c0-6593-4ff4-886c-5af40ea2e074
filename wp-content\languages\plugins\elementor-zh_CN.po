# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-06-09 04:34:47+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: modules/element-cache/module.php:144
msgid "Element Cache"
msgstr "元素缓存"

#: core/experiments/manager.php:378
msgid "Load Google Fonts locally"
msgstr "本地加载 Google 字体"

#: modules/ai/site-planner-connect/module.php:50
msgid "To connect your site to Site Planner, you need to generate an app password."
msgstr "要将您的网站连接到 Site Planner，您需要生成应用程序密码。"

#: modules/ai/site-planner-connect/module.php:49
msgid "Connect to Site Planner"
msgstr "连接到站点规划器"

#: modules/ai/module.php:225 modules/ai/module.php:260
msgid "Animate With AI"
msgstr "利用 AI 制作动画"

#: includes/template-library/sources/local.php:1766
msgid "Sorry, you are not allowed to do that."
msgstr "抱歉，您不能这样做。"

#: includes/managers/elements.php:298
msgid "Hello+"
msgstr "Hello+"

#: includes/elements/container.php:1447 includes/widgets/common-base.php:323
msgid "Row Span"
msgstr "行跨度"

#: includes/elements/container.php:1403 includes/widgets/common-base.php:279
msgid "Column Span"
msgstr "列跨度"

#: includes/elements/container.php:1395 includes/widgets/common-base.php:271
msgid "Grid Item"
msgstr "网格项目"

#: core/experiments/manager.php:379
msgid "To improve page load performance and user privacy, replace Google Fonts CDN links with self-hosted font files. This approach downloads and serves font files directly from your server, eliminating external requests to Google's servers."
msgstr "要改善页面加载性能和用户隐私，请用自托管字体文件替换Google字体CDN链接。此方法可直接从您的服务器下载并提供字体文件，从而消除了向Google服务器的外部请求。"

#: includes/widgets/image-gallery.php:303
msgid "Custom Gap"
msgstr "自定义间距"

#: modules/nested-tabs/widgets/nested-tabs.php:182
msgid "Add Tab"
msgstr "添加选项卡"

#: modules/atomic-widgets/elements/div-block/div-block.php:35
#: modules/library/documents/div-block.php:52 assets/js/editor.js:10577
msgid "Div Block"
msgstr "DIV 块"

#: modules/ai/site-planner-connect/module.php:51
msgid "Approve & Connect"
msgstr "批准和连接"

#: modules/promotions/promotion-data.php:78
msgid "Combine text, buttons, and images."
msgstr "组合文本、按钮和图像。"

#: modules/promotions/promotion-data.php:63
msgid "Seamlessly customize video appearance."
msgstr "无缝定制视频外观。"

#: modules/promotions/promotion-data.php:76
msgid "Boost Conversions with CTAs"
msgstr "通过 CTA 提高转化率"

#: modules/promotions/promotion-data.php:79
msgid "Add hover animations and CSS effects."
msgstr "添加悬停动画和 CSS 效果。"

#: modules/promotions/promotion-data.php:80
msgid "Create unique, interactive designs."
msgstr "创建独特的交互式设计。"

#: modules/promotions/promotion-data.php:93
msgid "Design Custom Carousels"
msgstr "设计定制轮播"

#: modules/promotions/promotion-data.php:95
msgid "Create flexible custom carousels."
msgstr "创建灵活的自定义轮播。"

#: modules/promotions/promotion-data.php:114
msgid "Customize layouts for visual appeal."
msgstr "自定义布局以获得视觉吸引力。"

#: modules/promotions/promotion-data.php:113
msgid "Boost credibility with dynamic testimonials."
msgstr "通过动态推荐提高可信度。"

#: modules/promotions/promotion-data.php:112
msgid "Display reviews in a rotating carousel."
msgstr "在旋转的轮播中显示评论。"

#: modules/promotions/promotion-data.php:110
msgid "Upgrade Your Testimonials"
msgstr "升级您的推荐"

#: modules/promotions/promotion-data.php:97
msgid "Showcase multiple items with style."
msgstr "展示多种风格的物品。"

#: modules/promotions/promotion-data.php:96
msgid "Adjust transitions and animations."
msgstr "调整过渡和动画。"

#: modules/promotions/promotion-data.php:62
msgid "Adjust layout and playback settings."
msgstr "调整布局和播放设置。"

#: modules/promotions/promotion-data.php:61
msgid "Embed videos with full control."
msgstr "完全控制嵌入视频。"

#: modules/promotions/promotion-data.php:59
msgid "Showcase Video Playlists"
msgstr "展示视频播放列表"

#: modules/promotions/promotion-data.php:46
msgid "Fully customize your headlines."
msgstr "完全自定义您的标题。"

#: modules/promotions/promotion-data.php:45
msgid "Apply rotating effects to text."
msgstr "对文本应用旋转效果。"

#: modules/promotions/promotion-data.php:44
msgid "Highlight key messages dynamically."
msgstr "动态突出显示关键消息。"

#: modules/promotions/promotion-data.php:42
msgid "Bring Headlines to Life"
msgstr "让头条新闻栩栩如生"

#: modules/global-classes/module.php:50
msgid "Enable global CSS classes."
msgstr "启用全局 CSS 类。"

#: modules/global-classes/module.php:49
msgid "Global Classes"
msgstr "全局类"

#: modules/ai/module.php:417
msgid "Image added successfully"
msgstr "图片添加成功"

#: modules/ai/module.php:321 assets/js/ai-unify-product-images.js:18369
msgid "Unify with Elementor AI"
msgstr "与 Elementor AI 统一"

#: modules/ai/feature-intro/product-image-unification-intro.php:35
msgid "Now you can process images in bulk and standardized the background and ratio - no manual editing required!"
msgstr "现在您可以批量处理图像并标准化背景和比例 - 无需手动编辑！"

#: modules/ai/feature-intro/product-image-unification-intro.php:34
msgid "New! Unify pack-shots with Elementor AI"
msgstr "新的！使用 Elementor AI 统一打包镜头"

#: includes/widgets/video.php:612
msgid "Video Playlist widget"
msgstr "视频播放列表小部件"

#: includes/widgets/testimonial.php:249
msgid "Loop Carousel widget"
msgstr "循环轮播小部件"

#: includes/widgets/image-carousel.php:653
msgid "Space Between Dots"
msgstr "点之间的间距"

#: includes/widgets/image-carousel.php:405
msgid "Carousel PRO widget"
msgstr "轮播 PRO 小部件"

#: includes/widgets/image-carousel.php:132
msgid "Carousel Name"
msgstr "轮播名称"

#: includes/widgets/heading.php:232
msgid "Animated Headline widget"
msgstr "动画标题小部件"

#: includes/widgets/button.php:103
msgid "Call to Action widget"
msgstr "号召性用语小部件"

#: core/admin/admin-notices.php:485
msgid "Use Elementor's Site Mailer to ensure your store emails like purchase confirmations, shipping updates and more are reliably delivered."
msgstr "使用 Elementor 的 Site Mailer 确保可靠地发送您的商店电子邮件，例如购买确认、发货更新等。"

#: core/admin/admin-notices.php:484
msgid "Improve Transactional Email Deliverability"
msgstr "提高交易电子邮件的送达率"

#: core/admin/admin-notices.php:464
msgid "Use Site Mailer for improved email deliverability, detailed email logs, and an easy setup."
msgstr "使用 Site Mailer 可以提高电子邮件的送达率、详细的电子邮件日志和轻松的设置。"

#: core/admin/admin-notices.php:463
msgid "Ensure your form emails avoid the spam folder!"
msgstr "确保您的表单电子邮件避开垃圾邮件文件夹！"

#: modules/checklist/steps/setup-header.php:70
msgid "Add a header"
msgstr "添加标题"

#: modules/checklist/steps/setup-header.php:66
msgid "This element applies across different pages, so visitors can easily navigate around your site."
msgstr "此元素适用于不同的页面，因此访问者可以轻松地浏览您的网站。"

#: modules/checklist/steps/setup-header.php:62
msgid "Set up a header"
msgstr "设置标题"

#: modules/checklist/steps/set-fonts-and-colors.php:31
msgid "Global colors and fonts ensure a cohesive look across your site. Start by defining one color and one font."
msgstr "全局颜色和字体确保整个网站的外观具有凝聚力。首先定义一种颜色和一种字体。"

#: modules/checklist/steps/set-fonts-and-colors.php:27
msgid "Set up your Global Fonts & Colors"
msgstr "设置您的全局字体和颜色"

#: modules/checklist/steps/assign-homepage.php:31
msgid "Assign homepage"
msgstr "分配主页"

#: modules/checklist/steps/assign-homepage.php:27
msgid "Before your launch, make sure to assign a homepage so visitors have a clear entry point into your site."
msgstr "在发布之前，请确保分配一个主页，以便访问者可以清楚地进入您的网站。"

#: modules/checklist/steps/assign-homepage.php:23
msgid "Assign a homepage"
msgstr "分配主页"

#: modules/checklist/steps/add-logo.php:33
#: modules/checklist/steps/set-fonts-and-colors.php:35
msgid "Go to Site Identity"
msgstr "转到站点标识"

#: modules/checklist/steps/add-logo.php:29
msgid "Let's start by adding your logo and filling in the site identity settings. This will establish your initial presence and also improve SEO."
msgstr "让我们首先添加您的徽标并填写站点标识设置。这将建立您的初始形象并改善搜索引擎优化。"

#: modules/checklist/steps/add-logo.php:25
msgid "Add your logo"
msgstr "添加您的 LOGO"

#: core/settings/editor-preferences/model.php:187
msgid "Show a checklist to guide you through your first steps of website creation."
msgstr "显示清单以指导您完成网站创建的第一步。"

#: core/experiments/manager.php:344
msgid "Create advanced layouts and responsive designs with %1$sFlexbox%2$s and %3$sGrid%4$s container elements. Give it a try using the %5$sContainer playground%6$s."
msgstr "使用 %1$sFlexbox%2$s 和 %3$sGrid%4$s 容器元素创建高级布局和响应式设计。使用 %5$s容器游乐场%6$s 尝试一下。"

#: modules/floating-buttons/widgets/floating-bars-var-1.php:25
msgid "Floating Bar CTA"
msgstr "浮动条 CTA"

#: modules/floating-buttons/module.php:46 assets/js/editor.js:53414
msgid "Floating Bars"
msgstr "浮动条"

#: modules/floating-buttons/documents/floating-buttons.php:203
#: modules/floating-buttons/module.php:373
msgid "Floating Element"
msgstr "浮动元素"

#. translators: 1: Platform name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:270
msgid "Open %1$s"
msgstr "打开 %1$s"

#. translators: 1: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:112
#: modules/floating-buttons/classes/render/floating-bars-core-render.php:112
msgid "Close %1$s"
msgstr "关闭  %1$s"

#. translators: 1: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:87
msgid "Toggle %1$s"
msgstr "切换 %1$s"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1259
msgid "Headline"
msgstr "标题"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1213
msgid "Element spacing"
msgstr "元素间距"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1183
msgid "Align Elements"
msgstr "对齐元素"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1049
msgid "Horizontal position"
msgstr "水平位置"

#: modules/floating-buttons/base/widget-floating-bars-base.php:339
msgid "Enter your text"
msgstr "输入您的文字"

#: modules/floating-buttons/base/widget-floating-bars-base.php:315
msgid "Headlines"
msgstr "头条"

#: modules/floating-buttons/base/widget-floating-bars-base.php:240
msgid "Pause Icon"
msgstr "暂停图标"

#: modules/floating-buttons/base/widget-floating-bars-base.php:228
#: modules/floating-buttons/base/widget-floating-bars-base.php:1130
msgid "Pause and Play"
msgstr "暂停和播放"

#: modules/floating-buttons/base/widget-floating-bars-base.php:219
#: modules/floating-buttons/base/widget-floating-bars-base.php:1173
msgid "Floating Bar"
msgstr "浮动条"

#: modules/floating-buttons/base/widget-floating-bars-base.php:199
msgid "Accessible Name"
msgstr "无障碍名称"

#: modules/floating-buttons/base/widget-floating-bars-base.php:157
msgid "Shop now"
msgstr "立即购买"

#: modules/floating-buttons/base/widget-floating-bars-base.php:156
#: modules/floating-buttons/base/widget-floating-bars-base.php:204
msgid "Enter text"
msgstr "输入文本"

#: modules/floating-buttons/base/widget-floating-bars-base.php:143
#: modules/floating-buttons/base/widget-floating-bars-base.php:555
msgid "CTA Button"
msgstr "CTA 按钮"

#: modules/floating-buttons/base/widget-floating-bars-base.php:131
msgid "Enter your text here"
msgstr "在这里输入你的文字......"

#: modules/floating-buttons/base/widget-floating-bars-base.php:105
#: modules/floating-buttons/base/widget-floating-bars-base.php:391
msgid "Announcement"
msgstr "公告"

#: modules/floating-buttons/base/widget-floating-bars-base.php:69
msgid "Banner"
msgstr "Banner"

#: modules/floating-buttons/base/widget-floating-bars-base.php:64
msgid "Just in! Cool summer tees"
msgstr "刚到！凉爽的夏季 T 恤"

#: modules/floating-buttons/base/widget-contact-button-base.php:764
msgid "Add up to <b>%d</b> contact buttons"
msgstr "添加最多 <b>%d</b> 个联系人按钮"

#: modules/floating-buttons/base/widget-contact-button-base.php:751
msgid "Add between <b>%1$d</b> to <b>%2$d</b> contact buttons"
msgstr "在 <b>%1$d</b> 到 <b>%2$d</b> 之间添加联系人按钮"

#: modules/floating-buttons/base/widget-contact-button-base.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:722
msgid "Add accessible name"
msgstr "添加可访问的名称"

#: modules/floating-buttons/base/widget-contact-button-base.php:463
#: modules/floating-buttons/base/widget-contact-button-base.php:719
msgid "Accessible name"
msgstr "无障碍名称"

#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:22
#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:26
#: modules/floating-buttons/documents/floating-buttons.php:207
msgid "Floating Elements"
msgstr "浮动元素"

#: modules/checklist/steps/create-pages.php:36
msgid "Create a new page"
msgstr "创建一个新页面"

#: modules/checklist/steps/create-pages.php:28
msgid "Create your first 3 pages"
msgstr "创建您的前 3 页"

#: core/settings/editor-preferences/model.php:182
#: modules/checklist/module.php:224
msgid "Launchpad Checklist"
msgstr "启动板清单"

#: modules/checklist/module.php:225
msgid "Launchpad Checklist feature to boost productivity and deliver your site faster"
msgstr "启动板清单可提高工作效率并更快地交付您的网站"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:68
msgid "Tag"
msgstr "标签"

#: modules/atomic-widgets/module.php:121
msgid "Enable atomic widgets."
msgstr "启用原子小部件。"

#: modules/atomic-widgets/module.php:120
msgid "Atomic Widgets"
msgstr "原子小部件"

#: modules/ai/preferences.php:73
msgid "Enable Elementor AI functionality"
msgstr "启用 Elementor AI 功能"

#: modules/ai/preferences.php:61
msgid "Elementor - AI"
msgstr "Elementor - AI"

#: includes/settings/settings.php:466
msgid "Improve initial page load performance by lazy loading all background images except the first one."
msgstr "通过延迟加载除第一个背景图像之外的所有背景图像来提高初始页面加载性能。"

#: includes/settings/settings.php:336
msgid "Personalize the way Elementor works on your website by choosing the advanced features and how they operate."
msgstr "通过选择高级功能及其操作方式，个性化 Elementor 在您网站上的工作方式。"

#: includes/settings/settings.php:259
msgid "Tailor how Elementor enhances your site, from post types to other functions."
msgstr "定制 Elementor 如何增强您的网站，从帖子类型到其他功能。"

#: includes/admin-templates/new-floating-elements.php:50
msgid "Create Floating Element"
msgstr "创建浮动元素"

#: includes/admin-templates/new-floating-elements.php:31
msgid "Choose Floating Element"
msgstr "选择浮动元素"

#: includes/admin-templates/new-floating-elements.php:21
msgid "Use floating elements to engage your visitors and increase conversions."
msgstr "使用浮动元素来吸引访问者并提高转化率。"

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-floating-elements.php:16
msgid "Floating Elements Help You %1$sWork Efficiently%2$s"
msgstr "浮动元素帮助您%1$s高效工作%2$s"

#: core/debug/classes/shop-page-edit.php:23
msgid "Sorry, The content area was not been found on your page"
msgstr "抱歉，在您的页面上找不到该内容区域"

#: core/debug/classes/shop-page-edit.php:15
msgid "You are trying to edit the Shop Page although it is a Product Archive. Use the Theme Builder to create your Shop Archive template instead"
msgstr "您正在尝试编辑商店页面，尽管它是产品存档。使用主题生成器来创建您的商店档案模板"

#: modules/checklist/steps/create-pages.php:32
msgid "Jumpstart your creation with professional designs from the Template Library or start from scratch."
msgstr "使用模板库中的专业设计快速启动您的创作或从头开始。"

#: modules/link-in-bio/widgets/link-in-bio.php:28
msgid "Minimalist"
msgstr "极简主义者"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1542
msgid "Dimensions"
msgstr "尺寸"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:332
msgid "CTA link"
msgstr "CTA 链接"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:284
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:48
msgid "Add item"
msgstr "添加项目"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:242
msgid "Images Per Row"
msgstr "每行图像"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:227
msgid "Add up to <b>%d</b> Images"
msgstr "添加最多 <b>%d</b> 张图片"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:215
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1023
msgid "Image Links"
msgstr "图片链接"

#: modules/floating-buttons/widgets/contact-buttons.php:26
msgid "Single Chat"
msgstr "私聊"

#: modules/floating-buttons/module.php:308
msgid "Entire Site"
msgstr "整个网站"

#: modules/floating-buttons/module.php:222
msgid "Instances"
msgstr "实例"

#: modules/floating-buttons/module.php:219
msgid "Click Tracking"
msgstr "点击跟踪"

#: modules/floating-buttons/documents/floating-buttons.php:195
msgid "Set as Entire Site"
msgstr "设置为整个站点"

#: modules/floating-buttons/documents/floating-buttons.php:188
msgid "Remove From Entire Site"
msgstr "从整个网站删除"

#: modules/floating-buttons/documents/floating-buttons.php:32
msgid "After publishing this widget, you will be able to set it as visible on the entire site in the Admin Table."
msgstr "发布此小部件后，您将能够在管理表中将其设置为在整个站点上可见。"

#: modules/floating-buttons/module.php:375
msgid "Add a Floating element so your users can easily get in touch!"
msgstr "添加浮动元素，以便您的用户可以轻松取得联系！"

#: modules/floating-buttons/classes/render/contact-buttons-core-render.php:58
msgid "Links window"
msgstr "链接窗口"

#: modules/floating-buttons/base/widget-contact-button-base.php:3080
#: modules/floating-buttons/base/widget-floating-bars-base.php:1488
msgid "CSS"
msgstr "CSS"

#: modules/floating-buttons/base/widget-contact-button-base.php:3067
#: modules/floating-buttons/base/widget-floating-bars-base.php:1475
msgid "Responsive visibility will take effect only on preview mode or live page, and not while editing in Elementor."
msgstr "响应式可见性仅在预览模式或实时页面上生效，在 Elementor 中编辑时无效。"

#: modules/floating-buttons/base/widget-contact-button-base.php:3043
msgid "Full Width on Mobile"
msgstr "移动设备上的全宽"

#: modules/floating-buttons/base/widget-contact-button-base.php:2479
#: modules/floating-buttons/base/widget-contact-button-base.php:2499
msgid "Text and Icon Color"
msgstr "文本和图标颜色"

#: modules/floating-buttons/base/widget-contact-button-base.php:2438
msgid "Link Spacing"
msgstr "链接间距"

#: modules/floating-buttons/base/widget-contact-button-base.php:2388
msgid "Info Links"
msgstr "信息链接"

#: modules/floating-buttons/base/widget-contact-button-base.php:2254
msgid "Resource Links"
msgstr "资源链接"

#: modules/floating-buttons/base/widget-contact-button-base.php:2224
msgid "Adjust transition duration to change the speed of the <b>hover animation on desktop</b> and the <b>click animation on touchscreen</b>."
msgstr "调整过渡持续时间以更改<b>桌面上的悬停动画</b>和<b>触摸屏上的单击动画</b>的速度。"

#: modules/floating-buttons/base/widget-contact-button-base.php:2150
msgid "Button Bar"
msgstr "按钮栏"

#: modules/floating-buttons/base/widget-contact-button-base.php:2105
msgid "Tooltips"
msgstr "工具提示"

#: modules/floating-buttons/base/widget-contact-button-base.php:2054
msgid "Buttons Spacing"
msgstr "按钮间距"

#: modules/floating-buttons/base/widget-contact-button-base.php:1828
msgid "Bubble Background Color"
msgstr "气泡背景颜色"

#: modules/announcements/module.php:110
msgid "Discover your new superpowers "
msgstr "发现你的新超能力 "

#. translators: 1: `<a>` opening tag, 2: `</a>` closing tag.
#: includes/widgets/video.php:342
msgid "Note: Autoplay is affected by %1$s Google’s Autoplay policy %2$s on Chrome browsers."
msgstr "注意：Chrome 浏览器上的自动播放受到 %1$s Google 自动播放政策 %2$s 的影响。"

#: includes/widgets/image.php:386
msgid "Scale Down"
msgstr "缩小"

#. translators: %s: <head> tag.
#: includes/settings/settings.php:423
msgid "Internal Embedding places all CSS in the %s which works great for troubleshooting, while External File uses external CSS file for better performance (recommended)."
msgstr "内部嵌入将所有 CSS 放置在 %s 中，这对于故障排除非常有用，而外部文件使用外部 CSS 文件以获得更好的性能（推荐）。"

#: includes/editor-templates/panel.php:142
msgid "Copy and Share Link"
msgstr "复制并分享链接"

#: includes/controls/repeater.php:194
msgid "In a Repeater control, if you specify a minimum number of items, you must also specify a default value that contains at least that number of items."
msgstr "在 重复器 控件中，如果指定最小项目数，则还必须指定至少包含该项目数的默认值。"

#: core/kits/documents/tabs/settings-background.php:75
msgid "Overscroll Behavior"
msgstr "过度滚动行为"

#: core/document-types/page-base.php:189
msgid "No %s found in Trash."
msgstr "在回收站中找不到 %s。"

#: core/document-types/page-base.php:188
msgid "No %s found."
msgstr "未找到 %s。"

#: core/document-types/page-base.php:187
msgid "Search %s"
msgstr "搜索 %s"

#: core/document-types/page-base.php:185
msgid "New %s"
msgstr "新增 %s"

#: core/document-types/page-base.php:181
msgid "All %s"
msgstr "所有 %s"

#: modules/floating-buttons/base/widget-contact-button-base.php:1485
msgid "Hover animation is <b>desktop only</b>"
msgstr "悬停动画<b>仅限桌面</b>"

#: modules/floating-buttons/base/widget-contact-button-base.php:826
msgid "Enter description"
msgstr "输入描述"

#: modules/floating-buttons/base/widget-contact-button-base.php:811
msgid "Enter title"
msgstr "输入标题"

#: modules/floating-buttons/base/widget-contact-button-base.php:736
msgid "Start conversation:"
msgstr "开始对话："

#: modules/floating-buttons/base/widget-contact-button-base.php:691
msgid "Typing Animation"
msgstr "打字动画"

#: modules/floating-buttons/base/widget-contact-button-base.php:626
msgid "Active Dot"
msgstr "激活点"

#: modules/floating-buttons/base/widget-contact-button-base.php:552
msgid "Enter the text"
msgstr "输入文字"

#: modules/floating-buttons/base/widget-contact-button-base.php:537
msgid "Call to Action"
msgstr "行为召唤"

#: modules/floating-buttons/base/widget-contact-button-base.php:546
#: modules/floating-buttons/base/widget-contact-button-base.php:734
#: modules/floating-buttons/base/widget-contact-button-base.php:1874
msgid "Call to Action Text"
msgstr "行为召唤文本"

#: modules/floating-buttons/base/widget-contact-button-base.php:536
msgid "Contact Details"
msgstr "联系方式"

#: modules/floating-buttons/base/widget-contact-button-base.php:532
msgid "Display Text"
msgstr "显示文字"

#: modules/floating-buttons/base/widget-contact-button-base.php:516
msgid "Notification Dot"
msgstr "通知点"

#: modules/floating-buttons/base/widget-contact-button-base.php:405
#: modules/floating-buttons/base/widget-contact-button-base.php:965
#: modules/link-in-bio/base/widget-link-in-bio-base.php:514
#: modules/link-in-bio/base/widget-link-in-bio-base.php:759
msgid "Paste Waze link"
msgstr "粘贴 Waze 链接"

#: modules/floating-buttons/base/widget-contact-button-base.php:146
msgid "Tooltip"
msgstr "工具提示"

#: modules/floating-buttons/base/widget-contact-button-base.php:86
msgid "Call now"
msgstr "立即致电"

#: modules/editor-events/module.php:48
msgid "Editor events processing"
msgstr "编辑器事件处理"

#: modules/editor-events/module.php:47
msgid "Elementor Editor Events"
msgstr "Elementor 编辑器事件"

#: modules/announcements/module.php:111
msgid "<p>With AI for text, code, image generation and editing, you can bring your vision to life faster than ever. Start your free trial now - <b>no credit card required!</b></p>"
msgstr "<p>借助用于文本、代码、图像生成和编辑的人工智能，您可以比以往更快地实现您的愿景。立即开始免费试用 - <b>无需信用卡！</b></p>"

#: modules/floating-buttons/module.php:45 assets/js/editor.js:53416
#: assets/js/editor.js:53418
msgid "Floating Buttons"
msgstr "悬浮按钮"

#: modules/element-cache/module.php:161
msgid "Specify the duration for which data is stored in the cache. Elements caching speeds up loading by serving pre-rendered copies of elements, rather than rendering them fresh each time. This control ensures efficient performance and up-to-date content."
msgstr "指定数据在缓存中存储的持续时间。元素缓存通过提供元素的预渲染副本来加速加载，而不是每次都渲染它们。这种控制可确保高效的性能和最新的内容。"

#: modules/element-cache/module.php:159
msgid "1 Year"
msgstr "1年"

#: modules/element-cache/module.php:158
msgid "1 Month"
msgstr "1个月"

#: modules/element-cache/module.php:157
msgid "2 Weeks"
msgstr "2周"

#: modules/element-cache/module.php:156
msgid "1 Week"
msgstr "1周"

#: modules/element-cache/module.php:155
msgid "3 Days"
msgstr "3天"

#: modules/element-cache/module.php:154
msgid "1 Day"
msgstr "1天"

#: modules/element-cache/module.php:153
msgid "12 Hours"
msgstr "12小时"

#: modules/element-cache/module.php:152
msgid "6 Hours"
msgstr "6小时"

#: modules/element-cache/module.php:151
msgid "1 Hour"
msgstr "1小时"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:25
msgid "Enjoy creative freedom %s with Custom Icons"
msgstr "使用自定义图标享受创作自由%s"

#. translators: %s: br
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:25
msgid "Create Forms and Collect Leads %s with Elementor Pro"
msgstr "使用 Elementor Pro 创建表单并收集潜在客户 %s"

#: modules/element-cache/module.php:123
msgid "Cache Settings"
msgstr "缓存设置"

#: modules/element-cache/module.php:48
msgid "Elements caching reduces loading times by serving up a copy of an element instead of rendering it fresh every time the page is loaded. When active, Elementor will determine which elements can benefit from static loading - but you can override this."
msgstr "元素缓存通过提供元素的副本而不是每次加载页面时重新渲染它来减少加载时间。处于活动状态时，Elementor 将确定哪些元素可以从静态加载中受益 - 但您可以覆盖此设置。"

#: modules/element-cache/module.php:46
msgid "Element Caching"
msgstr "元素缓存"

#: core/base/providers/social-network-provider.php:228
msgid "SMS"
msgstr "短信"

#: core/base/providers/social-network-provider.php:234
msgid "Viber"
msgstr "Viber"

#: core/base/providers/social-network-provider.php:240
msgid "Skype"
msgstr "Skype"

#: modules/floating-buttons/base/widget-contact-button-base.php:141
msgid "Contact Buttons"
msgstr "联系按钮"

#: core/base/traits/shared-widget-controls-trait.php:107
msgid "Icons Per Row"
msgstr "每行图标"

#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:222
msgid "Powered by Elementor"
msgstr "由 Elementor 提供支持"

#: includes/managers/elements.php:306
msgid "Link In Bio"
msgstr "Link In Bio"

#: core/base/providers/social-network-provider.php:222
msgid "File Download"
msgstr "文件下载"

#: core/base/providers/social-network-provider.php:204
msgid "Telephone"
msgstr "电话"

#: core/base/providers/social-network-provider.php:198
msgid "Messenger"
msgstr "Messenger"

#: core/base/providers/social-network-provider.php:192
msgid "Waze"
msgstr "Waze"

#: core/base/providers/social-network-provider.php:180
msgid "Dribbble"
msgstr "Dribbble"

#: core/base/providers/social-network-provider.php:174
msgid "Behance"
msgstr "Behance"

#: core/base/providers/social-network-provider.php:162
msgid "Spotify"
msgstr "Spotify"

#: core/base/providers/social-network-provider.php:156
msgid "Apple Music"
msgstr "Apple Music"

#: core/base/providers/social-network-provider.php:150
msgid "WhatsApp"
msgstr "WhatsApp"

#: core/base/providers/social-network-provider.php:144
msgid "TikTok"
msgstr "TikTok"

#: core/base/providers/social-network-provider.php:132
msgid "Pinterest"
msgstr "Pinterest"

#: core/base/providers/social-network-provider.php:126
msgid "LinkedIn"
msgstr "LinkedIn"

#: core/base/providers/social-network-provider.php:120
msgid "Instagram"
msgstr "Instagram"

#: core/base/providers/social-network-provider.php:114
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: core/base/providers/social-network-provider.php:108
msgid "Facebook"
msgstr "Facebook"

#: core/base/providers/social-network-provider.php:102
msgid "Save contact (vCard)"
msgstr "保存联系人 (vCard)"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1735
msgid "Bottom Border"
msgstr "下边框"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1031
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1707
msgid "Image Height"
msgstr "图像高度"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1671
msgid "Image Shape"
msgstr "图像形状"

#: modules/floating-buttons/base/widget-contact-button-base.php:2523
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1196
msgid "Dividers"
msgstr "分隔线"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:974
msgid "Profile"
msgstr "个人资料"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:970
msgid "Image style"
msgstr "图像风格"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:911
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1244
msgid "Identity"
msgstr "身份"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:883
msgid "About Me"
msgstr "关于我"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:882
msgid "About"
msgstr "关于"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:877
msgid "About Heading"
msgstr "关于标题"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:861
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1320
msgid "Title or Tagline"
msgstr "标题或标语"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:852
msgid "Sara Parker"
msgstr "Sara Parker"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:838
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1284
msgid "Bio"
msgstr "个人简历"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1626
msgid "Apply Full Screen Height on"
msgstr "应用全屏高度"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1609
msgid "Full Screen Height"
msgstr "全屏高度"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1562
msgid "Layout Width"
msgstr "布局宽度"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:797
msgid "Add Icon"
msgstr "添加图标"

#: modules/floating-buttons/base/widget-contact-button-base.php:147
#: modules/link-in-bio/base/widget-link-in-bio-base.php:587
msgid "Enter icon text"
msgstr "输入图标文本"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:569
msgid "Add up to <b>%d</b> icons"
msgstr "最多添加 <b>%d</b> 个图标"

#: modules/floating-buttons/base/widget-contact-button-base.php:2262
#: modules/link-in-bio/base/widget-link-in-bio-base.php:557
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1381
msgid "Icons"
msgstr "图标"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:543
msgid "Add CTA Link"
msgstr "添加号召性用语链接"

#: modules/floating-buttons/base/widget-contact-button-base.php:918
#: modules/link-in-bio/base/widget-link-in-bio-base.php:532
#: modules/link-in-bio/base/widget-link-in-bio-base.php:780
msgid "Enter your username"
msgstr "输入您的用户名"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:493
msgid "Enter your number"
msgstr "输入您的电话号码"

#: modules/floating-buttons/base/widget-contact-button-base.php:836
#: modules/link-in-bio/base/widget-link-in-bio-base.php:438
#: modules/link-in-bio/base/widget-link-in-bio-base.php:679
msgid "Enter your email"
msgstr "请输入您的邮箱"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:427
msgid "Mail"
msgstr "邮件"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:417
#: modules/link-in-bio/base/widget-link-in-bio-base.php:647
msgid "Enter your link"
msgstr "输入您的链接"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:354
msgid "Link Type"
msgstr "链接类型"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:333
msgid "Enter link text"
msgstr "输入链接文字"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:314
msgid "Add up to <b>%d</b> CTA links"
msgstr "最多添加 <b>%d</b> 个 CTA 链接"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:302
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1072
msgid "CTA Link Buttons"
msgstr "CTA 链接按钮"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:97
msgid "Healthy Living Resources"
msgstr "健康生活资源"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:94
msgid "Meal Prep"
msgstr "膳食准备"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:91
msgid "Top 10 Recipes"
msgstr "十大食谱"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:88
msgid "Get Healthy"
msgstr "保持健康"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:55
msgid "Join me on my journey to a healthier lifestyle"
msgstr "和我一起踏上更健康生活方式的旅程"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:52
msgid "Kitchen Chronicles"
msgstr "厨房纪事"

#: modules/floating-buttons/base/widget-contact-button-base.php:2774
msgid "Close Animation"
msgstr "关闭动画"

#: modules/floating-buttons/base/widget-contact-button-base.php:2764
msgid "Open Animation"
msgstr "打开动画"

#: modules/floating-buttons/base/widget-contact-button-base.php:2085
#: modules/floating-buttons/base/widget-contact-button-base.php:2176
#: modules/floating-buttons/base/widget-contact-button-base.php:2869
#: modules/floating-buttons/base/widget-floating-bars-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:120
msgid "Sharp"
msgstr "锋利"

#: modules/floating-buttons/base/widget-contact-button-base.php:2079
#: modules/floating-buttons/base/widget-contact-button-base.php:2170
#: modules/floating-buttons/base/widget-contact-button-base.php:2863
#: modules/floating-buttons/base/widget-floating-bars-base.php:840
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1156
msgid "Corners"
msgstr "边角"

#: modules/floating-buttons/base/widget-contact-button-base.php:247
msgid "Chat Box"
msgstr "聊天框"

#: modules/floating-buttons/base/widget-contact-button-base.php:1797
msgid "Time"
msgstr "时间"

#: modules/floating-buttons/base/widget-contact-button-base.php:1655
msgid "Close Button Color"
msgstr "关闭按钮颜色"

#: modules/floating-buttons/base/widget-contact-button-base.php:1641
#: modules/floating-buttons/base/widget-floating-bars-base.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:1036
msgid "Close Button"
msgstr "关闭按钮"

#: modules/floating-buttons/base/widget-contact-button-base.php:737
#: modules/floating-buttons/base/widget-contact-button-base.php:1045
msgid "Type your text here"
msgstr "在此输入文字"

#: modules/floating-buttons/base/widget-contact-button-base.php:186
msgid "Click to start chat"
msgstr "点击开始聊天"

#: modules/floating-buttons/base/widget-contact-button-base.php:183
msgid "Send Button"
msgstr "发送按钮"

#: modules/floating-buttons/base/widget-contact-button-base.php:682
msgid "14:20"
msgstr "14:20"

#: modules/floating-buttons/base/widget-contact-button-base.php:681
msgid "2:20 PM"
msgstr "2:20 PM"

#: modules/floating-buttons/base/widget-contact-button-base.php:677
msgid "Time format"
msgstr "时间格式"

#: modules/floating-buttons/base/widget-contact-button-base.php:669
msgid "Hey, how can I help you today?"
msgstr "嘿，今天我能为您做点什么吗？"

#: modules/floating-buttons/base/widget-contact-button-base.php:655
msgid "Rob"
msgstr "Rob"

#: modules/floating-buttons/base/widget-contact-button-base.php:645
#: modules/floating-buttons/base/widget-contact-button-base.php:1712
msgid "Message Bubble"
msgstr "消息气泡"

#: modules/floating-buttons/base/widget-contact-button-base.php:613
#: modules/floating-buttons/base/widget-contact-button-base.php:1530
msgid "Profile Image"
msgstr "个人资料图片"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:66
#: modules/floating-buttons/base/widget-contact-button-base.php:131
msgid "Type your title here"
msgstr "在此输入您的标题"

#: modules/floating-buttons/base/widget-contact-button-base.php:130
msgid "Store Manager"
msgstr "店经理"

#: modules/floating-buttons/base/widget-contact-button-base.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:656
msgid "Type your name here"
msgstr "于此处输入您的姓名"

#: modules/floating-buttons/base/widget-contact-button-base.php:122
msgid "Rob Jones"
msgstr "Rob Jones"

#: modules/floating-buttons/base/widget-contact-button-base.php:380
#: modules/floating-buttons/base/widget-contact-button-base.php:980
msgid "Action"
msgstr "操作"

#: modules/floating-buttons/base/widget-contact-button-base.php:342
#: modules/floating-buttons/base/widget-contact-button-base.php:894
#: modules/link-in-bio/base/widget-link-in-bio-base.php:734
msgid "+"
msgstr "+"

#: modules/floating-buttons/base/widget-contact-button-base.php:321
#: modules/floating-buttons/base/widget-contact-button-base.php:663
#: modules/floating-buttons/base/widget-contact-button-base.php:670
#: modules/floating-buttons/base/widget-contact-button-base.php:870
#: modules/floating-buttons/base/widget-contact-button-base.php:872
#: modules/floating-buttons/base/widget-contact-button-base.php:1766
#: modules/link-in-bio/base/widget-link-in-bio-base.php:463
#: modules/link-in-bio/base/widget-link-in-bio-base.php:474
#: modules/link-in-bio/base/widget-link-in-bio-base.php:713
#: modules/link-in-bio/base/widget-link-in-bio-base.php:715
msgid "Message"
msgstr "消息"

#: modules/floating-buttons/base/widget-contact-button-base.php:305
#: modules/floating-buttons/base/widget-contact-button-base.php:855
#: modules/floating-buttons/base/widget-contact-button-base.php:857
#: modules/link-in-bio/base/widget-link-in-bio-base.php:445
#: modules/link-in-bio/base/widget-link-in-bio-base.php:456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:698
#: modules/link-in-bio/base/widget-link-in-bio-base.php:700
msgid "Subject"
msgstr "主题"

#: modules/floating-buttons/base/widget-contact-button-base.php:294
msgid "@"
msgstr "@"

#: core/base/providers/social-network-provider.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:834
#: modules/link-in-bio/base/widget-link-in-bio-base.php:677
msgid "Email"
msgstr "Email"

#: modules/floating-buttons/base/widget-contact-button-base.php:479
#: modules/floating-buttons/base/widget-contact-button-base.php:777
#: modules/link-in-bio/base/widget-link-in-bio-base.php:595
msgid "Platform"
msgstr "平台"

#: modules/floating-buttons/base/widget-contact-button-base.php:65
msgid "Chat Button"
msgstr "聊天按钮"

#: modules/apps/admin-apps-page.php:126
msgid "Cannot Install"
msgstr "无法安装"

#: modules/apps/admin-apps-page.php:119 modules/apps/admin-apps-page.php:150
msgid "Cannot Activate"
msgstr "无法启用"

#: includes/widgets/traits/button-trait.php:299
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:163
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:209
msgid "Space between"
msgstr "间距"

#: includes/settings/settings.php:454
msgid "Reduce unnecessary render-blocking loads by dequeuing unused Gutenberg block editor scripts and styles."
msgstr "通过将未使用的古腾堡块编辑器脚本和样式出列来减少不必要的渲染阻塞负载。"

#. translators: 1: fetchpriority attribute, 2: lazy loading attribute.
#: includes/settings/settings.php:439
msgid "Improve performance by applying %1$s on LCP image and %2$s on images below the fold."
msgstr "通过在 LCP 图像上应用 %1$s 并在非首屏图像上应用 %2$s 来提高性能。"

#: core/settings/editor-preferences/model.php:233
msgid "Decide where you want to go when leaving the editor."
msgstr "决定离开编辑器后要去哪里。"

#: core/settings/editor-preferences/model.php:209
#: modules/styleguide/module.php:129
msgid "Temporarily overlay the canvas with the style guide to preview your changes to global colors and fonts."
msgstr "暂时用样式指南覆盖画布，以预览对全局颜色和字体的更改。"

#: core/settings/editor-preferences/model.php:204
#: modules/styleguide/module.php:127
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:442
msgid "Show global settings"
msgstr "显示全局设置"

#: core/settings/editor-preferences/model.php:165
msgid "This refers to elements you’ve hidden in the Responsive Visibility settings."
msgstr "这是指您在“响应式可见性”设置中隐藏的元素。"

#: includes/settings/settings.php:407
msgid "Improve loading times on your site by selecting the optimization tools that best fit your requirements."
msgstr "通过选择最适合您的要求的优化工具来缩短网站的加载时间。"

#: core/admin/admin.php:1022 includes/controls/gallery.php:122
#: includes/controls/media.php:318
msgid "Optimize your images to enhance site performance by using Image Optimizer."
msgstr "使用图像优化器优化图像以增强网站性能。"

#: core/settings/editor-preferences/model.php:160
msgid "Show hidden elements"
msgstr "显示隐藏元素"

#: core/settings/editor-preferences/model.php:153
msgid "This only applies while you’re working in the editor. The front end won’t be affected."
msgstr "这仅适用于您在编辑器中工作时。前端不会受到影响。"

#: core/settings/editor-preferences/model.php:148
msgid "Expand images in lightbox"
msgstr "展开灯箱中的图像"

#: core/settings/editor-preferences/model.php:141
msgid "Show additional actions while hovering over the handle of an element."
msgstr "将鼠标悬停在元素的手柄上时显示其他操作。"

#: core/settings/editor-preferences/model.php:137
msgid "Show quick edit options"
msgstr "显示快速编辑选项"

#: core/settings/editor-preferences/model.php:128
msgid "Choose which device to display when clicking the Responsive Mode icon."
msgstr "选择单击“响应模式”图标时要显示的设备。"

#: core/settings/editor-preferences/model.php:84
msgid "Set light or dark mode, or auto-detect to sync with your operating system settings."
msgstr "设置浅色或深色模式，或自动检测以与您的操作系统设置同步。"

#: core/settings/editor-preferences/model.php:75
msgid "Dark mode"
msgstr "深色模式"

#: core/settings/editor-preferences/model.php:71
msgid "Light mode"
msgstr "浅色模式"

#: core/settings/editor-preferences/model.php:67
msgid "Display mode"
msgstr "显示模式"

#: core/settings/editor-preferences/model.php:59
msgid "Panel"
msgstr "面板"

#: core/experiments/manager.php:371
msgid "Reduce the DOM size by eliminating HTML tags in various elements and widgets. This experiment includes markup changes so it might require updating custom CSS/JS code and cause compatibility issues with third party plugins."
msgstr "通过消除各种元素和小部件中的 HTML 标签来减小 DOM 大小。此实验包括标记更改，因此可能需要更新自定义 CSS/JS 代码并导致与第三方插件的兼容性问题。"

#: core/experiments/manager.php:369
msgid "Optimized Markup"
msgstr "优化标记"

#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:33
msgid "Upgrade Sale Now"
msgstr "立即升级促销"

#: core/admin/admin.php:349
msgid "Discounted Upgrades Now!"
msgstr "立即升级折扣！"

#: core/admin/admin-notices.php:571
msgid "Automatically compress and optimize images, resize larger files, or convert to WebP. Optimize images individually, in bulk, or on upload."
msgstr "自动压缩和优化图像、调整较大文件的大小或转换为 WebP。单独、批量或上传时优化图像。"

#: core/admin/admin-notices.php:570
msgid "Speed up your website with Image Optimizer by Elementor"
msgstr "使用 Elementor 的图像优化器加快您的网站速度"

#: modules/home/<USER>
msgid "Default Elementor menu page."
msgstr "默认 Elementor 菜单页面。"

#: modules/home/<USER>
msgid "Elementor Home Screen"
msgstr "Elementor 主屏幕"

#: includes/widgets/counter.php:452
msgid "Number Gap"
msgstr "数字间距"

#: includes/widgets/counter.php:424
msgid "Number Alignment"
msgstr "数字对齐"

#: includes/widgets/counter.php:388
msgid "Number Position"
msgstr "数字位置"

#: includes/widgets/counter.php:372
msgid "Title Gap"
msgstr "标题间距"

#: includes/widgets/counter.php:343
msgid "Title Vertical Alignment"
msgstr "标题垂直对齐方式"

#: includes/widgets/counter.php:314
msgid "Title Horizontal Alignment"
msgstr "标题水平对齐方式"

#: includes/widgets/counter.php:275
msgid "Title Position"
msgstr "标题位置"

#: includes/settings/settings.php:215
msgid "Home"
msgstr "主页"

#: elementor.php:96
msgid "Elementor isn’t running because WordPress is outdated."
msgstr "Elementor 未运行，因为 WordPress 已过时。"

#. translators: %s: PHP version.
#. translators: %s: WordPress version.
#: elementor.php:75 elementor.php:99
msgid "Update to version %s and get back to creating!"
msgstr "更新到版本 %s 并继续创建！"

#: elementor.php:72
msgid "Elementor isn’t running because PHP is outdated."
msgstr "Elementor 未运行，因为 PHP 已过时。"

#: core/files/uploads-manager.php:589
msgid "You do not have permission to upload JSON files."
msgstr "您没有上传 JSON 文件的权限。"

#: core/admin/admin.php:1024
msgid "Image Optimizer"
msgstr "图像优化器"

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:123
msgid "Install"
msgstr "安装"

#: modules/site-navigation/rest-fields/page-user-can.php:28
msgid "Whether the current user can edit or delete this post"
msgstr "当前用户是否可以编辑或删除该帖子"

#: modules/shapes/widgets/text-path.php:149
msgid "Want to create custom text paths with SVG?"
msgstr "想要使用 SVG 创建自定义文本路径？"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:36
msgid "Add any icon, anywhere on your website"
msgstr "在网站上的任何位置添加任何图标"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:33
msgid "Expand your icon library beyond FontAwesome and add icon %s libraries of your choice"
msgstr "将您的图标库扩展到 FontAwesome 之外，并添加您选择的图标 %s 库"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:31
msgid "Remain GDPR compliant with Custom Fonts that let you disable %s Google Fonts from your website"
msgstr "使用自定义字体保持 GDPR 合规性，让您可以从您的网站禁用 %s Google 字体"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:28
msgid "Upload any font to keep your website true to your brand"
msgstr "上传任何字体，让您的网站忠于您的品牌"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:23
msgid "Stay on brand with a Custom Font"
msgstr "使用自定义字体保持品牌形象"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:30
msgid "Leverage Elementor AI to instantly generate Custom Code for Elementor"
msgstr "利用 Elementor AI 立即生成 Elementor 的自定义代码"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:29
msgid "Use Custom Code to create sophisticated custom interactions to engage visitors"
msgstr "使用自定义代码创建复杂的自定义交互来吸引访问者"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:28
msgid "Add Custom Code snippets anywhere on your website, including the header or footer to measure your page’s performance*"
msgstr "在网站上的任何位置添加自定义代码片段（包括页眉或页脚）以衡量页面的性能*"

#: modules/apps/admin-pointer.php:35
msgid "Explore Add-ons"
msgstr "探索插件"

#: modules/apps/admin-pointer.php:29
msgid "New! Popular Add-ons"
msgstr "新的！热门插件"

#: modules/apps/admin-menu-apps.php:22 modules/apps/admin-menu-apps.php:26
#: modules/apps/module.php:37 assets/js/admin-top-bar.js:185
#: assets/js/editor.js:40505
msgid "Add-ons"
msgstr "扩展"

#: modules/apps/admin-apps-page.php:35
msgid "Please note that certain tools and services on this page are developed by third-party companies and are not part of Elementor's suite of products or support. Before using them, we recommend independently evaluating them. Additionally, when clicking on their action buttons, you may be redirected to an external website."
msgstr "请注意，此页面上的某些工具和服务由第三方公司开发，不属于 Elementor 产品或支持套件的一部分。在使用它们之前，我们建议对它们进行独立评估。此外，当单击其操作按钮时，您可能会被重定向到外部网站。"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:23
msgid "Enjoy Creative Freedom with Custom Code"
msgstr "通过自定义代码享受创作自由"

#: modules/apps/module.php:70
msgid "For Elementor"
msgstr "对于 Elementor"

#: includes/widgets/text-editor.php:191
msgid "10"
msgstr "10"

#: includes/widgets/text-editor.php:190
msgid "9"
msgstr "9"

#: includes/widgets/text-editor.php:189
msgid "8"
msgstr "8"

#: includes/widgets/text-editor.php:188
msgid "7"
msgstr "7"

#: includes/widgets/text-editor.php:187
msgid "6"
msgstr "6"

#: includes/widgets/text-editor.php:186
msgid "5"
msgstr "5"

#: includes/widgets/text-editor.php:185
msgid "4"
msgstr "4"

#: includes/widgets/text-editor.php:184
msgid "3"
msgstr "3"

#: includes/widgets/text-editor.php:183
msgid "2"
msgstr "2"

#: includes/widgets/text-editor.php:182
msgid "1"
msgstr "1"

#: includes/widgets/icon-box.php:352 includes/widgets/image-box.php:324
msgid "Content Spacing"
msgstr "内容间距"

#: includes/widgets/common-base.php:1028
msgid "Explore additional Premium Shape packs and use them in your site."
msgstr "探索其他高级形状包并在您的网站中使用它们。"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:344
msgid "It is strongly recommended to %1$sbackup the database%2$s before using replacing URLs."
msgstr "强烈建议在使用替换 URL 之前%1$s备份数据库%2$s。"

#: core/role-manager/role-manager.php:215
msgid "Giving broad access to edit the HTML widget can pose a security risk to your website because it enables users to run malicious scripts, etc."
msgstr "授予编辑 HTML 小部件的广泛访问权限可能会给您的网站带来安全风险，因为它使用户能够运行恶意脚本等。"

#: core/role-manager/role-manager.php:213
msgid "Enable the option to use the HTML widget"
msgstr "启用使用 HTML 小部件的选项"

#: includes/controls/base-units.php:138
msgid "Custom unit"
msgstr "自定义单位"

#: includes/editor-templates/panel-elements.php:33
msgid "Access all Pro widgets."
msgstr "访问所有 Pro 小部件。 "

#: includes/editor-templates/navigator.php:19
msgid "Access all Pro widgets"
msgstr "访问所有 Pro 小部件"

#: core/utils/hints.php:156 includes/controls/notice.php:83
msgid "Don’t show again."
msgstr "不再显示。"

#: includes/managers/controls.php:1301 assets/js/editor.js:55404
msgid "Scrolling Effects"
msgstr "滚动效果"

#: includes/managers/controls.php:1310 assets/js/editor.js:55419
msgid "Mouse Effects"
msgstr "鼠标效果"

#: includes/managers/controls.php:1319
#: modules/floating-buttons/base/widget-floating-bars-base.php:1453
#: assets/js/editor.js:55434
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:146
msgid "Sticky"
msgstr "Sticky"

#: core/admin/admin-notices.php:523 includes/controls/gallery.php:123
#: includes/controls/media.php:319
msgid "Install Plugin"
msgstr "安装插件"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:35
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:48
msgid "* Requires an Advanced subscription or higher"
msgstr "* 需要高级订阅或更高版本"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:35
msgid "Collect lead submissions directly within your WordPress Admin to manage, analyze and perform bulk actions on the submitted lead*"
msgstr "直接在您的 WordPress 管理员中收集潜在客户提交，以管理、分析提交的潜在客户并对其执行批量操作*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:34
msgid "Integrate your favorite marketing software*"
msgstr "集成您最喜欢的营销软件*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:33
msgid "Use any field to collect the information you need"
msgstr "使用任何字段收集您需要的信息"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:32
msgid "Create single or multi-step forms to engage and convert visitors"
msgstr "创建单步或多步表单来吸引和转化访问者"

#: includes/widgets/testimonial.php:176
msgid "Designer"
msgstr "设计师"

#: includes/widgets/testimonial.php:161
msgid "John Doe"
msgstr "John Doe"

#: includes/template-library/sources/local.php:826
msgid "You do not have permission to export this template."
msgstr "您无权导出此模板"

#: includes/template-library/manager.php:409
#: includes/template-library/manager.php:552
#: includes/template-library/sources/local.php:822
msgid "You do not have permission to access this template."
msgstr "您无权访问此模板"

#: includes/template-library/sources/local.php:817
msgid "Invalid template type or template does not exist."
msgstr "模板类型无效或模板不存在"

#: includes/managers/controls.php:1270 assets/js/editor.js:55389
msgid "Display Conditions"
msgstr "显示条件"

#: includes/editor-templates/templates.php:457
msgid "Generate Variations"
msgstr "生成变体"

#: includes/controls/groups/background.php:162
msgid "Set locations and angle for each breakpoint to ensure the gradient adapts to different screen sizes."
msgstr "设置每个断点的位置和角度，以确保渐变适应不同的屏幕尺寸。"

#: core/role-manager/role-manager.php:197
msgid "Giving broad access to upload JSON files can pose a security risk to your website because such files may contain malicious scripts, etc."
msgstr "授予上传 JSON 文件的广泛访问权限可能会给您的网站带来安全风险，因为此类文件可能包含恶意脚本等。"

#: core/role-manager/role-manager.php:197
#: core/role-manager/role-manager.php:215
msgid "Heads up"
msgstr "小心"

#: core/role-manager/role-manager.php:195
msgid "Enable the option to upload JSON files"
msgstr "启用上传 JSON 文件的选项"

#: core/files/uploads-manager.php:290
msgid "Invalid file name."
msgstr "文件名无效。"

#: includes/editor-templates/templates.php:176
#: assets/js/element-manager-admin.js:2290
#: assets/js/element-manager-admin.js:2359
msgid "Usage"
msgstr "使用"

#: modules/promotions/widgets/pro-widget-promotion.php:56
msgid "This result includes the Elementor Pro %s widget. Upgrade now to unlock it and grow your web creation toolkit."
msgstr "此结果包括 Elementor Pro %s 小部件。立即升级以解锁它并扩展您的网络创建工具包。"

#: modules/element-manager/ajax.php:154
msgid "Unexpected elements data."
msgstr "意外元素数据。"

#: modules/element-manager/ajax.php:148
msgid "No elements to save."
msgstr "没有要保存的元素。"

#: modules/element-manager/ajax.php:127
msgid "WordPress Widgets"
msgstr "WordPress 小部件"

#: modules/element-manager/ajax.php:117
msgid "Invalid nonce."
msgstr "无效的随机数。"

#: modules/element-manager/ajax.php:112
msgid "You do not have permission to edit these settings."
msgstr "您无权编辑这些设置。"

#: includes/widgets/image-gallery.php:108
msgid "Use interesting masonry layouts and other overlay features with Elementor's Pro Gallery widget."
msgstr "通过 Elementor 的 Pro 画廊小部件使用有趣的瀑布流布局和其他叠加功能。"

#: includes/template-library/sources/local.php:238
msgid "Parent Template:"
msgstr "父模板："

#: includes/template-library/sources/local.php:237
msgid "No Templates found in Trash"
msgstr "回收站未找到模板。"

#: includes/template-library/sources/local.php:236
msgid "No Templates found"
msgstr "未找到模板"

#: includes/template-library/sources/local.php:235
msgid "Search Template"
msgstr "搜索模板"

#: includes/template-library/sources/local.php:234
msgid "View Template"
msgstr "查看模板"

#: includes/template-library/sources/local.php:233
msgid "All Templates"
msgstr "所有模板"

#: includes/template-library/sources/local.php:229
#: includes/template-library/sources/local.php:230
msgid "Add New Template"
msgstr "新增模板"

#: includes/controls/groups/image-size.php:296 includes/controls/media.php:297
#: includes/widgets/testimonial.php:317
msgid "Image Resolution"
msgstr "图像分辨率"

#: includes/controls/groups/flex-container.php:187
msgid "No Wrap"
msgstr "不换行"

#: includes/controls/groups/flex-container.php:183
#: includes/controls/groups/flex-container.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:156
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:158
msgid "Wrap"
msgstr "换行"

#: core/common/modules/finder/categories/settings.php:79
#: modules/element-manager/admin-menu-app.php:22
#: modules/element-manager/admin-menu-app.php:26
#: modules/element-manager/admin-menu-app.php:35
msgid "Element Manager"
msgstr "元素管理器"

#: core/experiments/manager.php:361
msgid "Container-based content will be hidden from your site and may not be recoverable in all cases."
msgstr "基于容器的内容将从您的站点隐藏，并且可能无法在所有情况下恢复。"

#: includes/settings/settings.php:446
msgid "Optimized Gutenberg Loading"
msgstr "优化古腾堡加载"

#: includes/editor-templates/templates.php:539 assets/js/ai-admin.js:1034
#: assets/js/ai-admin.js:1081 assets/js/ai-admin.js:2494
#: assets/js/ai-admin.js:3225 assets/js/ai-gutenberg.js:2802
#: assets/js/ai-gutenberg.js:2849 assets/js/ai-gutenberg.js:4262
#: assets/js/ai-gutenberg.js:4993 assets/js/ai-layout.js:732
#: assets/js/ai-layout.js:779 assets/js/ai-media-library.js:2663
#: assets/js/ai-media-library.js:2710 assets/js/ai-media-library.js:4123
#: assets/js/ai-media-library.js:4854 assets/js/ai-unify-product-images.js:2663
#: assets/js/ai-unify-product-images.js:2710
#: assets/js/ai-unify-product-images.js:4123
#: assets/js/ai-unify-product-images.js:4854 assets/js/ai.js:3448
#: assets/js/ai.js:3495 assets/js/ai.js:4908 assets/js/ai.js:5639
#: assets/js/editor.js:10336 assets/js/editor.js:10338
#: assets/js/editor.js:11915 assets/js/editor.js:11916
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:516
msgid "Upgrade now"
msgstr "现在升级"

#. translators: %s: Recommended PHP version.
#: modules/system-info/reporters/server.php:131
msgid "We recommend using PHP version %s or higher."
msgstr "我们建议使用 PHP 版本 %s 或更高版本。"

#: modules/page-templates/module.php:158
msgid "Elementor Full Width"
msgstr "Elementor 全宽"

#: modules/page-templates/module.php:157
msgid "Elementor Canvas"
msgstr "Elementor 画布"

#: modules/nested-accordion/widgets/nested-accordion.php:320
msgid "Let Google know that this section contains an FAQ. Make sure to only use it only once per page"
msgstr "让 Google 知道此部分包含常见问题解答。确保每页仅使用一次"

#: modules/image-loading-optimization/module.php:240
msgid "An image should not be lazy-loaded and marked as high priority at the same time."
msgstr "图像不应同时延迟加载和标记为高优先级。"

#: includes/settings/settings.php:429
msgid "Optimized Image Loading"
msgstr "优化图像加载"

#: includes/widgets/video.php:291
msgid "VideoPress URL"
msgstr "VideoPress 网址"

#: includes/widgets/video.php:140
msgid "VideoPress"
msgstr "VideoPress"

#: includes/widgets/star-rating.php:129
msgid "You are currently editing a Star Rating widget in its old version. Drag a new Rating widget onto your page to use a newer version, providing better capabilities."
msgstr "您当前正在旧版本中编辑星级评定小部件。将新的评级小部件拖到您的页面上以使用更新的版本，提供更好的功能。"

#: includes/widgets/rating.php:308
msgid "Rated %1$s out of %2$s"
msgstr "评分为 %1$s（共 %2$s）"

#: includes/settings/tools.php:165
msgid "An error occurred, the selected version is invalid. Try selecting different version."
msgstr "发生错误，所选版本无效。尝试选择不同的版本。"

#: includes/controls/groups/flex-item.php:76
#: includes/controls/groups/flex-item.php:109
msgid "This control will affect contained elements only."
msgstr "此控件将仅影响包含的元素。"

#: includes/controls/groups/background.php:233
msgid "Linear"
msgstr "线性"

#: includes/controls/groups/background.php:234
msgid "Radial"
msgstr "径向"

#: includes/controls/groups/background.php:245
msgid "Angle"
msgstr "角度"

#: includes/controls/groups/background.php:301
msgid "Background Image"
msgstr "背景图像"

#: includes/controls/groups/background.php:154
msgid "Background Type"
msgstr "背景类型"

#: includes/controls/groups/background.php:95
msgid "Classic"
msgstr "经典"

#: includes/controls/groups/background.php:201
msgid "Second Color"
msgstr "次要颜色"

#: includes/controls/groups/flex-container.php:195
msgid "Items within the container can stay in a single line (No wrap), or break into multiple lines (Wrap)."
msgstr "容器内的项目可以保留在单行中（不换行），也可以分成多行（换行）。"

#: includes/controls/groups/css-filter.php:162
msgid "CSS Filters"
msgstr "CSS 滤镜"

#: includes/controls/groups/box-shadow.php:61
#: includes/controls/groups/box-shadow.php:96
msgid "Box Shadow"
msgstr "盒阴影"

#: includes/controls/groups/border.php:69
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:226
msgid "Groove"
msgstr "凹槽"

#: includes/controls/groups/border.php:60
msgid "Border Type"
msgstr "边框类型"

#: includes/controls/groups/background.php:607
msgid "Background Fallback"
msgstr "背景备选"

#: includes/controls/groups/background.php:478
msgid "Display Size"
msgstr "显示尺寸"

#: includes/controls/groups/typography.php:220
msgid "Letter Spacing"
msgstr "字符间距"

#: includes/controls/groups/typography.php:198
msgid "Line Height"
msgstr "行高"

#: includes/controls/groups/text-shadow.php:61
#: includes/controls/groups/text-shadow.php:85
msgid "Text Shadow"
msgstr "文本阴影"

#: includes/controls/groups/image-size.php:380
#: modules/atomic-widgets/image/image-sizes.php:38
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:81
msgid "Full"
msgstr "原图"

#: includes/controls/groups/image-size.php:301
msgid "Image Dimension"
msgstr "图像尺寸"

#: includes/controls/groups/flex-item.php:159
msgid "Flex Grow"
msgstr "Flex Grow (扩展)"

#: includes/controls/groups/flex-item.php:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:169
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:173
msgid "Shrink"
msgstr "Shrink (收缩)"

#: includes/controls/groups/flex-item.php:173
msgid "Flex Shrink"
msgstr "Flex Shrink(收缩)"

#: includes/controls/groups/flex-item.php:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:168
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:172
msgid "Grow"
msgstr "Grow (扩展)"

#: includes/controls/groups/flex-item.php:113
msgid "Custom Order"
msgstr "自定义顺序"

#: includes/controls/groups/flex-item.php:51
msgid "Align Self"
msgstr "Align Self (自对齐)"

#: includes/controls/groups/flex-item.php:20
msgid "Flex Basis"
msgstr "Flex Basis"

#: includes/controls/groups/flex-container.php:204
#: includes/controls/groups/grid-container.php:226
msgid "Align Content"
msgstr "Align Content (副轴多行对齐)"

#: includes/controls/groups/flex-container.php:128
#: includes/controls/groups/grid-container.php:159
msgid "Align Items"
msgstr "副轴对齐"

#: includes/controls/groups/flex-container.php:91
#: includes/controls/groups/grid-container.php:186
msgid "Justify Content"
msgstr "主轴对齐"

#: includes/controls/groups/flex-container.php:45
msgid "Column - reversed"
msgstr "列 - 反转"

#: includes/controls/groups/flex-container.php:41
msgid "Row - reversed"
msgstr "行 - 反转"

#: includes/controls/groups/flex-container.php:37
msgid "Column - vertical"
msgstr "列 - 垂直"

#: includes/controls/groups/flex-container.php:33
msgid "Row - horizontal"
msgstr "行 - 水平"

#: includes/controls/groups/grid-container.php:131
msgid "Justify Items"
msgstr "项目主轴对齐"

#: app/modules/onboarding/module.php:140
msgid "You do not have permission to perform this action."
msgstr "您没有执行此操作的权限。"

#: modules/nested-tabs/widgets/nested-tabs.php:1170
#: modules/nested-tabs/widgets/nested-tabs.php:1235
msgid "Tabs. Open items with Enter or Space, close with Escape and navigate using the Arrow keys."
msgstr "选项卡。使用 Enter 或 Space 打开项目，使用 ESC 关闭并使用箭头键导航。"

#. translators: %s: Post type (e.g. Page, Post, etc.)
#: core/document-types/page-base.php:186
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:17
msgid "View %s"
msgstr "查看 %s"

#: modules/site-navigation/module.php:74
msgid "Pages Panel"
msgstr "页面面板"

#: modules/nested-tabs/widgets/nested-tabs.php:199
#: modules/nested-tabs/widgets/nested-tabs.php:869
msgid "Below"
msgstr "下方"

#: modules/nested-tabs/widgets/nested-tabs.php:195
#: modules/nested-tabs/widgets/nested-tabs.php:861
msgid "Above"
msgstr "上方"

#: includes/editor-templates/templates.php:334
#: includes/editor-templates/templates.php:367
#: includes/editor-templates/templates.php:424 assets/js/editor.js:10847
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:70
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:4
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:19
msgid "Rename"
msgstr "重命名"

#: modules/apps/admin-apps-page.php:26
msgid "Boost your web-creation process with add-ons, plugins, and more tools specially selected to unleash your creativity, increase productivity, and enhance your Elementor-powered website."
msgstr "使用附加组件、插件和更多专为释放您的创造力、提高工作效率并增强您的 Elementor 支持的网站而精心挑选的工具来加快您的 Web 创建过程。"

#: modules/nested-accordion/widgets/nested-accordion.php:401
msgid "Space between Items"
msgstr "项目间距"

#: modules/nested-accordion/widgets/nested-accordion.php:356
msgid "One"
msgstr "一个"

#: modules/nested-accordion/widgets/nested-accordion.php:353
msgid "Max Items Expanded"
msgstr "扩展的最大项目数"

#: modules/nested-accordion/widgets/nested-accordion.php:343
msgid "All collapsed"
msgstr "全部折叠"

#: modules/nested-accordion/widgets/nested-accordion.php:342
msgid "First expanded"
msgstr "首个展开"

#: modules/nested-accordion/widgets/nested-accordion.php:339
msgid "Default State"
msgstr "默认状态"

#: modules/nested-accordion/widgets/nested-accordion.php:332
msgid "Interactions"
msgstr "互动"

#: modules/nested-accordion/widgets/nested-accordion.php:252
msgid "Collapse"
msgstr "折叠"

#: modules/nested-accordion/widgets/nested-accordion.php:238
#: assets/js/ai-admin.js:9402 assets/js/ai-gutenberg.js:11250
#: assets/js/ai-layout.js:4883 assets/js/ai-media-library.js:11031
#: assets/js/ai-unify-product-images.js:11031 assets/js/ai.js:12496
msgid "Expand"
msgstr "展开"

#: modules/nested-accordion/widgets/nested-accordion.php:168
msgid "Item Position"
msgstr "项目位置"

#: includes/widgets/video.php:929
msgid "Note: These controls have been deprecated and are only visible if they were previously in use. The video’s width and position are now set based on its aspect ratio."
msgstr "注意：这些控件已被弃用，并且仅在以前使用过时才可见。视频的宽度和位置现在根据其宽高比设置。"

#: includes/widgets/video.php:234
msgid "Choose Video File"
msgstr "选择视频文件"

#. translators: 1: Slide count, 2: Total slides count.
#: includes/widgets/image-carousel.php:969
msgid "%1$s of %2$s"
msgstr "%1$s / %2$s"

#: includes/widgets/icon-box.php:229 includes/widgets/image-box.php:204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1453
msgid "Box"
msgstr "盒子"

#. translators: 1: Link open tag, 2: Link open tag, 3: Link close tag.
#: core/kits/documents/tabs/settings-site-identity.php:60
msgid "Changes will be reflected only after %1$s saving %3$s and %2$s reloading %3$s preview."
msgstr "仅在 %1$s 保存 %3$s 并 %2$s 重新加载 %3$s 预览后才会反映更改。"

#: core/admin/admin.php:628
msgid "Build Smart with AI"
msgstr "利用 AI 构建智能"

#: modules/apps/admin-apps-page.php:27
msgid "Learn more about this page."
msgstr "了解有关此页面的更多信息。"

#: modules/nested-accordion/widgets/nested-accordion.php:357
msgid "Multiple"
msgstr "多样"

#: modules/apps/admin-pointer.php:30
msgid "Discover our collection of plugins and add-ons carefully selected to enhance your Elementor website and unleash your creativity."
msgstr "探索我们精心挑选的一系列插件和附加组件，以增强您的 Elementor 网站并释放您的创造力。"

#: modules/apps/admin-apps-page.php:25
msgid "Popular Add-ons, New Possibilities."
msgstr "流行的附加组件，新的可能性。"

#: modules/floating-buttons/base/widget-floating-bars-base.php:376
#: modules/nested-accordion/widgets/nested-accordion.php:157
msgid "Item #3"
msgstr "项目 #3"

#: modules/floating-buttons/base/widget-floating-bars-base.php:373
#: modules/nested-accordion/widgets/nested-accordion.php:154
msgid "Item #2"
msgstr "项目 #2"

#: modules/nested-accordion/widgets/nested-accordion.php:66
msgid "item #%s"
msgstr "项目 #%s"

#: modules/floating-buttons/base/widget-floating-bars-base.php:340
#: modules/nested-accordion/widgets/nested-accordion.php:117
#: modules/nested-accordion/widgets/nested-accordion.php:118
msgid "Item Title"
msgstr "项目标题"

#: modules/floating-buttons/base/widget-floating-bars-base.php:370
#: modules/nested-accordion/widgets/nested-accordion.php:151
msgid "Item #1"
msgstr "项目 #1"

#: modules/nested-tabs/widgets/nested-tabs.php:377
msgid "Horizontal Scroll"
msgstr "水平滚动"

#: modules/nested-tabs/widgets/nested-tabs.php:379
msgid "Note: Scroll tabs if they don’t fit into their parent container."
msgstr "注意：如果选项卡不适合其父容器，请滚动选项卡。"

#: includes/widgets/image.php:398
msgid "Object Position"
msgstr "对象位置"

#: includes/widgets/icon.php:326
msgid "Fit to Size"
msgstr "适合尺寸"

#: includes/frontend.php:1422
msgid "Go to slide"
msgstr "转到幻灯片"

#: includes/frontend.php:1421
msgid "This is the last slide"
msgstr "这是最后一张幻灯片"

#: includes/frontend.php:1420
msgid "This is the first slide"
msgstr "这是第一张幻灯片"

#: includes/frontend.php:1419
msgid "Next slide"
msgstr "下一张幻灯片"

#: includes/frontend.php:1418
msgid "Previous slide"
msgstr "上一张幻灯片"

#: includes/editor-templates/navigator.php:88
msgid "Show/hide Element"
msgstr "显示/隐藏元素"

#: includes/controls/groups/background.php:99 assets/js/ai-admin.js:11334
#: assets/js/ai-gutenberg.js:13182 assets/js/ai-media-library.js:12963
#: assets/js/ai-unify-product-images.js:12963 assets/js/ai.js:14428
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:85
msgid "Gradient"
msgstr "渐变"

#: includes/editor-templates/navigator.php:75
msgid "Show/hide inner elements"
msgstr "显示/隐藏内部元素"

#: includes/editor-templates/navigator.php:64
msgid "Resize navigator"
msgstr "调整导航器大小"

#: includes/editor-templates/navigator.php:63
msgid "Resize structure"
msgstr "调整结构大小"

#: includes/editor-templates/hotkeys.php:94
msgid "Panels"
msgstr "面板"

#: includes/controls/gallery.php:84 assets/js/editor.js:16773
msgid "Clear gallery"
msgstr "清除画廊"

#: core/document-types/page-base.php:257
msgid "Allow Comments"
msgstr "允许评论"

#: core/document-types/page-base.php:245
#: includes/controls/groups/flex-item.php:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:178
msgid "Order"
msgstr "排序"

#: includes/widgets/accordion.php:154
msgid "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."
msgstr "您当前正在编辑旧版本的 手风琴小部件。拖到画布中的任何新的 手风琴小部件都将是新的 手风琴小部件，具有改进的嵌套功能。"

#: includes/widgets/toggle.php:157
msgid "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."
msgstr "您当前正在旧版本中编辑切换小部件。将新的 手风琴小部件拖到页面上以使用较新的版本，并提供嵌套功能。"

#: includes/template-library/sources/local.php:515
msgid "Invalid template type."
msgstr "模板类型无效。"

#: core/admin/admin.php:347
msgid "Get Elementor Pro"
msgstr "获取 Element Pro"

#: modules/ai/connect/ai.php:27 assets/js/ai-admin.js:656
#: assets/js/ai-admin.js:7783 assets/js/ai-gutenberg.js:2424
#: assets/js/ai-gutenberg.js:9631 assets/js/ai-layout.js:488
#: assets/js/ai-layout.js:3264 assets/js/ai-media-library.js:2285
#: assets/js/ai-media-library.js:9412 assets/js/ai-unify-product-images.js:2285
#: assets/js/ai-unify-product-images.js:9412 assets/js/ai.js:3070
#: assets/js/ai.js:10877
msgid "AI"
msgstr "AI"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/widgets/image-carousel.php:370
#: includes/widgets/image-gallery.php:204 includes/widgets/image.php:229
msgid "Manage your site’s lightbox settings in the %1$sLightbox panel%2$s."
msgstr "在 %1$sLightbox 面板%2$s 中管理您网站的 Lightbox 设置。"

#: includes/elements/container.php:360
msgid "Container Layout"
msgstr "容器布局"

#: includes/editor-templates/panel-elements.php:15
msgid "Globals"
msgstr "全局"

#: includes/editor-templates/navigator.php:39
msgid "Close navigator"
msgstr "关闭导航器"

#: includes/editor-templates/navigator.php:39
msgid "Close structure"
msgstr "关闭结构"

#: includes/editor-templates/navigator.php:35 assets/js/editor.js:37629
msgid "Expand all elements"
msgstr "展开所有元素"

#: includes/editor-templates/global.php:52
msgid "Which layout would you like to use?"
msgstr "您想使用哪种布局？"

#: includes/editor-templates/global.php:9
msgid "Select your structure"
msgstr "选择您的结构"

#: includes/controls/base-units.php:130
msgid "Switch units"
msgstr "切换单位"

#: modules/floating-buttons/base/widget-contact-button-base.php:116
msgid "Top Bar"
msgstr "顶栏"

#. translators: %s: Document title.
#: core/editor/loader/v1/templates/editor-body-v1-view.php:27
#: core/editor/loader/v2/templates/editor-body-v2-view.php:27
#: includes/editor-templates/editor-wrapper.php:30
#: assets/js/packages/editor-documents/editor-documents.js:2
#: assets/js/packages/editor-documents/editor-documents.strings.js:2
msgid "Edit \"%s\" with Elementor"
msgstr "使用 Elementor 编辑 “%s”"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/document-types/page-base.php:100
msgid "Set a different selector for the title in the %1$sLayout panel%2$s."
msgstr "在%1$s布局面板%2$s中为标题设置不同的选择器。"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:118
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:35
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:180
msgid "Row"
msgstr "行"

#: includes/controls/groups/grid-container.php:71
msgid "Rows"
msgstr "行数"

#: core/kits/documents/tabs/settings-layout.php:100
#: includes/controls/groups/flex-container.php:156
#: includes/controls/groups/grid-container.php:96
#: includes/elements/container.php:497
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:167
msgid "Gaps"
msgstr "间距"

#: includes/controls/groups/grid-container.php:115
msgid "Auto Flow"
msgstr "Auto Flow (排列)"

#: includes/editor-templates/global.php:68 includes/elements/container.php:96
#: includes/elements/container.php:104 includes/elements/container.php:366
#: assets/js/editor.js:35729 assets/js/editor.js:44570
msgid "Grid"
msgstr "网格"

#: includes/controls/groups/grid-container.php:31
msgid "Grid Outline"
msgstr "网格轮廓"

#: includes/editor-templates/global.php:60 includes/elements/container.php:365
#: modules/atomic-widgets/elements/flexbox/flexbox.php:23
#: modules/library/documents/flexbox.php:52 assets/js/editor.js:10578
msgid "Flexbox"
msgstr "Flexbox (弹性盒)"

#: includes/widgets/icon-list.php:599
msgid "Adjust Vertical Position"
msgstr "调整垂直位置"

#: includes/elements/column.php:211 includes/widgets/icon-list.php:542
msgid "Horizontal Alignment"
msgstr "水平对齐"

#: modules/editor-app-bar/module.php:43
msgid "Editor Top Bar"
msgstr "编辑器顶栏"

#: core/breakpoints/manager.php:329
msgid "Tablet Landscape"
msgstr "平板电脑横向"

#: core/breakpoints/manager.php:324
msgid "Tablet Portrait"
msgstr "平板电脑纵向"

#: core/admin/admin-notices.php:624 core/admin/admin.php:1036
msgid "Dismiss this notice."
msgstr "忽略此通知。"

#: includes/widgets/alert.php:506 includes/widgets/alert.php:561
msgid "Dismiss this alert."
msgstr "忽略此提醒"

#: modules/editor-app-bar/module.php:46
msgid "Get a sneak peek of the new Editor powered by React. The beautiful design and experimental layout of the Top bar are just some of the exciting tools on their way."
msgstr "先睹为快，了解由 React 提供支持的新编辑器。顶部栏的精美设计和实验性布局只是他们即将推出的一些令人兴奋的工具。"

#: modules/generator-tag/module.php:84
msgid "A generator tag is a meta element that indicates the attributes used to create a webpage. It is used for analytical purposes."
msgstr "生成器标签是一个元元素，指示用于创建网页的属性。它用于分析目的。"

#: modules/generator-tag/module.php:76
msgid "Generator Tag"
msgstr "生成器标签"

#. translators: %d: Number of rows.
#: includes/utils.php:254
msgid "%d database row affected."
msgid_plural "%d database rows affected."
msgstr[0] "%d 个数据库行受到影响。"

#: core/kits/documents/tabs/theme-style-form-fields.php:188
msgid "Accent Color"
msgstr "强调色"

#: core/experiments/manager.php:546
msgid "Deactivate All"
msgstr "全部停用"

#: core/experiments/manager.php:545
msgid "Activate All"
msgstr "全部激活"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/experiments/manager.php:528
msgid "Personalize your Elementor experience by controlling which features and experiments are active on your site. Help make Elementor better by %1$ssharing your experience and feedback with us%2$s."
msgstr "通过控制网站上哪些功能和实验处于活动状态，个性化您的 Elementor 体验。 %1$s与我们分享您的经验和反馈%2$s，帮助让 Elementor 变得更好。"

#: core/experiments/manager.php:522
msgid "Experiments and Features"
msgstr "实验和特点"

#: modules/nested-tabs/widgets/nested-tabs.php:713
msgid "Titles"
msgstr "标题"

#: includes/widgets/video.php:853
msgid "Shadow"
msgstr "阴影"

#: modules/nested-accordion/widgets/nested-accordion.php:427
#: modules/nested-tabs/widgets/nested-tabs.php:469
msgid "Distance from content"
msgstr "与内容的距离"

#: modules/nested-tabs/widgets/nested-tabs.php:449
msgid "Gap between tabs"
msgstr "标签之间的间距"

#: modules/nested-tabs/widgets/nested-tabs.php:434
msgid "Note: Choose at which breakpoint tabs will automatically switch to a vertical (“accordion”) layout."
msgstr "注意：选择在哪个断点选项卡将自动切换到垂直（“手风琴”）布局。"

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#: modules/nested-tabs/widgets/nested-tabs.php:422
msgid "%1$s (%2$s %3$dpx)"
msgstr "%1$s (%2$s %3$dpx)"

#: modules/nested-tabs/widgets/nested-tabs.php:342
msgid "Align Title"
msgstr "对齐标题"

#: modules/nested-tabs/widgets/nested-tabs.php:178
msgid "Tab #3"
msgstr "Tab #3"

#: modules/nested-tabs/widgets/nested-tabs.php:80
msgid "Tab #%d"
msgstr "Tab #%d"

#: modules/nested-tabs/widgets/nested-tabs.php:61
msgid "Tab #%s"
msgstr "Tab #%s"

#: modules/nested-elements/module.php:17
msgid "Nested Elements"
msgstr "嵌套元素"

#: includes/widgets/video.php:577
msgid "Metadata"
msgstr "Meta数据"

#: includes/widgets/video.php:574
msgid "Preload"
msgstr "预加载"

#: includes/settings/settings.php:374
msgid "Disable this option if you want to prevent Google Fonts from being loaded. This setting is recommended when loading fonts from a different source (plugin, theme or %1$scustom fonts%2$s)."
msgstr "如果您想阻止加载 Google 字体，请禁用此选项。当从不同来源（插件、主题或%1$s自定义字体%2$s）加载字体时，建议使用此设置。"

#: includes/settings/settings.php:365
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:245
msgid "Google Fonts"
msgstr "Google 字体"

#: includes/widgets/tabs.php:153
msgid "You are currently editing a Tabs Widget in its old version. Any new tabs widget dragged into the canvas will be the new Tab widget, with the improved Nested capabilities."
msgstr "您当前正在编辑旧版本的选项卡小部件。拖到画布中的任何新选项卡小部件都将是新的选项卡小部件，具有改进的嵌套功能。"

#: modules/nested-elements/module.php:20
msgid "Create a rich user experience by layering widgets together inside \"Nested\" Tabs, etc. When turned on, we’ll automatically enable new nested features. Your old widgets won’t be affected."
msgstr "通过在“嵌套”选项卡等中将小部件分层在一起来创建丰富的用户体验。打开后，我们将自动启用新的嵌套功能。您的旧小部件不会受到影响。"

#: includes/widgets/video.php:583
msgid "Preload attribute lets you specify how the video should be loaded when the page loads."
msgstr "预加载属性可让您指定页面加载时应如何加载视频。"

#: includes/settings/settings.php:458
msgid "Lazy Load Background Images"
msgstr "延迟加载背景图像"

#: includes/elements/container.php:1933
msgid "Note: Avoid applying transform properties on sticky containers. Doing so might cause unexpected results."
msgstr "注意：避免在粘性容器上应用 transform 属性。这样做可能会导致意想不到的结果。"

#: includes/elements/container.php:566
msgid "(link)"
msgstr "(链接)"

#: includes/controls/groups/typography.php:150
msgctxt "Typography Control"
msgid "(Black)"
msgstr "(Black)"

#: includes/controls/groups/typography.php:149
msgctxt "Typography Control"
msgid "(Extra Bold)"
msgstr "(Extra Bold)"

#: includes/controls/groups/typography.php:148
msgctxt "Typography Control"
msgid "(Bold)"
msgstr "(Bold)"

#: includes/controls/groups/typography.php:147
msgctxt "Typography Control"
msgid "(Semi Bold)"
msgstr "(Semi Bold)"

#: includes/controls/groups/typography.php:146
msgctxt "Typography Control"
msgid "(Medium)"
msgstr "(Medium)"

#: includes/controls/groups/typography.php:145
msgctxt "Typography Control"
msgid "(Normal)"
msgstr "(Normal)"

#: includes/controls/groups/typography.php:144
msgctxt "Typography Control"
msgid "(Light)"
msgstr "(Light)"

#: includes/controls/groups/typography.php:143
msgctxt "Typography Control"
msgid "(Extra Light)"
msgstr "(Extra Light)"

#: includes/controls/groups/typography.php:142
msgctxt "Typography Control"
msgid "(Thin)"
msgstr "(Thin)"

#: core/experiments/manager.php:618
msgid "Requires"
msgstr "需要"

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:116
#: modules/apps/admin-apps-page.php:147
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:795
#: assets/js/editor-v4-opt-in.js:357
msgid "Activate"
msgstr "激活"

#: app/modules/import-export/module.php:235
msgid "Remove Kit"
msgstr "移除套件"

#: app/modules/import-export/module.php:228
msgid "Remove the most recent Kit"
msgstr "删除最新的套件"

#: app/modules/import-export/module.php:190
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."
msgstr "删除 %2$s 上“%1$s”附带的所有内容和站点设置。%3$s 将恢复您的原始站点设置。"

#: app/modules/import-export/module.php:182
#: app/modules/import-export/module.php:185
#: app/modules/import-export/module.php:191
msgid "imported kit"
msgstr "已导入套件"

#: app/modules/import-export/module.php:181
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."
msgstr "删除 %2$s %3$s 上“%1$s”附带的所有内容和网站设置，并恢复为 %5$s 上“%4$s”附带的网站设置。"

#: includes/widgets/image-carousel.php:282
msgid "Next Arrow Icon"
msgstr "右箭头图标"

#: includes/widgets/image-carousel.php:227
msgid "Previous Arrow Icon"
msgstr "左箭头图标"

#: includes/widgets/alert.php:394
#: modules/floating-buttons/base/widget-contact-button-base.php:2934
msgid "Horizontal Position"
msgstr "水平位置"

#: includes/widgets/alert.php:376
#: modules/floating-buttons/base/widget-contact-button-base.php:2988
#: modules/floating-buttons/base/widget-floating-bars-base.php:1433
msgid "Vertical Position"
msgstr "垂直位置"

#: includes/widgets/alert.php:161 includes/widgets/alert.php:342
msgid "Dismiss Icon"
msgstr "忽略图标"

#: core/settings/editor-preferences/model.php:231
msgid "WP Dashboard"
msgstr "WP仪表盘"

#: core/settings/editor-preferences/model.php:230
msgid "All Posts"
msgstr "所有文章"

#: core/settings/editor-preferences/model.php:229
msgid "This Post"
msgstr "此文章"

#: core/settings/editor-preferences/model.php:225
msgid "Exit to"
msgstr "退出前往"

#: core/kits/documents/tabs/settings-layout.php:91
msgid "Sets the default space inside the container (Default is 10px)"
msgstr "设置容器内元素的默认间隔（默认10像素）"

#: core/kits/documents/tabs/settings-layout.php:88
msgid "Container Padding"
msgstr "容器内距"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:313 core/experiments/manager.php:370
#: core/experiments/manager.php:380 includes/settings/settings.php:400
#: includes/settings/settings.php:403 modules/element-cache/module.php:47
msgid "Performance"
msgstr "性能"

#: core/admin/admin-notices.php:364
msgid "Try it out"
msgstr "试试看"

#. Translators: %s is the current item index.
#: modules/nested-accordion/widgets/nested-accordion.php:85
#: modules/nested-elements/base/widget-nested-base.php:45
#: assets/js/editor.js:27362
msgid "Item #%d"
msgstr "项目 #%d"

#: includes/elements/container.php:587
msgid "Don’t add links to elements nested in this container - it will break the layout."
msgstr "不要向嵌套在该容器中的元素添加链接 - 这会破坏布局。"

#: includes/editor-templates/panel.php:60
msgid "Any time you can change the settings in %1$sUser Preferences%2$s"
msgstr "您可以随时更改%1$s用户首选项%2$s中的设置"

#: includes/editor-templates/panel.php:56
msgid "Now you can choose where you want to go on the site from the following options"
msgstr "现在您可以从以下选项中选择您想要访问该网站的位置"

#: core/admin/admin-notices.php:361
msgid "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."
msgstr "借助我们的实验性提速功能，您可以比以往更快。在我们的实验页面上查找性能标签并激活这些实验以提高您的网站加载速度。"

#: core/admin/admin-notices.php:360
msgid "Improve your site’s performance score."
msgstr "提高网站的性能得分。"

#: app/modules/onboarding/module.php:158
msgid "There was a problem setting your site name."
msgstr "设置您的网站名称时出现问题。"

#: core/editor/notice-bar.php:45 assets/js/element-manager-admin.js:2352
msgid "Unleash the full power of Elementor's features and web creation tools."
msgstr "释放 Elementor 功能和 Web 创建工具的全部威力。"

#: includes/editor-templates/hotkeys.php:191 assets/js/notes.js:136
#: assets/js/notes.js:140 assets/js/notes.js:226
msgid "Notes"
msgstr "备注"

#: core/editor/notice-bar.php:41 core/editor/promotion.php:34
#: includes/editor-templates/navigator.php:20
#: includes/editor-templates/panel-elements.php:29
#: includes/editor-templates/panel-elements.php:34
#: includes/editor-templates/panel-elements.php:101
#: includes/managers/controls.php:1161 includes/widgets/image-gallery.php:110
#: modules/admin-top-bar/module.php:79
#: modules/checklist/steps/setup-header.php:93
#: modules/element-manager/ajax.php:73 modules/element-manager/ajax.php:80
#: modules/element-manager/ajax.php:88
#: modules/promotions/admin-menu-items/base-promotion-item.php:32
#: modules/promotions/admin-menu-items/base-promotion-template.php:37
#: modules/promotions/admin-menu-items/popups-promotion-item.php:24
#: modules/promotions/promotion-data.php:48
#: modules/promotions/promotion-data.php:65
#: modules/promotions/promotion-data.php:82
#: modules/promotions/promotion-data.php:99
#: modules/promotions/promotion-data.php:116 assets/js/app-packages.js:2413
#: assets/js/app-packages.js:5590 assets/js/app-packages.js:5856
#: assets/js/app.js:2727 assets/js/checklist.js:298 assets/js/editor.js:8274
#: assets/js/editor.js:12969 assets/js/editor.js:55394
#: assets/js/editor.js:55409 assets/js/editor.js:55424
#: assets/js/editor.js:55439
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:516
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1464
msgid "Upgrade Now"
msgstr "立即升级"

#: core/admin/admin.php:632 core/role-manager/role-manager.php:241
#: includes/editor-templates/panel-elements.php:40
#: includes/editor-templates/panel-elements.php:46
#: includes/editor-templates/panel-elements.php:65
#: includes/editor-templates/panel.php:328
#: includes/editor-templates/templates.php:513
#: includes/managers/controls.php:1152 includes/widgets/image-gallery.php:107
#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:30
#: modules/promotions/promotion-data.php:41
#: modules/promotions/promotion-data.php:58
#: modules/promotions/promotion-data.php:75
#: modules/promotions/promotion-data.php:92
#: modules/promotions/promotion-data.php:109 assets/js/ai-admin.js:1024
#: assets/js/ai-admin.js:2966 assets/js/ai-admin.js:3095
#: assets/js/ai-gutenberg.js:2792 assets/js/ai-gutenberg.js:4734
#: assets/js/ai-gutenberg.js:4863 assets/js/ai-layout.js:722
#: assets/js/ai-layout.js:1002 assets/js/ai-media-library.js:2653
#: assets/js/ai-media-library.js:4595 assets/js/ai-media-library.js:4724
#: assets/js/ai-unify-product-images.js:2653
#: assets/js/ai-unify-product-images.js:4595
#: assets/js/ai-unify-product-images.js:4724 assets/js/ai.js:3438
#: assets/js/ai.js:5380 assets/js/ai.js:5509 assets/js/app-packages.js:5821
#: assets/js/e-react-promotions.js:196 assets/js/editor.js:6791
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1320
#: assets/js/notes.js:149
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:770
#: assets/js/styleguide.js:196
msgid "Upgrade"
msgstr "升级"

#: modules/announcements/module.php:118
msgid "Let's do it"
msgstr "我们开始行动吧"

#: includes/controls/groups/typography.php:153
msgid "Bold"
msgstr "粗体"

#: modules/container-converter/module.php:86
#: modules/container-converter/module.php:119
msgid "Convert"
msgstr "转换"

#: modules/container-converter/module.php:85
#: modules/container-converter/module.php:118
msgid "Convert to container"
msgstr "转换为容器"

#: includes/controls/groups/background.php:727
#: includes/widgets/image-carousel.php:423
msgid "Lazyload"
msgstr "延迟加载"

#: core/kits/documents/tabs/global-typography.php:160
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:227
msgid "System Fonts"
msgstr "系统字体"

#: core/kits/documents/tabs/global-colors.php:101
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:178
msgid "System Colors"
msgstr "系统颜色"

#: core/common/modules/finder/categories/tools.php:68
msgid "Import Export"
msgstr "导入导出"

#: core/common/modules/event-tracker/personal-data.php:24
msgid "Elementor Event Tracker"
msgstr "Elementor 事件跟踪器"

#: core/experiments/manager.php:342 includes/elements/container.php:72
#: includes/elements/container.php:344
#: modules/library/documents/container.php:52 assets/js/editor.js:10576
#: assets/js/editor.js:35729 assets/js/editor.js:41372
msgid "Container"
msgstr "容器"

#: includes/widgets/image-carousel.php:624
msgid "Pagination"
msgstr "分页"

#: elementor.php:78 elementor.php:102 assets/js/ai-admin.js:1063
#: assets/js/ai-gutenberg.js:2831 assets/js/ai-layout.js:761
#: assets/js/ai-media-library.js:2692 assets/js/ai-unify-product-images.js:2692
#: assets/js/ai.js:3477
msgid "Show me how"
msgstr "告诉我怎么做"

#: modules/container-converter/module.php:88
#: modules/container-converter/module.php:121
msgid "Copies all of the selected sections and columns and pastes them in a container beneath the original."
msgstr "复制所有选定的节和列，并将它们粘贴到原始文件下方的容器中。"

#: includes/widgets/video.php:989
msgid "Play Video about"
msgstr "播放有关的视频"

#: includes/elements/column.php:461 includes/elements/section.php:730
#: includes/widgets/heading.php:323
msgid "Hue"
msgstr "色调"

#: includes/elements/column.php:460 includes/elements/section.php:729
#: includes/widgets/heading.php:322
msgid "Exclusion"
msgstr "排除"

#: includes/elements/column.php:459 includes/elements/section.php:728
#: includes/widgets/heading.php:321
msgid "Difference"
msgstr "不同之处"

#: core/kits/views/panel.php:36
msgid "Reorder"
msgstr "重新排序"

#: includes/elements/container.php:481
msgid "To achieve full height Container use %s."
msgstr "要实现容器的全高度，请使用 %s。"

#: includes/editor-templates/global.php:33 assets/js/editor.js:35588
msgid "Add New Container"
msgstr "添加新容器 (同层级)"

#: app/modules/onboarding/module.php:215
msgid "There was a problem setting your site logo."
msgstr "设置您的网站 Logo 时出现问题"

#: app/modules/onboarding/module.php:265 app/modules/onboarding/module.php:350
msgid "There was a problem uploading your file."
msgstr "上传您的文件时出现问题。"

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "添加新的页面模板"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "自定义代码"

#: includes/admin-templates/beta-tester.php:53 assets/js/ai-admin.js:6389
#: assets/js/ai-admin.js:15922 assets/js/ai-gutenberg.js:8237
#: assets/js/ai-gutenberg.js:17770 assets/js/ai-layout.js:2491
#: assets/js/ai-layout.js:5208 assets/js/ai-media-library.js:8018
#: assets/js/ai-media-library.js:17551
#: assets/js/ai-unify-product-images.js:8018
#: assets/js/ai-unify-product-images.js:17551 assets/js/ai.js:9380
#: assets/js/ai.js:9483 assets/js/ai.js:19016
msgid "Privacy Policy"
msgstr "隐私政策"

#: includes/admin-templates/beta-tester.php:48 assets/js/ai-admin.js:6385
#: assets/js/ai-admin.js:15918 assets/js/ai-gutenberg.js:8233
#: assets/js/ai-gutenberg.js:17766 assets/js/ai-layout.js:2487
#: assets/js/ai-layout.js:5204 assets/js/ai-media-library.js:8014
#: assets/js/ai-media-library.js:17547
#: assets/js/ai-unify-product-images.js:8014
#: assets/js/ai-unify-product-images.js:17547 assets/js/ai.js:9376
#: assets/js/ai.js:9479 assets/js/ai.js:19012
msgid "Terms of Service"
msgstr "服务条款"

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:6791 assets/js/notes.js:149 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "连接服务器并激活"

#: includes/base/element-base.php:987
msgid "Perspective"
msgstr "透明度"

#: includes/base/element-base.php:1022
msgid "Offset X"
msgstr "X轴偏移量"

#: includes/base/element-base.php:964
msgid "Rotate Y"
msgstr "水平翻转"

#: includes/base/element-base.php:941
msgid "Rotate X"
msgstr "上下翻转"

#: modules/usage/usage-reporter.php:22
msgid "Elements Usage"
msgstr "元素使用"

#. translators: 1: Integration settings link open tag, 2: Create API key link
#. open tag, 3: Link close tag.
#: includes/widgets/google-maps.php:140
msgid "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"
msgstr "在 Elementor 的 %1$sIntegrations Settings%3$s 页面中设置您的 Google Maps API 密钥。创建您的密钥 %2$shere.%3$s"

#: includes/base/element-base.php:1346
msgid "Y Anchor Point"
msgstr "Y 锚点"

#: includes/base/element-base.php:1318
msgid "X Anchor Point"
msgstr "X 锚点"

#: includes/base/element-base.php:1244 includes/base/element-base.php:1248
msgid "Flip Vertical"
msgstr "垂直翻转"

#: includes/base/element-base.php:1225 includes/base/element-base.php:1229
msgid "Flip Horizontal"
msgstr "水平翻转"

#: includes/base/element-base.php:1179
msgid "Skew X"
msgstr "倾斜 X"

#: includes/base/element-base.php:1201
msgid "Skew Y"
msgstr "倾斜 Y"

#: includes/base/element-base.php:1167
msgid "Skew"
msgstr "倾斜"

#: includes/base/element-base.php:1143
msgid "Scale Y"
msgstr "缩放 Y"

#: includes/base/element-base.php:1121
msgid "Scale X"
msgstr "缩放 X"

#: includes/base/element-base.php:1088
msgid "Keep Proportions"
msgstr "保持比例"

#: includes/base/element-base.php:1048
msgid "Offset Y"
msgstr "偏移 Y"

#: includes/base/element-base.php:924
msgid "3D Rotate"
msgstr "3D 旋转"

#: includes/controls/groups/text-stroke.php:85
msgid "Stroke Color"
msgstr "描边颜色"

#: includes/controls/groups/text-stroke.php:60
#: includes/controls/groups/text-stroke.php:111
msgid "Text Stroke"
msgstr "文字描边"

#: core/logger/log-reporter.php:25
msgid "Log"
msgstr "日志"

#: core/experiments/manager.php:551
msgid "Ongoing Experiments"
msgstr "正在进行的实验"

#: core/experiments/manager.php:508
msgid "Stable Features"
msgstr "稳定的特性"

#: includes/base/element-base.php:861
msgid "Transform"
msgstr "变换"

#. translators: 1. "Terms of service" link, 2. "Privacy policy" link
#: includes/admin-templates/beta-tester.php:44
msgid "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"
msgstr "单击“注册”即表示您同意 Elementor 的 %1$s 和 %2$s"

#: core/editor/data/globals/endpoints/base.php:46
msgid "Invalid title"
msgstr "标题无效"

#: core/experiments/manager.php:316
msgid "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."
msgstr "“内联字体图标”会将图标渲染为内联 SVG，而无需加载 Font-Awesome 和 eicons 库及其相关 CSS 文件和字体。"

#: core/experiments/manager.php:312
msgid "Inline Font Icons"
msgstr "内联字体图标"

#: core/experiments/experiments-reporter.php:21
msgid "Elementor Experiments"
msgstr "Elementor 实验"

#: includes/settings/tools.php:158
msgid "Not allowed to rollback versions"
msgstr "不允许回滚版本"

#: modules/page-templates/module.php:316
msgid "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."
msgstr "默认页面模板在 Elementor 面板 → 汉堡菜单 → 站点设置中定义。"

#: includes/elements/column.php:462 includes/elements/container.php:856
#: includes/elements/section.php:727 includes/widgets/heading.php:324
msgid "Luminosity"
msgstr "亮度"

#: includes/elements/column.php:457 includes/elements/container.php:854
#: includes/elements/section.php:725 includes/widgets/heading.php:319
msgid "Saturation"
msgstr "饱和度"

#: includes/elements/column.php:455 includes/elements/container.php:852
#: includes/elements/section.php:723 includes/widgets/heading.php:317
msgid "Lighten"
msgstr "变亮"

#: includes/elements/column.php:454 includes/elements/container.php:851
#: includes/elements/section.php:722 includes/widgets/heading.php:316
msgid "Darken"
msgstr "变暗"

#: includes/elements/column.php:453 includes/elements/container.php:850
#: includes/elements/section.php:721 includes/widgets/heading.php:315
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:82
msgid "Overlay"
msgstr "覆盖"

#: includes/elements/column.php:452 includes/elements/container.php:849
#: includes/elements/section.php:720 includes/widgets/heading.php:314
msgid "Screen"
msgstr "屏幕"

#: includes/elements/column.php:451 includes/elements/container.php:848
#: includes/elements/section.php:719 includes/widgets/heading.php:313
msgid "Multiply"
msgstr "多个"

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1120
msgid "Page Transitions"
msgstr "翻页效果"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:290
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "请注意！我们无法在安全模式下停用您的所有插件。请 %1$s阅读更多%2$s 有关此问题的信息"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:261 modules/safe-mode/module.php:274
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$s单击此处%2$s进行故障排除"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:383
msgid "Or view %1$sTrashed Items%1$s"
msgstr "或查看%1$s已删除的项目%1$s"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "了解有关 %1$sWordPress 修订%2$s 的更多信息"

#. translators: 1: Function argument, 2: Elementor version number.
#: modules/dev-tools/deprecation.php:292
msgid "The %1$s argument is deprecated since version %2$s!"
msgstr "自版本 %2$s 起，%1$s 参数已被弃用！"

#. translators: 1: Function argument, 2: Elementor version number, 3:
#. Replacement argument name.
#: modules/dev-tools/deprecation.php:288
msgid "The %1$s argument is deprecated since version %2$s! Use %3$s instead."
msgstr "自版本 %2$s 起，%1$s 参数已被弃用！请改用 %3$s。"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:403
msgid "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"
msgstr "%1$s单击此处%2$s 加入我们的第一时间了解电子邮件更新。"

#: includes/managers/controls.php:1126
msgid "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."
msgstr "页面过渡允许您设计页面之间的进入和退出动画以及显示加载程序，直到页面资源加载。"

#: includes/managers/controls.php:1138
msgid "Meet Page Transitions"
msgstr "页面切换过渡效果"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "选择一个或立即继续%1$s创建一个%2$s。"

#: includes/elements/column.php:456 includes/elements/container.php:853
#: includes/elements/section.php:724 includes/widgets/heading.php:318
msgid "Color Dodge"
msgstr "颜色减淡"

#. translators: %d: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:26
msgid "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"
msgstr "宽屏 <br> 为宽屏设备添加的设置将应用于屏幕尺寸 %dpx 及以上"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:213
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "您可以从 %1$sElementor 设置页面%2$s 启用它。"

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "模板帮助您%1$s高效工作%2$s"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "为了使主题样式影响所有相关 Elementor 元素，请从 %1$s设置页面%2$s 禁用默认颜色和字体。"

#: core/kits/documents/tabs/settings-layout.php:371
msgid "Widescreen breakpoint settings will apply from the selected value and up."
msgstr "宽屏断点设置将从所选值开始应用。"

#: core/experiments/manager.php:332
msgid "Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen."
msgstr "获得适合每种屏幕尺寸的像素完美设计。现在，除了默认桌面设置之外，您还可以添加最多 6 个可自定义断点：​​移动设备、移动额外设备、平板电脑、平板电脑额外设备、笔记本电脑和宽屏设备。"

#: core/experiments/manager.php:329
msgid "Additional Custom Breakpoints"
msgstr "额外的自定义断点"

#. translators: %s: Device name.
#: includes/base/element-base.php:1393
msgid "Hide On %s"
msgstr "在 %s 隐藏"

#: includes/managers/elements.php:328
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3662
msgid "Favorites"
msgstr "最爱"

#: core/common/modules/finder/categories/settings.php:74
#: core/experiments/manager.php:485
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3286
msgid "Features"
msgstr "特性"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3277
msgid "Categories"
msgstr "分类"

#: modules/nested-accordion/widgets/nested-accordion.php:554
#: assets/js/app-packages.js:5346
msgid "Header"
msgstr "页眉"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "提交"

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "区段"

#: includes/settings/tools.php:435
msgid "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."
msgstr "您的网站似乎没有任何有效的套件。活动套件包括您的所有站点设置。通过重新创建您的工具包，您将能够再次开始编辑您的站点设置。"

#: includes/settings/tools.php:431 includes/settings/tools.php:434
#: assets/js/editor.js:30987
msgid "Recreate Kit"
msgstr "重建套件"

#: includes/settings/tools.php:112
msgid "New kit have been created successfully"
msgstr "新套件已成功创建"

#: includes/settings/tools.php:107
msgid "An error occurred while trying to create a kit."
msgstr "尝试创建套件时发生错误。"

#: includes/settings/tools.php:101
msgid "There's already an active kit."
msgstr "已经有一个活跃的套件。"

#: includes/editor-templates/panel.php:302 assets/js/editor.js:15912
msgid "Color Sampler"
msgstr "颜色采样器"

#: core/settings/editor-preferences/model.php:119
msgid "Default device view"
msgstr "默认设备视图 "

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "未找到套件"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "套件不存在。"

#: app/modules/kit-library/connect/kit-library.php:16
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:34 app/modules/kit-library/module.php:35
#: core/common/modules/finder/categories/general.php:78
#: assets/js/import-export-admin.js:300
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1538
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3715
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4153
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4453
msgid "Kit Library"
msgstr "套件库"

#: includes/settings/tools.php:341 assets/js/app.js:7614 assets/js/app.js:8335
msgid "Important:"
msgstr "重要"

#: modules/global-classes/global-classes-rest-api.php:168
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:26
msgid "Something went wrong"
msgstr "出了点问题"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "兼容"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "不兼容"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:311
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "Google Maps Embed API 是 Google 提供的一项免费服务，允许在您的网站中嵌入 Google 地图。有关更多详细信息，请访问 Google 地图的%1$s使用 API 密钥%2$s 页面。"

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "Google 地图嵌入 API"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "兼容性未知"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "未指定兼容性"

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "API 密钥"

#: core/admin/admin-notices.php:323
msgid "Managing a multi-user site?"
msgstr "管理多用户站点？"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1191
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:98
msgid "No-repeat"
msgstr "不重复"

#: includes/widgets/common-base.php:1195
#: modules/floating-buttons/base/widget-contact-button-base.php:2083
#: modules/floating-buttons/base/widget-contact-button-base.php:2174
#: modules/floating-buttons/base/widget-contact-button-base.php:2867
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "圆形"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1188 includes/widgets/common-base.php:1192
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:95
#: assets/js/packages/editor-controls/editor-controls.strings.js:99
msgid "Repeat"
msgstr "平铺"

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1152
msgid "Y Position"
msgstr "Y轴坐标"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1116
msgid "X Position"
msgstr "X轴坐标"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1102 includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
msgid "Bottom Right"
msgstr "右下角"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1101 includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
msgid "Bottom Left"
msgstr "左下角"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1100 includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
msgid "Bottom Center"
msgstr "底部中心"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1099 includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
msgid "Top Right"
msgstr "顶部右侧"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1098 includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
msgid "Top Left"
msgstr "顶部左侧"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1097 includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
msgid "Top Center"
msgstr "顶部中心"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1096 includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
msgid "Center Right"
msgstr "中间右侧"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1095 includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
msgid "Center Left"
msgstr "中间左侧"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1094 includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
msgid "Center Center"
msgstr "中心居中"

#: includes/base/element-base.php:1076 includes/base/element-base.php:1099
#: includes/widgets/common-base.php:1059
msgid "Scale"
msgstr "缩放"

#: includes/widgets/common-base.php:1044
msgid "Fit"
msgstr "适应"

#: includes/widgets/common-base.php:1024
msgid "Need More Shapes?"
msgstr "需要更多的图形？"

#: includes/widgets/common-base.php:140
msgid "Hexagon"
msgstr "六角形"

#: includes/widgets/common-base.php:138
msgid "Triangle"
msgstr "三角形"

#: includes/widgets/common-base.php:139
msgid "Blob"
msgstr "墨迹"

#: includes/widgets/common-base.php:137
msgid "Sketch"
msgstr "草图"

#: includes/widgets/common-base.php:136
msgid "Flower"
msgstr "花"

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:307
msgid "FAQ Schema"
msgstr "解答 (FAQ) 页"

#: includes/settings/settings.php:392
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "font-display 属性定义浏览器如何加载和显示字体文件。"

#: includes/settings/settings.php:390
msgid "Optional"
msgstr "Optional"

#: includes/settings/settings.php:388
msgid "Swap"
msgstr "Swap"

#: includes/settings/settings.php:387
msgid "Blocking"
msgstr "Blocking"

#: includes/settings/settings.php:381
msgid "Google Fonts Load"
msgstr "加载 Google Fonts"

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "管理断点"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "全宽屏"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "数据库更新程序在后台运行。需要时间吗?"

#: includes/widgets/common-base.php:971 includes/widgets/common-base.php:979
msgid "Mask"
msgstr "遮罩"

#: includes/controls/groups/background.php:465
#: includes/widgets/common-base.php:1193
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:96
msgid "Repeat-x"
msgstr "平铺 - X轴"

#: includes/controls/groups/background.php:466
#: includes/widgets/common-base.php:1194
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:97
msgid "Repeat-y"
msgstr "平铺 - Y轴"

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "桌面 <br> 为基本设备添加的设置将应用于所有断点，除非进行编辑"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> 为 %1$s 设备添加的设置将应用于 %2$spx 屏幕及以下屏幕"

#: core/admin/admin-notices.php:324
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "借助 Elementor Pro，您可以控制用户访问并确保没有人弄乱您的设计。"

#: includes/settings/settings.php:392
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Recommended: Swap)."
msgstr "通过选择 font-display 属性（推荐：Swap）来设置 Google 字体的加载方式。"

#: app/modules/import-export/module.php:119
msgid "Template Kits"
msgstr "模板库"

#: app/modules/import-export/module.php:116
msgid "Import / Export Kit"
msgstr "导入/导出 库"

#: app/modules/import-export/module.php:159
msgid "Start Import"
msgstr "开始导入"

#: app/modules/import-export/module.php:156
msgid "Import a Template Kit"
msgstr "导入一个模板库"

#: app/modules/import-export/module.php:147
msgid "Start Export"
msgstr "开始导出"

#: app/modules/import-export/module.php:161
msgid "Apply the design and settings of another site to this one."
msgstr "将其他站点的设计和设置应用于此站点。"

#: app/modules/import-export/module.php:149
msgid "Bundle your whole site - or just some of its elements - to be used for another website."
msgstr "打包您的整个网站 - 或者其中的一些元素 - 用于另一个网站。"

#: app/modules/import-export/module.php:144
msgid "Export a Template Kit"
msgstr "导出一个模板库"

#. translators: 1: New line break, 2: Learn more link.
#: app/modules/import-export/module.php:137
msgid "Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s"
msgstr "使用包含完整网站的部分或所有组件(如模板、内容和网站设置)的模板套件更快地设计网站。%1$s 您可以导入一个工具包并将其应用到您的站点，或者从该站点导出元素以便在其他任何地方使用。%2$s"

#: core/admin/admin-notices.php:232
msgid "Love using Elementor?"
msgstr "喜欢使用 Elementor?"

#: app/modules/import-export/module.php:133 core/admin/admin-notices.php:369
#: core/admin/admin-notices.php:425 core/admin/admin-notices.php:473
#: core/experiments/manager.php:317 core/experiments/manager.php:333
#: core/experiments/manager.php:362 core/experiments/manager.php:539
#: includes/controls/url.php:78 includes/elements/section.php:473
#: includes/settings/settings-page.php:404
#: includes/widgets/common-base.php:1029 includes/widgets/video.php:584
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:102
#: modules/editor-app-bar/module.php:47 modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:150 assets/js/app.js:7604
#: assets/js/editor-v4-opt-in-alphachip.js:187
#: assets/js/editor-v4-opt-in.js:500 assets/js/editor-v4-welcome-opt-in.js:86
#: assets/js/editor.js:30089
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3497
msgid "Learn more"
msgstr "了解更多"

#: core/utils/import-export/wp-import.php:1127
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "对不起，出于安全原因，不允许使用此文件类型。"

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1095
msgid "Remote file is too large, limit is %s"
msgstr "远程文件太大，限制为 %s"

#: core/utils/import-export/wp-import.php:1087
msgid "Downloaded file has incorrect size"
msgstr "下载的文件大小不正确"

#: core/utils/import-export/wp-import.php:1081
msgid "Zero size file downloaded"
msgstr "下载的文件大小为零"

#: core/utils/import-export/wp-import.php:1073
msgid "Remote server did not respond"
msgstr "远程服务器没有响应"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1055
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "由于错误导致请求失败： %1$s （ %2$s ）"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1064
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "远程服务器返回意外结果： %1$s （ %2$s ）"

#: core/utils/import-export/wp-import.php:1143
msgid "The uploaded file could not be moved"
msgstr "上传的文件无法移动"

#: core/utils/import-export/wp-import.php:1039
msgid "Could not create temporary file."
msgstr "无法创建临时文件。"

#: core/utils/import-export/wp-import.php:995
msgid "Invalid file type"
msgstr "无效的文件类型"

#: core/utils/import-export/wp-import.php:978
msgid "Fetching attachments is not enabled"
msgstr "获取附件未启用"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:891
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "菜单项由于菜单项别名无效被跳过：%s"

#: core/utils/import-export/wp-import.php:878
msgid "Menu item skipped due to missing menu slug"
msgstr "菜单项由于缺少菜单别名被跳过"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:584
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "导入%1$s失败：无效的文章类型 %2$s"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:485
#: core/utils/import-export/wp-import.php:676
#: core/utils/import-export/wp-import.php:726
msgid "Failed to import %1$s %2$s"
msgstr "无法导入 %1$s %2$s"

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:320
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "导入作者 %s 失败。他们的文章将归属于当前用户。"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:385
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "无法为 %s 创建新用户。他们的文章将归于当前用户。"

#: core/utils/import-export/wp-import.php:253
msgid "The file does not exist, please try again."
msgstr "该文件不存在，请重试。"

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "读取这个 WXR 文件时，出现了一个错误。"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "这似乎不是WXR文件，WXR版本号缺少或无效"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "不能删除“手机”和“平板”选项。"

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "激活断点"

#: modules/shapes/widgets/text-path.php:520
#: modules/shapes/widgets/text-path.php:591
msgid "Stroke"
msgstr "描边"

#: modules/shapes/widgets/text-path.php:485
msgid "Path"
msgstr "路径"

#: modules/shapes/widgets/text-path.php:382
msgid "Starting Point"
msgstr "起点"

#: modules/shapes/widgets/text-path.php:217
msgid "Show Path"
msgstr "显示路径"

#: modules/shapes/widgets/text-path.php:204
msgid "RTL"
msgstr "从右向左"

#: modules/shapes/widgets/text-path.php:205
msgid "LTR"
msgstr "从左向右"

#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:33
#: modules/shapes/widgets/text-path.php:136
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:1
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:126
msgid "Path Type"
msgstr "路径类型"

#: modules/shapes/widgets/text-path.php:199
msgid "Text Direction"
msgstr "文本方向"

#: modules/shapes/widgets/text-path.php:114
msgid "Add Your Curvy Text Here"
msgstr "添加您的曲线文本"

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "螺旋"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "椭圆形"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "圆弧"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "波浪"

#: modules/shapes/widgets/text-path.php:51
#: modules/shapes/widgets/text-path.php:103
#: modules/shapes/widgets/text-path.php:243
msgid "Text Path"
msgstr "文本路径"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:347
msgid "Word Spacing"
msgstr "词间距"

#: core/experiments/manager.php:684
msgid "Inactive by default"
msgstr "作为默认停用"

#: core/experiments/manager.php:683
msgid "Active by default"
msgstr "默认处于启用状态"

#. translators: %s Release status.
#: core/experiments/manager.php:570
msgid "Status: %s"
msgstr "状态：%s"

#: core/experiments/manager.php:412
msgid "Stable"
msgstr "稳定"

#: core/experiments/manager.php:409
msgid "Development"
msgstr "开发"

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "实验"

#: core/experiments/manager.php:470
msgid "No available experiments"
msgstr "无可用的实验"

#: core/experiments/manager.php:473
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "当前版本的 Elementor 没有任何实验性功能。如果您感到好奇，请务必在未来的版本中回来。"

#: core/experiments/manager.php:410 modules/atomic-widgets/module.php:240
#: assets/js/editor-v4-opt-in.js:347 assets/js/editor-v4-opt-in.js:495
msgid "Alpha"
msgstr "内测版"

#: core/experiments/manager.php:411 assets/js/ai-admin.js:657
#: assets/js/ai-admin.js:7784 assets/js/ai-gutenberg.js:2425
#: assets/js/ai-gutenberg.js:9632 assets/js/ai-layout.js:489
#: assets/js/ai-layout.js:3265 assets/js/ai-media-library.js:2286
#: assets/js/ai-media-library.js:9413 assets/js/ai-unify-product-images.js:2286
#: assets/js/ai-unify-product-images.js:9413 assets/js/ai.js:3071
#: assets/js/ai.js:10878
msgid "Beta"
msgstr "公测版"

#: core/experiments/manager.php:538
msgid "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "要在您的网站上使用实验或功能，只需单击其旁边的下拉菜单并切换到“活动”即可。您可以随时停用它们。"

#: modules/landing-pages/module.php:292
msgid "No landing pages found in trash"
msgstr "在回收站中未找到着陆页"

#: modules/landing-pages/module.php:290
msgid "Search Landing Pages"
msgstr "搜索着陆页"

#: modules/landing-pages/module.php:289
msgid "View Landing Page"
msgstr "查看着陆页"

#: modules/landing-pages/module.php:288
msgid "All Landing Pages"
msgstr "所有着陆页"

#: modules/landing-pages/module.php:287
msgid "New Landing Page"
msgstr "新着陆页"

#: modules/landing-pages/module.php:286
msgid "Edit Landing Page"
msgstr "编辑着陆页"

#: modules/landing-pages/module.php:285
msgid "Add New Landing Page"
msgstr "添加新的着陆页"

#: modules/landing-pages/module.php:217
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "为您的企业营销活动建立有效的着陆页面。"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:217 modules/landing-pages/module.php:283
msgid "Landing Page"
msgstr "着陆页面"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:139
msgid "Unknown"
msgstr "未知"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "测试到%s版本"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:2298
#: assets/js/element-manager-admin.js:2359
msgid "Plugin"
msgstr "插件"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "您正在使用的一些插件还没有经过最新版本的%1$s (%2$s)的测试。为了避免问题，在更新%1$s之前，请确保它们都是最新的和兼容的。"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "兼容性警示"

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "自定义列间距"

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:45 modules/landing-pages/module.php:151
#: modules/landing-pages/module.php:282 modules/landing-pages/module.php:294
#: assets/js/app.js:10352 assets/js/editor.js:55153
msgid "Landing Pages"
msgstr "着陆页面"

#: modules/landing-pages/module.php:46
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "添加了新的 Elementor 内容类型，允许在简化的工作流程中立即创建漂亮的登陆页面。"

#: modules/landing-pages/module.php:291
msgid "No landing pages found"
msgstr "未找到着陆页"

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "保留我的设置"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "如果删除此模板，您将删除整个网站设置。如果这个模板被删除，所有相关的设置，全局颜色和字体，主题风格，布局，背景和灯箱设置将从您的现有网站删除。全局颜色和字体，主题风格，布局，背景和灯箱设置将从您现有的网站中删除。此操作不能被撤销。"

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "您确定要删除站点设置吗?"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "您尝试使用的全局值不可用。"

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "选择SVG文件"

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "Elementor 网站构建器应有尽有：拖放页面构建器、像素级设计、移动响应式编辑等等。立即开始吧！"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:10360
#: assets/js/editor.js:49514
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:173
msgid "Global Colors"
msgstr "全局颜色"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:432
msgid "Breakpoint"
msgstr "断点"

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "主题"

#: includes/frontend.php:1409
msgid "Download"
msgstr "下载"

#: includes/widgets/common-base.php:1045 includes/widgets/image.php:383
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:135
msgid "Fill"
msgstr "填充"

#: includes/widgets/image.php:376
msgid "Object Fit"
msgstr "对象拟合"

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "应用链接在"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:146
msgid "Items"
msgstr "项目"

#: core/admin/admin-notices.php:216 includes/settings/settings-page.php:403
msgid "Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us."
msgstr "通过选择共享非敏感插件数据并定期接收我们的电子邮件更新，成为超级贡献者。"

#: modules/nested-tabs/widgets/nested-tabs.php:371 assets/js/editor.js:49873
msgid "Additional Settings"
msgstr "附加设置"

#: core/settings/editor-preferences/model.php:195 assets/js/editor.js:49881
msgid "Design System"
msgstr "设计系统"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "网站描述"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "选择名称"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1047
msgid "Site Name"
msgstr "站点名称"

#: modules/page-templates/module.php:365
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "更改仅在页面重新加载后反映在预览中。"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "站点标识"

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "断点"

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "默认页面布局"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:10360
msgid "Layout Settings"
msgstr "布局设置"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "\"主题颜色\"元标记仅在受支持的浏览器和设备中可用。"

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "移动浏览器背景"

#: core/settings/editor-preferences/model.php:39
#: includes/editor-templates/hotkeys.php:153 assets/js/editor.js:40485
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "User Preferences"
msgstr "用户首选项"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:115
#: assets/js/app.js:10358 assets/js/app.js:10838 assets/js/editor.js:49463
#: assets/js/editor.js:49467 assets/js/editor.js:49477
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "Site Settings"
msgstr "站点设置"

#: core/admin/admin.php:867
msgid "Heads up, Please backup before upgrade!"
msgstr "注意，请在升级前备份！"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "选择描述"

#: core/frontend/render-mode-manager.php:150
#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "错误"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:873
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "最新更新包括插件不同领域的一些重大更改。我们强烈建议您%1$s在升级%2$s之前备份您的站点，并确保首先在暂存环境中进行更新"

#. translators: 1: Width number pixel, 2: Height number pixel.
#: core/kits/documents/tabs/settings-site-identity.php:102
msgid "Suggested image dimensions: %1$s × %2$s pixels."
msgstr "建议的图像尺寸：%1$s × %2$s 像素。"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "备选字体系列"

#: core/kits/documents/tabs/settings-site-identity.php:118
msgid "Site Favicon"
msgstr "网站 Favicon"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1051
msgid "Site Logo"
msgstr "网站 LOGO"

#: core/kits/documents/tabs/settings-site-identity.php:125
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "建议的 Favicon 尺寸：512 × 512 像素。"

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "行间距"

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "重置数据"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "观看完整指南"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "通过观看我们的\"入门\"视频系列，向 Elementor 介绍。它将指导您完成创建网站所需的步骤。然后单击以创建第一页。"

#: includes/controls/media.php:270
msgid "Click the media icon to upload file"
msgstr "单击媒体图标上传文件"

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/ai-admin.js:64 assets/js/ai-admin.js:74
#: assets/js/ai-gutenberg.js:1693 assets/js/ai-gutenberg.js:1703
#: assets/js/ai-media-library.js:1693 assets/js/ai-media-library.js:1703
#: assets/js/ai-unify-product-images.js:1693
#: assets/js/ai-unify-product-images.js:1703 assets/js/ai.js:1693
#: assets/js/ai.js:1703 assets/js/common.js:2146 assets/js/common.js:2156
#: assets/js/editor.js:42299 assets/js/editor.js:42309
msgid "Enable Unfiltered File Uploads"
msgstr "启用未过滤的文件上传"

#: modules/safe-mode/module.php:367
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "如果您遇到加载问题，请与您的站点管理员联系，以使用安全模式解决问题。"

#: includes/editor-templates/panel.php:296
msgid "Dynamic Tags"
msgstr "动态标签"

#: includes/frontend.php:1410
msgid "Download image"
msgstr "下载图片"

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "自定义属性"

#: includes/managers/icons.php:490
msgid "The upgrade process includes a database update"
msgstr "升级过程包括数据库更新"

#: includes/managers/controls.php:1204
msgid "Attributes"
msgstr "属性"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1411
msgid "Fullscreen"
msgstr "全屏"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1413
msgid "Share"
msgstr "分享"

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: includes/editor-templates/templates.php:232 assets/js/ai-admin.js:9578
#: assets/js/ai-gutenberg.js:11426 assets/js/ai-media-library.js:11207
#: assets/js/ai-unify-product-images.js:11207 assets/js/ai.js:12672
#: assets/js/app.js:7176 assets/js/editor.js:49769
msgid "Back"
msgstr "返回"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "字段"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "标签"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "表单字段"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "按钮"

#: core/kits/documents/kit.php:154
msgid "Draft"
msgstr "草稿"

#: core/kits/documents/tabs/theme-style-typography.php:75
#: includes/widgets/text-editor.php:306
msgid "Paragraph Spacing"
msgstr "段落间距"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "工具栏图标大小"

#: includes/frontend.php:1407
msgid "Share on Twitter"
msgstr "分享到Twitter"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "导航图标大小"

#: includes/managers/controls.php:1216
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "通过属性，您可以将自定义HTML属性添加到任何元素。"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt键"

#: includes/managers/controls.php:1214
msgid "Meet Our Attributes"
msgstr "符合我们的属性"

#: includes/editor-templates/panel.php:325
msgid "You’re missing out!"
msgstr "您错过了！"

#: includes/frontend.php:1406
msgid "Share on Facebook"
msgstr "在脸书上分享"

#: includes/frontend.php:1408
msgid "Pin it"
msgstr "钉住"

#: core/kits/documents/kit.php:43
msgid "Kit"
msgstr "套件"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "焦点"

#: core/isolation/elementor-adapter.php:28 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "默认套件"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "单击此处立即运行"

#: core/kits/documents/tabs/theme-style-typography.php:48
msgid "Body"
msgstr "内容"

#: includes/managers/icons.php:491
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "我们强烈建议备份您执行此升级之前数据库。"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "设置链接元素的自定义属性。使用 | 将属性键与值分开（管道）字符。用逗号分隔键值对。"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "使用 %s 小部件和更多专业功能来扩展您的工具箱并更快更好地构建网站。"

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "%s 小部件"

#: includes/editor-templates/panel.php:322
msgid "Elementor Dynamic Content"
msgstr "Elementor 动态内容"

#: includes/editor-templates/panel.php:326
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "通过合并数十个 Elementor 的原生动态标签，获得更多动态功能。"

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "已经连接。"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "背景位置"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691 includes/widgets/image.php:385
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:92
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:137
msgid "Contain"
msgstr "包含"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:549 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:578
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:46
#: assets/js/packages/editor-controls/editor-controls.strings.js:90
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:123
msgid "Auto"
msgstr "自动"

#: includes/settings/settings-page.php:396
msgid "Usage Data Sharing"
msgstr "使用情况数据共享"

#: core/settings/editor-preferences/model.php:79
msgid "Auto detect"
msgstr "自动检测"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:975
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:91
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:136
msgid "Cover"
msgstr "覆盖"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "连接为 %s"

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "背景尺寸"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "%s 视频播放器"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "连接到库失败。请尝试重新加载页面，然后重试"

#: core/settings/editor-preferences/model.php:173 assets/js/ai-admin.js:15932
#: assets/js/ai-gutenberg.js:17780 assets/js/ai-layout.js:5218
#: assets/js/ai-media-library.js:17561
#: assets/js/ai-unify-product-images.js:17561 assets/js/ai.js:9390
#: assets/js/ai.js:19026 assets/js/editor.js:10093
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:658
msgid "Get Started"
msgstr "开始使用"

#: includes/widgets/image-carousel.php:462
msgid "Pause on Interaction"
msgstr "交互时暂停"

#: core/settings/editor-preferences/model.php:52
msgid "Preferences"
msgstr "偏好设置"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "自定义图标"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "清除日志"

#: includes/controls/groups/background.php:645
msgid "Duration"
msgstr "时效"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "过渡"

#: core/document-types/post.php:51
msgid "Post"
msgstr "文章"

#: includes/settings/tools.php:386
msgid "Reinstall"
msgstr "重新安装"

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "YouTube/Vimeo 链接或视频文件链接（建议使用 mp4）。"

#: includes/controls/groups/background.php:583 includes/widgets/video.php:354
msgid "Play On Mobile"
msgstr "在手机上播放"

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "此封面图像将替换背景视频，以防无法加载视频。"

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "平移缩放效果"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "退出"

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "添加元素"

#: includes/widgets/divider.php:667
msgid "Amount"
msgstr "合计"

#: includes/widgets/divider.php:181
msgctxt "Shapes"
msgid "Wavy"
msgstr "波浪"

#: includes/widgets/divider.php:197
msgctxt "Shapes"
msgid "Arrows"
msgstr "箭头"

#: includes/widgets/divider.php:205
msgctxt "Shapes"
msgid "Pluses"
msgstr "加号"

#: includes/widgets/divider.php:213
msgctxt "Shapes"
msgid "Rhombus"
msgstr "菱形"

#: includes/widgets/divider.php:221
msgctxt "Shapes"
msgid "Parallelogram"
msgstr "平行四边形"

#: includes/widgets/divider.php:229
msgctxt "Shapes"
msgid "Rectangles"
msgstr "矩形"

#: includes/widgets/divider.php:265
msgctxt "Shapes"
msgid "Leaves"
msgstr "树叶"

#: includes/widgets/divider.php:274
msgctxt "Shapes"
msgid "Stripes"
msgstr "条带"

#: includes/widgets/divider.php:283
msgctxt "Shapes"
msgid "Squares"
msgstr "方形"

#: includes/widgets/divider.php:292
msgctxt "Shapes"
msgid "Trees"
msgstr "树形"

#: includes/widgets/divider.php:310
msgctxt "Shapes"
msgid "X"
msgstr "X"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "线条"

#: includes/widgets/divider.php:238
msgctxt "Shapes"
msgid "Dots"
msgstr "圆点"

#: includes/widgets/divider.php:247
msgctxt "Shapes"
msgid "Fir Tree"
msgstr "杉树"

#: includes/widgets/divider.php:256
msgctxt "Shapes"
msgid "Half Rounds"
msgstr "半圈"

#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Tribal"
msgstr "部落"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "基本图库"

#: includes/frontend.php:1416 assets/js/app.js:7115 assets/js/app.js:8795
#: assets/js/app.js:9738
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1686
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1737
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2017
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2294
msgid "Next"
msgstr "下一页"

#: includes/frontend.php:1415 assets/js/app.js:7988 assets/js/app.js:8782
#: assets/js/app.js:9731
msgid "Previous"
msgstr "上一页"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "进入"

#: includes/widgets/divider.php:156
msgctxt "Shapes"
msgid "Multiple"
msgstr "多样"

#: includes/widgets/divider.php:140
msgctxt "Shapes"
msgid "Curly"
msgstr "卷曲"

#: includes/widgets/divider.php:164
msgctxt "Shapes"
msgid "Slashes"
msgstr "斜线"

#: includes/widgets/divider.php:173
msgctxt "Shapes"
msgid "Squared"
msgstr "方形"

#: includes/widgets/divider.php:148
msgctxt "Shapes"
msgid "Curved"
msgstr "弧形"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "文件路径：%s"

#: includes/managers/icons.php:488
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "请注意，由于Font Awesome进行了微小的设计更改，升级过程可能会导致以前使用的一些Font Awesome 4图标看起来有点不同。"

#: includes/controls/media.php:283 includes/controls/media.php:285
#: assets/js/editor.js:8112
#: assets/js/packages/editor-controls/editor-controls.js:12
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:4
#: assets/js/packages/editor-controls/editor-controls.strings.js:27
msgid "Upload"
msgstr "上传"

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:30
#: includes/editor-templates/panel.php:183
msgid "Need Help"
msgstr "需要帮助"

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "模板不存在。"

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "您的邮箱"

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "注册"

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "如果您了解所涉及的安全风险，我们建议您仅启用此功能。"

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - 常规"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - 实心"

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome - 品牌"

#: includes/managers/icons.php:248
msgid "All Icons"
msgstr "所有图标"

#: includes/managers/icons.php:464
msgid "Load Font Awesome 4 Support"
msgstr "加载 Font Awesome 4 支持"

#: includes/managers/icons.php:472
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "Font Awesome 4支持脚本（shim.js）是一个脚本，可确保在使用Font Awesome 5库时正确显示所有先前选择的Font Awesome 4图标。"

#: includes/managers/icons.php:479 includes/managers/icons.php:483
#: includes/managers/icons.php:498
msgid "Font Awesome Upgrade"
msgstr "Font Awesome 更新"

#: includes/managers/icons.php:485
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "访问1,500多个令人惊叹的Font Awesome 5图标，享受更快的性能和设计灵活性。"

#: includes/managers/icons.php:493
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "此操作不可逆，无法通过回滚到以前的版本来撤消。"

#: includes/managers/icons.php:505
msgid "Upgrade To Font Awesome 5"
msgstr "升级到 Font Awesome 5"

#: includes/managers/icons.php:551
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "真棒！ Font Awesome 5的升级过程已成功完成。"

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:8696
msgid "Icon Library"
msgstr "图标库"

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "上传 SVG 图像"

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "播放一次"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "选择视频"

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "缺少一些主题文件。"

#: core/editor/editor.php:210
msgid "Document not found."
msgstr "找不到文件。"

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "请注意！允许上传任何文件(包括SVG和JSON)是潜在的安全风险。"

#: core/experiments/manager.php:664 includes/base/widget-base.php:1019
msgid "Deprecated"
msgstr "弃用"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/elements/column.php:934 includes/elements/container.php:1887
#: includes/elements/section.php:1407 includes/widgets/common-base.php:1230
msgid "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."
msgstr "响应式可见性仅在 %1$s 预览模式 %2$s 或实时页面上生效，在 Elementor 中编辑时无效。"

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:590
msgid "This file is not allowed for security reasons."
msgstr "出于安全原因不允许使用此文件。"

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "作为 Beta 测试人员，您将直接通过电子邮件收到更新，其中包括 Elementor 的测试版本及其内容"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "获取 Beta 版更新"

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "Elementor 将尝试清理未过滤的文件，删除潜在的恶意代码和脚本。"

#: includes/managers/icons.php:486
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "通过升级，每当您编辑包含 Font Awesome 4 图标的页面时，Elementor 都会将其转换为新的 Font Awesome 5 图标。"

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "您网站的 .htaccess 文件似乎丢失。"

#: includes/elements/container.php:1530 includes/widgets/common-base.php:450
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:111
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:145
msgid "Fixed"
msgstr "固定"

#: includes/elements/container.php:1511 includes/widgets/common-base.php:432
msgid "Please note!"
msgstr "请注意！"

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:249
msgid "Custom Width"
msgstr "自定义宽度"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "超级管理员"

#: includes/elements/container.php:548 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:122
msgid "Hidden"
msgstr "隐藏"

#: includes/elements/column.php:869 includes/elements/container.php:1807
#: includes/elements/section.php:1316 includes/widgets/common-base.php:720
msgid "Motion Effects"
msgstr "动作效果"

#: includes/elements/section.php:420 includes/widgets/common-base.php:404
#: includes/widgets/image-carousel.php:732
msgid "Vertical Align"
msgstr "垂直对齐"

#: includes/elements/container.php:1529 includes/widgets/common-base.php:449
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:144
msgid "Absolute"
msgstr "绝对"

#: includes/elements/container.php:1549 includes/widgets/common-base.php:464
msgid "Horizontal Orientation"
msgstr "水平方向"

#: includes/base/element-base.php:1010 includes/elements/container.php:1574
#: includes/elements/container.php:1612 includes/elements/container.php:1674
#: includes/elements/container.php:1711 includes/widgets/common-base.php:489
#: includes/widgets/common-base.php:527 includes/widgets/common-base.php:589
#: includes/widgets/common-base.php:626
#: modules/floating-buttons/base/widget-contact-button-base.php:2959
#: modules/floating-buttons/base/widget-contact-button-base.php:3013
msgid "Offset"
msgstr "偏移"

#: includes/elements/container.php:1650 includes/widgets/common-base.php:565
msgid "Vertical Orientation"
msgstr "垂直方向"

#: includes/elements/container.php:1512 includes/widgets/common-base.php:433
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "自定义定位不被视为响应式网页设计的最佳实践，不应过于频繁使用。"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "获取帮助"

#: includes/elements/container.php:543 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:124
msgid "Overflow"
msgstr "Overflow (溢出)"

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:193 includes/elements/column.php:221
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "Space Evenly (全同等间距)"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "Space Around (首尾半内部等间距)"

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "注意：ID链接仅接受这些字符：%s"

#: modules/safe-mode/module.php:256
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "这个问题可能是由某个插件或主题引起的。"

#: modules/safe-mode/module.php:358
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "加载 Elementor 时遇到问题？请启用安全模式来排除故障。"

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:387
msgid "Choose File"
msgstr "选择文件"

#: includes/widgets/read-more.php:124
msgid "Read More Text"
msgstr "阅读更多文字"

#: includes/widgets/read-more.php:95
msgid "Continue reading"
msgstr "继续阅读"

#: includes/widgets/read-more.php:40 includes/widgets/read-more.php:91
msgid "Read More"
msgstr "阅读更多"

#: modules/safe-mode/module.php:352 modules/safe-mode/module.php:364
msgid "Can't Edit?"
msgstr "无法编辑？"

#: modules/safe-mode/module.php:246 modules/safe-mode/module.php:476
msgid "Disable Safe Mode"
msgstr "禁用安全模式"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "无法启用安全模式"

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "安全模式允许您通过仅加载编辑器来解决问题，而无需加载主题或任何其他插件。"

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "安全模式"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "分类"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "模板"

#: modules/safe-mode/module.php:354
msgid "Enable Safe Mode"
msgstr "启用安全模式"

#: modules/safe-mode/module.php:244
msgid "Safe Mode ON"
msgstr "开启安全模式"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:316
msgid "Every %d minutes"
msgstr "每 %d 分钟"

#: core/upgrade/manager.php:51
msgid "Elementor Data Updater"
msgstr "Elementor 数据更新"

#: includes/widgets/video.php:223
msgid "External URL"
msgstr "外部链接"

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "排序"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:956
#: modules/link-in-bio/base/widget-link-in-bio-base.php:500
#: modules/link-in-bio/base/widget-link-in-bio-base.php:750
msgid "Location"
msgstr "位置"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "分类"

#: includes/template-library/sources/local.php:1733
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:10343
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2219
msgid "Popups"
msgstr "弹窗"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "获取弹窗构建器"

#: includes/frontend.php:1544
msgid "(more&hellip;)"
msgstr "(更多&hellip;)"

#. translators: %s: Current post name.
#: includes/frontend.php:1551
msgid "Continue reading %s"
msgstr "继续阅读%s"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "全部"

#: includes/template-library/sources/local.php:1455
msgctxt "Template Library"
msgid "Filter by category"
msgstr "按类别过滤"

#: modules/safe-mode/module.php:253
msgid "Editor successfully loaded?"
msgstr "已成功加载编辑器？"

#: modules/safe-mode/module.php:269
msgid "Still experiencing issues?"
msgstr "仍然遇到问题？"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1732
#: assets/js/app-packages.js:5890 assets/js/editor.js:49488
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Theme Builder"
msgstr "主题生成器"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "数据库更新过程现已完成。 感谢您更新到最新版本！"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "您的站点数据库需要更新到最新版本。"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:115
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "注意：此小部件仅影响在存档页面中使用 “%s” 的主题。"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "弹出窗口生成器可让您利用 Elementor 中的所有令人惊叹的功能，因此您可以构建美观且高转化率的弹出窗口。立即获取 Elementor Pro 并开始设计您的弹出窗口。"

#: modules/library/documents/not-supported.php:56
msgid "Not Supported"
msgstr "不支持"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "用户"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:10846
msgid "Plugins"
msgstr "插件"

#: includes/widgets/video.php:494
msgid "Current Video Channel"
msgstr "当前视频频道"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "评分"

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "自定义标题"

#: includes/editor-templates/hotkeys.php:200 assets/js/editor.js:7421
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "键盘快捷键"

#: includes/editor-templates/hotkeys.php:32 assets/js/ai-admin.js:12614
#: assets/js/ai-admin.js:13703 assets/js/ai-gutenberg.js:14462
#: assets/js/ai-gutenberg.js:15551 assets/js/ai-media-library.js:14243
#: assets/js/ai-media-library.js:15332
#: assets/js/ai-unify-product-images.js:14243
#: assets/js/ai-unify-product-images.js:15332 assets/js/ai.js:15708
#: assets/js/ai.js:16797
msgid "Redo"
msgstr "重做"

#: includes/editor-templates/hotkeys.php:24 assets/js/ai-admin.js:12603
#: assets/js/ai-admin.js:13692 assets/js/ai-gutenberg.js:14451
#: assets/js/ai-gutenberg.js:15540 assets/js/ai-media-library.js:14232
#: assets/js/ai-media-library.js:15321
#: assets/js/ai-unify-product-images.js:14232
#: assets/js/ai-unify-product-images.js:15321 assets/js/ai.js:15697
#: assets/js/ai.js:16786 assets/js/editor.js:11564
#: assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "撤销"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "自定义"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "菜单"

#: core/common/modules/connect/apps/base-app.php:220 assets/js/editor.js:11960
#: assets/js/editor.js:12025 assets/js/editor.js:13007
msgid "Connected successfully."
msgstr "连接成功。"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: includes/editor-templates/templates.php:521
#: modules/cloud-library/module.php:125 assets/js/ai-admin.js:1072
#: assets/js/ai-admin.js:6399 assets/js/ai-gutenberg.js:2840
#: assets/js/ai-gutenberg.js:8247 assets/js/ai-layout.js:770
#: assets/js/ai-layout.js:2501 assets/js/ai-media-library.js:2701
#: assets/js/ai-media-library.js:8028 assets/js/ai-unify-product-images.js:2701
#: assets/js/ai-unify-product-images.js:8028 assets/js/ai.js:3486
#: assets/js/ai.js:9401 assets/js/ai.js:9493 assets/js/editor.js:11910
msgid "Connect"
msgstr "连接"

#: core/common/modules/connect/apps/base-app.php:232
msgid "Disconnected successfully."
msgstr "已成功断开连接。"

#: core/common/modules/finder/categories/create.php:27
#: assets/js/editor.js:10966 assets/js/editor.js:48086
msgid "Create"
msgstr "创建"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:12
msgid "Homepage"
msgstr "首页"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "仪表盘"

#: includes/editor-templates/hotkeys.php:99 assets/js/admin-top-bar.js:189
#: assets/js/common.js:4651 assets/js/editor.js:40497
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "搜索"

#: includes/editor-templates/hotkeys.php:167
msgid "Go To"
msgstr "转到"

#: includes/editor-templates/hotkeys.php:107
msgid "Show / Hide Panel"
msgstr "显示/隐藏面板"

#: includes/editor-templates/hotkeys.php:208
msgid "Quit"
msgstr "退出"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "星级"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "无标记风格"

#: includes/widgets/star-rating.php:319
msgid "Stars"
msgstr "星级"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:390
msgid "Unmarked Color"
msgstr "无标记颜色"

#: core/base/document.php:1994
msgid "Future"
msgstr "定时"

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "附件说明"

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "断开连接"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "评级标准"

#: includes/widgets/video.php:495
msgid "Any Video"
msgstr "任何视频"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "轮廓"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "输入以在 Elementor 中查找任何内容"

#: includes/widgets/video.php:597
msgid "Poster"
msgstr "海报"

#: includes/widgets/video.php:457
msgid "Lazy Load"
msgstr "延迟加载"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:55
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:124 assets/js/app-packages.js:2837
#: assets/js/app.js:3271
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1271
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1490
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1582
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1834
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2008
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2329
msgid "Skip"
msgstr "跳过"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "调试栏"

#: core/admin/admin-notices.php:285
msgid "Hide Notification"
msgstr "隐藏通知"

#: core/admin/admin-notices.php:275
msgid "Congrats!"
msgstr "恭喜！"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "创建您的第一篇文章"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "创建您的第一个页面"

#: includes/editor-templates/navigator.php:97
msgid "Empty"
msgstr "空白"

#: modules/floating-buttons/base/widget-contact-button-base.php:949
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:270
#: modules/shapes/widgets/text-path.php:164
msgid "Paste URL or type"
msgstr "粘贴URL或类型"

#: core/admin/admin-notices.php:279
msgid "Happy To Help"
msgstr "乐于帮助"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "色调"

#: includes/widgets/video.php:159 includes/widgets/video.php:184
#: includes/widgets/video.php:208 includes/widgets/video.php:268
msgid "Enter your URL"
msgstr "输入您的网址"

#: includes/editor-templates/hotkeys.php:126
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:87
#: includes/editor-templates/panel.php:91 assets/js/editor.js:34004
msgid "Navigator"
msgstr "导航器"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:795
msgid "Getting Started"
msgstr "入门"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:30103
msgid "Inner Section"
msgstr "内部区段"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:124 includes/widgets/text-editor.php:142
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "这是测试文本，单击 “编辑” 按钮更改此文本。"

#: includes/editor-templates/navigator.php:103
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "当您用内容填充页面后，此窗口将为您提供所有页面元素的概览显示。这样，您就可以轻松地在任何段、列或小部件之间移动。"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "欢迎来到 Elementor"

#: includes/editor-templates/navigator.php:102
msgid "Easy Navigation is Here!"
msgstr "轻松导航就在这里！"

#: core/admin/admin-notices.php:276
msgid "You created over 10 pages with Elementor. Great job! If you can spare a minute, please help us by leaving a five star review on WordPress.org."
msgstr "您使用 Elementor 创建了 10 多个页面。做得好！如果您能抽出一点时间，请在 WordPress.org 上留下五星级评论来帮助我们。"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "调试栏添加了一个管理栏菜单，其中列出了正在显示的页面上使用的所有模板。"

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1412
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10240
#: assets/js/ai-admin.js:10243 assets/js/ai-gutenberg.js:12088
#: assets/js/ai-gutenberg.js:12091 assets/js/ai-media-library.js:11869
#: assets/js/ai-media-library.js:11872
#: assets/js/ai-unify-product-images.js:11869
#: assets/js/ai-unify-product-images.js:11872 assets/js/ai.js:13334
#: assets/js/ai.js:13337
msgid "Zoom"
msgstr "缩放"

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "单页"

#: includes/widgets/video.php:402
msgid "Video Info"
msgstr "视频信息"

#: includes/widgets/video.php:429
msgid "Logo"
msgstr "Logo"

#: includes/editor-templates/templates.php:122
#: includes/editor-templates/templates.php:640
#: includes/managers/elements.php:292
#: modules/promotions/widgets/pro-widget-promotion.php:66
#: assets/js/ai-admin.js:8001 assets/js/ai-gutenberg.js:9849
#: assets/js/ai-layout.js:3482 assets/js/ai-media-library.js:9630
#: assets/js/ai-unify-product-images.js:9630 assets/js/ai.js:11095
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
msgid "Pro"
msgstr "专业版"

#: includes/elements/column.php:447 includes/elements/container.php:844
#: includes/elements/section.php:715 includes/widgets/heading.php:309
msgid "Blend Mode"
msgstr "混合模式"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "模糊"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:319
msgid "Specify an end time (in seconds)"
msgstr "指定结束时间（以秒为单位）"

#: includes/widgets/video.php:133
msgid "Source"
msgstr "资源"

#: includes/managers/elements.php:317
msgid "WooCommerce"
msgstr "WooCommerce"

#: includes/widgets/video.php:141
msgid "Self Hosted"
msgstr "自托管"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:308
msgid "Specify a start time (in seconds)"
msgstr "指定开始时间（以秒为单位）"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "饱和度"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:310 modules/admin-bar/module.php:149
msgid "Site"
msgstr "站点"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:317
msgid "End Time"
msgstr "结束时间"

#: includes/controls/groups/background.php:549 includes/widgets/video.php:306
msgid "Start Time"
msgstr "开始时间"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Elementor 调试器"

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "艺术品"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "对比"

#: includes/editor-templates/hotkeys.php:41
#: includes/editor-templates/templates.php:165 assets/js/editor.js:10664
#: assets/js/editor.js:10678 assets/js/editor.js:32758
msgid "Copy"
msgstr "复制"

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "按钮 ID"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "亮度"

#: core/base/providers/social-network-provider.php:216
#: includes/widgets/video.php:255 includes/widgets/video.php:279
msgid "URL"
msgstr "链接"

#: includes/widgets/traits/button-trait.php:216
msgid "Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "请确保 ID 是唯一的，并且不会在显示此表单的页面的其他地方使用。该字段允许 %1$sA-z 0-9%2$s 和不带空格的下划线字符。"

#: includes/editor-templates/global.php:49
#: assets/js/cf70912a0f34653ad242.bundle.js:130
msgid "Drag widget here"
msgstr "将小部件拖动到此处"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "稍等！ 不要停用 Elementor。您必须激活 Elementor 和 Elementor Pro 才能使插件正常工作。"

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "我有 Elementor Pro"

#: includes/widgets/video.php:139
msgid "Dailymotion"
msgstr "Dailymotion"

#: core/admin/admin.php:219 assets/js/admin.js:2058 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "返回 WordPress 编辑器"

#. translators: %s: Document title.
#: core/documents-manager.php:388
msgid "Elementor %s"
msgstr "Elementor %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:269
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1416
msgid "Add New %s"
msgstr "添加 %s"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:416 includes/elements/column.php:490
#: includes/elements/container.php:798 includes/elements/container.php:912
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:429 includes/widgets/image-box.php:464
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1499
msgid "Opacity"
msgstr "不透明度"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "最大宽度"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1524 includes/widgets/common-base.php:444
#: includes/widgets/common-base.php:1091 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:572
#: includes/widgets/image-carousel.php:636 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:251
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:940
#: modules/link-in-bio/base/widget-link-in-bio-base.php:995
#: modules/nested-accordion/widgets/nested-accordion.php:213
#: modules/nested-tabs/widgets/nested-tabs.php:857
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:39
#: assets/js/packages/editor-controls/editor-controls.strings.js:110
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:147
msgid "Position"
msgstr "定位 (Position)"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "新模板"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:408
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "喜欢 %1$s 吗？请留下我们一个%2$s评分。非常感谢您的支持！"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "知识库"

#: modules/page-templates/module.php:297
msgid "Page Layout"
msgstr "页面布局"

#: modules/page-templates/module.php:352
msgid "This template includes the header, full-width content and footer"
msgstr "该模板包含标题，全宽内容和页脚"

#: modules/page-templates/module.php:328
msgid "Default Page Template from your theme."
msgstr "主题中的默认页面模板。"

#: modules/page-templates/module.php:340
msgid "No header, no footer, just Elementor"
msgstr "无页眉、页脚，只有使用 Elementor 添加的内容"

#: includes/frontend.php:1414 includes/widgets/video.php:987
msgid "Play Video"
msgstr "播放视频"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "分隔器"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "我的模板"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "创建模板"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "输入模板名称（可选）"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "命名您的模板"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "选择您想要处理的模板类型"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "选择模板类型"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:244
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:235
msgid "Custom Fonts"
msgstr "自定义字体"

#: includes/editor-templates/templates.php:310
#: includes/editor-templates/templates.php:355
#: includes/editor-templates/templates.php:402
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:3
msgid "More actions"
msgstr "更多操作"

#: includes/editor-templates/templates.php:152
msgid "Search Templates:"
msgstr "搜索模板："

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:11125 assets/js/editor.js:10055
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2191
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "网页"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "该标签没有设置。"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "角色管理器"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "页面风格"

#: core/base/document.php:259
msgid "Document"
msgstr "文件"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "未找到操作。"

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "令牌已过期。"

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1409
msgid "Create Your First %s"
msgstr "创建您的第一个%s"

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1229 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:80
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "%s设置"

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "特色图像"

#: includes/template-library/sources/local.php:1376
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "添加模板并在您的站点上重复使用它们。轻松导出并导入到任何其他项目，以实现优化的工作流程。"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "排除角色"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "没有访问编辑器的权限"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "只想给某些内容提供访问权限？"

#: includes/widgets/common-base.php:233 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "行内"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "管理用户可以在 Elementor 中编辑的内容"

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:284 assets/js/app-packages.js:4592
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "添加"

#: includes/template-library/sources/local.php:1281
msgid "All"
msgstr "所有"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:389
msgid "Fallback"
msgstr "备选"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "使用模板创建网站的不同模块，并在需要时一键重复使用它们。"

#: includes/widgets/image-carousel.php:183
msgid "Set how many slides are scrolled per swipe."
msgstr "设置每次滑动滚动的幻灯片数量。"

#: core/admin/admin.php:477
msgid "Create New Post"
msgstr "创建新文章"

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "注意：背景固定只能在桌面上使用。"

#: includes/fonts.php:77
msgid "Google (Early Access)"
msgstr "谷歌（早期版本）"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "当前版本"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "输入您的代码"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "摘要"

#: core/admin/admin.php:474
msgid "Create New Page"
msgstr "创建新页面"

#: includes/controls/groups/background.php:597 includes/widgets/video.php:444
msgid "Privacy Mode"
msgstr "隐私模式"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "这是一个警报"

#: core/admin/admin.php:617
msgid "Blog"
msgstr "博客"

#: core/admin/admin.php:565
msgid "News & Updates"
msgstr "新闻与更新"

#: core/admin/admin.php:524
msgid "Recently Edited"
msgstr "最近编辑"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "下划线"

#: includes/editor-templates/panel.php:133
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "保存草稿"

#: includes/editor-templates/panel.php:119
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "保存选项"

#: core/base/document.php:173 includes/editor-templates/panel.php:114
#: assets/js/editor.js:27768
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "发布"

#: includes/editor-templates/panel.php:102
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "预览更改"

#: includes/editor-templates/panel.php:137
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "另存为模板"

#: includes/editor-templates/panel.php:151
#: includes/editor-templates/panel.php:153 assets/js/editor.js:38825
msgid "Hide Panel"
msgstr "隐藏面板"

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "拖放"

#: includes/editor-templates/templates.php:563
msgid "Import Template to Your Library"
msgstr "导入模板到您的模版库"

#: includes/widgets/traits/button-trait.php:57
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:49
#: assets/js/app.js:6856 assets/js/app.js:7861
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2536
msgid "Click here"
msgstr "点击这里"

#: includes/editor-templates/templates.php:256
msgid "Favorite"
msgstr "喜爱"

#: includes/editor-templates/templates.php:208
msgid "Creation Date"
msgstr "创建日期"

#: includes/editor-templates/templates.php:153
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:55
msgid "Search"
msgstr "搜索"

#: includes/editor-templates/templates.php:103
msgid "My Favorites"
msgstr "我最喜欢的"

#: includes/editor-templates/templates.php:13
#: includes/editor-templates/templates.php:14
msgid "Import Template"
msgstr "导入模板"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:13071
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1879
msgid "No Results Found"
msgstr "未找到结果"

#: includes/editor-templates/templates.php:566 assets/js/app-packages.js:2528
#: assets/js/app.js:2962
msgid "Select File"
msgstr "选择文件"

#: includes/editor-templates/templates.php:565
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2180
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2524
msgid "or"
msgstr "要么"

#: includes/editor-templates/templates.php:81
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3811
msgid "Popular"
msgstr "流行"

#: includes/editor-templates/templates.php:79
msgid "Trend"
msgstr "趋势"

#: includes/editor-templates/templates.php:77
#: includes/editor-templates/templates.php:121
#: includes/editor-templates/templates.php:639
#: assets/js/atomic-widgets-editor.js:720 assets/js/editor.js:35576
#: assets/js/editor.js:36074 assets/js/editor.js:50372
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3807
msgid "New"
msgstr "新的"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:279
#: includes/controls/media.php:281 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:74
#: assets/js/ai-admin.js:2311 assets/js/ai-admin.js:7489
#: assets/js/ai-gutenberg.js:4079 assets/js/ai-gutenberg.js:9337
#: assets/js/ai-layout.js:2970 assets/js/ai-media-library.js:3940
#: assets/js/ai-media-library.js:9118 assets/js/ai-unify-product-images.js:3940
#: assets/js/ai-unify-product-images.js:9118 assets/js/ai.js:4725
#: assets/js/ai.js:10583
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:919
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:52
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:65
msgid "Remove"
msgstr "删除"

#: core/experiments/manager.php:547 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "回到默认"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "通过线"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "取消关联的值"

#: core/admin/admin.php:532
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "M jS"

#: core/base/document.php:1540
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "M j，H：i"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "输入您的图像标题"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "输入您的描述"

#: core/admin/admin.php:428
msgid "Elementor Overview"
msgstr "Elementor 概述"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "输入您的简码"

#: core/admin/admin.php:597
msgid "(opens in a new window)"
msgstr "（在新窗口中打开）"

#: includes/editor-templates/templates.php:204
msgid "Created By"
msgstr "制作著："

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "上划线"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "文本装饰"

#: includes/editor-templates/hotkeys.php:73
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:32743
#: assets/js/editor.js:53857
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:49
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:20
msgid "Duplicate"
msgstr "复制到此对象下"

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:137
msgid "Active Icon"
msgstr "激活图标"

#: includes/editor-templates/panel-elements.php:74
msgid "Search Widget:"
msgstr "搜索小部件："

#: includes/widgets/heading.php:170
msgid "Add Your Heading Text Here"
msgstr "在此添加您的标题文本"

#: includes/editor-templates/templates.php:564
msgid "Drag & drop your .JSON or .zip template file"
msgstr "拖放您的 .JSON 或 .zip 模板文件"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1553
msgid "Last edited on %1$s by %2$s"
msgstr "最后由 %2$s 于 %1$s 编辑"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1546
msgid "Draft saved on %1$s by %2$s"
msgstr "%2$s 在 %1$s 保存草稿"

#: includes/widgets/video.php:446
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "当您打开隐私模式时，YouTube/Vimeo 不会存储您网站上访问者的信息，除非他们播放视频。"

#: core/kits/documents/kit.php:155
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "已发布"

#. translators: %s: Document title.
#: core/base/document.php:200
msgid "Hurray! Your %s is live."
msgstr "恭喜！您的 %s 发布成功！"

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:762
#: includes/template-library/sources/local.php:771
msgid "Access denied."
msgstr "拒绝访问。"

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "禁用默认字体"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:216 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:408 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:577
#: includes/widgets/image-carousel.php:736 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:287
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:173
#: modules/nested-accordion/widgets/nested-accordion.php:217
#: modules/nested-tabs/widgets/nested-tabs.php:234
#: modules/nested-tabs/widgets/nested-tabs.php:276
#: modules/nested-tabs/widgets/nested-tabs.php:346
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:88
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:160
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:196
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:201
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:206
msgid "Start"
msgstr "开端"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:218 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:416 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:585
#: includes/widgets/image-carousel.php:744 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:181
#: modules/nested-accordion/widgets/nested-accordion.php:221
#: modules/nested-tabs/widgets/nested-tabs.php:242
#: modules/nested-tabs/widgets/nested-tabs.php:284
#: modules/nested-tabs/widgets/nested-tabs.php:354
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:90
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:162
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:198
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:203
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:208
msgid "End"
msgstr "末端"

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "预览无法加载"

#: core/debug/loading-inspection-manager.php:43
msgid "We’re sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "我们很抱歉，但有些不对劲。单击“了解更多”并按照每个步骤快速解决问题。"

#: core/admin/admin-notices.php:145 core/admin/admin-notices.php:180
msgid "Update Notification"
msgstr "更新通知"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#: includes/editor-templates/hotkeys.php:19
#: includes/editor-templates/templates.php:211
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:53584
msgid "Actions"
msgstr "操作"

#: includes/editor-templates/hotkeys.php:144
#: includes/editor-templates/panel.php:96 assets/js/ai-admin.js:2060
#: assets/js/ai-gutenberg.js:3828 assets/js/ai-media-library.js:3689
#: assets/js/ai-unify-product-images.js:3689 assets/js/ai.js:4474
#: assets/js/editor.js:54179
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "历史"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:53587
msgid "Revisions"
msgstr "修订"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "切换到旧版本的修订标签"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:905
msgid "UI Hover Color"
msgstr "用户界面悬停颜色"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:893
msgid "UI Color"
msgstr "用户界面颜色"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "一旦您开始工作，您将能够重做/撤消您在编辑器中做的任何动作。"

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "尚无历史记录"

#: includes/widgets/video.php:366
msgid "Mute"
msgstr "静音"

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "图像灯箱效果"

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "在灯箱弹窗中打开所有图像链接。灯箱会自动在导致图像文件的任何链接上工作。"

#: includes/template-library/sources/local.php:1018
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "选择 Elementor 模板 JSON 文件或 Elementor 模板的 .zip 存档，并将它们添加到库中可用的模板列表中。"

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "链接选项"

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:365
msgid "Version Control"
msgstr "版本控制"

#: includes/elements/column.php:906 includes/elements/container.php:1844
#: includes/elements/section.php:1353 includes/widgets/common-base.php:757
#: modules/floating-buttons/base/widget-contact-button-base.php:1415
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "动画延迟"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1264
#: includes/controls/groups/background.php:673 includes/elements/column.php:361
#: includes/elements/column.php:521 includes/elements/column.php:629
#: includes/elements/container.php:721 includes/elements/container.php:935
#: includes/elements/container.php:1095 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:823
#: includes/widgets/common-base.php:938 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:385 includes/widgets/icon-box.php:475
#: includes/widgets/icon-box.php:710 includes/widgets/icon-list.php:471
#: includes/widgets/icon-list.php:698 includes/widgets/image-box.php:482
#: includes/widgets/image-box.php:610 includes/widgets/image.php:501
#: includes/widgets/text-editor.php:389
#: includes/widgets/traits/button-trait.php:450
#: modules/floating-buttons/base/widget-contact-button-base.php:1492
#: modules/floating-buttons/base/widget-contact-button-base.php:2231
#: modules/nested-tabs/widgets/nested-tabs.php:609
#: modules/shapes/widgets/text-path.php:460
#: modules/shapes/widgets/text-path.php:638
msgid "Transition Duration"
msgstr "过渡时间"

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "在新窗口中打开"

#: includes/rollback.php:165 includes/settings/tools.php:193
#: includes/settings/tools.php:368 assets/js/admin.js:2252
msgid "Rollback to Previous Version"
msgstr "回滚到以前的版本"

#: includes/settings/tools.php:389
msgid "Warning: Please backup your database before making the rollback."
msgstr "警告：请在回滚之前备份您的数据库。"

#: includes/settings/tools.php:381
msgid "Rollback Version"
msgstr "回滚版本"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "开关编辑器加载程序方法"

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "集成"

#: includes/settings/tools.php:412
msgid "Beta Tester"
msgstr "Beta 测试者"

#: includes/settings/tools.php:396
msgid "Become a Beta Tester"
msgstr "成为Beta测试者"

#: includes/elements/column.php:797 includes/elements/container.php:1748
#: includes/elements/section.php:1264 includes/widgets/common-base.php:663
msgid "Z-Index"
msgstr "Z-index"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "添加 nofollow"

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "设置小部件之间的默认间距（默认值：20px）"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:237
msgid "Widgets Space"
msgstr "小部件间距"

#: includes/settings/tools.php:420
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "请注意：我们不建议在生产站点上更新到 beta 版。"

#: includes/settings/tools.php:399
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "打开 Beta 测试程序，以便在 Elementor 或 Elementor Pro 的新 Beta 版本可用时收到通知。 Beta 版本不会自动安装。您始终可以选择忽略它。"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:372
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Elementor 版本 %s 遇到问题？回滚到问题出现之前的先前版本。"

#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:184 assets/js/atomic-widgets-editor.js:854
#: assets/js/editor.js:32538 assets/js/editor.js:32730
#: assets/js/editor.js:35154 assets/js/editor.js:35624
#: assets/js/editor.js:35725 assets/js/editor.js:36051
#: assets/js/editor.js:39419 assets/js/editor.js:50506
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:21
msgid "Edit %s"
msgstr "编辑 %s"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "外阴影"

#: includes/settings/settings.php:419
msgid "Internal Embedding"
msgstr "内部嵌入"

#: includes/settings/settings.php:418
msgid "External File"
msgstr "外部文件"

#: includes/settings/settings.php:412
msgid "CSS Print Method"
msgstr "CSS 打印方法"

#: core/base/document.php:2000 modules/ai/preferences.php:67
#: assets/js/element-manager-admin.js:2282
#: assets/js/element-manager-admin.js:2359
msgid "Status"
msgstr "状态"

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:434 includes/settings/settings.php:451
#: includes/settings/settings.php:463 includes/settings/tools.php:418
#: modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:382
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/ai-admin.js:64 assets/js/ai-gutenberg.js:1693
#: assets/js/ai-media-library.js:1693 assets/js/ai-unify-product-images.js:1693
#: assets/js/ai.js:1693 assets/js/app-packages.js:2833 assets/js/app.js:3267
#: assets/js/common.js:2146 assets/js/editor.js:42299
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:75
msgid "Enable"
msgstr "启用"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "用于服务器配置冲突的故障排除."

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/tools.php:417
#: modules/element-cache/module.php:150 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:381
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "禁用"

#: core/kits/documents/kit.php:154 includes/maintenance-mode.php:215
#: assets/js/editor.js:53856
msgid "Disabled"
msgstr "已停用"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "选择"

#: core/settings/editor-preferences/model.php:108
msgid "Canvas"
msgstr "画布"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "选择模板"

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "选择即将上线模式（返回 HTTP 200）或维护模式（返回 HTTP 503）。"

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "维护"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "选择模式"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "维护模式"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:12712
#: assets/js/editor.js:21412
msgid "Template"
msgstr "模板"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "角色"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "隐藏标题"

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:9600
msgid "Edit Template"
msgstr "编辑模板"

#: includes/widgets/common-base.php:1196 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:503
msgid "Space"
msgstr "间隔"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "维护模式"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "要启用维护模式，您必须为维护模式页设置一个模板."

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "登录"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "谁可以访问"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "即将返回HTTP 200代码，这意味着该站点已准备好进行索引."

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "维护模式返回HTTP 503代码，因此搜索引擎知道稍后会返回。不推荐使用这种模式超过两天."

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "马上就来"

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "页面标题选择器"

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "把您的整个站点设置为维护模式，这意味着站点暂时离线维护，或者设置为即将到来的模式，这意味着该站点离线，直到它准备好启动."

#: includes/editor-templates/hotkeys.php:57
msgid "Paste Style"
msgstr "粘贴样式"

#: includes/widgets/text-editor.php:148 includes/widgets/text-editor.php:410
msgid "Drop Cap"
msgstr "首字下沉"

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "Elementor 允许您隐藏页面标题。这适用于具有“h1.entry-title”选择器的主题。如果您的主题选择器不同，请在上面输入。"

#: includes/elements/container.php:1320 includes/elements/section.php:1115
msgid "Bring to Front"
msgstr "置于顶层"

#: includes/shapes.php:191
msgctxt "Shapes"
msgid "Waves"
msgstr "波浪"

#: includes/shapes.php:179
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "扇形不透明度"

#: includes/shapes.php:161
msgctxt "Shapes"
msgid "Triangle"
msgstr "三角形"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "山"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "列表"

#: includes/shapes.php:165
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "不对称三角形"

#: includes/elements/container.php:1307 includes/elements/section.php:1102
msgid "Invert"
msgstr "反转"

#: includes/elements/container.php:1293 includes/elements/section.php:1088
msgid "Flip"
msgstr "翻转"

#: includes/elements/container.php:1161 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "形状分隔线"

#: includes/shapes.php:147
msgctxt "Shapes"
msgid "Clouds"
msgstr "云"

#: includes/shapes.php:212
msgctxt "Shapes"
msgid "Book"
msgstr "书"

#: includes/shapes.php:208
msgctxt "Shapes"
msgid "Split"
msgstr "分割"

#: includes/shapes.php:204
msgctxt "Shapes"
msgid "Arrow"
msgstr "箭头"

#: includes/shapes.php:186
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "不对称曲线"

#: includes/shapes.php:182
msgctxt "Shapes"
msgid "Curve"
msgstr "曲线"

#: includes/shapes.php:175
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "倾斜不透明度"

#: includes/shapes.php:170
msgctxt "Shapes"
msgid "Tilt"
msgstr "倾斜"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:366
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:721 includes/widgets/video.php:869
msgid "Lightbox"
msgstr "灯箱"

#: includes/shapes.php:200
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "波浪样式"

#: includes/shapes.php:196
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "波浪笔刷"

#: includes/shapes.php:156
msgctxt "Shapes"
msgid "Pyramids"
msgstr "金字塔"

#: includes/shapes.php:153 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "之字型"

#: includes/shapes.php:141
msgctxt "Shapes"
msgid "Drops"
msgstr "滴状物"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "Space Between (首尾无内部等间距)"

#: includes/elements/column.php:809 includes/elements/container.php:1760
#: includes/elements/section.php:1276 includes/widgets/common-base.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:3088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:129
#: modules/nested-tabs/widgets/nested-tabs.php:151
msgid "CSS ID"
msgstr "CSS ID"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "导航宽度"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:148
#: includes/base/element-base.php:883 includes/elements/column.php:346
#: includes/elements/column.php:475 includes/elements/column.php:594
#: includes/elements/container.php:706 includes/elements/container.php:887
#: includes/elements/container.php:1048 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:808
#: includes/widgets/common-base.php:903 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:367 includes/widgets/icon-box.php:433
#: includes/widgets/icon-box.php:687 includes/widgets/icon-list.php:451
#: includes/widgets/icon-list.php:679 includes/widgets/icon.php:256
#: includes/widgets/image-box.php:449 includes/widgets/image-box.php:587
#: includes/widgets/image.php:468 includes/widgets/text-editor.php:371
#: includes/widgets/traits/button-trait.php:392
#: modules/floating-buttons/base/widget-contact-button-base.php:1251
#: modules/floating-buttons/base/widget-contact-button-base.php:2010
#: modules/floating-buttons/base/widget-contact-button-base.php:2492
#: modules/floating-buttons/base/widget-contact-button-base.php:2672
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:663
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:773
#: modules/nested-tabs/widgets/nested-tabs.php:954
#: modules/shapes/widgets/text-path.php:433
#: modules/shapes/widgets/text-path.php:572
msgid "Hover"
msgstr "悬停"

#: includes/elements/column.php:818 includes/elements/container.php:1769
#: includes/elements/section.php:1285 includes/widgets/common-base.php:683
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3097
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:138
#: modules/nested-tabs/widgets/nested-tabs.php:160
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "添加您的自定义 ID，无需使用井号键。例如：my-id"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "类型"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "位置"

#: core/admin/admin.php:378
msgid "Video Tutorials"
msgstr "视频教程"

#: core/admin/admin.php:378
msgid "View Elementor Video Tutorials"
msgstr "查看 Elementor 视频教程"

#: core/admin/admin.php:377
msgid "Docs & FAQs"
msgstr "文档 & 常见问题解答"

#: core/admin/admin.php:377
msgid "View Elementor Documentation"
msgstr "查阅 Elementor 参考资料"

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "选中此框将禁用 Elementor 的默认字体，并使 Elementor 继承主题中的字体。"

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "选中此框将禁用 Elementor 的默认颜色，并使 Elementor 继承主题中的颜色。"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "M j @ H:i"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "自动保存"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "修改"

#: includes/settings/tools.php:352
msgid "Update Site Address (URL)"
msgstr "更新站点网址 (URL)"

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "它看起来像后修订功能不可用在您的站点。"

#: includes/settings/tools.php:356
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "输入您的 WordPress 安装的旧 URL 和新 URL, 更新所有 Elementor 数据 (相关的域名转让或移到 'HTTPS')."

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s之前（%2$s）"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:334 includes/settings/tools.php:338
#: includes/settings/tools.php:355
msgid "Replace URL"
msgstr "替换网址"

#: modules/apps/admin-apps-page.php:177
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "作者"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "开始设计页面，您将可以在此处查看整个修订历史记录。"

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "尚未保存修订"

#. translators: 1: Minimum recommended_memory, 2: Preferred memory, 3:
#. WordPress wp-config memory documentation.
#: modules/system-info/reporters/server.php:170
msgid "We recommend setting memory to at least %1$s. (%2$s or higher is preferred) For more information, read about <a href=\"%3$s\">how to increase memory allocated to PHP</a>."
msgstr "我们建议将内存设置为至少%1$s。 （%2$s 或更高版本优先）有关详细信息，请阅读<a href=\"%3$s\">如何增加分配给 PHP 的内存</a>。"

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "修订历史记录可让您保存以前的版本，并随时恢复它们。"

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "千位数分隔"

#: includes/base/element-base.php:927 includes/base/element-base.php:1091
#: includes/widgets/common-base.php:982 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:150
#: includes/widgets/video.php:724 modules/shapes/widgets/text-path.php:220
msgid "Off"
msgstr "关"

#: includes/base/element-base.php:926 includes/base/element-base.php:1090
#: includes/widgets/common-base.php:981 includes/widgets/icon-list.php:286
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:151
#: includes/widgets/video.php:725 modules/shapes/widgets/text-path.php:219
msgid "On"
msgstr "开"

#: includes/managers/controls.php:1093
msgid "Meet Our Custom CSS"
msgstr "了解我们的自定义 CSS"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "得到更多有关 Elementor 专业版本的说明"

#: modules/promotions/widgets/pro-widget-promotion.php:75
#: assets/js/ai-admin.js:7932 assets/js/ai-gutenberg.js:9780
#: assets/js/ai-layout.js:3413 assets/js/ai-media-library.js:9561
#: assets/js/ai-unify-product-images.js:9561 assets/js/ai.js:11026
msgid "Go Pro"
msgstr "到专业版本"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1075
msgid "Custom CSS"
msgstr "自定义 CSS"

#: includes/managers/controls.php:1081
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "自定义 CSS 允许您将 CSS 代码添加到任何小部件，并查看它在编辑器中实时呈现。"

#: includes/editor-templates/panel-elements.php:100
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "使用此功能，您可以将小部件保存为全局，然后将其添加到多个区域。所有区域都可以从一个地方进行编辑。"

#: includes/editor-templates/panel-elements.php:99
msgid "Meet Our Global Widget"
msgstr "认识我们的全局小部件"

#: includes/base/widget-base.php:312 includes/base/widget-base.php:321
msgid "Skin"
msgstr "皮肤"

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "特大号"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "特小号"

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "完善 Elementor"

#: includes/frontend.php:1257
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "无效数据：模板 ID 不能​​与当前编辑的模板相同，换一个其他的试试。"

#: includes/editor-templates/panel.php:209
msgid "%s are disabled"
msgstr "%s被禁用"

#: includes/editor-templates/panel.php:173
msgid "Update changes to page"
msgstr "更新变更到所有页面"

#: core/admin/admin-notices.php:241
msgid "No thanks"
msgstr "不，谢谢"

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "拉伸段"

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "设置内容区域的默认宽度 (默认值: 1140px)"

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "拉伸段以适合"

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "输入适合拉伸段的父元素选择器（例如 #primary / .wrapper / main 等）。留空以适合页面宽度。"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "使用 JS 将该段拉伸到页面的整个宽度。"

#: core/admin/admin-notices.php:229
msgid "Learn more."
msgstr "了解更多。"

#: includes/elements/section.php:1384
msgid "Reverse Columns"
msgstr "反向列"

#: core/settings/editor-preferences/model.php:124
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4354
msgid "Mobile"
msgstr "手机"

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "将值链接在一起"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "简码"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "远程"

#: includes/template-library/sources/local.php:1016
msgid "Import Templates"
msgstr "导入模板"

#: includes/template-library/sources/local.php:988
msgid "Export Template"
msgstr "导出模板"

#: includes/template-library/sources/cloud.php:106
#: includes/template-library/sources/cloud.php:327
#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(无标题)"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "类型"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "模板"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:453
msgid "Tools"
msgstr "工具"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:10574
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "页面"

#: core/base/document.php:172 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1417
#: assets/js/ai-admin.js:588 assets/js/ai-gutenberg.js:2356
#: assets/js/ai-layout.js:420 assets/js/ai-media-library.js:2217
#: assets/js/ai-unify-product-images.js:2217 assets/js/ai.js:3002
#: assets/js/app-packages.js:2027 assets/js/app-packages.js:3994
#: assets/js/app-packages.js:4513 assets/js/app.js:2216 assets/js/app.js:4329
#: assets/js/app.js:4732 assets/js/app.js:6840 assets/js/app.js:7671
#: assets/js/app.js:11222 assets/js/cf70912a0f34653ad242.bundle.js:211
#: assets/js/cf70912a0f34653ad242.bundle.js:212 assets/js/editor.js:49772
#: assets/js/import-export-admin.js:313
msgid "Close"
msgstr "关闭"

#: includes/editor-templates/templates.php:328
#: includes/editor-templates/templates.php:361
#: includes/editor-templates/templates.php:418
#: includes/template-library/sources/local.php:1203 assets/js/app.js:11321
msgid "Export"
msgstr "导出"

#: includes/editor-templates/hotkeys.php:181
#: includes/editor-templates/templates.php:554
#: includes/editor-templates/templates.php:570
#: includes/editor-templates/templates.php:584
msgid "Template Library"
msgstr "模板库"

#: includes/editor-templates/templates.php:305
#: includes/editor-templates/templates.php:393
#: includes/editor-templates/templates.php:439
#: includes/editor-templates/templates.php:453 assets/js/ai-admin.js:6721
#: assets/js/ai-gutenberg.js:8569 assets/js/ai-media-library.js:8350
#: assets/js/ai-unify-product-images.js:8350 assets/js/ai.js:9815
#: assets/js/editor.js:8516
msgid "Insert"
msgstr "插入"

#: includes/editor-templates/templates.php:484
msgid "Enter Template Name"
msgstr "输入模板名称"

#: includes/editor-templates/templates.php:221
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "敬请关注！更棒的模板即将推出。"

#: includes/editor-templates/templates.php:41
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1473
msgid "Back to Library"
msgstr "返回到模版库"

#: includes/template-library/sources/local.php:1025
msgid "Import Now"
msgstr "现在输入"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "局部"

#: includes/editor-templates/templates.php:18
#: includes/editor-templates/templates.php:19 includes/settings/tools.php:322
#: includes/settings/tools.php:325
msgid "Sync Library"
msgstr "同步模版库"

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "添加模板"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:11713
msgid "Library"
msgstr "模版库"

#: includes/settings/tools.php:326
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "Elementor 模版库每天会自动更新，您也可以点击同步按钮手动更新。"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1731 assets/js/app.js:10342
msgid "Saved Templates"
msgstr "已保存模板"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "如果您想修改主题的源代码，我们建议使用<a href=\"%s\">子主题</a>。"

#: app/modules/import-export/module.php:152
#: app/modules/import-export/module.php:164 core/admin/admin-notices.php:328
#: modules/apps/admin-apps-page.php:187 modules/safe-mode/module.php:359
#: modules/safe-mode/module.php:368
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:105
#: assets/js/app-packages.js:2706 assets/js/app-packages.js:5662
#: assets/js/app-packages.js:5770 assets/js/app.js:3140 assets/js/app.js:7124
#: assets/js/app.js:8265 assets/js/app.js:9762 assets/js/app.js:10071
#: assets/js/app.js:10117 assets/js/app.js:11221 assets/js/editor.js:17108
#: assets/js/editor.js:30635 assets/js/editor.js:30666
#: assets/js/editor.js:43038 assets/js/element-manager-admin.js:2169
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3842
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:17
msgid "Learn More"
msgstr "了解更多"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:10360
#: assets/js/editor.js:49523
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:222
msgid "Global Fonts"
msgstr "全局字体"

#: core/base/traits/shared-widget-controls-trait.php:289
#: includes/widgets/icon-box.php:490 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:503 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:465
#: modules/floating-buttons/base/widget-contact-button-base.php:1474
#: modules/floating-buttons/base/widget-contact-button-base.php:2510
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:601
#: modules/shapes/widgets/text-path.php:452
msgid "Hover Animation"
msgstr "悬停动画"

#: modules/floating-buttons/base/widget-contact-button-base.php:2214
#: assets/js/ai-admin.js:3607 assets/js/ai-gutenberg.js:5375
#: assets/js/ai-media-library.js:5236 assets/js/ai-unify-product-images.js:5236
#: assets/js/ai.js:6054
msgid "Animation"
msgstr "动画"

#: includes/elements/column.php:892 includes/elements/container.php:1830
#: includes/elements/section.php:1339 includes/widgets/common-base.php:743
#: modules/floating-buttons/base/widget-contact-button-base.php:1404
#: modules/floating-buttons/base/widget-contact-button-base.php:2787
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "慢速"

#: includes/elements/column.php:894 includes/elements/container.php:1832
#: includes/elements/section.php:1341 includes/widgets/common-base.php:745
#: modules/floating-buttons/base/widget-contact-button-base.php:1406
#: modules/floating-buttons/base/widget-contact-button-base.php:2789
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "快速"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:44
msgid "Blur"
msgstr "模糊"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:43
msgid "Vertical"
msgstr "垂直"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:42
msgid "Horizontal"
msgstr "水平"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:45
msgid "Spread"
msgstr "扩散"

#: includes/elements/column.php:879 includes/elements/container.php:1817
#: includes/elements/section.php:1326 includes/widgets/common-base.php:730
#: includes/widgets/video.php:917
#: modules/floating-buttons/base/widget-contact-button-base.php:1390
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "进入动画"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "禁用默认颜色"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "内阴影"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "我的技能"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "社交图标"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2084
#: modules/floating-buttons/base/widget-contact-button-base.php:2175
#: modules/floating-buttons/base/widget-contact-button-base.php:2868
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "圆角"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "官方颜色"

#: includes/widgets/testimonial.php:201
msgid "Aside"
msgstr "在旁边"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:185
msgid "Custom Colors"
msgstr "自定义颜色"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:111
msgid "Testimonial"
msgstr "推荐信"

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "购买按钮"

#: core/admin/admin-notices.php:141 core/admin/admin-notices.php:149
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "立即更新"

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:909
#: modules/link-in-bio/base/widget-link-in-bio-base.php:521
#: modules/link-in-bio/base/widget-link-in-bio-base.php:774
msgid "Username"
msgstr "用户名"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "分享按钮"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "我无法让插件工作"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "请分享哪个插件"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "注释"

#: includes/widgets/audio.php:182 includes/widgets/video.php:561
msgid "Download Button"
msgstr "下载按钮"

#: includes/elements/column.php:388 includes/elements/container.php:760
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1480
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:83
msgid "Background Overlay"
msgstr "背景覆盖"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "扩展"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "快速反馈"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "请分享原因"

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "喜欢按钮"

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "播放次数"

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "视频播放器"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "其他"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "我找到一个更好的插件"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "我不再需要插件了"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:136
msgid "View Elementor version %s details"
msgstr "查看 Elementor 版本 %s 细节"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "临时停用"

#: core/base/providers/social-network-provider.php:168
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "如果您有时间，可以告诉我们为什么停用 Elementor 吗？"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:132
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "Elementor 页面生成器 现已推出新版本。 <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">查看版本 %3$s 详细信息</a> 或 <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">立即更新</a>。"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:276 includes/elements/container.php:631
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:497 includes/widgets/common-base.php:781
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:521
#: modules/floating-buttons/base/widget-contact-button-base.php:1676
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1461
#: assets/js/ai-admin.js:11330 assets/js/ai-gutenberg.js:13178
#: assets/js/ai-media-library.js:12959
#: assets/js/ai-unify-product-images.js:12959 assets/js/ai.js:14424
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:17
msgid "Background"
msgstr "背景"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "宽阔"

#: includes/widgets/image-carousel.php:343 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "自定义 URL"

#: includes/editor-templates/hotkeys.php:49 assets/js/editor.js:32770
#: assets/js/editor.js:35083 assets/js/editor.js:44460
#: assets/js/editor.js:45498
msgid "Paste"
msgstr "粘贴"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:302
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:309
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:22
msgid "General"
msgstr "常规"

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:534
#: modules/nested-tabs/widgets/nested-tabs.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:81
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:184
msgid "Direction"
msgstr "方向"

#: includes/widgets/image-carousel.php:577
#: includes/widgets/image-carousel.php:640
msgid "Outside"
msgstr "外面"

#: includes/widgets/image-carousel.php:523
msgid "Animation Speed"
msgstr "动画速度"

#: includes/widgets/image-carousel.php:576
#: includes/widgets/image-carousel.php:641
msgid "Inside"
msgstr "里面"

#: includes/elements/container.php:535 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:416
msgid "Additional Options"
msgstr "其他选项"

#: includes/widgets/image-carousel.php:215
msgid "Arrows and Dots"
msgstr "箭头和圆点"

#: includes/elements/column.php:888 includes/elements/container.php:1826
#: includes/elements/section.php:1335 includes/widgets/common-base.php:739
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1400
#: modules/floating-buttons/base/widget-contact-button-base.php:2783
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:367
msgid "Animation Duration"
msgstr "动画持续时间"

#: includes/widgets/image-carousel.php:198
msgid "Image Stretch"
msgstr "图像拉伸"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:760
#: includes/widgets/image-carousel.php:894
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:352 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:627
#: modules/nested-tabs/widgets/nested-tabs.php:915
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
msgid "Spacing"
msgstr "间距"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "可以将原始图像大小裁剪为任意自定义大小。您还可以设置一个值的高度或宽度，以保持原来的大小比."

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:125
#: includes/widgets/image-carousel.php:134
msgid "Image Carousel"
msgstr "图像轮播"

#: includes/widgets/video.php:535
msgid "Intro Byline"
msgstr "简介署名"

#: includes/widgets/alert.php:240
msgid "Left Border Width"
msgstr "左边框宽度"

#: core/base/providers/social-network-provider.php:186
#: includes/widgets/video.php:138
msgid "Vimeo"
msgstr "Vimeo"

#: includes/widgets/audio.php:251 includes/widgets/video.php:549
msgid "Controls Color"
msgstr "控制颜色"

#: includes/widgets/video.php:375
msgid "Loop"
msgstr "循环"

#: includes/widgets/video.php:330
msgid "Video Options"
msgstr "视频选项"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:126 includes/widgets/video.php:739
msgid "Video"
msgstr "视频"

#: includes/widgets/video.php:521
msgid "Intro Portrait"
msgstr "介绍画象"

#: includes/widgets/video.php:507
msgid "Intro Title"
msgstr "介绍标题"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "服务器没有安装和/或启用 ImageMagick 或 GD！WordPress 需要这些库才能调整图像大小。在继续之前，请联系您的服务器管理员启用此功能。"

#: includes/controls/groups/background.php:525
msgid "Video Link"
msgstr "视频链接"

#: includes/widgets/image-carousel.php:506
msgid "Effect"
msgstr "效果"

#: includes/widgets/image-carousel.php:511
msgid "Fade"
msgstr "淡入淡出"

#: includes/controls/gallery.php:92
msgid "Edit gallery"
msgstr "编辑图库"

#: includes/controls/media.php:362
msgctxt "Image Size Control"
msgid "Full"
msgstr "原图"

#: includes/elements/column.php:183 includes/widgets/icon-box.php:265
#: includes/widgets/icon-list.php:573 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "垂直对齐"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:493
msgid "Infinite Loop"
msgstr "无限循环"

#: includes/widgets/image-carousel.php:217
msgid "Dots"
msgstr "圆点"

#: includes/controls/groups/background.php:107
msgid "Slideshow"
msgstr "幻灯片"

#: includes/controls/groups/typography.php:321
msgctxt "Typography Control"
msgid "Width"
msgstr "宽度"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "这是标题"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:270
msgid "Title HTML Tag"
msgstr "标题 HTML 标签"

#: includes/elements/column.php:837 includes/elements/container.php:1788
#: includes/elements/section.php:1304 includes/widgets/common-base.php:701
#: modules/floating-buttons/base/widget-contact-button-base.php:3114
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "添加不带点的自定义类。例如：my-class"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "如果所选字体不可用，则使用字体列表."

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:22048
#: assets/js/editor.js:23947 assets/js/editor.js:24356
#: assets/js/editor.js:39581
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
#: assets/js/packages/editor-canvas/editor-canvas.js:2
#: assets/js/packages/editor-canvas/editor-canvas.strings.js:6
msgid "Elements"
msgstr "元素"

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "表单"

#: core/admin/admin.php:343 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:80
#: includes/managers/controls.php:339
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/div-block/div-block.php:62
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:9572
#: assets/js/editor.js:40481 assets/js/editor.js:49889
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:38
msgid "Settings"
msgstr "设置"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "大写字母"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "小写字母"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "强制斜体"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "字体斜体"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "首字母大写"

#: includes/widgets/progress.php:222
msgid "Web Designer"
msgstr "网页设计师"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "下载系统信息"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: includes/widgets/video.php:387
msgid "Player Controls"
msgstr "播放器控制"

#: core/base/providers/social-network-provider.php:138
#: includes/widgets/video.php:137
msgid "YouTube"
msgstr "YouTube"

#: includes/widgets/progress.php:188
msgid "Percentage"
msgstr "百分比"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:426
msgid "Framed"
msgstr "框架"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:207
#: modules/nested-tabs/widgets/nested-tabs.php:873
msgid "Before"
msgstr "之前"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1041 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:191
#: includes/widgets/icon-box.php:502 includes/widgets/icon-list.php:492
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:589
#: includes/widgets/image-carousel.php:673 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:327
#: includes/widgets/text-editor.php:474
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:825
#: modules/floating-buttons/base/widget-contact-button-base.php:1114
#: modules/floating-buttons/base/widget-contact-button-base.php:1539
#: modules/floating-buttons/base/widget-contact-button-base.php:1921
#: modules/floating-buttons/base/widget-contact-button-base.php:2271
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1400
#: modules/nested-accordion/widgets/nested-accordion.php:603
#: modules/nested-tabs/widgets/nested-tabs.php:895
#: modules/shapes/widgets/text-path.php:251
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:94
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:14
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:171
msgid "Size"
msgstr "尺寸"

#: includes/widgets/heading.php:199
msgid "XXL"
msgstr "XXL"

#: includes/widgets/heading.php:198
msgid "XL"
msgstr "XL"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:729
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:629
#: includes/widgets/image-carousel.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:2340
#: modules/link-in-bio/base/widget-link-in-bio-base.php:892
#: modules/link-in-bio/base/widget-link-in-bio-base.php:897
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1348
msgid "Description"
msgstr "描述"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:174
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "警告"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:173
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "成功"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "排除角色"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "摘要"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:626
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1042
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:326
#: modules/link-in-bio/base/widget-link-in-bio-base.php:582
#: modules/shapes/widgets/text-path.php:111
#: modules/shapes/widgets/text-path.php:304 assets/js/ai-admin.js:3603
#: assets/js/ai-gutenberg.js:5371 assets/js/ai-media-library.js:5232
#: assets/js/ai-unify-product-images.js:5232 assets/js/ai.js:6050
msgid "Text"
msgstr "文本"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "次要"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "主要"

#: includes/managers/elements.php:351
msgid "WordPress"
msgstr "WordPress"

#: includes/managers/elements.php:288
msgid "Basic"
msgstr "基本"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "最小高度"

#: includes/elements/container.php:472 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "最小高度"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1267 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1062
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:363
#: includes/widgets/image.php:354 includes/widgets/progress.php:266
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:115
msgid "Height"
msgstr "高度"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:267
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:185
#: modules/nested-tabs/widgets/nested-tabs.php:246
#: modules/nested-tabs/widgets/nested-tabs.php:288
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:199
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:204
msgid "Stretch"
msgstr "拉伸"

#: core/document-types/page-base.php:132 includes/elements/column.php:772
#: includes/elements/container.php:1370 includes/elements/section.php:1233
#: includes/widgets/common-base.php:200
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:112
msgid "Margin"
msgstr "外距"

#: core/kits/documents/tabs/theme-style-typography.php:18
#: core/kits/documents/tabs/theme-style-typography.php:37
#: includes/controls/groups/typography.php:436 includes/elements/column.php:674
#: includes/elements/section.php:1137 assets/js/editor-modules.js:1431
#: assets/js/editor.js:45170
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:16
msgid "Typography"
msgstr "排版"

#: core/base/traits/shared-widget-controls-trait.php:270
#: core/settings/editor-preferences/model.php:126
#: includes/base/element-base.php:1386 includes/editor-templates/panel.php:283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4338
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "桌面"

#: core/kits/documents/tabs/theme-style-typography.php:109
#: includes/elements/container.php:597 includes/widgets/audio.php:111
#: includes/widgets/heading.php:177 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:337
#: includes/widgets/image-carousel.php:351
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:183
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:150
#: includes/widgets/video.php:175 includes/widgets/video.php:199
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:931
#: modules/floating-buttons/base/widget-contact-button-base.php:1059
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:263
#: modules/link-in-bio/base/widget-link-in-bio-base.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:640
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1096
#: modules/shapes/widgets/text-path.php:158
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:18
msgid "Link"
msgstr "链接"

#: includes/widgets/image-carousel.php:478
msgid "Autoplay Speed"
msgstr "自动播放速度"

#: includes/widgets/image-carousel.php:216
#: includes/widgets/image-carousel.php:560
msgid "Arrows"
msgstr "箭头"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "选择侧边栏"

#: includes/widgets/progress.php:331
msgid "Title Style"
msgstr "标题样式"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "侧边栏"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:233
msgid "Progress Bar"
msgstr "进度条"

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "菜单锚点"

#: includes/fonts.php:71
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:243
msgid "System"
msgstr "系统"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:303
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:223
msgid "Dashed"
msgstr "虚线"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:224
msgid "Dotted"
msgstr "点线"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:225
msgid "Double"
msgstr "双实线"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:300 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:222
msgid "Solid"
msgstr "实线"

#: includes/editor-templates/hotkeys.php:172
#: includes/editor-templates/panel.php:99
msgid "Responsive Mode"
msgstr "响应模式"

#: includes/editor-templates/panel.php:69
#: includes/editor-templates/panel.php:70
msgid "Menu"
msgstr "菜单"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "选择图标"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1005
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:161
msgid "Add Item"
msgstr "新增项目"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1430 includes/elements/container.php:1474
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:144 includes/widgets/common-base.php:234
#: includes/widgets/common-base.php:306 includes/widgets/common-base.php:350
#: includes/widgets/common-base.php:1046 includes/widgets/common-base.php:1103
#: includes/widgets/image-carousel.php:764
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1212
#: modules/floating-buttons/base/widget-contact-button-base.php:1264
#: modules/floating-buttons/base/widget-contact-button-base.php:1351
#: modules/floating-buttons/base/widget-contact-button-base.php:1560
#: modules/floating-buttons/base/widget-contact-button-base.php:1726
#: modules/floating-buttons/base/widget-contact-button-base.php:2290
#: modules/floating-buttons/base/widget-contact-button-base.php:2616
#: modules/floating-buttons/base/widget-contact-button-base.php:2685
#: modules/floating-buttons/base/widget-contact-button-base.php:2816
#: modules/shapes/module.php:52 assets/js/editor.js:47912
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:47
#: assets/js/packages/editor-controls/editor-controls.strings.js:93
#: assets/js/packages/editor-controls/editor-controls.strings.js:109
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:170
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:177
msgid "Custom"
msgstr "自定义"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:175
#: includes/editor-templates/templates.php:446
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:10276 assets/js/editor.js:40562
msgid "Apply"
msgstr "应用"

#: core/editor/loader/v1/templates/editor-body-v1-view.php:31
#: core/editor/loader/v2/templates/editor-body-v2-view.php:33
#: includes/editor-templates/templates.php:296 assets/js/ai-admin.js:7576
#: assets/js/ai-gutenberg.js:9424 assets/js/ai-layout.js:3057
#: assets/js/ai-media-library.js:9205 assets/js/ai-unify-product-images.js:9205
#: assets/js/ai.js:10670 assets/js/editor.js:30293
msgid "Preview"
msgstr "预览"

#: includes/editor-templates/hotkeys.php:81
#: includes/editor-templates/templates.php:22
#: includes/editor-templates/templates.php:23
#: includes/editor-templates/templates.php:477
#: includes/editor-templates/templates.php:489 assets/js/e-home-screen.js:246
#: assets/js/editor.js:10652 assets/js/editor.js:47361
#: assets/js/element-manager-admin.js:2412
#: assets/js/kit-elements-defaults-editor.js:598
msgid "Save"
msgstr "保存"

#: core/admin/admin.php:621 core/admin/menu/main.php:41
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "帮助"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "重置"

#: includes/editor-templates/hotkeys.php:125
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:86
#: includes/editor-templates/panel.php:90 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:34004
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Structure"
msgstr "结构"

#: includes/managers/controls.php:335 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:297 assets/js/ai-admin.js:10518
#: assets/js/ai-gutenberg.js:12366 assets/js/ai-media-library.js:12147
#: assets/js/ai-unify-product-images.js:12147 assets/js/ai.js:13612
#: assets/js/editor.js:9575 assets/js/editor.js:39386
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:23
msgid "Style"
msgstr "样式"

#: modules/floating-buttons/base/widget-contact-button-base.php:1207
#: modules/floating-buttons/base/widget-contact-button-base.php:1259
#: modules/floating-buttons/base/widget-contact-button-base.php:1346
#: modules/floating-buttons/base/widget-contact-button-base.php:1555
#: modules/floating-buttons/base/widget-contact-button-base.php:1721
#: modules/floating-buttons/base/widget-contact-button-base.php:2611
#: modules/floating-buttons/base/widget-contact-button-base.php:2680
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:444
msgid "Colors"
msgstr "颜色"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:118 includes/elements/container.php:1362
#: includes/elements/section.php:231 includes/managers/controls.php:338
#: includes/managers/elements.php:284 includes/widgets/common-base.php:182
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2926
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:107
#: assets/js/editor.js:39392
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
msgid "Layout"
msgstr "布局"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:763
#: includes/elements/section.php:1225 includes/managers/controls.php:336
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2919
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:78
#: modules/floating-buttons/module.php:83 assets/js/editor.js:9578
#: assets/js/editor.js:39389
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3184
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1444
msgid "Advanced"
msgstr "高级设置"

#: core/settings/editor-preferences/model.php:91
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:393 includes/elements/container.php:1233
#: includes/elements/section.php:263 includes/elements/section.php:1028
#: includes/widgets/common-base.php:227 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:344 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2840
#: modules/nested-tabs/widgets/nested-tabs.php:312
#: modules/shapes/widgets/text-path.php:540
#: modules/shapes/widgets/text-path.php:611
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:114
msgid "Width"
msgstr "宽度"

#: includes/elements/column.php:731 includes/elements/section.php:1193
msgid "Text Align"
msgstr "文本对齐"

#: includes/elements/column.php:719 includes/elements/section.php:1181
msgid "Link Hover Color"
msgstr "链接悬停颜色"

#: includes/elements/column.php:707 includes/elements/section.php:1169
#: includes/widgets/heading.php:374 includes/widgets/text-editor.php:358
#: includes/widgets/text-editor.php:378
msgid "Link Color"
msgstr "链接颜色"

#: includes/elements/column.php:683 includes/elements/section.php:1145
msgid "Heading Color"
msgstr "标题颜色"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:55
#: includes/elements/column.php:695 includes/elements/section.php:1157
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:351 includes/widgets/image-carousel.php:863
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:339 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:269 includes/widgets/testimonial.php:367
#: includes/widgets/testimonial.php:412 includes/widgets/text-editor.php:343
#: includes/widgets/traits/button-trait.php:347
#: includes/widgets/traits/button-trait.php:400
#: modules/floating-buttons/base/widget-contact-button-base.php:1581
#: modules/floating-buttons/base/widget-contact-button-base.php:1613
#: modules/floating-buttons/base/widget-contact-button-base.php:1744
#: modules/floating-buttons/base/widget-contact-button-base.php:1775
#: modules/floating-buttons/base/widget-contact-button-base.php:1806
#: modules/floating-buttons/base/widget-contact-button-base.php:2114
#: modules/floating-buttons/base/widget-contact-button-base.php:2321
#: modules/floating-buttons/base/widget-contact-button-base.php:2349
#: modules/floating-buttons/base/widget-contact-button-base.php:2642
#: modules/floating-buttons/base/widget-contact-button-base.php:2711
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1105
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1301
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1329
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1357
msgid "Text Color"
msgstr "文本颜色"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "适应屏幕"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "宽"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "窄"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:379 includes/elements/section.php:249
#: includes/widgets/video.php:942
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1587
msgid "Content Width"
msgstr "内容宽度"

#: core/settings/editor-preferences/model.php:216
#: includes/widgets/image-carousel.php:211
#: includes/widgets/image-carousel.php:549
msgid "Navigation"
msgstr "导航"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "文章类型"

#: includes/editor-templates/templates.php:194
#: includes/widgets/testimonial.php:153 includes/widgets/testimonial.php:359
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:653
#: modules/floating-buttons/base/widget-contact-button-base.php:1735
msgid "Name"
msgstr "名称"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "系统信息"

#: includes/editor-templates/templates.php:198
#: includes/elements/container.php:1192 includes/elements/section.php:987
#: includes/template-library/sources/local.php:1702
#: includes/widgets/alert.php:118 includes/widgets/progress.php:168
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1091
msgid "Type"
msgstr "类型"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:172
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:5651
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3437
msgid "Info"
msgstr "信息"

#: includes/widgets/alert.php:125 includes/widgets/progress.php:175
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "危险"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/alert.php:229 includes/widgets/social-icons.php:562
#: includes/widgets/tabs.php:334 includes/widgets/toggle.php:305
#: includes/widgets/traits/button-trait.php:429
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:514
#: modules/nested-tabs/widgets/nested-tabs.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:664
#: modules/nested-tabs/widgets/nested-tabs.php:1016
msgid "Border Color"
msgstr "边框颜色"

#: core/settings/editor-preferences/model.php:184
#: includes/base/element-base.php:1400
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:156 includes/widgets/progress.php:206
#: includes/widgets/video.php:390 includes/widgets/video.php:405
#: includes/widgets/video.php:432 includes/widgets/video.php:510
#: includes/widgets/video.php:524 includes/widgets/video.php:538
#: includes/widgets/video.php:564 includes/widgets/video.php:633
#: includes/widgets/video.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:518
#: modules/floating-buttons/base/widget-contact-button-base.php:628
#: modules/floating-buttons/base/widget-contact-button-base.php:693
#: modules/floating-buttons/base/widget-contact-button-base.php:2525
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:2076
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:50
msgid "Show"
msgstr "显示"

#: core/settings/editor-preferences/model.php:185
#: includes/base/element-base.php:1399
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:157 includes/widgets/progress.php:207
#: includes/widgets/video.php:389 includes/widgets/video.php:404
#: includes/widgets/video.php:431 includes/widgets/video.php:509
#: includes/widgets/video.php:523 includes/widgets/video.php:537
#: includes/widgets/video.php:563 includes/widgets/video.php:632
#: includes/widgets/video.php:673
#: modules/floating-buttons/base/widget-contact-button-base.php:519
#: modules/floating-buttons/base/widget-contact-button-base.php:629
#: modules/floating-buttons/base/widget-contact-button-base.php:694
#: modules/floating-buttons/base/widget-contact-button-base.php:2526
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:51
msgid "Hide"
msgstr "隐藏"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:255
#: includes/widgets/tabs.php:345 includes/widgets/video.php:882
#: modules/floating-buttons/base/widget-contact-button-base.php:1235
#: modules/floating-buttons/base/widget-contact-button-base.php:1287
#: modules/floating-buttons/base/widget-contact-button-base.php:1328
#: modules/floating-buttons/base/widget-contact-button-base.php:1374
#: modules/floating-buttons/base/widget-contact-button-base.php:1690
#: modules/floating-buttons/base/widget-contact-button-base.php:1960
#: modules/floating-buttons/base/widget-contact-button-base.php:1996
#: modules/floating-buttons/base/widget-contact-button-base.php:2029
#: modules/floating-buttons/base/widget-contact-button-base.php:2133
#: modules/floating-buttons/base/widget-contact-button-base.php:2159
#: modules/floating-buttons/base/widget-contact-button-base.php:2368
#: modules/floating-buttons/base/widget-contact-button-base.php:2655
#: modules/floating-buttons/base/widget-contact-button-base.php:2724
#: modules/floating-buttons/base/widget-contact-button-base.php:2811
#: modules/floating-buttons/base/widget-contact-button-base.php:2825
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1125
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:564
#: modules/nested-tabs/widgets/nested-tabs.php:648
#: modules/nested-tabs/widgets/nested-tabs.php:1003 assets/js/ai-admin.js:14073
#: assets/js/ai-admin.js:14795 assets/js/ai-gutenberg.js:15921
#: assets/js/ai-gutenberg.js:16643 assets/js/ai-media-library.js:15702
#: assets/js/ai-media-library.js:16424
#: assets/js/ai-unify-product-images.js:15702
#: assets/js/ai-unify-product-images.js:16424 assets/js/ai.js:17167
#: assets/js/ai.js:17889
msgid "Background Color"
msgstr "背景颜色"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2334
#: assets/js/ai-admin.js:10213 assets/js/ai-admin.js:10220
#: assets/js/ai-gutenberg.js:4102 assets/js/ai-gutenberg.js:12061
#: assets/js/ai-gutenberg.js:12068 assets/js/ai-media-library.js:3963
#: assets/js/ai-media-library.js:11842 assets/js/ai-media-library.js:11849
#: assets/js/ai-unify-product-images.js:3963
#: assets/js/ai-unify-product-images.js:11842
#: assets/js/ai-unify-product-images.js:11849 assets/js/ai.js:4748
#: assets/js/ai.js:13307 assets/js/ai.js:13314
#: assets/js/element-manager-admin.js:2500
#: assets/js/element-manager-admin.js:2556
msgid "Edit"
msgstr "编辑"

#: core/base/document.php:1978
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:127 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:191 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:161
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:625
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:525
#: includes/widgets/image-carousel.php:394 includes/widgets/progress.php:117
#: includes/widgets/star-rating.php:203 includes/widgets/star-rating.php:247
#: includes/widgets/tabs.php:127 includes/widgets/tabs.php:357
#: includes/widgets/testimonial.php:168 includes/widgets/testimonial.php:404
#: includes/widgets/toggle.php:132 includes/widgets/toggle.php:350
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:65
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2312
#: modules/link-in-bio/base/widget-link-in-bio-base.php:866
#: modules/nested-accordion/widgets/nested-accordion.php:115
#: modules/nested-accordion/widgets/nested-accordion.php:563
#: modules/nested-tabs/widgets/nested-tabs.php:113
msgid "Title"
msgstr "标题"

#: includes/widgets/button.php:48 includes/widgets/button.php:93
#: includes/widgets/button.php:114
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:32
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1095
msgid "Button"
msgstr "按钮"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:131
#: includes/widgets/video.php:641
#: modules/link-in-bio/base/widget-link-in-bio-base.php:251
#: modules/link-in-bio/base/widget-link-in-bio-base.php:341
#: modules/link-in-bio/base/widget-link-in-bio-base.php:929
#: modules/link-in-bio/base/widget-link-in-bio-base.php:984
msgid "Choose Image"
msgstr "选择图像"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:203
#: modules/nested-tabs/widgets/nested-tabs.php:865
msgid "After"
msgstr "之后"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:547 includes/elements/container.php:979
#: includes/elements/section.php:831 includes/widgets/common-base.php:856
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:18
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:241
msgid "Border"
msgstr "边框"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1130
#: modules/floating-buttons/base/widget-contact-button-base.php:2396
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "图标的位置"

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:379
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:416
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:495
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-accordion/widgets/nested-accordion.php:595
#: modules/nested-tabs/widgets/nested-tabs.php:126
#: modules/nested-tabs/widgets/nested-tabs.php:847
msgid "Icon"
msgstr "图标"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "间隔"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:283
msgid "Divider"
msgstr "分隔线"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:885
#: modules/link-in-bio/base/widget-link-in-bio-base.php:481
#: modules/link-in-bio/base/widget-link-in-bio-base.php:728
msgid "Number"
msgstr "数字"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "修饰数字"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "数字后缀"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "数字前缀"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "结束数字"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "开始数字"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:251 includes/widgets/icon-box.php:293
#: includes/widgets/icon-list.php:260 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:221
#: includes/widgets/text-editor.php:258 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:283
#: modules/shapes/widgets/text-path.php:172
msgid "Alignment"
msgstr "对齐"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:126
#: core/kits/documents/tabs/theme-style-typography.php:155
#: core/kits/documents/tabs/theme-style-typography.php:200
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:458
#: includes/elements/container.php:855 includes/elements/container.php:1219
#: includes/elements/section.php:726 includes/elements/section.php:1014
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:508 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:320
#: includes/widgets/icon-box.php:670 includes/widgets/icon-box.php:694
#: includes/widgets/icon-box.php:757 includes/widgets/icon-list.php:396
#: includes/widgets/icon-list.php:433 includes/widgets/icon-list.php:458
#: includes/widgets/icon-list.php:662 includes/widgets/icon-list.php:686
#: includes/widgets/image-box.php:570 includes/widgets/image-box.php:594
#: includes/widgets/image-box.php:657 includes/widgets/image-carousel.php:609
#: includes/widgets/image-carousel.php:693 includes/widgets/progress.php:241
#: includes/widgets/progress.php:299 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:378 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:532
#: includes/widgets/video.php:809
#: modules/floating-buttons/base/widget-contact-button-base.php:1886
#: modules/floating-buttons/base/widget-contact-button-base.php:2285
#: modules/floating-buttons/base/widget-contact-button-base.php:2298
#: modules/floating-buttons/base/widget-contact-button-base.php:2536
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1389
#: modules/nested-accordion/widgets/nested-accordion.php:686
#: modules/nested-accordion/widgets/nested-accordion.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:744
#: modules/nested-tabs/widgets/nested-tabs.php:780
#: modules/nested-tabs/widgets/nested-tabs.php:816
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/nested-tabs/widgets/nested-tabs.php:976
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:440
#: modules/shapes/widgets/text-path.php:508
#: modules/shapes/widgets/text-path.php:528
#: modules/shapes/widgets/text-path.php:579
#: modules/shapes/widgets/text-path.php:599 assets/js/editor.js:50027
#: assets/js/editor.js:50070
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:38
#: assets/js/packages/editor-controls/editor-controls.strings.js:77
#: assets/js/packages/editor-controls/editor-controls.strings.js:86
msgid "Color"
msgstr "颜色"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "计数器"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:432
#: includes/widgets/video.php:339
msgid "Autoplay"
msgstr "自动播放"

#: includes/widgets/image-carousel.php:181
msgid "Slides to Scroll"
msgstr "幻灯片滚动"

#: includes/widgets/image-carousel.php:164
msgid "Slides to Show"
msgstr "幻灯片显示"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "谷歌地图"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "随机"

#: includes/widgets/image-carousel.php:342
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "媒体文件"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "附件页面"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:519
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "间距"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "图标框"

#: includes/widgets/icon-box.php:323
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1160
#: modules/floating-buttons/base/widget-contact-button-base.php:2416
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "图标间距"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:425
msgid "Stacked"
msgstr "堆叠"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "图标列表"

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:776
msgid "Image Spacing"
msgstr "图像间距"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "图像框"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "图标悬停"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:584
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:551 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:517
#: modules/nested-tabs/widgets/nested-tabs.php:525
#: modules/nested-tabs/widgets/nested-tabs.php:583
#: modules/nested-tabs/widgets/nested-tabs.php:667
#: modules/nested-tabs/widgets/nested-tabs.php:1019
msgid "Border Width"
msgstr "边框宽度"

#: includes/base/element-base.php:890 includes/base/element-base.php:902
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:562
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:283
msgid "Rotate"
msgstr "旋转"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:415
#: includes/widgets/icon-box.php:457 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:451
msgid "Secondary Color"
msgstr "次要颜色"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:399
#: includes/widgets/icon-box.php:440 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:436
msgid "Primary Color"
msgstr "主要颜色"

#: includes/widgets/common-base.php:135 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1675
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "圆形"

#: includes/widgets/common-base.php:990 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "形状"

#: includes/widgets/progress.php:204
msgid "Display Percentage"
msgstr "显示百分比"

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "例如：关于"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:133
#: includes/widgets/text-editor.php:250
msgid "Text Editor"
msgstr "文本编辑器"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "标签内容"

#: includes/widgets/video.php:670 includes/widgets/video.php:797
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "播放图标"

#: includes/widgets/video.php:623 includes/widgets/video.php:630
#: includes/widgets/video.php:785
msgid "Image Overlay"
msgstr "图像覆盖"

#: includes/widgets/video.php:491
msgid "Suggested Videos"
msgstr "建议的视频"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "切换内容"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "切换标题"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "切换"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "切换项目"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:115
#: modules/nested-tabs/widgets/nested-tabs.php:116
msgid "Tab Title"
msgstr "标签标题"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "手风琴项目"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "伦敦眼，伦敦，英国"

#: includes/widgets/video.php:747
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:120
msgid "Aspect Ratio"
msgstr "纵横比"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "切换 #2"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "切换 #1"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:175
msgid "Tab #2"
msgstr "标签 #2"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tab #1"
msgstr "标签 #1"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:167
msgid "Tabs Items"
msgstr "标签项目"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "无侧边栏被发现"

#: includes/widgets/progress.php:216 includes/widgets/progress.php:290
msgid "Inner Text"
msgstr "内部文本"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:196
msgid "Image Position"
msgstr "图像的位置"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1648
msgid "Image Size"
msgstr "图像尺寸"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1676
#: assets/js/ai-admin.js:11383 assets/js/ai-gutenberg.js:13231
#: assets/js/ai-media-library.js:13012
#: assets/js/ai-unify-product-images.js:13012 assets/js/ai.js:14477
msgid "Square"
msgstr "正方形"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "列表项"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "列表项 #3"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "列表项 #2"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "列表项 #1"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3604
#: assets/js/ai-gutenberg.js:5372 assets/js/ai-media-library.js:5233
#: assets/js/ai-unify-product-images.js:5233 assets/js/ai.js:6051
msgid "Images"
msgstr "图像"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:141
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "添加图像"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1004 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:724 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:306
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:31
#: assets/js/packages/editor-controls/editor-controls.js:12
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:29
#: assets/js/packages/editor-controls/editor-controls.strings.js:84
msgid "Image"
msgstr "图像"

#: includes/elements/column.php:747 includes/elements/section.php:1209
#: includes/widgets/heading.php:267 includes/widgets/icon-box.php:309
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:849
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:274
msgid "Justified"
msgstr "两端对齐"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "手风琴的内容"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "手风琴的标题"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "手风琴 #2"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "手风琴 #1"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:393
msgid "Accordion"
msgstr "手风琴"

#: modules/system-info/module.php:202
msgid "You do not have permission to download this file."
msgstr "您没有下载此文件的权限"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "复制 & 粘贴信息"

#: includes/widgets/video.php:962
msgid "Content Position"
msgstr "内容定位"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:189 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:273 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2996
msgid "Middle"
msgstr "中间"

#: includes/elements/container.php:384 includes/elements/section.php:254
#: includes/widgets/common-base.php:232 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1551
msgid "Full Width"
msgstr "全宽度"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:785 includes/elements/container.php:1382
#: includes/elements/section.php:1252 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:541 includes/widgets/common-base.php:212
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:520
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:565
#: includes/widgets/traits/button-trait.php:501
#: modules/floating-buttons/base/widget-contact-button-base.php:1459
#: modules/floating-buttons/base/widget-contact-button-base.php:2184
#: modules/floating-buttons/base/widget-contact-button-base.php:2199
#: modules/floating-buttons/base/widget-contact-button-base.php:2747
#: modules/floating-buttons/base/widget-contact-button-base.php:2887
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1176
#: modules/nested-accordion/widgets/nested-accordion.php:474
#: modules/nested-accordion/widgets/nested-accordion.php:538
#: modules/nested-tabs/widgets/nested-tabs.php:701
#: modules/nested-tabs/widgets/nested-tabs.php:1051
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:113
msgid "Padding"
msgstr "内距"

#: includes/managers/controls.php:334 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:489 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:617 includes/widgets/image-box.php:517
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:118 includes/widgets/testimonial.php:261
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:513
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:58
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:62
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:57
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:58
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:55
#: modules/nested-accordion/widgets/nested-accordion.php:492
#: modules/nested-tabs/widgets/nested-tabs.php:990 assets/js/app.js:10350
#: assets/js/app.js:10842 assets/js/editor.js:39383
msgid "Content"
msgstr "内容"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:66
#: includes/editor-templates/templates.php:168
#: includes/editor-templates/templates.php:339
#: includes/editor-templates/templates.php:372
#: includes/editor-templates/templates.php:429 assets/js/editor.js:11020
#: assets/js/editor.js:11039 assets/js/editor.js:11200
#: assets/js/editor.js:30354 assets/js/editor.js:32835
#: assets/js/editor.js:50080 assets/js/import-export-admin.js:272
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:5
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:18
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:21
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:29
msgid "Delete"
msgstr "删除"

#: includes/fonts.php:76
msgid "Google"
msgstr "谷歌"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:119
#: includes/base/element-base.php:883
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:286
#: includes/elements/column.php:401 includes/elements/column.php:450
#: includes/elements/column.php:557 includes/elements/column.php:893
#: includes/elements/container.php:644 includes/elements/container.php:773
#: includes/elements/container.php:847 includes/elements/container.php:992
#: includes/elements/container.php:1831 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1340
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:744
#: includes/widgets/common-base.php:791 includes/widgets/common-base.php:866
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:312
#: includes/widgets/heading.php:344 includes/widgets/icon-box.php:392
#: includes/widgets/icon-box.php:663 includes/widgets/icon-list.php:426
#: includes/widgets/icon-list.php:655 includes/widgets/icon.php:213
#: includes/widgets/image-box.php:414 includes/widgets/image-box.php:563
#: includes/widgets/image.php:434 includes/widgets/text-editor.php:336
#: includes/widgets/traits/button-trait.php:339
#: modules/floating-buttons/base/widget-contact-button-base.php:1199
#: modules/floating-buttons/base/widget-contact-button-base.php:1405
#: modules/floating-buttons/base/widget-contact-button-base.php:1977
#: modules/floating-buttons/base/widget-contact-button-base.php:2472
#: modules/floating-buttons/base/widget-contact-button-base.php:2603
#: modules/floating-buttons/base/widget-contact-button-base.php:2788
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:493
#: modules/nested-tabs/widgets/nested-tabs.php:737
#: modules/nested-tabs/widgets/nested-tabs.php:937
#: modules/shapes/widgets/text-path.php:409
#: modules/shapes/widgets/text-path.php:501
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:105
msgid "Normal"
msgstr "标准"

#: core/experiments/manager.php:395
#: core/kits/documents/tabs/settings-background.php:78
#: core/settings/editor-preferences/model.php:123
#: includes/base/widget-base.php:297 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:254 includes/elements/column.php:187
#: includes/elements/column.php:215 includes/elements/column.php:258
#: includes/elements/container.php:547 includes/elements/container.php:570
#: includes/elements/container.php:1528 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:386
#: includes/widgets/common-base.php:231 includes/widgets/common-base.php:448
#: includes/widgets/divider.php:835 includes/widgets/heading.php:194
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-carousel.php:167
#: includes/widgets/image-carousel.php:185
#: includes/widgets/image-carousel.php:376
#: includes/widgets/image-carousel.php:763
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:171
#: includes/widgets/text-editor.php:181 includes/widgets/text-editor.php:424
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:127
#: modules/floating-buttons/base/widget-contact-button-base.php:1211
#: modules/floating-buttons/base/widget-contact-button-base.php:1263
#: modules/floating-buttons/base/widget-contact-button-base.php:1350
#: modules/floating-buttons/base/widget-contact-button-base.php:1559
#: modules/floating-buttons/base/widget-contact-button-base.php:1725
#: modules/floating-buttons/base/widget-contact-button-base.php:2289
#: modules/floating-buttons/base/widget-contact-button-base.php:2615
#: modules/floating-buttons/base/widget-contact-button-base.php:2684
#: modules/floating-buttons/base/widget-contact-button-base.php:2815
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:301
#: modules/shapes/widgets/text-path.php:203 assets/js/editor.js:47904
#: assets/js/editor.js:47915
msgid "Default"
msgstr "默认"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:169
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "输入您的标题"

#: includes/widgets/image-carousel.php:510
msgid "Slide"
msgstr "幻灯片"

#: core/base/document.php:1970
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "常规设置"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:139
#: core/settings/editor-preferences/model.php:151
#: core/settings/editor-preferences/model.php:162
#: core/settings/editor-preferences/model.php:207
#: includes/controls/switcher.php:74 includes/managers/icons.php:470
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:203
#: includes/widgets/image-carousel.php:377
#: includes/widgets/image-carousel.php:434
#: includes/widgets/image-carousel.php:447
#: includes/widgets/image-carousel.php:464
#: includes/widgets/image-carousel.php:495
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3045
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1553
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1611
#: modules/nested-accordion/widgets/nested-accordion.php:309
#: modules/styleguide/module.php:132 assets/js/app.js:8951
msgid "Yes"
msgstr "是"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:140
#: core/settings/editor-preferences/model.php:152
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:208
#: includes/controls/switcher.php:73 includes/managers/icons.php:469
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:202
#: includes/widgets/image-carousel.php:378
#: includes/widgets/image-carousel.php:435
#: includes/widgets/image-carousel.php:448
#: includes/widgets/image-carousel.php:465
#: includes/widgets/image-carousel.php:496
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3046
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
#: modules/nested-accordion/widgets/nested-accordion.php:310
#: modules/styleguide/module.php:131
msgid "No"
msgstr "否"

#: core/admin/admin.php:223 core/admin/admin.php:231 core/base/document.php:651
#: modules/admin-bar/module.php:124 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2677
msgid "Edit with Elementor"
msgstr "使用 Elementor 编辑"

#: includes/elements/column.php:827 includes/elements/container.php:1778
#: includes/elements/section.php:1294 includes/widgets/common-base.php:692
#: modules/floating-buttons/base/widget-contact-button-base.php:3105
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
msgid "CSS Classes"
msgstr "CSS 类"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:292
#: core/admin/admin.php:409 core/admin/admin.php:487
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:384 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:47 includes/editor-templates/navigator.php:101
#: includes/editor-templates/panel-elements.php:98
#: includes/editor-templates/panel.php:208
#: includes/editor-templates/templates.php:220 includes/plugin.php:867
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:487 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:1821 assets/js/app.js:2010
msgid "Elementor"
msgstr "Elementor"

#: includes/base/element-base.php:1326 includes/base/element-base.php:1354
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:217 includes/elements/column.php:739
#: includes/elements/section.php:1201 includes/widgets/common-base.php:412
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:259 includes/widgets/icon-box.php:301
#: includes/widgets/icon-list.php:268 includes/widgets/icon-list.php:550
#: includes/widgets/icon-list.php:581 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:740
#: includes/widgets/image-carousel.php:841
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:230
#: includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:259
#: includes/widgets/traits/button-trait.php:291 includes/widgets/video.php:966
#: modules/floating-buttons/base/widget-contact-button-base.php:2942
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:177
#: modules/nested-tabs/widgets/nested-tabs.php:238
#: modules/nested-tabs/widgets/nested-tabs.php:280
#: modules/nested-tabs/widgets/nested-tabs.php:350
#: modules/shapes/widgets/text-path.php:181
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:89
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:161
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:197
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:202
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:207
msgid "Center"
msgstr "居中"

#: core/admin/admin-notices.php:236
msgid "Sure! I'd love to help"
msgstr "当然！我很乐意帮忙"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "HTML 代码"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:264 includes/elements/container.php:576
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:211
#: modules/atomic-widgets/elements/div-block/div-block.php:65
msgid "HTML Tag"
msgstr "HTML 标签"

#: includes/widgets/progress.php:221
msgid "e.g. Web Designer"
msgstr "例如：站点设计师"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "菜单锚点的 ID。"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:47362
msgid "Discard"
msgstr "舍弃"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "主题"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592;  返回 WordPress 编辑器"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "您可以将下面的信息复制为简单文本 Ctrl+C / Ctrl+V："

#: core/settings/editor-preferences/model.php:125
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4345
msgid "Tablet"
msgstr "平板"

#: includes/widgets/heading.php:196 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1119
#: modules/floating-buttons/base/widget-contact-button-base.php:1544
#: modules/floating-buttons/base/widget-contact-button-base.php:1926
#: modules/floating-buttons/base/widget-contact-button-base.php:2276
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1405
msgid "Medium"
msgstr "中等"

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "这个 ID 是您在页面中使用的 CSS ID，不加 #"

#: core/experiments/manager.php:397 core/experiments/manager.php:687
#: modules/element-cache/module.php:128 assets/js/editor.js:30083
#: assets/js/element-manager-admin.js:2224
msgid "Inactive"
msgstr "未启用"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "笔记本电脑"

#: includes/controls/icons.php:80 includes/controls/media.php:238
msgid "Add"
msgstr "添加"

#: includes/editor-templates/hotkeys.php:135
msgid "Page Settings"
msgstr "页面设置"

#: core/breakpoints/manager.php:314
msgid "Mobile Portrait"
msgstr "手机纵向"

#: core/breakpoints/manager.php:319
msgid "Mobile Landscape"
msgstr "手机横向"

#: modules/nested-tabs/widgets/nested-tabs.php:230
#: modules/nested-tabs/widgets/nested-tabs.php:272
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:91
msgid "Justify"
msgstr "对齐"

#: core/experiments/manager.php:396 core/experiments/manager.php:686
#: modules/element-cache/module.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:1310
#: modules/nested-accordion/widgets/nested-accordion.php:667
#: modules/nested-accordion/widgets/nested-accordion.php:730
#: modules/nested-tabs/widgets/nested-tabs.php:629
#: modules/nested-tabs/widgets/nested-tabs.php:809
#: modules/nested-tabs/widgets/nested-tabs.php:971 assets/js/editor.js:30085
#: assets/js/element-manager-admin.js:2221
msgid "Active"
msgstr "启用"

#: core/admin/admin.php:242
#: core/editor/loader/v1/templates/editor-body-v1-view.php:23
#: core/editor/loader/v2/templates/editor-body-v2-view.php:23
#: includes/editor-templates/templates.php:54 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1741 assets/js/ai-gutenberg.js:3509
#: assets/js/ai-media-library.js:3370 assets/js/ai-unify-product-images.js:3370
#: assets/js/ai.js:4155 assets/js/app-packages.js:5283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:236
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:46
msgid "Loading"
msgstr "加载中…"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:572 includes/elements/column.php:609
#: includes/elements/container.php:1024 includes/elements/container.php:1075
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:881 includes/widgets/common-base.php:918
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:599
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:399
#: includes/widgets/image-carousel.php:808
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:278 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:344 includes/widgets/text-editor.php:529
#: includes/widgets/traits/button-trait.php:488
#: modules/nested-accordion/widgets/nested-accordion.php:461
#: modules/nested-accordion/widgets/nested-accordion.php:526
#: modules/nested-tabs/widgets/nested-tabs.php:688
#: modules/nested-tabs/widgets/nested-tabs.php:1028
msgid "Border Radius"
msgstr "边框圆角"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/elements/container.php:1169
#: includes/elements/section.php:964 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:218
#: includes/widgets/image-carousel.php:341
#: includes/widgets/image-carousel.php:393
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:579
#: modules/nested-tabs/widgets/nested-tabs.php:406 assets/js/ai-admin.js:11303
#: assets/js/ai-admin.js:11309 assets/js/ai-admin.js:11321
#: assets/js/ai-admin.js:11332 assets/js/ai-admin.js:11343
#: assets/js/ai-admin.js:11354 assets/js/ai-admin.js:11370
#: assets/js/ai-gutenberg.js:13151 assets/js/ai-gutenberg.js:13157
#: assets/js/ai-gutenberg.js:13169 assets/js/ai-gutenberg.js:13180
#: assets/js/ai-gutenberg.js:13191 assets/js/ai-gutenberg.js:13202
#: assets/js/ai-gutenberg.js:13218 assets/js/ai-media-library.js:12932
#: assets/js/ai-media-library.js:12938 assets/js/ai-media-library.js:12950
#: assets/js/ai-media-library.js:12961 assets/js/ai-media-library.js:12972
#: assets/js/ai-media-library.js:12983 assets/js/ai-media-library.js:12999
#: assets/js/ai-unify-product-images.js:12932
#: assets/js/ai-unify-product-images.js:12938
#: assets/js/ai-unify-product-images.js:12950
#: assets/js/ai-unify-product-images.js:12961
#: assets/js/ai-unify-product-images.js:12972
#: assets/js/ai-unify-product-images.js:12983
#: assets/js/ai-unify-product-images.js:12999 assets/js/ai.js:14397
#: assets/js/ai.js:14403 assets/js/ai.js:14415 assets/js/ai.js:14426
#: assets/js/ai.js:14437 assets/js/ai.js:14448 assets/js/ai.js:14464
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:73
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:82
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:192
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:221
msgid "None"
msgstr "无"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "无间距"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:319
#: modules/floating-buttons/base/widget-contact-button-base.php:2550
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1215
msgid "Weight"
msgstr "粗细"

#: includes/widgets/image-carousel.php:445
msgid "Pause on Hover"
msgstr "悬停时暂停"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:421
#: assets/js/editor.js:12719
msgid "View"
msgstr "视图"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:444
msgid "Tabs"
msgstr "Tab选项卡"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:708 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "激活颜色"

#: includes/elements/column.php:924 includes/elements/container.php:1869
#: includes/elements/section.php:1372 includes/managers/controls.php:337
#: includes/widgets/common-base.php:1220
#: modules/floating-buttons/base/widget-contact-button-base.php:3059
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "响应式"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "大小写转换"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "字体粗细"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "警告框"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "字体大小"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "字体正斜"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "字体系列"

#: includes/elements/column.php:161
msgid "Column Width"
msgstr "列宽度"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:202
msgid "Columns Gap"
msgstr "列间距"

#: includes/elements/container.php:1877 includes/elements/section.php:1396
msgid "Visibility"
msgstr "可见性"

#: includes/elements/container.php:383 includes/elements/section.php:253
msgid "Boxed"
msgstr "盒式布局"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "列位置"

#: includes/controls/gaps.php:57
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:61
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:34
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:181
msgid "Column"
msgstr "列"

#: includes/widgets/heading.php:195 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1118
#: modules/floating-buttons/base/widget-contact-button-base.php:1543
#: modules/floating-buttons/base/widget-contact-button-base.php:1925
#: modules/floating-buttons/base/widget-contact-button-base.php:2275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1404
msgid "Small"
msgstr "小"

#: includes/widgets/heading.php:197 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1120
#: modules/floating-buttons/base/widget-contact-button-base.php:1545
#: modules/floating-buttons/base/widget-contact-button-base.php:1927
#: modules/floating-buttons/base/widget-contact-button-base.php:2277
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1406
msgid "Large"
msgstr "大"

#: includes/editor-templates/panel.php:74
#: includes/editor-templates/panel.php:75
msgid "Widgets Panel"
msgstr "小部件面板"

#: includes/editor-templates/panel-elements.php:75
msgid "Search Widget..."
msgstr "搜索小部件..."

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:389
#: includes/widgets/image-carousel.php:395
#: includes/widgets/image-carousel.php:822
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "标题"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "固定 (不跟随页面滚动)"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "背景固定"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "滚动 (跟随页面滚动)"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:177
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:111
msgid "Columns"
msgstr "列数"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "文章"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "我是一个描述，点击编辑按钮来改变这个文本。"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:10575
msgid "Section"
msgstr "板块"

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "新增板块"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "背景重复"

#: core/admin/admin-notices.php:520 includes/controls/gallery.php:123
#: includes/controls/media.php:319
msgid "Activate Plugin"
msgstr "激活插件"

#: includes/base/element-base.php:1350 includes/controls/dimensions.php:82
#: includes/elements/column.php:188 includes/elements/container.php:1177
#: includes/elements/container.php:1656 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:972
#: includes/widgets/common-base.php:571 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:269
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:205 includes/widgets/video.php:967
#: modules/floating-buttons/base/widget-contact-button-base.php:2992
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:11
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:153
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:213
msgid "Top"
msgstr "上 ▲"

#: includes/base/element-base.php:1358 includes/controls/dimensions.php:84
#: includes/elements/column.php:190 includes/elements/container.php:1178
#: includes/elements/container.php:1660 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:973
#: includes/widgets/common-base.php:575 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3000
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:14
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:154
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:216
msgid "Bottom"
msgstr "下 ▼"

#: includes/base/element-base.php:1330 includes/controls/dimensions.php:83
#: includes/elements/column.php:743 includes/elements/container.php:1541
#: includes/elements/section.php:1205 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:263 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:305 includes/widgets/icon-list.php:272
#: includes/widgets/icon-list.php:554 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:539
#: includes/widgets/image-carousel.php:845
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:234 includes/widgets/text-editor.php:270
#: includes/widgets/traits/button-trait.php:263
#: modules/floating-buttons/base/widget-contact-button-base.php:1138
#: modules/floating-buttons/base/widget-contact-button-base.php:2404
#: modules/floating-buttons/base/widget-contact-button-base.php:2946
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:185
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:13
#: assets/js/packages/editor-controls/editor-controls.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:149
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:215
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:217
msgid "Right"
msgstr "右 ▶"

#: includes/base/element-base.php:1322 includes/controls/dimensions.php:85
#: includes/elements/column.php:735 includes/elements/container.php:1540
#: includes/elements/section.php:1197 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:255 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:297 includes/widgets/icon-list.php:264
#: includes/widgets/icon-list.php:546 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:538
#: includes/widgets/image-carousel.php:837
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:226 includes/widgets/text-editor.php:262
#: includes/widgets/traits/button-trait.php:255
#: modules/floating-buttons/base/widget-contact-button-base.php:1134
#: modules/floating-buttons/base/widget-contact-button-base.php:2400
#: modules/floating-buttons/base/widget-contact-button-base.php:2938
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:12
#: assets/js/packages/editor-controls/editor-controls.strings.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:151
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:214
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:218
msgid "Left"
msgstr "左 ◀"

#: modules/floating-buttons/base/widget-contact-button-base.php:205
#: modules/floating-buttons/base/widget-contact-button-base.php:235
#: modules/floating-buttons/base/widget-contact-button-base.php:1317
#: modules/floating-buttons/base/widget-contact-button-base.php:2017
#: modules/floating-buttons/base/widget-contact-button-base.php:2626
#: modules/floating-buttons/base/widget-contact-button-base.php:2695
#: modules/floating-buttons/base/widget-floating-bars-base.php:1141
msgid "Icon Color"
msgstr "图标颜色"

#: includes/widgets/heading.php:47 includes/widgets/heading.php:154
#: includes/widgets/heading.php:243
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:32
#: modules/link-in-bio/base/widget-link-in-bio-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:851
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1292
msgid "Heading"
msgstr "标题"

#: modules/floating-buttons/base/widget-contact-button-base.php:1844
msgid "Chat Background Color"
msgstr "聊天背景颜色"

#: modules/floating-buttons/documents/floating-buttons.php:31
msgid "Go To Dashboard"
msgstr "转到仪表板"