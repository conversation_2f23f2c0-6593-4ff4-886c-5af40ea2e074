# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-12-16 16:53:45+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: core/files/uploads-manager.php:290
msgid "Invalid file name."
msgstr "Invalid file name."

#: modules/apps/admin-apps-page.php:27
msgid "Learn more about this page."
msgstr "Learn more about this page."

#: modules/nested-accordion/widgets/nested-accordion.php:401
msgid "Space between Items"
msgstr "Space between Items"

#: modules/nested-accordion/widgets/nested-accordion.php:357
msgid "Multiple"
msgstr "Multiple"

#: modules/nested-accordion/widgets/nested-accordion.php:356
msgid "One"
msgstr "One"

#: modules/nested-accordion/widgets/nested-accordion.php:353
msgid "Max Items Expanded"
msgstr "Max Items Expanded"

#: modules/nested-accordion/widgets/nested-accordion.php:343
msgid "All collapsed"
msgstr "All collapsed"

#: modules/nested-accordion/widgets/nested-accordion.php:342
msgid "First expanded"
msgstr "First expanded"

#: modules/nested-accordion/widgets/nested-accordion.php:339
msgid "Default State"
msgstr "Default State"

#: modules/nested-accordion/widgets/nested-accordion.php:332
msgid "Interactions"
msgstr "Interactions"

#: modules/nested-accordion/widgets/nested-accordion.php:252
msgid "Collapse"
msgstr "Collapse"

#: modules/nested-accordion/widgets/nested-accordion.php:238
#: assets/js/ai-admin.js:9259 assets/js/ai-gutenberg.js:11107
#: assets/js/ai-layout.js:4870 assets/js/ai-media-library.js:10888
#: assets/js/ai-unify-product-images.js:10888 assets/js/ai.js:12335
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:18
msgid "Expand"
msgstr "Expand"

#: modules/nested-accordion/widgets/nested-accordion.php:168
msgid "Item Position"
msgstr "Item Position"

#: includes/widgets/video.php:234
msgid "Choose Video File"
msgstr "Choose Video File"

#. translators: 1: Slide count, 2: Total slides count.
#: includes/widgets/image-carousel.php:969
msgid "%1$s of %2$s"
msgstr "%1$s of %2$s"

#: includes/widgets/icon-box.php:229 includes/widgets/image-box.php:204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1453
msgid "Box"
msgstr "Box"

#: modules/apps/admin-apps-page.php:25
msgid "Popular Add-ons, New Possibilities."
msgstr "Popular add-ons, new possibilities."

#: includes/controls/groups/background.php:99 assets/js/ai-admin.js:11115
#: assets/js/ai-gutenberg.js:12963 assets/js/ai-media-library.js:12744
#: assets/js/ai-unify-product-images.js:12744 assets/js/ai.js:14191
msgid "Gradient"
msgstr "Gradient"

#: modules/nested-tabs/widgets/nested-tabs.php:379
msgid "Note: Scroll tabs if they don’t fit into their parent container."
msgstr "Note: Scroll tabs if they don’t fit into their parent container."

#: modules/nested-tabs/widgets/nested-tabs.php:377
msgid "Horizontal Scroll"
msgstr "Horizontal Scroll"

#: modules/floating-buttons/base/widget-floating-bars-base.php:376
#: modules/nested-accordion/widgets/nested-accordion.php:157
msgid "Item #3"
msgstr "Item #3"

#: modules/floating-buttons/base/widget-floating-bars-base.php:373
#: modules/nested-accordion/widgets/nested-accordion.php:154
msgid "Item #2"
msgstr "Item #2"

#: modules/floating-buttons/base/widget-floating-bars-base.php:370
#: modules/nested-accordion/widgets/nested-accordion.php:151
msgid "Item #1"
msgstr "Item #1"

#: modules/floating-buttons/base/widget-floating-bars-base.php:340
#: modules/nested-accordion/widgets/nested-accordion.php:117
#: modules/nested-accordion/widgets/nested-accordion.php:118
msgid "Item Title"
msgstr "Item Title"

#: modules/nested-accordion/widgets/nested-accordion.php:66
msgid "item #%s"
msgstr "item #%s"

#: includes/widgets/toggle.php:157
msgid "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."
msgstr "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."

#: includes/widgets/image.php:398
msgid "Object Position"
msgstr "Object Position"

#: includes/widgets/icon.php:326
msgid "Fit to Size"
msgstr "Fit to Size"

#: includes/widgets/accordion.php:154
msgid "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."
msgstr "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."

#: includes/frontend.php:1424
msgid "Go to slide"
msgstr "Go to slide"

#: includes/frontend.php:1423
msgid "This is the last slide"
msgstr "This is the last slide"

#: includes/frontend.php:1422
msgid "This is the first slide"
msgstr "This is the first slide"

#: includes/frontend.php:1421
msgid "Next slide"
msgstr "Next slide"

#: includes/frontend.php:1420
msgid "Previous slide"
msgstr "Previous slide"

#: includes/editor-templates/navigator.php:88
msgid "Show/hide Element"
msgstr "Show/hide Element"

#: includes/editor-templates/navigator.php:75
msgid "Show/hide inner elements"
msgstr "Show/hide inner elements"

#: includes/editor-templates/navigator.php:64
msgid "Resize navigator"
msgstr "Resize navigator"

#: includes/editor-templates/navigator.php:63
msgid "Resize structure"
msgstr "Resize structure"

#: includes/editor-templates/hotkeys.php:94
msgid "Panels"
msgstr "Panels"

#: includes/controls/gallery.php:84 assets/js/editor.js:14135
msgid "Clear gallery"
msgstr "Clear gallery"

#: core/document-types/page-base.php:257
msgid "Allow Comments"
msgstr "Allow Comments"

#: core/document-types/page-base.php:245
#: includes/controls/groups/flex-item.php:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:100
msgid "Order"
msgstr "Order"

#: core/admin/admin.php:348
msgid "Get Elementor Pro"
msgstr "Get Elementor Pro"

#: modules/ai/connect/ai.php:27 assets/js/ai-admin.js:553
#: assets/js/ai-admin.js:7640 assets/js/ai-gutenberg.js:2321
#: assets/js/ai-gutenberg.js:9488 assets/js/ai-layout.js:483
#: assets/js/ai-layout.js:3251 assets/js/ai-media-library.js:2182
#: assets/js/ai-media-library.js:9269 assets/js/ai-unify-product-images.js:2182
#: assets/js/ai-unify-product-images.js:9269 assets/js/ai.js:2961
#: assets/js/ai.js:10716
msgid "AI"
msgstr "AI"

#: core/admin/admin-notices.php:574 core/admin/admin.php:1037
msgid "Dismiss this notice."
msgstr "Dismiss this notice."

#: core/experiments/manager.php:563
msgid "Activate All"
msgstr "Activate All"

#: core/kits/documents/tabs/theme-style-form-fields.php:188
msgid "Accent Color"
msgstr "Accent Colour"

#: modules/nested-tabs/widgets/nested-tabs.php:178
msgid "Tab #3"
msgstr "Tab #3"

#: includes/widgets/video.php:866
msgid "Shadow"
msgstr "Shadow"

#: modules/nested-tabs/widgets/nested-tabs.php:713
msgid "Titles"
msgstr "Titles"

#: includes/widgets/video.php:587
msgid "Preload"
msgstr "Preload"

#: modules/nested-tabs/widgets/nested-tabs.php:61
msgid "Tab #%s"
msgstr "Tab #%s"

#: modules/nested-tabs/widgets/nested-tabs.php:80
msgid "Tab #%d"
msgstr "Tab #%d"

#: includes/widgets/video.php:590
msgid "Metadata"
msgstr "Metadata"

#: modules/nested-tabs/widgets/nested-tabs.php:342
msgid "Align Title"
msgstr "Align Title"

#: includes/settings/settings.php:365
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:2
msgid "Google Fonts"
msgstr "Google Fonts"

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#: modules/nested-tabs/widgets/nested-tabs.php:422
msgid "%1$s (%2$s %3$dpx)"
msgstr "%1$s (%2$s %3$dpx)"

#: modules/nested-accordion/widgets/nested-accordion.php:427
#: modules/nested-tabs/widgets/nested-tabs.php:469
msgid "Distance from content"
msgstr "Distance from content"

#: modules/nested-tabs/widgets/nested-tabs.php:449
msgid "Gap between tabs"
msgstr "Gap between tabs"

#: modules/nested-elements/module.php:17
msgid "Nested Elements"
msgstr "Nested Elements"

#: includes/settings/settings.php:458
msgid "Lazy Load Background Images"
msgstr "Lazy Load Background Images"

#: core/admin/admin.php:1025 modules/apps/admin-apps-page.php:116
#: modules/apps/admin-apps-page.php:149
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:795
msgid "Activate"
msgstr "Activate"

#: includes/elements/container.php:564
msgid "(link)"
msgstr "(link)"

#: includes/controls/groups/typography.php:150
msgctxt "Typography Control"
msgid "(Black)"
msgstr "(Black)"

#: includes/controls/groups/typography.php:149
msgctxt "Typography Control"
msgid "(Extra Bold)"
msgstr "(Extra Bold)"

#: includes/controls/groups/typography.php:148
msgctxt "Typography Control"
msgid "(Bold)"
msgstr "(Bold)"

#: includes/controls/groups/typography.php:147
msgctxt "Typography Control"
msgid "(Semi Bold)"
msgstr "(Semi Bold)"

#: includes/controls/groups/typography.php:146
msgctxt "Typography Control"
msgid "(Medium)"
msgstr "(Medium)"

#: includes/controls/groups/typography.php:145
msgctxt "Typography Control"
msgid "(Normal)"
msgstr "(Normal)"

#: includes/controls/groups/typography.php:144
msgctxt "Typography Control"
msgid "(Light)"
msgstr "(Light)"

#: includes/controls/groups/typography.php:143
msgctxt "Typography Control"
msgid "(Extra Light)"
msgstr "(Extra Light)"

#: includes/controls/groups/typography.php:142
msgctxt "Typography Control"
msgid "(Thin)"
msgstr "(Thin)"

#: core/experiments/manager.php:636
msgid "Requires"
msgstr "Requires"

#: app/modules/import-export/module.php:235
msgid "Remove Kit"
msgstr "Remove Kit"

#: app/modules/import-export/module.php:228
msgid "Remove the most recent Kit"
msgstr "Remove the most recent Kit"

#: app/modules/import-export/module.php:190
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."
msgstr "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."

#: app/modules/import-export/module.php:182
#: app/modules/import-export/module.php:185
#: app/modules/import-export/module.php:191
msgid "imported kit"
msgstr "imported kit"

#: app/modules/import-export/module.php:181
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."
msgstr "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."

#. Translators: %s is the current item index.
#: modules/nested-accordion/widgets/nested-accordion.php:85
#: modules/nested-elements/base/widget-nested-base.php:45
#: assets/js/editor.js:24636
msgid "Item #%d"
msgstr "Item #%d"

#: includes/widgets/image-carousel.php:282
msgid "Next Arrow Icon"
msgstr "Next Arrow Icon"

#: includes/widgets/image-carousel.php:227
msgid "Previous Arrow Icon"
msgstr "Previous Arrow Icon"

#: includes/widgets/alert.php:394
#: modules/floating-buttons/base/widget-contact-button-base.php:2937
msgid "Horizontal Position"
msgstr "Horizontal Position"

#: includes/widgets/alert.php:376
#: modules/floating-buttons/base/widget-contact-button-base.php:2991
#: modules/floating-buttons/base/widget-floating-bars-base.php:1433
msgid "Vertical Position"
msgstr "Vertical Position"

#: includes/widgets/alert.php:161 includes/widgets/alert.php:342
msgid "Dismiss Icon"
msgstr "Dismiss Icon"

#: includes/elements/container.php:585
msgid "Don’t add links to elements nested in this container - it will break the layout."
msgstr "Don’t add links to elements nested in this container - it will break the layout."

#: includes/editor-templates/panel.php:60
msgid "Any time you can change the settings in %1$sUser Preferences%2$s"
msgstr "Any time you can change the settings in %1$sUser Preferences%2$s"

#: includes/editor-templates/panel.php:56
msgid "Now you can choose where you want to go on the site from the following options"
msgstr "Now you can choose where you want to go on the site from the following options"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:310 core/experiments/manager.php:377
#: core/experiments/manager.php:396 includes/settings/settings.php:400
#: includes/settings/settings.php:403 modules/element-cache/module.php:44
msgid "Performance"
msgstr "Performance"

#: core/admin/admin-notices.php:366
msgid "Try it out"
msgstr "Try it out"

#: core/admin/admin-notices.php:363
msgid "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."
msgstr "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."

#: core/admin/admin-notices.php:362
msgid "Improve your site’s performance score."
msgstr "Improve your site’s performance score."

#: core/settings/editor-preferences/model.php:233
msgid "WP Dashboard"
msgstr "WP Dashboard"

#: core/settings/editor-preferences/model.php:232
msgid "All Posts"
msgstr "All Posts"

#: core/settings/editor-preferences/model.php:231
msgid "This Post"
msgstr "This Post"

#: core/settings/editor-preferences/model.php:227
msgid "Exit to"
msgstr "Exit to"

#: core/kits/documents/tabs/settings-layout.php:91
msgid "Sets the default space inside the container (Default is 10px)"
msgstr "Sets the default space inside the container (Default is 10px)"

#: core/kits/documents/tabs/settings-layout.php:88
msgid "Container Padding"
msgstr "Container Padding"

#: core/editor/notice-bar.php:45 assets/js/element-manager-admin.js:2363
msgid "Unleash the full power of Elementor's features and web creation tools."
msgstr "Unleash the full power of Elementor's features and web creation tools."

#: includes/editor-templates/hotkeys.php:191 assets/js/notes.js:136
#: assets/js/notes.js:140 assets/js/notes.js:226
msgid "Notes"
msgstr "Notes"

#: core/editor/notice-bar.php:41 core/editor/promotion.php:34
#: includes/editor-templates/navigator.php:20
#: includes/editor-templates/panel-elements.php:29
#: includes/editor-templates/panel-elements.php:34
#: includes/editor-templates/panel-elements.php:102
#: includes/managers/controls.php:1154 includes/widgets/image-gallery.php:110
#: modules/admin-top-bar/module.php:79
#: modules/checklist/steps/setup-header.php:93
#: modules/element-manager/ajax.php:72 modules/element-manager/ajax.php:79
#: modules/element-manager/ajax.php:87
#: modules/promotions/admin-menu-items/base-promotion-item.php:32
#: modules/promotions/admin-menu-items/base-promotion-template.php:37
#: modules/promotions/admin-menu-items/popups-promotion-item.php:24
#: modules/promotions/promotion-data.php:48
#: modules/promotions/promotion-data.php:65
#: modules/promotions/promotion-data.php:82
#: modules/promotions/promotion-data.php:99
#: modules/promotions/promotion-data.php:116 assets/js/app-packages.js:2413
#: assets/js/app-packages.js:5590 assets/js/app-packages.js:5856
#: assets/js/app.js:2727 assets/js/checklist.js:298 assets/js/editor.js:8255
#: assets/js/editor.js:52124 assets/js/editor.js:52139
#: assets/js/editor.js:52154 assets/js/editor.js:52169
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:513
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1461
msgid "Upgrade Now"
msgstr "Upgrade Now"

#: core/admin/admin.php:633 core/role-manager/role-manager.php:241
#: includes/editor-templates/panel-elements.php:40
#: includes/editor-templates/panel-elements.php:46
#: includes/editor-templates/panel-elements.php:64
#: includes/editor-templates/panel.php:328 includes/managers/controls.php:1145
#: includes/widgets/image-gallery.php:107
#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:30
#: modules/promotions/promotion-data.php:41
#: modules/promotions/promotion-data.php:58
#: modules/promotions/promotion-data.php:75
#: modules/promotions/promotion-data.php:92
#: modules/promotions/promotion-data.php:109 assets/js/ai-admin.js:921
#: assets/js/ai-admin.js:2863 assets/js/ai-admin.js:2992
#: assets/js/ai-gutenberg.js:2689 assets/js/ai-gutenberg.js:4631
#: assets/js/ai-gutenberg.js:4760 assets/js/ai-layout.js:717
#: assets/js/ai-layout.js:997 assets/js/ai-media-library.js:2550
#: assets/js/ai-media-library.js:4492 assets/js/ai-media-library.js:4621
#: assets/js/ai-unify-product-images.js:2550
#: assets/js/ai-unify-product-images.js:4492
#: assets/js/ai-unify-product-images.js:4621 assets/js/ai.js:3329
#: assets/js/ai.js:5271 assets/js/ai.js:5400 assets/js/app-packages.js:5821
#: assets/js/e-react-promotions.js:196 assets/js/editor.js:6772
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1320
#: assets/js/notes.js:149
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:767
#: assets/js/styleguide.js:196
msgid "Upgrade"
msgstr "Upgrade"

#: includes/widgets/image-carousel.php:624
msgid "Pagination"
msgstr "Pagination"

#: includes/elements/column.php:455 includes/elements/section.php:730
#: includes/widgets/heading.php:324
msgid "Hue"
msgstr "Hue"

#: includes/elements/column.php:454 includes/elements/section.php:729
#: includes/widgets/heading.php:323
msgid "Exclusion"
msgstr "Exclusion"

#: includes/elements/column.php:453 includes/elements/section.php:728
#: includes/widgets/heading.php:322
msgid "Difference"
msgstr "Difference"

#: core/kits/views/panel.php:36
msgid "Reorder"
msgstr "Reorder"

#: core/experiments/manager.php:339 includes/elements/container.php:72
#: includes/elements/container.php:342
#: modules/library/documents/container.php:52 assets/js/editor.js:10275
#: assets/js/editor.js:32800 assets/js/editor.js:38378
msgid "Container"
msgstr "Container"

#: core/kits/documents/tabs/global-colors.php:101
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:178
msgid "System Colors"
msgstr "System Colours"

#: modules/container-converter/module.php:88
#: modules/container-converter/module.php:121
msgid "Copies all of the selected sections and columns and pastes them in a container beneath the original."
msgstr "Copies all of the selected sections and columns and pastes them in a container below the original."

#: modules/container-converter/module.php:86
#: modules/container-converter/module.php:119
msgid "Convert"
msgstr "Convert"

#: modules/container-converter/module.php:85
#: modules/container-converter/module.php:118
msgid "Convert to container"
msgstr "Convert to container"

#: includes/controls/groups/background.php:727
#: includes/widgets/image-carousel.php:423
msgid "Lazyload"
msgstr "Lazyload"

#: core/kits/documents/tabs/global-typography.php:160
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:227
msgid "System Fonts"
msgstr "System Fonts"

#: core/common/modules/finder/categories/tools.php:68
msgid "Import Export"
msgstr "Import Export"

#: core/common/modules/event-tracker/personal-data.php:24
msgid "Elementor Event Tracker"
msgstr "Elementor Event Tracker"

#: includes/widgets/video.php:1002
msgid "Play Video about"
msgstr "Play Video about"

#: includes/elements/container.php:479
msgid "To achieve full height Container use %s."
msgstr "To achieve full height Container use %s."

#: includes/editor-templates/global.php:33 assets/js/editor.js:32659
msgid "Add New Container"
msgstr "Add New Container"

#: includes/controls/groups/typography.php:153
msgid "Bold"
msgstr "Bold"

#: elementor.php:79 elementor.php:103 assets/js/ai-admin.js:960
#: assets/js/ai-gutenberg.js:2728 assets/js/ai-layout.js:756
#: assets/js/ai-media-library.js:2589 assets/js/ai-unify-product-images.js:2589
#: assets/js/ai.js:3368
msgid "Show me how"
msgstr "Show me how"

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "Add New Page Template"

#: includes/base/element-base.php:1190
msgid "Skew Y"
msgstr "Skew Y"

#: includes/base/element-base.php:1168
msgid "Skew X"
msgstr "Skew X"

#: includes/base/element-base.php:1156
msgid "Skew"
msgstr "Skew"

#: includes/base/element-base.php:1132
msgid "Scale Y"
msgstr "Scale Y"

#: includes/base/element-base.php:1110
msgid "Scale X"
msgstr "Scale X"

#: includes/base/element-base.php:1037
msgid "Offset Y"
msgstr "Offset Y"

#: includes/base/element-base.php:1011
msgid "Offset X"
msgstr "Offset X"

#: includes/base/element-base.php:976
msgid "Perspective"
msgstr "Perspective"

#: includes/base/element-base.php:953
msgid "Rotate Y"
msgstr "Rotate Y"

#: includes/base/element-base.php:930
msgid "Rotate X"
msgstr "Rotate X"

#: includes/base/element-base.php:850
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:33
msgid "Transform"
msgstr "Transform"

#: core/logger/log-reporter.php:25
msgid "Log"
msgstr "Log"

#. translators: 1. "Terms of service" link, 2. "Privacy policy" link
#: includes/admin-templates/beta-tester.php:44
msgid "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"
msgstr "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"

#: includes/controls/groups/text-stroke.php:85
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:5
msgid "Stroke Color"
msgstr "Stroke Colour"

#: includes/base/element-base.php:1307
msgid "X Anchor Point"
msgstr "X Anchor Point"

#: includes/base/element-base.php:1233 includes/base/element-base.php:1237
msgid "Flip Vertical"
msgstr "Flip Vertical"

#: includes/base/element-base.php:1214 includes/base/element-base.php:1218
msgid "Flip Horizontal"
msgstr "Flip Horizontal"

#: includes/base/element-base.php:1077
msgid "Keep Proportions"
msgstr "Keep Proportions"

#: includes/base/element-base.php:913
msgid "3D Rotate"
msgstr "3D Rotate"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "Custom Code"

#: includes/controls/groups/text-stroke.php:60
#: includes/controls/groups/text-stroke.php:111
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:35
msgid "Text Stroke"
msgstr "Text Stroke"

#: includes/admin-templates/beta-tester.php:53 assets/js/ai-admin.js:6254
#: assets/js/ai-admin.js:15509 assets/js/ai-gutenberg.js:8102
#: assets/js/ai-gutenberg.js:17357 assets/js/ai-layout.js:2477
#: assets/js/ai-layout.js:5195 assets/js/ai-media-library.js:7883
#: assets/js/ai-media-library.js:17138
#: assets/js/ai-unify-product-images.js:7883
#: assets/js/ai-unify-product-images.js:17138 assets/js/ai.js:9227
#: assets/js/ai.js:9330 assets/js/ai.js:18585
msgid "Privacy Policy"
msgstr "Privacy Policy"

#: includes/admin-templates/beta-tester.php:48 assets/js/ai-admin.js:6250
#: assets/js/ai-admin.js:15505 assets/js/ai-gutenberg.js:8098
#: assets/js/ai-gutenberg.js:17353 assets/js/ai-layout.js:2473
#: assets/js/ai-layout.js:5191 assets/js/ai-media-library.js:7879
#: assets/js/ai-media-library.js:17134
#: assets/js/ai-unify-product-images.js:7879
#: assets/js/ai-unify-product-images.js:17134 assets/js/ai.js:9223
#: assets/js/ai.js:9326 assets/js/ai.js:18581
msgid "Terms of Service"
msgstr "Terms of Service"

#: core/experiments/manager.php:569
msgid "Ongoing Experiments"
msgstr "Ongoing Experiments"

#: core/experiments/manager.php:526
msgid "Stable Features"
msgstr "Stable Features"

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:6772 assets/js/notes.js:149 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "Connect & Activate"

#: modules/usage/usage-reporter.php:22
msgid "Elements Usage"
msgstr "Elements Usage"

#. translators: 1: Integration settings link open tag, 2: Create API key link
#. open tag, 3: Link close tag.
#: includes/widgets/google-maps.php:140
msgid "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"
msgstr "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"

#: includes/base/element-base.php:1335
msgid "Y Anchor Point"
msgstr "Y Anchor Point"

#: core/editor/data/globals/endpoints/base.php:46
msgid "Invalid title"
msgstr "Invalid title"

#: core/experiments/manager.php:313
msgid "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."
msgstr "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."

#: core/experiments/manager.php:309
msgid "Inline Font Icons"
msgstr "Inline Font Icons"

#: core/experiments/experiments-reporter.php:21
msgid "Elementor Experiments"
msgstr "Elementor Experiments"

#: includes/settings/tools.php:140
msgid "Not allowed to rollback versions"
msgstr "Not allowed to rollback versions"

#: modules/page-templates/module.php:314
msgid "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."
msgstr "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."

#: includes/elements/column.php:451 includes/elements/container.php:852
#: includes/elements/section.php:725 includes/widgets/heading.php:320
msgid "Saturation"
msgstr "Saturation"

#: includes/elements/column.php:456 includes/elements/container.php:854
#: includes/elements/section.php:727 includes/widgets/heading.php:325
msgid "Luminosity"
msgstr "Luminosity"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:213
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "You can enable it from the %1$sElementor settings page%2$s."

#: core/kits/documents/tabs/settings-layout.php:371
msgid "Widescreen breakpoint settings will apply from the selected value and up."
msgstr "Widescreen breakpoint settings will apply from the selected value and up."

#: includes/elements/column.php:445 includes/elements/container.php:846
#: includes/elements/section.php:719 includes/widgets/heading.php:314
msgid "Multiply"
msgstr "Multiply"

#: includes/elements/column.php:446 includes/elements/container.php:847
#: includes/elements/section.php:720 includes/widgets/heading.php:315
msgid "Screen"
msgstr "Screen"

#: includes/elements/column.php:447 includes/elements/container.php:848
#: includes/elements/section.php:721 includes/widgets/heading.php:316
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:35
msgid "Overlay"
msgstr "Overlay"

#: includes/elements/column.php:448 includes/elements/container.php:849
#: includes/elements/section.php:722 includes/widgets/heading.php:317
msgid "Darken"
msgstr "Darken"

#: includes/elements/column.php:449 includes/elements/container.php:850
#: includes/elements/section.php:723 includes/widgets/heading.php:318
msgid "Lighten"
msgstr "Lighten"

#: includes/elements/column.php:450 includes/elements/container.php:851
#: includes/elements/section.php:724 includes/widgets/heading.php:319
msgid "Color Dodge"
msgstr "Colour Dodge"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colous and Fonts from the %1$sSettings Page%2$s."

#: includes/managers/controls.php:1131
msgid "Meet Page Transitions"
msgstr "Meet Page Transitions"

#: includes/managers/controls.php:1119
msgid "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."
msgstr "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "Select one or go ahead and %1$screate one%2$s now."

#. translators: %d: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:26
msgid "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"
msgstr "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"

#. translators: %s: Device name.
#: includes/base/element-base.php:1382
msgid "Hide On %s"
msgstr "Hide On %s"

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "Templates Help You %1$sWork Efficiently%2$s"

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1113
msgid "Page Transitions"
msgstr "Page Transitions"

#: core/experiments/manager.php:329
msgid "Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen."
msgstr "Get pixel-perfect design for every screen size. You can now add up to 6 customisable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and wide-screen."

#: core/experiments/manager.php:326
msgid "Additional Custom Breakpoints"
msgstr "Additional Custom Breakpoints"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:306
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:277 modules/safe-mode/module.php:290
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$sClick here%2$s to troubleshoot"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:397 modules/landing-pages/module.php:212
msgid "Or view %1$sTrashed Items%1$s"
msgstr "Or view %1$sTrashed Items%1$s"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "Learn more about %1$sWordPress revisions%2$s"

#. translators: 1: Function argument, 2: Elementor version number.
#: modules/dev-tools/deprecation.php:291
msgid "The %1$s argument is deprecated since version %2$s!"
msgstr "The %1$s argument is deprecated since version %2$s!"

#. translators: 1: Function argument, 2: Elementor version number, 3:
#. Replacement argument name.
#: modules/dev-tools/deprecation.php:287
msgid "The %1$s argument is deprecated since version %2$s! Use %3$s instead."
msgstr "The %1$s argument is deprecated since version %2$s! Use %3$s instead."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:384
msgid "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"
msgstr "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3277
msgid "Categories"
msgstr "Categories"

#: core/common/modules/finder/categories/settings.php:74
#: core/experiments/manager.php:503
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3286
msgid "Features"
msgstr "Features"

#: includes/managers/elements.php:324
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3662
msgid "Favorites"
msgstr "Favourites"

#: modules/nested-accordion/widgets/nested-accordion.php:554
#: assets/js/app-packages.js:5346
msgid "Header"
msgstr "Header"

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "Sections"

#: includes/settings/tools.php:83
msgid "There's already an active kit."
msgstr "There's already an active kit."

#: includes/settings/tools.php:89
msgid "An error occurred while trying to create a kit."
msgstr "An error occurred while trying to create a kit."

#: includes/settings/tools.php:94
msgid "New kit have been created successfully"
msgstr "New kit has been created successfully"

#: includes/settings/tools.php:299
msgid "Styles set in Elementor are saved in CSS files in the uploads folder and in the site’s database. Recreate those files and settings, according to the most recent settings."
msgstr "Styles set in Elementor are saved in CSS files in the uploads folder and in the site’s database. Recreate those files and settings, according to the most recent settings."

#: includes/editor-templates/panel.php:302 assets/js/editor.js:13274
msgid "Color Sampler"
msgstr "Colour Sampler"

#: includes/settings/tools.php:416
msgid "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."
msgstr "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."

#: includes/settings/tools.php:412 includes/settings/tools.php:415
#: assets/js/editor.js:28251
msgid "Recreate Kit"
msgstr "Recreate Kit"

#: includes/settings/tools.php:298
msgid "Regenerate Files & Data"
msgstr "Regenerate Files & Data"

#: includes/settings/tools.php:295
msgid "Regenerate CSS & Data"
msgstr "Regenerate CSS & Data"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "Submissions"

#: core/settings/editor-preferences/model.php:121
msgid "Default device view"
msgstr "Default device view"

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "Kit not found"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "Kit does not exist."

#: app/modules/kit-library/connect/kit-library.php:16
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:34 app/modules/kit-library/module.php:35
#: core/common/modules/finder/categories/general.php:78
#: assets/js/import-export-admin.js:300
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1538
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3715
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4153
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4453
msgid "Kit Library"
msgstr "Kit Library"

#: includes/settings/tools.php:322 assets/js/app.js:7614 assets/js/app.js:8335
msgid "Important:"
msgstr "Important:"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "Compatible"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "Incompatible"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "Compatibility not specified"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "Compatibility unknown"

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "API Key"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:311
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "Google Maps Embed API"

#: core/admin/admin-notices.php:325
msgid "Managing a multi-user site?"
msgstr "Managing a multi-user site?"

#: core/admin/admin-notices.php:326
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "With Elementor Pro, you can control user access and make sure no one messes up your design."

#: includes/settings/settings.php:387
msgid "Blocking"
msgstr "Blocking"

#: includes/settings/settings.php:388
msgid "Swap"
msgstr "Swap"

#: includes/settings/settings.php:390
msgid "Optional"
msgstr "Optional"

#: includes/widgets/common-base.php:136
msgid "Flower"
msgstr "Flower"

#: includes/widgets/common-base.php:137
msgid "Sketch"
msgstr "Sketch"

#: includes/widgets/common-base.php:138
msgid "Triangle"
msgstr "Triangle"

#: includes/widgets/common-base.php:139
msgid "Blob"
msgstr "Blob"

#: includes/widgets/common-base.php:140
msgid "Hexagon"
msgstr "Hexagon"

#: includes/widgets/common-base.php:971 includes/widgets/common-base.php:979
msgid "Mask"
msgstr "Mask"

#: includes/widgets/common-base.php:1044
msgid "Fit"
msgstr "Fit"

#: includes/base/element-base.php:1065 includes/base/element-base.php:1088
#: includes/widgets/common-base.php:1059
msgid "Scale"
msgstr "Scale"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "Database update process is running in the background. Taking a while?"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "Widescreen"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1097 includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
msgid "Top Center"
msgstr "Top Centre"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1096 includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
msgid "Center Right"
msgstr "Centre Right"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1095 includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
msgid "Center Left"
msgstr "Centre Left"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1094 includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
msgid "Center Center"
msgstr "Centre Centre"

#: includes/widgets/common-base.php:1024
msgid "Need More Shapes?"
msgstr "Need More Shapes?"

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:307
msgid "FAQ Schema"
msgstr "FAQ Schema"

#: includes/settings/settings.php:392
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "Font-display property defines how font files are loaded and displayed by the browser."

#: includes/settings/settings.php:381
msgid "Google Fonts Load"
msgstr "Google Fonts Load"

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "Manage Breakpoints"

#: includes/widgets/common-base.php:1195
#: modules/floating-buttons/base/widget-contact-button-base.php:2086
#: modules/floating-buttons/base/widget-contact-button-base.php:2177
#: modules/floating-buttons/base/widget-contact-button-base.php:2870
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "Round"

#: includes/controls/groups/background.php:465
#: includes/widgets/common-base.php:1193
msgid "Repeat-x"
msgstr "Repeat-x"

#: includes/controls/groups/background.php:466
#: includes/widgets/common-base.php:1194
msgid "Repeat-y"
msgstr "Repeat-y"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1191
msgid "No-repeat"
msgstr "No-repeat"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1188 includes/widgets/common-base.php:1192
msgid "Repeat"
msgstr "Repeat"

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1152
msgid "Y Position"
msgstr "Y Position"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1116
msgid "X Position"
msgstr "X Position"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1102 includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:139
msgid "Bottom Right"
msgstr "Bottom Right"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1101 includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:140
msgid "Bottom Left"
msgstr "Bottom Left"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1100 includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
msgid "Bottom Center"
msgstr "Bottom Centre"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1099 includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:138
msgid "Top Right"
msgstr "Top Right"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1098 includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:137
msgid "Top Left"
msgstr "Top Left"

#: core/admin/admin-notices.php:236
msgid "Love using Elementor?"
msgstr "Love using Elementor?"

#: app/modules/import-export/module.php:119
msgid "Template Kits"
msgstr "Template Kits"

#: app/modules/import-export/module.php:116
msgid "Import / Export Kit"
msgstr "Import / Export Kit"

#: app/modules/import-export/module.php:161
msgid "Apply the design and settings of another site to this one."
msgstr "Apply the design and settings of another site to this one."

#: app/modules/import-export/module.php:159
msgid "Start Import"
msgstr "Start Import"

#: app/modules/import-export/module.php:156
msgid "Import a Template Kit"
msgstr "Import a Template Kit"

#: app/modules/import-export/module.php:149
msgid "Bundle your whole site - or just some of its elements - to be used for another website."
msgstr "Bundle your whole site - or just some of its elements - to be used for another website."

#: app/modules/import-export/module.php:147
msgid "Start Export"
msgstr "Start Export"

#: app/modules/import-export/module.php:144
msgid "Export a Template Kit"
msgstr "Export a Template Kit"

#. translators: 1: New line break, 2: Learn more link.
#: app/modules/import-export/module.php:137
msgid "Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s"
msgstr "Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:478
#: core/utils/import-export/wp-import.php:669
#: core/utils/import-export/wp-import.php:719
msgid "Failed to import %1$s %2$s"
msgstr "Failed to import %1$s %2$s"

#: core/utils/import-export/wp-import.php:871
msgid "Menu item skipped due to missing menu slug"
msgstr "Menu item skipped due to missing menu slug"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:884
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "Menu item skipped due to invalid menu slug: %s"

#: core/utils/import-export/wp-import.php:971
msgid "Fetching attachments is not enabled"
msgstr "Fetching attachments is not enabled"

#: core/utils/import-export/wp-import.php:988
msgid "Invalid file type"
msgstr "Invalid file type"

#: core/utils/import-export/wp-import.php:1032
msgid "Could not create temporary file."
msgstr "Could not create temporary file."

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1048
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "Request failed due to an error: %1$s (%2$s)"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1057
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "Remote server returned the following unexpected result: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1066
msgid "Remote server did not respond"
msgstr "Remote server did not respond"

#: core/utils/import-export/wp-import.php:1074
msgid "Zero size file downloaded"
msgstr "Zero size file downloaded"

#: core/utils/import-export/wp-import.php:1080
msgid "Downloaded file has incorrect size"
msgstr "Downloaded file has incorrect size"

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1088
msgid "Remote file is too large, limit is %s"
msgstr "Remote file is too large, limit is %s"

#: core/utils/import-export/wp-import.php:1120
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "Sorry, this file type is not permitted for security reasons."

#: modules/shapes/widgets/text-path.php:490
msgid "Path"
msgstr "Path"

#: modules/shapes/widgets/text-path.php:525
#: modules/shapes/widgets/text-path.php:596
msgid "Stroke"
msgstr "Stroke"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "Wave"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "Arc"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "Oval"

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "Spiral"

#: core/utils/import-export/wp-import.php:246
msgid "The file does not exist, please try again."
msgstr "The file does not exist, please try again."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:313
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "Failed to import author %s. Their posts will be attributed to the current user."

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:378
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "Failed to create new user for %s. Their posts will be attributed to the current user."

#: core/utils/import-export/wp-import.php:1136
msgid "The uploaded file could not be moved"
msgstr "The uploaded file could not be moved"

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "There was an error when reading this WXR file"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "This does not appear to be a WXR file, missing/invalid WXR version number"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:577
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "Failed to import %1$s: Invalid post type %2$s"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "Mobile and Tablet options cannot be deleted."

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "Active Breakpoints"

#: modules/shapes/widgets/text-path.php:387
msgid "Starting Point"
msgstr "Starting Point"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:352
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:29
msgid "Word Spacing"
msgstr "Word Spacing"

#: modules/shapes/widgets/text-path.php:222
msgid "Show Path"
msgstr "Show Path"

#: modules/shapes/widgets/text-path.php:210
msgid "LTR"
msgstr "LTR"

#: modules/shapes/widgets/text-path.php:209
msgid "RTL"
msgstr "RTL"

#: modules/shapes/widgets/text-path.php:204
msgid "Text Direction"
msgstr "Text Direction"

#: modules/shapes/widgets/text-path.php:141
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:131
msgid "Path Type"
msgstr "Path Type"

#: modules/shapes/widgets/text-path.php:119
msgid "Add Your Curvy Text Here"
msgstr "Add Your Curvy Text Here"

#: modules/shapes/widgets/text-path.php:54
#: modules/shapes/widgets/text-path.php:108
#: modules/shapes/widgets/text-path.php:248
msgid "Text Path"
msgstr "Text Path"

#: app/modules/import-export/module.php:133 core/admin/admin-notices.php:371
#: core/admin/admin-notices.php:427 core/experiments/manager.php:314
#: core/experiments/manager.php:330 core/experiments/manager.php:359
#: core/experiments/manager.php:557 includes/controls/url.php:78
#: includes/elements/section.php:473 includes/settings/settings-page.php:404
#: includes/widgets/common-base.php:1029 includes/widgets/video.php:597
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:101
#: modules/editor-app-bar/module.php:52 modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:155 assets/js/app.js:7604
#: assets/js/editor.js:27355
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3497
msgid "Learn more"
msgstr "Learn more"

#: core/admin/notices/elementor-dev-notice.php:76
msgid "Get a sneak peek at our in progress development versions, and help us improve Elementor to perfection. Developer Edition releases contain experimental functionality for testing purposes."
msgstr "Get a sneak peek at our in progress development versions, and help us improve Elementor to perfection. Developer Edition releases contain experimental functionality for testing purposes."

#: core/experiments/manager.php:425
msgid "Development"
msgstr "Development"

#: core/admin/notices/elementor-dev-notice.php:75
msgid "Elementor Developer Edition"
msgstr "Elementor Developer Edition"

#: core/experiments/manager.php:702
msgid "Inactive by default"
msgstr "Inactive by default"

#: core/experiments/manager.php:701
msgid "Active by default"
msgstr "Active by default"

#. translators: %s Release status.
#: core/experiments/manager.php:588
msgid "Status: %s"
msgstr "Status: %s"

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "Experiments"

#: core/experiments/manager.php:488
msgid "No available experiments"
msgstr "No available experiments"

#: core/experiments/manager.php:429
msgid "Stable"
msgstr "Stable"

#: core/experiments/manager.php:428
msgid "Release Candidate"
msgstr "Release Candidate"

#: core/experiments/manager.php:427 assets/js/ai-admin.js:554
#: assets/js/ai-admin.js:7641 assets/js/ai-gutenberg.js:2322
#: assets/js/ai-gutenberg.js:9489 assets/js/ai-layout.js:484
#: assets/js/ai-layout.js:3252 assets/js/ai-media-library.js:2183
#: assets/js/ai-media-library.js:9270 assets/js/ai-unify-product-images.js:2183
#: assets/js/ai-unify-product-images.js:9270 assets/js/ai.js:2962
#: assets/js/ai.js:10717
msgid "Beta"
msgstr "Beta"

#: core/experiments/manager.php:426
msgid "Alpha"
msgstr "Alpha"

#: core/experiments/manager.php:491
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "The current version of Elementor doesn't have any experimental features. If you're feeling curious, make sure to come back in future versions."

#: core/admin/notices/elementor-dev-notice.php:81
msgid "Install & Activate"
msgstr "Install and activate"

#: core/experiments/manager.php:556
msgid "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."

#: modules/landing-pages/module.php:279
msgid "No landing pages found"
msgstr "No landing pages found."

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:138
msgid "Unknown"
msgstr "Unknown"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "Tested up to %s version"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:2309
#: assets/js/element-manager-admin.js:2370
msgid "Plugin"
msgstr "Plugin"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "Custom columns gap"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "Compatibility alert"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:205 modules/landing-pages/module.php:271
msgid "Landing Page"
msgstr "Landing page"

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:47 modules/landing-pages/module.php:139
#: modules/landing-pages/module.php:270 modules/landing-pages/module.php:282
#: assets/js/app.js:10352 assets/js/editor.js:51883
msgid "Landing Pages"
msgstr "Landing pages"

#: modules/landing-pages/module.php:48
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "Adds a new Elementor content type that allows you to create beautiful landing pages instantly in a streamlined workflow."

#: modules/landing-pages/module.php:205
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "Build effective landing pages for your business' marketing campaigns."

#: modules/landing-pages/module.php:273
msgid "Add New Landing Page"
msgstr "Add new landing page"

#: modules/landing-pages/module.php:274
msgid "Edit Landing Page"
msgstr "Edit landing page"

#: modules/landing-pages/module.php:275
msgid "New Landing Page"
msgstr "New landing page"

#: modules/landing-pages/module.php:276
msgid "All Landing Pages"
msgstr "All landing pages"

#: modules/landing-pages/module.php:277
msgid "View Landing Page"
msgstr "View landing page"

#: modules/landing-pages/module.php:278
msgid "Search Landing Pages"
msgstr "Search landing pages"

#: modules/landing-pages/module.php:280
msgid "No landing pages found in trash"
msgstr "No landing pages found in bin"

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "Keep my settings"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "By removing this template you will delete your entire site settings. If this template is deleted, all associated settings: global colours and fonts, theme style, layout, background, and Lightbox settings will be removed from your existing site. This action cannot be undone."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "Are you sure you want to delete your site settings?"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "The Global value you are trying to use is not available."

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "Choose SVG"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:432
msgid "Breakpoint"
msgstr "Breakpoint"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:10360
#: assets/js/editor.js:46426
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:173
msgid "Global Colors"
msgstr "Global Colours"

#: modules/nested-tabs/widgets/nested-tabs.php:371 assets/js/editor.js:46785
msgid "Additional Settings"
msgstr "Additional Settings"

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "Apply Link On"

#: core/admin/admin-notices.php:220 includes/settings/settings-page.php:403
msgid "Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us."
msgstr "Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us."

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "Breakpoints"

#: modules/page-templates/module.php:363
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "Changes will be reflected in the preview only after the page reloads."

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "Choose description"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "Choose name"

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "Default Page Layout"

#: core/settings/editor-preferences/model.php:197 assets/js/editor.js:46793
msgid "Design System"
msgstr "Design System"

#: includes/frontend.php:1411
msgid "Download"
msgstr "Download"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "Fallback Font Family"

#: includes/widgets/common-base.php:1045 includes/widgets/image.php:383
msgid "Fill"
msgstr "Fill"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:146
msgid "Items"
msgstr "Items"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:10360
msgid "Layout Settings"
msgstr "Layout Settings"

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "Mobile Browser Background"

#: includes/widgets/image.php:376
msgid "Object Fit"
msgstr "Object Fit"

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "Rows Gap"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "Site Description"

#: core/kits/documents/tabs/settings-site-identity.php:113
msgid "Site Favicon"
msgstr "Site Favicon"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "Site Identity"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1048
msgid "Site Logo"
msgstr "Site Logo"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1044
msgid "Site Name"
msgstr "Site Name"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:115
#: assets/js/app.js:10358 assets/js/app.js:10838 assets/js/editor.js:46375
#: assets/js/editor.js:46379 assets/js/editor.js:46389
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "Site Settings"
msgstr "Site Settings"

#: core/kits/documents/tabs/settings-site-identity.php:120
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "Suggested favicon dimensions: 512 × 512 pixels."

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "Theme"

#: core/settings/editor-preferences/model.php:41
#: includes/editor-templates/hotkeys.php:153 assets/js/editor.js:37491
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "User Preferences"
msgstr "User Preferences"

#: core/admin/admin.php:868
msgid "Heads up, Please backup before upgrade!"
msgstr "Heads up, please back up before upgrade!"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "The `theme-color` meta tag will only be available in supported browsers and devices."

#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "Error"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:874
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"

#. translators: 1: Width number pixel, 2: Height number pixel.
#: core/kits/documents/tabs/settings-site-identity.php:102
msgid "Suggested image dimensions: %1$s × %2$s pixels."
msgstr "Suggested image dimensions: %1$s × %2$s pixels."

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "Reset Data"

#: includes/controls/media.php:285
msgid "Click the media icon to upload file"
msgstr "Click the media icon to upload file"

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/common.js:2146 assets/js/common.js:2156
#: assets/js/editor.js:39273 assets/js/editor.js:39283
msgid "Enable Unfiltered File Uploads"
msgstr "Enable Unfiltered File Uploads"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "Watch the Full Guide"

#: modules/safe-mode/module.php:383
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."

#: includes/frontend.php:1412
msgid "Download image"
msgstr "Download image"

#: includes/frontend.php:1410
msgid "Pin it"
msgstr "Pin it"

#: includes/frontend.php:1409
msgid "Share on Twitter"
msgstr "Share on Twitter"

#: includes/frontend.php:1408
msgid "Share on Facebook"
msgstr "Share on Facebook"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."

#: includes/editor-templates/panel.php:326
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."

#: includes/editor-templates/panel.php:325
msgid "You’re missing out!"
msgstr "You’re missing out!"

#: includes/managers/icons.php:497
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "We highly recommend backing up your database before performing this upgrade."

#: includes/managers/icons.php:496
msgid "The upgrade process includes a database update"
msgstr "The upgrade process includes a database update"

#: includes/managers/controls.php:1209
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "Attributes lets you add custom HTML attributes to any element."

#: includes/managers/controls.php:1197
msgid "Attributes"
msgstr "Attributes"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "Click here to run it now"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1415
msgid "Share"
msgstr "Share"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1413
msgid "Fullscreen"
msgstr "Fullscreen"

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: assets/js/ai-admin.js:9435 assets/js/ai-gutenberg.js:11283
#: assets/js/ai-media-library.js:11064
#: assets/js/ai-unify-product-images.js:11064 assets/js/ai.js:12511
#: assets/js/app.js:7176 assets/js/editor.js:46681
msgid "Back"
msgstr "Back"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "Focus"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "Field"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "Label"

#: core/kits/documents/tabs/theme-style-typography.php:49
msgid "Body"
msgstr "Body"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "Buttons"

#: core/kits/documents/kit.php:155
msgid "Draft"
msgstr "Draft"

#: core/kits/documents/kit.php:44
msgid "Kit"
msgstr "Kit"

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "%s Widget"

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "Custom Attributes"

#: core/isolation/elementor-adapter.php:26 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "Default Kit"

#: includes/editor-templates/panel.php:296
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:21
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:27
msgid "Dynamic Tags"
msgstr "Dynamic Tags"

#: includes/editor-templates/panel.php:322
msgid "Elementor Dynamic Content"
msgstr "Elementor Dynamic Content"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "Form Fields"

#: includes/managers/controls.php:1207
msgid "Meet Our Attributes"
msgstr "Meet Our Attributes"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "Navigation Icons Size"

#: core/kits/documents/tabs/theme-style-typography.php:76
#: includes/widgets/text-editor.php:293
msgid "Paragraph Spacing"
msgstr "Paragraph Spacing"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "Toolbar Icons Size"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "Already connected."

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "Connected as %s"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "%s Video Player"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:547 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:591
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:65
msgid "Auto"
msgstr "Auto"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "Background Position"

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "Background Size"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "Connecting to the Library failed. Please try reloading the page and try again"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691 includes/widgets/image.php:385
msgid "Contain"
msgstr "Contain"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:975
msgid "Cover"
msgstr "Cover"

#: core/settings/editor-preferences/model.php:175 assets/js/ai-admin.js:15519
#: assets/js/ai-gutenberg.js:17367 assets/js/ai-layout.js:5205
#: assets/js/ai-media-library.js:17148
#: assets/js/ai-unify-product-images.js:17148 assets/js/ai.js:9237
#: assets/js/ai.js:18595 assets/js/editor.js:10027
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:658
msgid "Get Started"
msgstr "Get Started"

#: includes/widgets/image-carousel.php:462
msgid "Pause on Interaction"
msgstr "Pause on Interaction"

#: core/settings/editor-preferences/model.php:54
msgid "Preferences"
msgstr "Preferences"

#: includes/settings/settings-page.php:396
msgid "Usage Data Sharing"
msgstr "Usage Data Sharing"

#: core/settings/editor-preferences/model.php:81
msgid "Auto detect"
msgstr "Auto detect"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "Custom Icons"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "Transition"

#: includes/controls/groups/background.php:645
msgid "Duration"
msgstr "Duration"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "Clear Log"

#: includes/frontend.php:1418 assets/js/app.js:7115 assets/js/app.js:8795
#: assets/js/app.js:9738
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1676
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1727
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2006
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2283
msgid "Next"
msgstr "Next"

#: includes/frontend.php:1417 assets/js/app.js:7988 assets/js/app.js:8782
#: assets/js/app.js:9731
msgid "Previous"
msgstr "Previous"

#: includes/widgets/divider.php:667
msgid "Amount"
msgstr "Amount"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "Line"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "Out"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "In"

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "This cover image will replace the background video in case that the video could not be loaded."

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "YouTube/Vimeo link, or link to video file (MP4 is recommended)."

#: includes/settings/tools.php:367
msgid "Reinstall"
msgstr "Reinstall"

#: core/document-types/post.php:51
msgid "Post"
msgstr "Post"

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "Add Element"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "Basic Gallery"

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "Ken Burns Effect"

#: includes/controls/groups/background.php:583 includes/widgets/video.php:354
msgid "Play On Mobile"
msgstr "Play On Mobile"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "File Path: %s"

#: core/experiments/manager.php:682 includes/base/widget-base.php:1020
msgid "Deprecated"
msgstr "Deprecated"

#: includes/managers/icons.php:557
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "Hurray! The upgrade process to Font Awesome 5 was completed successfully."

#: includes/managers/icons.php:511
msgid "Upgrade To Font Awesome 5"
msgstr "Upgrade To Font Awesome 5"

#: includes/managers/icons.php:499
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "This action is not reversible and cannot be undone by rolling back to previous versions."

#: includes/managers/icons.php:494
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."

#: includes/managers/icons.php:492
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."

#: includes/managers/icons.php:491
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."

#: includes/controls/media.php:298 includes/controls/media.php:300
#: assets/js/editor.js:8093
msgid "Upload"
msgstr "Upload"

#: includes/managers/icons.php:247
msgid "All Icons"
msgstr "All Icons"

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "Choose Video"

#: core/editor/editor.php:210
msgid "Document not found."
msgstr "Document not found."

#: includes/managers/icons.php:485 includes/managers/icons.php:489
#: includes/managers/icons.php:504
msgid "Font Awesome Upgrade"
msgstr "Font Awesome Upgrade"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "Get Beta Updates"

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:8677
msgid "Icon Library"
msgstr "Icon Library"

#: includes/managers/icons.php:470
msgid "Load Font Awesome 4 Support"
msgstr "Load Font Awesome 4 Support"

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:30
#: includes/editor-templates/panel.php:183
msgid "Need Help"
msgstr "Need Help"

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "Play Once"

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "Sign Up"

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "Some of your theme files are missing."

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "Upload SVG"

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "We recommend you only enable this feature if you understand the security risks involved."

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "Your Email"

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "Your site's .htaccess file appears to be missing."

#: includes/managers/icons.php:478
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome - Brands"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - Solid"

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - Regular"

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "Elementor will try to sanitise the unfiltered files, removing potential malicious code and scripts."

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "Template does not exist."

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "Please note! Allowing uploads of any files (SVG and JSON included) is a potential security risk."

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:590
msgid "This file is not allowed for security reasons."
msgstr "This file is not allowed for security reasons."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/elements/column.php:928 includes/elements/container.php:1885
#: includes/elements/section.php:1407 includes/widgets/common-base.php:1230
msgid "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."
msgstr "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."

#: includes/elements/container.php:546 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:64
msgid "Hidden"
msgstr "Hidden"

#: includes/elements/container.php:541 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:66
msgid "Overflow"
msgstr "Overflow"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "Get Help"

#: includes/elements/container.php:1510 includes/widgets/common-base.php:433
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."

#: includes/elements/container.php:1509 includes/widgets/common-base.php:432
msgid "Please note!"
msgstr "Please note!"

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "Space Evenly"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "Space Around"

#: includes/elements/container.php:1648 includes/widgets/common-base.php:565
msgid "Vertical Orientation"
msgstr "Vertical Orientation"

#: includes/base/element-base.php:999 includes/elements/container.php:1572
#: includes/elements/container.php:1610 includes/elements/container.php:1672
#: includes/elements/container.php:1709 includes/widgets/common-base.php:489
#: includes/widgets/common-base.php:527 includes/widgets/common-base.php:589
#: includes/widgets/common-base.php:626
#: modules/floating-buttons/base/widget-contact-button-base.php:2962
#: modules/floating-buttons/base/widget-contact-button-base.php:3016
msgid "Offset"
msgstr "Offset"

#: includes/elements/container.php:1547 includes/widgets/common-base.php:464
msgid "Horizontal Orientation"
msgstr "Horizontal Orientation"

#: includes/elements/container.php:1528 includes/widgets/common-base.php:450
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:71
msgid "Fixed"
msgstr "Fixed"

#: includes/elements/container.php:1527 includes/widgets/common-base.php:449
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:70
msgid "Absolute"
msgstr "Absolute"

#: includes/elements/section.php:420 includes/widgets/common-base.php:404
#: includes/widgets/image-carousel.php:732
msgid "Vertical Align"
msgstr "Vertical Align"

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:249
msgid "Custom Width"
msgstr "Custom Width"

#: includes/elements/column.php:863 includes/elements/container.php:1805
#: includes/elements/section.php:1316 includes/widgets/common-base.php:720
msgid "Motion Effects"
msgstr "Motion Effects"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "Super Admin"

#: modules/safe-mode/module.php:374
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."

#: modules/safe-mode/module.php:272
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "The issue was probably caused by one of your plugins or theme."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "Note: The ID link ONLY accepts these chars: %s"

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:387
msgid "Choose File"
msgstr "Choose File"

#: includes/widgets/video.php:223
msgid "External URL"
msgstr "External URL"

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "Order By"

#: includes/widgets/read-more.php:125
msgid "Read More Text"
msgstr "Read More Text"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:116
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "Note: This widget only affects themes that use `%s` in archive pages."

#: includes/widgets/read-more.php:96
msgid "Continue reading"
msgstr "Continue reading"

#: includes/widgets/read-more.php:41 includes/widgets/read-more.php:92
msgid "Read More"
msgstr "Read More"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:959
#: modules/link-in-bio/base/widget-link-in-bio-base.php:500
#: modules/link-in-bio/base/widget-link-in-bio-base.php:750
msgid "Location"
msgstr "Location"

#. translators: %s: Current post name.
#: includes/frontend.php:1553
msgid "Continue reading %s"
msgstr "Continue reading %s"

#: includes/frontend.php:1546
msgid "(more&hellip;)"
msgstr "(more&hellip;)"

#: includes/template-library/sources/local.php:1418
msgctxt "Template Library"
msgid "Filter by category"
msgstr "Filter by category"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "All Categories"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "Category"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "Categories"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "Templates"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "Get Popup Builder"

#: includes/template-library/sources/local.php:1723
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:10343
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2219
msgid "Popups"
msgstr "Popups"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1722
#: assets/js/app-packages.js:5890 assets/js/editor.js:46400
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Theme Builder"
msgstr "Theme Builder"

#: modules/safe-mode/module.php:370
msgid "Enable Safe Mode"
msgstr "Enable Safe Mode"

#: modules/safe-mode/module.php:368 modules/safe-mode/module.php:380
msgid "Can't Edit?"
msgstr "Can't Edit?"

#: modules/safe-mode/module.php:285
msgid "Still experiencing issues?"
msgstr "Still experiencing issues?"

#: modules/safe-mode/module.php:269
msgid "Editor successfully loaded?"
msgstr "Editor successfully loaded?"

#: modules/safe-mode/module.php:262 modules/safe-mode/module.php:492
msgid "Disable Safe Mode"
msgstr "Disable Safe Mode"

#: modules/safe-mode/module.php:260
msgid "Safe Mode ON"
msgstr "Safe Mode ON"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "Cannot enable Safe Mode"

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "Safe Mode"

#: core/upgrade/manager.php:47
msgid "Elementor Data Updater"
msgstr "Elementor Data Updater"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:312
msgid "Every %d minutes"
msgstr "Every %d minutes"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "The database update process is now complete. Thank you for updating to the latest version!"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "Your site database needs to be updated to the latest version."

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."

#: modules/library/documents/not-supported.php:57
msgid "Not Supported"
msgstr "Not Supported"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "Users"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:10846
msgid "Plugins"
msgstr "Plugins"

#: includes/widgets/video.php:508
msgid "Any Video"
msgstr "Any Video"

#: includes/widgets/video.php:507
msgid "Current Video Channel"
msgstr "Current Video Channel"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:390
msgid "Unmarked Color"
msgstr "Unmarked Colour"

#: includes/widgets/star-rating.php:319
msgid "Stars"
msgstr "Stars"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "Outline"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "Unmarked Style"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "Rating Scale"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "Rating"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "Star Rating"

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "Custom Caption"

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "Attachment Caption"

#: includes/editor-templates/hotkeys.php:208
msgid "Quit"
msgstr "Quit"

#: includes/editor-templates/hotkeys.php:200 assets/js/editor.js:7402
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "Keyboard Shortcuts"

#: includes/editor-templates/hotkeys.php:107
msgid "Show / Hide Panel"
msgstr "Show / Hide Panel"

#: includes/editor-templates/hotkeys.php:167
msgid "Go To"
msgstr "Go To"

#: includes/editor-templates/hotkeys.php:32 assets/js/ai-admin.js:12377
#: assets/js/ai-admin.js:13430 assets/js/ai-gutenberg.js:14225
#: assets/js/ai-gutenberg.js:15278 assets/js/ai-media-library.js:14006
#: assets/js/ai-media-library.js:15059
#: assets/js/ai-unify-product-images.js:14006
#: assets/js/ai-unify-product-images.js:15059 assets/js/ai.js:15453
#: assets/js/ai.js:16506
msgid "Redo"
msgstr "Redo"

#: includes/editor-templates/hotkeys.php:24 assets/js/ai-admin.js:12366
#: assets/js/ai-admin.js:13419 assets/js/ai-gutenberg.js:14214
#: assets/js/ai-gutenberg.js:15267 assets/js/ai-media-library.js:13995
#: assets/js/ai-media-library.js:15048
#: assets/js/ai-unify-product-images.js:13995
#: assets/js/ai-unify-product-images.js:15048 assets/js/ai.js:15442
#: assets/js/ai.js:16495 assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "Undo"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "Type to find anything in Elementor"

#: includes/editor-templates/hotkeys.php:99 assets/js/admin-top-bar.js:189
#: assets/js/common.js:4694 assets/js/editor.js:37503
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "Finder"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "Customiser"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "Menus"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "Dashboard"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:12
msgid "Homepage"
msgstr "Home page"

#: core/common/modules/finder/categories/create.php:27
#: assets/js/editor.js:44998
msgid "Create"
msgstr "Create"

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "Disconnect"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: assets/js/ai-admin.js:969 assets/js/ai-admin.js:6264
#: assets/js/ai-gutenberg.js:2737 assets/js/ai-gutenberg.js:8112
#: assets/js/ai-layout.js:765 assets/js/ai-layout.js:2487
#: assets/js/ai-media-library.js:2598 assets/js/ai-media-library.js:7893
#: assets/js/ai-unify-product-images.js:2598
#: assets/js/ai-unify-product-images.js:7893 assets/js/ai.js:3377
#: assets/js/ai.js:9248 assets/js/ai.js:9340
msgid "Connect"
msgstr "Connect"

#: core/base/document.php:1989
msgid "Future"
msgstr "Future"

#: core/common/modules/connect/apps/base-app.php:232
msgid "Disconnected successfully."
msgstr "Disconnected successfully."

#: core/common/modules/connect/apps/base-app.php:220 assets/js/editor.js:10719
msgid "Connected successfully."
msgstr "Connected successfully."

#: includes/widgets/video.php:610
msgid "Poster"
msgstr "Poster"

#: includes/widgets/video.php:470
msgid "Lazy Load"
msgstr "Lazy Load"

#: includes/widgets/video.php:159 includes/widgets/video.php:184
#: includes/widgets/video.php:208 includes/widgets/video.php:268
msgid "Enter your URL"
msgstr "Enter your URL"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "Welcome to Elementor"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "Create Your First Post"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "Create Your First Page"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:792
msgid "Getting Started"
msgstr "Getting Started"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:27369
msgid "Inner Section"
msgstr "Inner Section"

#: includes/editor-templates/navigator.php:103
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."

#: includes/editor-templates/navigator.php:102
msgid "Easy Navigation is Here!"
msgstr "Easy Navigation is Here!"

#: includes/editor-templates/navigator.php:97
msgid "Empty"
msgstr "Empty"

#: includes/editor-templates/hotkeys.php:126
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:87
#: includes/editor-templates/panel.php:91 assets/js/editor.js:31088
msgid "Navigator"
msgstr "Navigator"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:55
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:122 assets/js/app-packages.js:2837
#: assets/js/app.js:3271
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1268
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1487
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1579
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1822
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1997
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2318
msgid "Skip"
msgstr "Skip"

#: includes/controls/url.php:68
#: modules/floating-buttons/base/widget-contact-button-base.php:952
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:270
#: modules/shapes/widgets/text-path.php:169
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:12
msgid "Paste URL or type"
msgstr "Paste URL or type"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "Hue"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "Debug Bar"

#: core/admin/admin-notices.php:287
msgid "Hide Notification"
msgstr "Hide Notification"

#: core/admin/admin-notices.php:281
msgid "Happy To Help"
msgstr "Happy To Help"

#: core/admin/admin-notices.php:277
msgid "Congrats!"
msgstr "Congrats!"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:124 includes/widgets/text-editor.php:132
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1414
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10095
#: assets/js/ai-admin.js:10098 assets/js/ai-gutenberg.js:11943
#: assets/js/ai-gutenberg.js:11946 assets/js/ai-media-library.js:11724
#: assets/js/ai-media-library.js:11727
#: assets/js/ai-unify-product-images.js:11724
#: assets/js/ai-unify-product-images.js:11727 assets/js/ai.js:13171
#: assets/js/ai.js:13174
msgid "Zoom"
msgstr "Zoom"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "Single"

#: includes/widgets/video.php:442
msgid "Logo"
msgstr "Logo"

#: includes/widgets/video.php:416
msgid "Modest Branding"
msgstr "Modest Branding"

#: includes/widgets/video.php:402
msgid "Video Info"
msgstr "Video Info"

#: core/base/providers/social-network-provider.php:217
#: includes/widgets/video.php:255 includes/widgets/video.php:279
msgid "URL"
msgstr "URL"

#: includes/widgets/video.php:141
msgid "Self Hosted"
msgstr "Self Hosted"

#: includes/widgets/video.php:139
msgid "Dailymotion"
msgstr "Dailymotion"

#: includes/widgets/video.php:133
msgid "Source"
msgstr "Source"

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "Button ID"

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "Artwork"

#: includes/managers/elements.php:313
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:306
msgid "Site"
msgstr "Site"

#: includes/managers/elements.php:288
#: modules/promotions/widgets/pro-widget-promotion.php:66
#: assets/js/ai-admin.js:7858 assets/js/ai-gutenberg.js:9706
#: assets/js/ai-layout.js:3469 assets/js/ai-media-library.js:9487
#: assets/js/ai-unify-product-images.js:9487 assets/js/ai.js:10934
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
msgid "Pro"
msgstr "Pro"

#: includes/elements/column.php:441 includes/elements/container.php:842
#: includes/elements/section.php:715 includes/widgets/heading.php:310
msgid "Blend Mode"
msgstr "Blend Mode"

#: includes/editor-templates/hotkeys.php:41 assets/js/editor.js:29992
msgid "Copy"
msgstr "Copy"

#: includes/editor-templates/global.php:49
#: assets/js/cf70912a0f34653ad242.bundle.js:130
msgid "Drag widget here"
msgstr "Drag widget here"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "Saturation"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "Contrast"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "Brightness"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "Blur"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:319
msgid "Specify an end time (in seconds)"
msgstr "Specify an end time (in seconds)"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:317
msgid "End Time"
msgstr "End Time"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:308
msgid "Specify a start time (in seconds)"
msgstr "Specify a start time (in seconds)"

#: includes/controls/groups/background.php:549 includes/widgets/video.php:306
msgid "Start Time"
msgstr "Start Time"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "I have Elementor Pro"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Elementor Debugger"

#: includes/widgets/traits/button-trait.php:216
msgid "Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."

#: core/admin/admin.php:220 assets/js/admin.js:2058 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "Back to WordPress Editor"

#. translators: %s: Document title.
#: core/documents-manager.php:390
msgid "Elementor %s"
msgstr "Elementor %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:267
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1379
msgid "Add New %s"
msgstr "Add New %s"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:410 includes/elements/column.php:484
#: includes/elements/container.php:796 includes/elements/container.php:910
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:429 includes/widgets/image-box.php:464
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1499
msgid "Opacity"
msgstr "Opacity"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "Max Width"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "New Template"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1522 includes/widgets/common-base.php:444
#: includes/widgets/common-base.php:1091 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:572
#: includes/widgets/image-carousel.php:636 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:251
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:940
#: modules/link-in-bio/base/widget-link-in-bio-base.php:995
#: modules/nested-accordion/widgets/nested-accordion.php:213
#: modules/nested-tabs/widgets/nested-tabs.php:857
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:28
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:5
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:72
msgid "Position"
msgstr "Position"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:409
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "Knowledge Base"

#: modules/page-templates/module.php:350
msgid "This template includes the header, full-width content and footer"
msgstr "This template includes the header, full-width content and footer"

#: modules/page-templates/module.php:338
msgid "No header, no footer, just Elementor"
msgstr "No header, no footer, just Elementor"

#: modules/page-templates/module.php:295
msgid "Page Layout"
msgstr "Page Layout"

#: modules/page-templates/module.php:326
msgid "Default Page Template from your theme."
msgstr "Default Page Template from your theme."

#: includes/frontend.php:1416 includes/widgets/video.php:1000
msgid "Play Video"
msgstr "Play Video"

#: includes/widgets/common-base.php:233 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "Inline"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "Separator"

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:272 assets/js/app-packages.js:4592
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "Add New"

#: includes/template-library/sources/local.php:1339
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimised workflow."

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1372
msgid "Create Your First %s"
msgstr "Create Your First %s"

#: includes/template-library/sources/local.php:1244
msgid "All"
msgstr "All"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "My Templates"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "Create Template"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "Enter template name (optional)"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "Name your template"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "Select the type of template you want to work on"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "Choose Template Type"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:3
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:235
msgid "Custom Fonts"
msgstr "Custom Fonts"

#: includes/editor-templates/templates.php:189
msgid "More actions"
msgstr "More actions"

#: includes/editor-templates/templates.php:109
msgid "Search Templates:"
msgstr "Search Templates:"

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:11125 assets/js/editor.js:9995
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2191
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "Pages"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "This tag has no settings."

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1225 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:80
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "%s Settings"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "Want to give access only to content?"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "No access to editor"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "Role Excluded"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "Manage What Your Users Can Edit In Elementor"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "Role Manager"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:389
msgid "Fallback"
msgstr "Fallback"

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "Featured Image"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "Body Style"

#: core/base/document.php:257
msgid "Document"
msgstr "Document"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "Action not found."

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "Token Expired."

#: includes/widgets/image-carousel.php:183
msgid "Set how many slides are scrolled per swipe."
msgstr "Set how many slides are scrolled per swipe."

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "Note: Attachment Fixed works only on desktop."

#: core/admin/admin.php:478
msgid "Create New Post"
msgstr "Create New Post"

#: includes/fonts.php:77
msgid "Google (Early Access)"
msgstr "Google (Early Access)"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "Current Version"

#: includes/controls/groups/background.php:597 includes/widgets/video.php:457
msgid "Privacy Mode"
msgstr "Privacy Mode"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "Enter your shortcode"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "Enter your image caption"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "Enter your code"

#: includes/widgets/heading.php:171
msgid "Add Your Heading Text Here"
msgstr "Add Your Heading Text Here"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "Enter your description"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "This is an Alert"

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:137
msgid "Active Icon"
msgstr "Active Icon"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1550
msgid "Last edited on %1$s by %2$s"
msgstr "Last edited on %1$s by %2$s"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1543
msgid "Draft saved on %1$s by %2$s"
msgstr "Draft saved on %1$s by %2$s"

#: core/base/document.php:1537
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "M j, H:i"

#. translators: %s: Document title.
#: core/base/document.php:198
msgid "Hurray! Your %s is live."
msgstr "Hurray! Your %s is live."

#: core/kits/documents/kit.php:156
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "Published"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:11066
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1879
msgid "No Results Found"
msgstr "No Results Found"

#: includes/editor-templates/templates.php:272 assets/js/app-packages.js:2528
#: assets/js/app.js:2962
msgid "Select File"
msgstr "Select File"

#: includes/editor-templates/templates.php:271
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2169
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2513
msgid "or"
msgstr "or"

#: includes/editor-templates/templates.php:270
msgid "Drag & drop your .JSON or .zip template file"
msgstr "Drag & drop your .JSON or .zip template file"

#: includes/editor-templates/templates.php:269
msgid "Import Template to Your Library"
msgstr "Import Template to Your Library"

#: includes/editor-templates/templates.php:260
#: includes/editor-templates/templates.php:276
#: includes/editor-templates/templates.php:289
#: includes/widgets/traits/button-trait.php:57 assets/js/app.js:6856
#: assets/js/app.js:7861
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2525
msgid "Click here"
msgstr "Click here"

#: includes/editor-templates/templates.php:259
#: includes/editor-templates/templates.php:275
#: includes/editor-templates/templates.php:288
msgid "Want to learn more about the Elementor library?"
msgstr "Want to learn more about the Elementor library?"

#: includes/editor-templates/templates.php:167
msgid "Favorite"
msgstr "Favourite"

#: includes/editor-templates/templates.php:130
msgid "Creation Date"
msgstr "Creation Date"

#: includes/editor-templates/templates.php:126
msgid "Created By"
msgstr "Created By"

#: includes/editor-templates/templates.php:110
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:21
msgid "Search"
msgstr "Search"

#: includes/editor-templates/templates.php:101
msgid "My Favorites"
msgstr "My Favourites"

#: includes/editor-templates/templates.php:79
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3811
msgid "Popular"
msgstr "Popular"

#: includes/editor-templates/templates.php:77
msgid "Trend"
msgstr "Trend"

#: includes/editor-templates/templates.php:75
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3807
msgid "New"
msgstr "New"

#: includes/editor-templates/templates.php:11
#: includes/editor-templates/templates.php:12
msgid "Import Template"
msgstr "Import Template"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:294
#: includes/controls/media.php:296 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:74
#: assets/js/ai-admin.js:2208 assets/js/ai-admin.js:7345
#: assets/js/ai-gutenberg.js:3976 assets/js/ai-gutenberg.js:9193
#: assets/js/ai-layout.js:2956 assets/js/ai-media-library.js:3837
#: assets/js/ai-media-library.js:8974 assets/js/ai-unify-product-images.js:3837
#: assets/js/ai-unify-product-images.js:8974 assets/js/ai.js:4616
#: assets/js/ai.js:10421
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:919
msgid "Remove"
msgstr "Remove"

#: includes/editor-templates/hotkeys.php:73
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:29977
#: assets/js/editor.js:50587
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:20
msgid "Duplicate"
msgstr "Duplicate"

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "Drag & Drop"

#: includes/editor-templates/panel.php:151
#: includes/editor-templates/panel.php:153 assets/js/editor.js:35841
msgid "Hide Panel"
msgstr "Hide Panel"

#: includes/editor-templates/panel.php:137 assets/js/editor.js:32647
#: assets/js/editor.js:33143
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "Save as Template"

#: includes/editor-templates/panel.php:133
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "Save Draft"

#: includes/editor-templates/panel.php:119
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "Save Options"

#: core/base/document.php:171 includes/editor-templates/panel.php:114
#: assets/js/editor.js:25042
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "Publish"

#: includes/editor-templates/panel.php:102
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "Preview Changes"

#: includes/editor-templates/panel-elements.php:78
msgid "Search Widget:"
msgstr "Search Widget:"

#: core/experiments/manager.php:565 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "Back to default"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "Line Through"

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "Overline"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "Underline"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "Decoration"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "Unlinked values"

#: core/admin/admin.php:618
msgid "Blog"
msgstr "Blog"

#: core/admin/admin.php:598
msgid "(opens in a new window)"
msgstr "(opens in a new window)"

#: core/admin/admin.php:566
msgid "News & Updates"
msgstr "News & Updates"

#: core/admin/admin.php:533
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "M jS"

#: core/admin/admin.php:525
msgid "Recently Edited"
msgstr "Recently Edited"

#: core/admin/admin.php:475
msgid "Create New Page"
msgstr "Create New Page"

#: core/admin/admin.php:429
msgid "Elementor Overview"
msgstr "Elementor Overview"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "Excerpt"

#: includes/widgets/video.php:459
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:754
msgid "Access denied."
msgstr "Access denied."

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "Disable Default Fonts"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:217 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:416 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:585
#: includes/widgets/image-carousel.php:744 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:181
#: modules/nested-accordion/widgets/nested-accordion.php:221
#: modules/nested-tabs/widgets/nested-tabs.php:242
#: modules/nested-tabs/widgets/nested-tabs.php:284
#: modules/nested-tabs/widgets/nested-tabs.php:354
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:84
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:114
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:119
msgid "End"
msgstr "End"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:215 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:408 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:577
#: includes/widgets/image-carousel.php:736 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:287
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:173
#: modules/nested-accordion/widgets/nested-accordion.php:217
#: modules/nested-tabs/widgets/nested-tabs.php:234
#: modules/nested-tabs/widgets/nested-tabs.php:276
#: modules/nested-tabs/widgets/nested-tabs.php:346
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:82
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:112
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:117
msgid "Start"
msgstr "Start"

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "The preview could not be loaded"

#: core/debug/loading-inspection-manager.php:43
msgid "We’re sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "We're sorry, but something went wrong. Click on “Learn more” and follow each of the steps to quickly solve it."

#: core/admin/admin-notices.php:149 core/admin/admin-notices.php:184
msgid "Update Notification"
msgstr "Update Notification"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "Once you start working, you'll be able to redo / undo any action you make in the editor."

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "No History Yet"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "Switch to Revisions tab for older versions"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:50320
msgid "Revisions"
msgstr "Revisions"

#: includes/editor-templates/hotkeys.php:19
#: includes/editor-templates/templates.php:133
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:50317
msgid "Actions"
msgstr "Actions"

#: includes/editor-templates/hotkeys.php:144
#: includes/editor-templates/panel.php:96 assets/js/ai-admin.js:1957
#: assets/js/ai-gutenberg.js:3725 assets/js/ai-media-library.js:3586
#: assets/js/ai-unify-product-images.js:3586 assets/js/ai.js:4365
#: assets/js/editor.js:50909
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "History"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:918
msgid "UI Hover Color"
msgstr "UI Hover Colour"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:906
msgid "UI Color"
msgstr "UI Colour"

#: includes/widgets/video.php:366
msgid "Mute"
msgstr "Mute"

#: includes/template-library/sources/local.php:981
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "Image Lightbox"

#: includes/settings/tools.php:401
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "Please Note: We do not recommend updating to a beta version on production sites."

#: includes/settings/tools.php:393
msgid "Beta Tester"
msgstr "Beta Tester"

#: includes/settings/tools.php:377
msgid "Become a Beta Tester"
msgstr "Become a Beta Tester"

#: includes/settings/tools.php:370
msgid "Warning: Please backup your database before making the rollback."
msgstr "Warning: Please backup your database before making the rollback."

#: includes/settings/tools.php:362
msgid "Rollback Version"
msgstr "Rollback Version"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:353
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:346
msgid "Version Control"
msgstr "Version Control"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "Switch Editor Loader Method"

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "Integrations"

#: includes/rollback.php:165 includes/settings/tools.php:175
#: includes/settings/tools.php:349 assets/js/admin.js:2251
msgid "Rollback to Previous Version"
msgstr "Rollback to Previous Version"

#: includes/elements/column.php:900 includes/elements/container.php:1842
#: includes/elements/section.php:1353 includes/widgets/common-base.php:757
#: modules/floating-buttons/base/widget-contact-button-base.php:1418
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "Animation Delay"

#: includes/elements/column.php:791 includes/elements/container.php:1746
#: includes/elements/section.php:1264 includes/widgets/common-base.php:663
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:67
msgid "Z-Index"
msgstr "Z-Index"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:231
msgid "Widgets Space"
msgstr "Widgets Space"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "Add nofollow"

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "Open in new window"

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "Link Options"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "Outline"

#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:184
#: assets/js/atomic-widgets-editor.js:1601 assets/js/editor.js:29778
#: assets/js/editor.js:29964 assets/js/editor.js:32229
#: assets/js/editor.js:32695 assets/js/editor.js:32796
#: assets/js/editor.js:33122 assets/js/editor.js:36435
#: assets/js/editor.js:47318
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:11
msgid "Edit %s"
msgstr "Edit %s"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1253
#: includes/controls/groups/background.php:673 includes/elements/column.php:355
#: includes/elements/column.php:515 includes/elements/column.php:623
#: includes/elements/container.php:719 includes/elements/container.php:933
#: includes/elements/container.php:1093 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:823
#: includes/widgets/common-base.php:938 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:386 includes/widgets/icon-list.php:471
#: includes/widgets/icon-list.php:698 includes/widgets/image-box.php:482
#: includes/widgets/image.php:501 includes/widgets/traits/button-trait.php:435
#: modules/floating-buttons/base/widget-contact-button-base.php:1495
#: modules/floating-buttons/base/widget-contact-button-base.php:2234
#: modules/nested-tabs/widgets/nested-tabs.php:609
#: modules/shapes/widgets/text-path.php:465
#: modules/shapes/widgets/text-path.php:643
msgid "Transition Duration"
msgstr "Transition Duration"

#: includes/settings/tools.php:380
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "Sets the default space between widgets (Default: 20px)"

#: includes/settings/settings.php:419
msgid "Internal Embedding"
msgstr "Internal Embedding"

#: includes/settings/settings.php:418
msgid "External File"
msgstr "External File"

#: includes/settings/settings.php:412
msgid "CSS Print Method"
msgstr "CSS Print Method"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "For troubleshooting server configuration conflicts."

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:434 includes/settings/settings.php:451
#: includes/settings/settings.php:463 includes/settings/tools.php:399
#: modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:382
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/app-packages.js:2833 assets/js/app.js:3267
#: assets/js/common.js:2146 assets/js/editor.js:39273
msgid "Enable"
msgstr "Enable"

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/tools.php:398
#: modules/element-cache/module.php:132 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:381
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "Disable"

#: core/base/document.php:1995 modules/ai/preferences.php:67
#: assets/js/element-manager-admin.js:2293
#: assets/js/element-manager-admin.js:2370
msgid "Status"
msgstr "Status"

#: includes/widgets/common-base.php:1196 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:409
msgid "Space"
msgstr "Space"

#: includes/widgets/text-editor.php:138 includes/widgets/text-editor.php:316
msgid "Drop Cap"
msgstr "Drop Cap"

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "Page Title Selector"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "Select"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:18687
msgid "Template"
msgstr "Template"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "Hide Title"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "Maintenance Mode ON"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "Choose Template"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "To enable maintenance mode you have to set a template for the maintenance mode page."

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:9600
msgid "Edit Template"
msgstr "Edit Template"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "Roles"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "Logged In"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "Who Can Access"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "Maintenance"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "Coming Soon"

#: core/kits/documents/kit.php:155 includes/maintenance-mode.php:215
#: assets/js/editor.js:50586
msgid "Disabled"
msgstr "Disabled"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "Choose Mode"

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "Maintenance Mode"

#: includes/editor-templates/hotkeys.php:57
msgid "Paste Style"
msgstr "Paste Style"

#: includes/elements/container.php:1318 includes/elements/section.php:1115
msgid "Bring to Front"
msgstr "Bring to Front"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:366
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:734 includes/widgets/video.php:882
msgid "Lightbox"
msgstr "Lightbox"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:190 includes/elements/column.php:218
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "Space Between"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "List"

#: includes/shapes.php:212
msgctxt "Shapes"
msgid "Book"
msgstr "Book"

#: includes/shapes.php:208
msgctxt "Shapes"
msgid "Split"
msgstr "Split"

#: includes/shapes.php:204
msgctxt "Shapes"
msgid "Arrow"
msgstr "Arrow"

#: includes/shapes.php:200
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "Waves Pattern"

#: includes/shapes.php:196
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "Waves Brush"

#: includes/shapes.php:191
msgctxt "Shapes"
msgid "Waves"
msgstr "Waves"

#: includes/shapes.php:186
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "Curve Asymmetrical"

#: includes/shapes.php:182
msgctxt "Shapes"
msgid "Curve"
msgstr "Curve"

#: includes/shapes.php:179
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "Fan Opacity"

#: includes/shapes.php:175
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "Tilt Opacity"

#: includes/shapes.php:170
msgctxt "Shapes"
msgid "Tilt"
msgstr "Tilt"

#: includes/shapes.php:165
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "Triangle Asymmetrical"

#: includes/shapes.php:161
msgctxt "Shapes"
msgid "Triangle"
msgstr "Triangle"

#: includes/shapes.php:156
msgctxt "Shapes"
msgid "Pyramids"
msgstr "Pyramids"

#: includes/shapes.php:153 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "Zigzag"

#: includes/shapes.php:147
msgctxt "Shapes"
msgid "Clouds"
msgstr "Clouds"

#: includes/shapes.php:141
msgctxt "Shapes"
msgid "Drops"
msgstr "Drops"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "Mountains"

#: includes/elements/container.php:1305 includes/elements/section.php:1102
msgid "Invert"
msgstr "Invert"

#: includes/elements/container.php:1291 includes/elements/section.php:1088
msgid "Flip"
msgstr "Flip"

#: includes/elements/container.php:1159 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "Shape Divider"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "Navigation Width"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:149
#: includes/base/element-base.php:872 includes/elements/column.php:340
#: includes/elements/column.php:469 includes/elements/column.php:588
#: includes/elements/container.php:704 includes/elements/container.php:885
#: includes/elements/container.php:1046 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:808
#: includes/widgets/common-base.php:903 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:368 includes/widgets/icon-box.php:433
#: includes/widgets/icon-list.php:451 includes/widgets/icon-list.php:679
#: includes/widgets/icon.php:256 includes/widgets/image-box.php:449
#: includes/widgets/image.php:468 includes/widgets/traits/button-trait.php:383
#: modules/floating-buttons/base/widget-contact-button-base.php:1254
#: modules/floating-buttons/base/widget-contact-button-base.php:2013
#: modules/floating-buttons/base/widget-contact-button-base.php:2495
#: modules/floating-buttons/base/widget-contact-button-base.php:2675
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:663
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:773
#: modules/nested-tabs/widgets/nested-tabs.php:954
#: modules/shapes/widgets/text-path.php:438
#: modules/shapes/widgets/text-path.php:577
msgid "Hover"
msgstr "Hover"

#: includes/elements/column.php:812 includes/elements/container.php:1767
#: includes/elements/section.php:1285 includes/widgets/common-base.php:683
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3100
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:138
#: modules/nested-tabs/widgets/nested-tabs.php:160
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Add your custom id WITHOUT the Pound key. e.g: my-id"

#: includes/elements/column.php:803 includes/elements/container.php:1758
#: includes/elements/section.php:1276 includes/widgets/common-base.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:3091
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:129
#: modules/nested-tabs/widgets/nested-tabs.php:151
msgid "CSS ID"
msgstr "CSS ID"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "Type"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "Location"

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "Checking this box will disable Elementor's Default Colours, and make Elementor inherit the colours from your theme."

#: core/admin/admin.php:379
msgid "Video Tutorials"
msgstr "Video Tutorials"

#: core/admin/admin.php:379
msgid "View Elementor Video Tutorials"
msgstr "View Elementor Video Tutorials"

#: core/admin/admin.php:378
msgid "Docs & FAQs"
msgstr "Docs & FAQs"

#: core/admin/admin.php:378
msgid "View Elementor Documentation"
msgstr "View Elementor Documentation"

#: includes/settings/tools.php:337
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."

#: includes/settings/tools.php:333
msgid "Update Site Address (URL)"
msgstr "Update Site Address (URL)"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:315 includes/settings/tools.php:319
#: includes/settings/tools.php:336
msgid "Replace URL"
msgstr "Replace URL"

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s ago (%2$s)"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "M j @ H:i"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "Autosave"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "Revision"

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "It looks like the post revision feature is unavailable in your website."

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "Revision history lets you save your previous versions of your work, and restore them any time."

#: modules/apps/admin-apps-page.php:179
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "By"

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "No Revisions Saved Yet"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "Start designing your page and you will be able to see the entire revision history here."

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "Thousand Separator"

#: includes/managers/controls.php:1074
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."

#: includes/managers/controls.php:1086
msgid "Meet Our Custom CSS"
msgstr "Meet Our Custom CSS"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1068
msgid "Custom CSS"
msgstr "Custom CSS"

#: includes/editor-templates/panel-elements.php:101
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."

#: includes/editor-templates/panel-elements.php:100
msgid "Meet Our Global Widget"
msgstr "Meet Our Global Widget"

#: modules/promotions/widgets/pro-widget-promotion.php:75
#: assets/js/ai-admin.js:7789 assets/js/ai-gutenberg.js:9637
#: assets/js/ai-layout.js:3400 assets/js/ai-media-library.js:9418
#: assets/js/ai-unify-product-images.js:9418 assets/js/ai.js:10865
msgid "Go Pro"
msgstr "Go Pro"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "Get more with Elementor Pro"

#: includes/base/element-base.php:916 includes/base/element-base.php:1080
#: includes/widgets/common-base.php:982 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:140
#: includes/widgets/video.php:737 modules/shapes/widgets/text-path.php:225
msgid "Off"
msgstr "Off"

#: includes/base/element-base.php:915 includes/base/element-base.php:1079
#: includes/widgets/common-base.php:981 includes/widgets/icon-list.php:286
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:141
#: includes/widgets/video.php:738 modules/shapes/widgets/text-path.php:224
msgid "On"
msgstr "On"

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "Extra Large"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "Extra Small"

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "Improve Elementor"

#: includes/frontend.php:1259
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."

#: includes/base/widget-base.php:311 includes/base/widget-base.php:320
msgid "Skin"
msgstr "Skin"

#: includes/editor-templates/panel.php:209
msgid "%s are disabled"
msgstr "%s are disabled"

#: includes/editor-templates/panel.php:173
msgid "Update changes to page"
msgstr "Update changes to page"

#: core/admin/admin-notices.php:244
msgid "No thanks"
msgstr "No thanks"

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "Stretched Section Fit To"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "Stretch the section to the full width of the page using JS."

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "Stretch Section"

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "Sets the default width of the content area (Default: 1140px)"

#: core/admin/admin-notices.php:233
msgid "Learn more."
msgstr "Learn more."

#: includes/elements/section.php:1384
msgid "Reverse Columns"
msgstr "Reverse Columns"

#: core/settings/editor-preferences/model.php:126
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4354
msgid "Mobile"
msgstr "Mobile"

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "Link values together"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "Shortcode"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "Remote"

#: includes/template-library/sources/local.php:988
msgid "Import Now"
msgstr "Import Now"

#: includes/template-library/sources/local.php:979
msgid "Import Templates"
msgstr "Import Templates"

#: includes/template-library/sources/local.php:951
msgid "Export Template"
msgstr "Export Template"

#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(no title)"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "Type"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "Template"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "Local"

#: includes/settings/tools.php:307
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."

#: includes/editor-templates/templates.php:16
#: includes/editor-templates/templates.php:17 includes/settings/tools.php:303
#: includes/settings/tools.php:306
msgid "Sync Library"
msgstr "Sync Library"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:434
msgid "Tools"
msgstr "Tools"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:10273
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "Page"

#: includes/editor-templates/templates.php:250
msgid "Enter Template Name"
msgstr "Enter Template Name"

#: includes/editor-templates/templates.php:199
#: includes/template-library/sources/local.php:1166 assets/js/app.js:11321
msgid "Export"
msgstr "Export"

#: includes/editor-templates/templates.php:143
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "Stay tuned! More awesome templates coming real soon."

#: includes/editor-templates/templates.php:39
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1473
msgid "Back to Library"
msgstr "Back to Library"

#: includes/editor-templates/templates.php:185
#: includes/editor-templates/templates.php:209
#: includes/editor-templates/templates.php:223 assets/js/ai-admin.js:6586
#: assets/js/ai-gutenberg.js:8434 assets/js/ai-media-library.js:8215
#: assets/js/ai-unify-product-images.js:8215 assets/js/ai.js:9662
#: assets/js/editor.js:8497
msgid "Insert"
msgstr "Insert"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:10607
msgid "Library"
msgstr "Library"

#: core/base/document.php:170 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1419
#: assets/js/ai-admin.js:485 assets/js/ai-gutenberg.js:2253
#: assets/js/ai-layout.js:415 assets/js/ai-media-library.js:2114
#: assets/js/ai-unify-product-images.js:2114 assets/js/ai.js:2893
#: assets/js/app-packages.js:2027 assets/js/app-packages.js:3994
#: assets/js/app-packages.js:4513 assets/js/app.js:2216 assets/js/app.js:4329
#: assets/js/app.js:4732 assets/js/app.js:6840 assets/js/app.js:7671
#: assets/js/app.js:11222 assets/js/cf70912a0f34653ad242.bundle.js:211
#: assets/js/cf70912a0f34653ad242.bundle.js:212 assets/js/editor.js:46684
#: assets/js/import-export-admin.js:313
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:17
msgid "Close"
msgstr "Close"

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "Add Template"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1721 assets/js/app.js:10342
msgid "Saved Templates"
msgstr "Saved Templates"

#: includes/editor-templates/hotkeys.php:181
msgid "Template Library"
msgstr "Template Library"

#: app/modules/import-export/module.php:152
#: app/modules/import-export/module.php:164 core/admin/admin-notices.php:330
#: modules/apps/admin-apps-page.php:189 modules/safe-mode/module.php:375
#: modules/safe-mode/module.php:384
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:106
#: assets/js/app-packages.js:2706 assets/js/app-packages.js:5662
#: assets/js/app-packages.js:5770 assets/js/app.js:3140 assets/js/app.js:7124
#: assets/js/app.js:8265 assets/js/app.js:9762 assets/js/app.js:10071
#: assets/js/app.js:10117 assets/js/app.js:11221 assets/js/editor.js:14470
#: assets/js/editor.js:27899 assets/js/editor.js:27930
#: assets/js/editor.js:40002 assets/js/element-manager-admin.js:2180
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3842
msgid "Learn More"
msgstr "Learn More"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:10360
#: assets/js/editor.js:46435
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:222
msgid "Global Fonts"
msgstr "Global Fonts"

#: core/base/traits/shared-widget-controls-trait.php:288
#: includes/widgets/icon-box.php:469 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:503 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:450
#: modules/floating-buttons/base/widget-contact-button-base.php:1477
#: modules/floating-buttons/base/widget-contact-button-base.php:2513
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:601
#: modules/shapes/widgets/text-path.php:457
msgid "Hover Animation"
msgstr "Hover Animation"

#: includes/elements/column.php:888 includes/elements/container.php:1830
#: includes/elements/section.php:1341 includes/widgets/common-base.php:745
#: modules/floating-buttons/base/widget-contact-button-base.php:1409
#: modules/floating-buttons/base/widget-contact-button-base.php:2792
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "Fast"

#: includes/elements/column.php:886 includes/elements/container.php:1828
#: includes/elements/section.php:1339 includes/widgets/common-base.php:743
#: modules/floating-buttons/base/widget-contact-button-base.php:1407
#: modules/floating-buttons/base/widget-contact-button-base.php:2790
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "Slow"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "Disable Default Colours"

#: includes/elements/column.php:873 includes/elements/container.php:1815
#: includes/elements/section.php:1326 includes/widgets/common-base.php:730
#: includes/widgets/video.php:930
#: modules/floating-buttons/base/widget-contact-button-base.php:1393
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "Entrance Animation"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "Inset"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:32
msgid "Vertical"
msgstr "Vertical"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:31
msgid "Horizontal"
msgstr "Horizontal"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:34
msgid "Spread"
msgstr "Spread"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:33
msgid "Blur"
msgstr "Blur"

#: includes/widgets/testimonial.php:201
msgid "Aside"
msgstr "Aside"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:111
msgid "Testimonial"
msgstr "Testimonial"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "Official Colour"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2087
#: modules/floating-buttons/base/widget-contact-button-base.php:2178
#: modules/floating-buttons/base/widget-contact-button-base.php:2871
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "Rounded"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "Social Icons"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "My Skill"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:185
msgid "Custom Colors"
msgstr "Custom Colours"

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:912
#: modules/link-in-bio/base/widget-link-in-bio-base.php:521
#: modules/link-in-bio/base/widget-link-in-bio-base.php:774
msgid "Username"
msgstr "Username"

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "Play Counts"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "Comments"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "Share Button"

#: includes/widgets/audio.php:182 includes/widgets/video.php:574
msgid "Download Button"
msgstr "Download Button"

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "Like Button"

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "Buy Button"

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "Visual Player"

#: core/base/providers/social-network-provider.php:169
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: includes/elements/column.php:382 includes/elements/container.php:758
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1480
msgid "Background Overlay"
msgstr "Background Overlay"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "Extended"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "If you have a moment, please share why you are deactivating Elementor:"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "Quick Feedback"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "Please share the reason"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "Other"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "It's a temporary deactivation"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "I couldn't get the plugin to work"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "Please share which plugin"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "I found a better plugin"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "I no longer need the plugin"

#: core/admin/admin-notices.php:145 core/admin/admin-notices.php:153
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "Update Now"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:140
msgid "View Elementor version %s details"
msgstr "View Elementor version %s details"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:136
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."

#: includes/widgets/image-carousel.php:343 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "Custom URL"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:270 includes/elements/container.php:629
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:497 includes/widgets/common-base.php:781
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:521
#: modules/floating-buttons/base/widget-contact-button-base.php:1679
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1461
#: assets/js/ai-admin.js:11111 assets/js/ai-gutenberg.js:12959
#: assets/js/ai-media-library.js:12740
#: assets/js/ai-unify-product-images.js:12740 assets/js/ai.js:14187
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:7
msgid "Background"
msgstr "Background"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "Wider"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:298
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:290
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
msgid "General"
msgstr "General"

#: includes/editor-templates/hotkeys.php:49 assets/js/editor.js:30004
#: assets/js/editor.js:32158 assets/js/editor.js:41380
#: assets/js/editor.js:42414
msgid "Paste"
msgstr "Paste"

#: includes/widgets/video.php:548
msgid "Intro Byline"
msgstr "Intro Byline"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:760
#: includes/widgets/image-carousel.php:894
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:352 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:627
#: modules/nested-tabs/widgets/nested-tabs.php:915
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:3
msgid "Spacing"
msgstr "Spacing"

#: includes/widgets/image-carousel.php:577
#: includes/widgets/image-carousel.php:640
msgid "Outside"
msgstr "Outside"

#: includes/widgets/image-carousel.php:576
#: includes/widgets/image-carousel.php:641
msgid "Inside"
msgstr "Inside"

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:534
#: modules/nested-tabs/widgets/nested-tabs.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:38
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:106
msgid "Direction"
msgstr "Direction"

#: includes/elements/container.php:533 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:416
msgid "Additional Options"
msgstr "Additional Options"

#: includes/widgets/image-carousel.php:215
msgid "Arrows and Dots"
msgstr "Arrows and Dots"

#: includes/widgets/alert.php:240
msgid "Left Border Width"
msgstr "Left Border Width"

#: includes/elements/column.php:882 includes/elements/container.php:1824
#: includes/elements/section.php:1335 includes/widgets/common-base.php:739
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1403
#: modules/floating-buttons/base/widget-contact-button-base.php:2786
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:367
msgid "Animation Duration"
msgstr "Animation Duration"

#: includes/widgets/image-carousel.php:198
msgid "Image Stretch"
msgstr "Image Stretch"

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:125
#: includes/widgets/image-carousel.php:134
msgid "Image Carousel"
msgstr "Image Carousel"

#: includes/widgets/image-carousel.php:523
msgid "Animation Speed"
msgstr "Animation Speed"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."

#: includes/widgets/audio.php:251 includes/widgets/video.php:562
msgid "Controls Color"
msgstr "Controls Colour"

#: includes/widgets/video.php:534
msgid "Intro Portrait"
msgstr "Intro Portrait"

#: includes/widgets/video.php:520
msgid "Intro Title"
msgstr "Intro Title"

#: includes/widgets/video.php:375
msgid "Loop"
msgstr "Loop"

#: includes/widgets/video.php:330
msgid "Video Options"
msgstr "Video Options"

#: core/base/providers/social-network-provider.php:187
#: includes/widgets/video.php:138
msgid "Vimeo"
msgstr "Vimeo"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:126 includes/widgets/video.php:752
msgid "Video"
msgstr "Video"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."

#: includes/widgets/image-carousel.php:511
msgid "Fade"
msgstr "Fade"

#: includes/widgets/image-carousel.php:506
msgid "Effect"
msgstr "Effect"

#: includes/controls/media.php:343
msgctxt "Image Size Control"
msgid "Full"
msgstr "Full"

#: includes/controls/gallery.php:92
msgid "Edit gallery"
msgstr "Edit gallery"

#: includes/elements/column.php:182 includes/widgets/icon-box.php:265
#: includes/widgets/icon-list.php:573 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "Vertical Alignment"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:493
msgid "Infinite Loop"
msgstr "Infinite Loop"

#: includes/widgets/image-carousel.php:217
msgid "Dots"
msgstr "Dots"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:270
msgid "Title HTML Tag"
msgstr "Title HTML Tag"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "This is the heading"

#: includes/elements/column.php:831 includes/elements/container.php:1786
#: includes/elements/section.php:1304 includes/widgets/common-base.php:701
#: modules/floating-buttons/base/widget-contact-button-base.php:3117
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Add your custom class WITHOUT the dot. e.g: my-class"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "The list of fonts used if the chosen font is not available."

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "Form"

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:19323
#: assets/js/editor.js:21221 assets/js/editor.js:21630
#: assets/js/editor.js:36597
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
msgid "Elements"
msgstr "Elements"

#: core/admin/admin.php:344 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:80
#: includes/managers/controls.php:334
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/div-block.php:40
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:9539
#: assets/js/editor.js:37487 assets/js/editor.js:46801
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:28
msgid "Settings"
msgstr "Settings"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "Capitalise"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "Oblique"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "Italic"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "Lowercase"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "Uppercase"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:708 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "Active Colour"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:239
#: includes/widgets/tabs.php:345 includes/widgets/video.php:895
#: modules/floating-buttons/base/widget-contact-button-base.php:1238
#: modules/floating-buttons/base/widget-contact-button-base.php:1290
#: modules/floating-buttons/base/widget-contact-button-base.php:1331
#: modules/floating-buttons/base/widget-contact-button-base.php:1377
#: modules/floating-buttons/base/widget-contact-button-base.php:1693
#: modules/floating-buttons/base/widget-contact-button-base.php:1963
#: modules/floating-buttons/base/widget-contact-button-base.php:1999
#: modules/floating-buttons/base/widget-contact-button-base.php:2032
#: modules/floating-buttons/base/widget-contact-button-base.php:2136
#: modules/floating-buttons/base/widget-contact-button-base.php:2162
#: modules/floating-buttons/base/widget-contact-button-base.php:2371
#: modules/floating-buttons/base/widget-contact-button-base.php:2658
#: modules/floating-buttons/base/widget-contact-button-base.php:2727
#: modules/floating-buttons/base/widget-contact-button-base.php:2814
#: modules/floating-buttons/base/widget-contact-button-base.php:2828
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1125
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:564
#: modules/nested-tabs/widgets/nested-tabs.php:648
#: modules/nested-tabs/widgets/nested-tabs.php:1003 assets/js/ai-admin.js:14382
#: assets/js/ai-gutenberg.js:16230 assets/js/ai-media-library.js:16011
#: assets/js/ai-unify-product-images.js:16011 assets/js/ai.js:17458
msgid "Background Color"
msgstr "Background Colour"

#: modules/floating-buttons/base/widget-contact-button-base.php:1847
msgid "Chat Background Color"
msgstr "Chat background colour"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/alert.php:229 includes/widgets/social-icons.php:562
#: includes/widgets/tabs.php:334 includes/widgets/toggle.php:305
#: includes/widgets/traits/button-trait.php:420
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:514
#: modules/nested-tabs/widgets/nested-tabs.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:664
#: modules/nested-tabs/widgets/nested-tabs.php:1016
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:143
msgid "Border Color"
msgstr "Border Colour"

#: includes/base/element-base.php:1315 includes/base/element-base.php:1343
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:216 includes/elements/column.php:733
#: includes/elements/section.php:1201 includes/widgets/common-base.php:412
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:260 includes/widgets/icon-box.php:301
#: includes/widgets/icon-list.php:268 includes/widgets/icon-list.php:550
#: includes/widgets/icon-list.php:581 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:740
#: includes/widgets/image-carousel.php:841
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:230
#: includes/widgets/text-editor.php:239
#: includes/widgets/traits/button-trait.php:259
#: includes/widgets/traits/button-trait.php:291 includes/widgets/video.php:979
#: modules/floating-buttons/base/widget-contact-button-base.php:2945
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:177
#: modules/nested-tabs/widgets/nested-tabs.php:238
#: modules/nested-tabs/widgets/nested-tabs.php:280
#: modules/nested-tabs/widgets/nested-tabs.php:350
#: modules/shapes/widgets/text-path.php:186
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:41
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:83
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:113
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:118
msgid "Center"
msgstr "Centre"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: includes/widgets/video.php:683 includes/widgets/video.php:810
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "Play Icon"

#: includes/widgets/video.php:636 includes/widgets/video.php:643
#: includes/widgets/video.php:798
msgid "Image Overlay"
msgstr "Image Overlay"

#: includes/widgets/video.php:504
msgid "Suggested Videos"
msgstr "Suggested Videos"

#: includes/widgets/video.php:760
msgid "Aspect Ratio"
msgstr "Aspect Ratio"

#: core/base/providers/social-network-provider.php:139
#: includes/widgets/video.php:137
msgid "YouTube"
msgstr "YouTube"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "Toggle Content"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "Toggle Title"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "Toggle #2"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "Toggle #1"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "Toggle Items"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "Toggle"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:123
#: includes/widgets/text-editor.php:223
msgid "Text Editor"
msgstr "Text Editor"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "Tab Content"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:115
#: modules/nested-tabs/widgets/nested-tabs.php:116
msgid "Tab Title"
msgstr "Tab Title"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:175
msgid "Tab #2"
msgstr "Tab #2"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tab #1"
msgstr "Tab #1"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:167
msgid "Tabs Items"
msgstr "Tabs Items"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:444
msgid "Tabs"
msgstr "Tabs"

#: includes/widgets/image-carousel.php:445
msgid "Pause on Hover"
msgstr "Pause on Hover"

#: includes/widgets/image-carousel.php:478
msgid "Autoplay Speed"
msgstr "Autoplay Speed"

#: includes/widgets/image-carousel.php:216
#: includes/widgets/image-carousel.php:560
msgid "Arrows"
msgstr "Arrows"

#: includes/widgets/image-carousel.php:510
msgid "Slide"
msgstr "Slide"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "Choose Sidebar"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "No sidebars were found"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "Sidebar"

#: includes/widgets/progress.php:315
msgid "Title Style"
msgstr "Title Style"

#: includes/widgets/progress.php:207
msgid "Web Designer"
msgstr "Web Designer"

#: includes/widgets/progress.php:206
msgid "e.g. Web Designer"
msgstr "e.g. Web Designer"

#: includes/widgets/progress.php:201 includes/widgets/progress.php:274
msgid "Inner Text"
msgstr "Inner Text"

#: includes/widgets/progress.php:189
msgid "Display Percentage"
msgstr "Display Percentage"

#: includes/widgets/progress.php:174
msgid "Percentage"
msgstr "Percentage"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:217
msgid "Progress Bar"
msgstr "Progress Bar"

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "For Example: About"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "The ID of Menu Anchor."

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "This ID will be the CSS ID you will have to use in your own page, Without #."

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "Menu Anchor"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:196
msgid "Image Position"
msgstr "Image Position"

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:776
msgid "Image Spacing"
msgstr "Image Spacing"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1648
msgid "Image Size"
msgstr "Image Size"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "Image Box"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "Icon Hover"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:563
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:458 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:517
#: modules/nested-tabs/widgets/nested-tabs.php:525
#: modules/nested-tabs/widgets/nested-tabs.php:583
#: modules/nested-tabs/widgets/nested-tabs.php:667
#: modules/nested-tabs/widgets/nested-tabs.php:1019
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:126
msgid "Border Width"
msgstr "Border Width"

#: includes/base/element-base.php:879 includes/base/element-base.php:891
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:541
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:288
msgid "Rotate"
msgstr "Rotate"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:415
#: includes/widgets/icon-box.php:453 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:357
msgid "Secondary Color"
msgstr "Secondary Colour"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:399
#: includes/widgets/icon-box.php:440 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:342
msgid "Primary Color"
msgstr "Primary Colour"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1676
#: assets/js/ai-admin.js:11164 assets/js/ai-gutenberg.js:13012
#: assets/js/ai-media-library.js:12793
#: assets/js/ai-unify-product-images.js:12793 assets/js/ai.js:14240
msgid "Square"
msgstr "Square"

#: includes/widgets/common-base.php:135 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1675
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "Circle"

#: includes/widgets/common-base.php:990 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "Shape"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:332
msgid "Framed"
msgstr "Framed"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:331
msgid "Stacked"
msgstr "Stacked"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "List Item"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "List Item #3"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "List Item #2"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "List Item #1"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "Icon List"

#: includes/widgets/icon-box.php:323
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1163
#: modules/floating-buttons/base/widget-contact-button-base.php:2419
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "Icon Spacing"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "Icon Box"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "HTML Code"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:258 includes/elements/container.php:574
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:212
#: modules/atomic-widgets/elements/div-block.php:43
msgid "HTML Tag"
msgstr "HTML Tag"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:170
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "Enter your title"

#: includes/widgets/heading.php:48 includes/widgets/heading.php:155
#: includes/widgets/heading.php:244
#: modules/link-in-bio/base/widget-link-in-bio-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:851
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1292
msgid "Heading"
msgstr "Heading"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "London Eye, London, United Kingdom"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "Google Maps"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:389
#: includes/widgets/image-carousel.php:395
#: includes/widgets/image-carousel.php:822
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "Caption"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3501
#: assets/js/ai-gutenberg.js:5269 assets/js/ai-media-library.js:5130
#: assets/js/ai-unify-product-images.js:5130 assets/js/ai.js:5942
msgid "Images"
msgstr "Images"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "Random"

#: includes/widgets/image-carousel.php:342
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "Media File"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "Attachment Page"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:141
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "Add Images"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:519
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "Gap"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:319
#: modules/floating-buttons/base/widget-contact-button-base.php:2553
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1215
msgid "Weight"
msgstr "Weight"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "Spacer"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:283
msgid "Divider"
msgstr "Divider"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:888
#: modules/link-in-bio/base/widget-link-in-bio-base.php:481
#: modules/link-in-bio/base/widget-link-in-bio-base.php:728
msgid "Number"
msgstr "Number"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "Cool Number"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "Number Suffix"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "Number Prefix"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "Ending Number"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "Starting Number"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "Counter"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:432
#: includes/widgets/video.php:339
msgid "Autoplay"
msgstr "Autoplay"

#: includes/widgets/image-carousel.php:181
msgid "Slides to Scroll"
msgstr "Slides to Scroll"

#: includes/widgets/image-carousel.php:164
msgid "Slides to Show"
msgstr "Slides to Show"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:131
#: includes/widgets/video.php:654
#: modules/link-in-bio/base/widget-link-in-bio-base.php:251
#: modules/link-in-bio/base/widget-link-in-bio-base.php:341
#: modules/link-in-bio/base/widget-link-in-bio-base.php:929
#: modules/link-in-bio/base/widget-link-in-bio-base.php:984
msgid "Choose Image"
msgstr "Choose Image"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1004 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:724 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:306
msgid "Image"
msgstr "Image"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:127
#: core/kits/documents/tabs/theme-style-typography.php:156
#: core/kits/documents/tabs/theme-style-typography.php:201
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:452
#: includes/elements/container.php:853 includes/elements/container.php:1217
#: includes/elements/section.php:726 includes/elements/section.php:1014
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:508 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:321
#: includes/widgets/icon-box.php:613 includes/widgets/icon-box.php:664
#: includes/widgets/icon-list.php:396 includes/widgets/icon-list.php:433
#: includes/widgets/icon-list.php:458 includes/widgets/icon-list.php:662
#: includes/widgets/icon-list.php:686 includes/widgets/image-box.php:534
#: includes/widgets/image-box.php:585 includes/widgets/image-carousel.php:609
#: includes/widgets/image-carousel.php:693 includes/widgets/progress.php:225
#: includes/widgets/progress.php:283 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:378 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:532
#: includes/widgets/video.php:822
#: modules/floating-buttons/base/widget-contact-button-base.php:1889
#: modules/floating-buttons/base/widget-contact-button-base.php:2288
#: modules/floating-buttons/base/widget-contact-button-base.php:2301
#: modules/floating-buttons/base/widget-contact-button-base.php:2539
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1389
#: modules/nested-accordion/widgets/nested-accordion.php:686
#: modules/nested-accordion/widgets/nested-accordion.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:744
#: modules/nested-tabs/widgets/nested-tabs.php:780
#: modules/nested-tabs/widgets/nested-tabs.php:816
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/nested-tabs/widgets/nested-tabs.php:976
#: modules/shapes/widgets/text-path.php:421
#: modules/shapes/widgets/text-path.php:445
#: modules/shapes/widgets/text-path.php:513
#: modules/shapes/widgets/text-path.php:533
#: modules/shapes/widgets/text-path.php:584
#: modules/shapes/widgets/text-path.php:604 assets/js/editor.js:46939
#: assets/js/editor.js:46982
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.strings.js:27
#: assets/js/packages/editor-controls/editor-controls.strings.js:36
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:144
msgid "Color"
msgstr "Colour"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:541 includes/elements/container.php:977
#: includes/elements/section.php:831 includes/widgets/common-base.php:856
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:142
msgid "Border"
msgstr "Border"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:203
#: modules/nested-tabs/widgets/nested-tabs.php:865
msgid "After"
msgstr "After"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:207
#: modules/nested-tabs/widgets/nested-tabs.php:873
msgid "Before"
msgstr "Before"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1133
#: modules/floating-buttons/base/widget-contact-button-base.php:2399
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "Icon Position"

#: includes/elements/column.php:741 includes/elements/section.php:1209
#: includes/widgets/heading.php:268 includes/widgets/icon-box.php:309
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:849
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:247
msgid "Justified"
msgstr "Justified"

#: includes/widgets/heading.php:200
msgid "XXL"
msgstr "XXL"

#: includes/widgets/heading.php:199
msgid "XL"
msgstr "XL"

#: includes/widgets/heading.php:198 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1123
#: modules/floating-buttons/base/widget-contact-button-base.php:1548
#: modules/floating-buttons/base/widget-contact-button-base.php:1930
#: modules/floating-buttons/base/widget-contact-button-base.php:2280
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1406
msgid "Large"
msgstr "Large"

#: includes/widgets/heading.php:197 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1122
#: modules/floating-buttons/base/widget-contact-button-base.php:1547
#: modules/floating-buttons/base/widget-contact-button-base.php:1929
#: modules/floating-buttons/base/widget-contact-button-base.php:2279
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1405
msgid "Medium"
msgstr "Medium"

#: includes/widgets/heading.php:196 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1121
#: modules/floating-buttons/base/widget-contact-button-base.php:1546
#: modules/floating-buttons/base/widget-contact-button-base.php:1928
#: modules/floating-buttons/base/widget-contact-button-base.php:2278
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1404
msgid "Small"
msgstr "Small"

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:379
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:416
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:699
#: modules/floating-buttons/base/widget-contact-button-base.php:498
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-accordion/widgets/nested-accordion.php:595
#: modules/nested-tabs/widgets/nested-tabs.php:126
#: modules/nested-tabs/widgets/nested-tabs.php:847
msgid "Icon"
msgstr "Icon"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1041 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:192
#: includes/widgets/icon-box.php:481 includes/widgets/icon-list.php:492
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:589
#: includes/widgets/image-carousel.php:673 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:327
#: includes/widgets/text-editor.php:380
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:838
#: modules/floating-buttons/base/widget-contact-button-base.php:1117
#: modules/floating-buttons/base/widget-contact-button-base.php:1542
#: modules/floating-buttons/base/widget-contact-button-base.php:1924
#: modules/floating-buttons/base/widget-contact-button-base.php:2274
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1400
#: modules/nested-accordion/widgets/nested-accordion.php:603
#: modules/nested-tabs/widgets/nested-tabs.php:895
#: modules/shapes/widgets/text-path.php:256
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:4
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:93
msgid "Size"
msgstr "Size"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:252 includes/widgets/icon-box.php:293
#: includes/widgets/icon-list.php:260 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:221
#: includes/widgets/text-editor.php:231 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:283
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:44
msgid "Alignment"
msgstr "Alignment"

#: includes/widgets/button.php:48 includes/widgets/button.php:93
#: includes/widgets/button.php:114
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1095
msgid "Button"
msgstr "Button"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2231
#: assets/js/ai-admin.js:10068 assets/js/ai-admin.js:10075
#: assets/js/ai-gutenberg.js:3999 assets/js/ai-gutenberg.js:11916
#: assets/js/ai-gutenberg.js:11923 assets/js/ai-media-library.js:3860
#: assets/js/ai-media-library.js:11697 assets/js/ai-media-library.js:11704
#: assets/js/ai-unify-product-images.js:3860
#: assets/js/ai-unify-product-images.js:11697
#: assets/js/ai-unify-product-images.js:11704 assets/js/ai.js:4639
#: assets/js/ai.js:13144 assets/js/ai.js:13151
#: assets/js/element-manager-admin.js:2511
#: assets/js/element-manager-admin.js:2567
msgid "Edit"
msgstr "Edit"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:655
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:576
#: includes/widgets/image-carousel.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:2343
#: modules/link-in-bio/base/widget-link-in-bio-base.php:892
#: modules/link-in-bio/base/widget-link-in-bio-base.php:897
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1348
msgid "Description"
msgstr "Description"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "I am a description. Click the edit button to change this text."

#: includes/widgets/alert.php:125 includes/widgets/progress.php:161
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "Danger"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:160
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "Warning"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:159
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "Success"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:158
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:5651
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3437
msgid "Info"
msgstr "Info"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:566 includes/elements/column.php:603
#: includes/elements/container.php:1022 includes/elements/container.php:1073
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:881 includes/widgets/common-base.php:918
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:578
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:399
#: includes/widgets/image-carousel.php:808
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:262 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:344 includes/widgets/text-editor.php:436
#: includes/widgets/traits/button-trait.php:473
#: modules/nested-accordion/widgets/nested-accordion.php:461
#: modules/nested-accordion/widgets/nested-accordion.php:526
#: modules/nested-tabs/widgets/nested-tabs.php:688
#: modules/nested-tabs/widgets/nested-tabs.php:1028
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:141
msgid "Border Radius"
msgstr "Border Radius"

#: core/base/document.php:1973
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:126 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:191 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:162
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:604
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:525
#: includes/widgets/image-carousel.php:394 includes/widgets/progress.php:117
#: includes/widgets/star-rating.php:203 includes/widgets/star-rating.php:247
#: includes/widgets/tabs.php:127 includes/widgets/tabs.php:357
#: includes/widgets/testimonial.php:168 includes/widgets/testimonial.php:404
#: includes/widgets/toggle.php:132 includes/widgets/toggle.php:350
#: modules/atomic-widgets/widgets/atomic-heading.php:106
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2315
#: modules/link-in-bio/base/widget-link-in-bio-base.php:866
#: modules/nested-accordion/widgets/nested-accordion.php:115
#: modules/nested-accordion/widgets/nested-accordion.php:563
#: modules/nested-tabs/widgets/nested-tabs.php:113
msgid "Title"
msgstr "Title"

#: core/settings/editor-preferences/model.php:187
#: includes/base/element-base.php:1388
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:192 includes/widgets/video.php:389
#: includes/widgets/video.php:404 includes/widgets/video.php:444
#: includes/widgets/video.php:522 includes/widgets/video.php:536
#: includes/widgets/video.php:550 includes/widgets/video.php:576
#: includes/widgets/video.php:645 includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:522
#: modules/floating-buttons/base/widget-contact-button-base.php:632
#: modules/floating-buttons/base/widget-contact-button-base.php:697
#: modules/floating-buttons/base/widget-contact-button-base.php:2529
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
msgid "Hide"
msgstr "Hide"

#: core/settings/editor-preferences/model.php:186
#: includes/base/element-base.php:1389
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:191 includes/widgets/video.php:390
#: includes/widgets/video.php:405 includes/widgets/video.php:445
#: includes/widgets/video.php:523 includes/widgets/video.php:537
#: includes/widgets/video.php:551 includes/widgets/video.php:577
#: includes/widgets/video.php:646 includes/widgets/video.php:687
#: modules/floating-buttons/base/widget-contact-button-base.php:521
#: modules/floating-buttons/base/widget-contact-button-base.php:631
#: modules/floating-buttons/base/widget-contact-button-base.php:696
#: modules/floating-buttons/base/widget-contact-button-base.php:2528
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:2087
msgid "Show"
msgstr "Show"

#: includes/editor-templates/templates.php:122
#: includes/elements/container.php:1190 includes/elements/section.php:987
#: includes/template-library/sources/local.php:1692
#: includes/widgets/alert.php:118 includes/widgets/progress.php:154
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1091
msgid "Type"
msgstr "Type"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "Alert"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:327
msgid "View"
msgstr "View"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "Accordion Content"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "Accordion Title"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "Accordion #2"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "Accordion #1"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "Accordion Items"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:393
msgid "Accordion"
msgstr "Accordion"

#: core/admin/admin-notices.php:239
msgid "Sure! I'd love to help"
msgstr "Sure! I'd love to help"

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "Download System Info"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "Copy & Paste Info"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "System Info"

#: includes/editor-templates/templates.php:118
#: includes/widgets/testimonial.php:153 includes/widgets/testimonial.php:359
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:656
#: modules/floating-buttons/base/widget-contact-button-base.php:1738
msgid "Name"
msgstr "Name"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:142
#: core/settings/editor-preferences/model.php:154
#: core/settings/editor-preferences/model.php:165
#: core/settings/editor-preferences/model.php:210
#: includes/controls/switcher.php:73 includes/managers/icons.php:475
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:202
#: includes/widgets/image-carousel.php:378
#: includes/widgets/image-carousel.php:435
#: includes/widgets/image-carousel.php:448
#: includes/widgets/image-carousel.php:465
#: includes/widgets/image-carousel.php:496
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3049
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
#: modules/nested-accordion/widgets/nested-accordion.php:310
#: modules/styleguide/module.php:127
msgid "No"
msgstr "No"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:141
#: core/settings/editor-preferences/model.php:153
#: core/settings/editor-preferences/model.php:164
#: core/settings/editor-preferences/model.php:209
#: includes/controls/switcher.php:74 includes/managers/icons.php:476
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:203
#: includes/widgets/image-carousel.php:377
#: includes/widgets/image-carousel.php:434
#: includes/widgets/image-carousel.php:447
#: includes/widgets/image-carousel.php:464
#: includes/widgets/image-carousel.php:495
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3048
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1553
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1611
#: modules/nested-accordion/widgets/nested-accordion.php:309
#: modules/styleguide/module.php:128 assets/js/app.js:8951
msgid "Yes"
msgstr "Yes"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "Exclude Roles"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "Post Types"

#: core/base/document.php:1965
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "General Settings"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "Accent"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "Secondary"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "Primary"

#: includes/managers/elements.php:347
msgid "WordPress"
msgstr "WordPress"

#: core/settings/editor-preferences/model.php:218
#: includes/widgets/image-carousel.php:211
#: includes/widgets/image-carousel.php:549
msgid "Navigation"
msgstr "Navigation"

#: includes/managers/elements.php:284
msgid "Basic"
msgstr "Basic"

#: includes/elements/container.php:1875 includes/elements/section.php:1396
msgid "Visibility"
msgstr "Visibility"

#: includes/widgets/video.php:975
msgid "Content Position"
msgstr "Content Position"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:188 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:273 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2999
msgid "Middle"
msgstr "Middle"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:626
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1045
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:326
#: modules/link-in-bio/base/widget-link-in-bio-base.php:582
#: modules/shapes/widgets/text-path.php:116
#: modules/shapes/widgets/text-path.php:309 assets/js/ai-admin.js:3500
#: assets/js/ai-gutenberg.js:5268 assets/js/ai-media-library.js:5129
#: assets/js/ai-unify-product-images.js:5129 assets/js/ai.js:5941
msgid "Text"
msgstr "Text"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:267
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:185
#: modules/nested-tabs/widgets/nested-tabs.php:246
#: modules/nested-tabs/widgets/nested-tabs.php:288
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:115
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:120
msgid "Stretch"
msgstr "Stretch"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "Column Position"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "Minimum Height"

#: includes/elements/container.php:470 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "Min Height"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "Fit To Screen"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1265 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1062
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:363
#: includes/widgets/image.php:354 includes/widgets/progress.php:250
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:58
msgid "Height"
msgstr "Height"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "Wide"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "Narrow"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "No Gap"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:175
msgid "Columns Gap"
msgstr "Columns Gap"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:377 includes/elements/section.php:249
#: includes/widgets/video.php:955
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1587
msgid "Content Width"
msgstr "Content Width"

#: includes/elements/container.php:382 includes/elements/section.php:254
#: includes/widgets/common-base.php:232 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1551
msgid "Full Width"
msgstr "Full Width"

#: includes/elements/container.php:381 includes/elements/section.php:253
msgid "Boxed"
msgstr "Boxed"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:117 includes/elements/container.php:1360
#: includes/elements/section.php:231 includes/managers/controls.php:333
#: includes/managers/elements.php:280 includes/widgets/common-base.php:182
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2929
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:107
#: assets/js/editor.js:36408
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:2
msgid "Layout"
msgstr "Layout"

#: includes/elements/column.php:160
msgid "Column Width"
msgstr "Column Width"

#: includes/elements/column.php:821 includes/elements/container.php:1776
#: includes/elements/section.php:1294 includes/widgets/common-base.php:692
#: modules/floating-buttons/base/widget-contact-button-base.php:3108
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
msgid "CSS Classes"
msgstr "CSS Classes"

#: core/document-types/page-base.php:132 includes/elements/column.php:766
#: includes/elements/container.php:1368 includes/elements/section.php:1233
#: includes/widgets/common-base.php:200
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:56
msgid "Margin"
msgstr "Margin"

#: includes/elements/column.php:725 includes/elements/section.php:1193
msgid "Text Align"
msgstr "Text Align"

#: core/settings/editor-preferences/model.php:93
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:391 includes/elements/container.php:1231
#: includes/elements/section.php:263 includes/elements/section.php:1028
#: includes/widgets/common-base.php:227 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:344 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2843
#: modules/nested-tabs/widgets/nested-tabs.php:312
#: modules/shapes/widgets/text-path.php:545
#: modules/shapes/widgets/text-path.php:616
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:57
msgid "Width"
msgstr "Width"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:779 includes/elements/container.php:1380
#: includes/elements/section.php:1252 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:541 includes/widgets/common-base.php:212
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:499
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:565
#: includes/widgets/traits/button-trait.php:495
#: modules/floating-buttons/base/widget-contact-button-base.php:1462
#: modules/floating-buttons/base/widget-contact-button-base.php:2187
#: modules/floating-buttons/base/widget-contact-button-base.php:2202
#: modules/floating-buttons/base/widget-contact-button-base.php:2750
#: modules/floating-buttons/base/widget-contact-button-base.php:2890
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1176
#: modules/nested-accordion/widgets/nested-accordion.php:474
#: modules/nested-accordion/widgets/nested-accordion.php:538
#: modules/nested-tabs/widgets/nested-tabs.php:701
#: modules/nested-tabs/widgets/nested-tabs.php:1051
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:55
msgid "Padding"
msgstr "Padding"

#: includes/elements/column.php:713 includes/elements/section.php:1181
msgid "Link Hover Color"
msgstr "Link Hover Colour"

#: includes/elements/column.php:701 includes/elements/section.php:1169
msgid "Link Color"
msgstr "Link Colour"

#: includes/elements/column.php:677 includes/elements/section.php:1145
msgid "Heading Color"
msgstr "Heading Colour"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:56
#: includes/elements/column.php:689 includes/elements/section.php:1157
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:352 includes/widgets/heading.php:375
#: includes/widgets/image-carousel.php:863
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:323 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:269 includes/widgets/testimonial.php:367
#: includes/widgets/testimonial.php:412 includes/widgets/text-editor.php:260
#: includes/widgets/traits/button-trait.php:347
#: includes/widgets/traits/button-trait.php:391
#: modules/floating-buttons/base/widget-contact-button-base.php:1584
#: modules/floating-buttons/base/widget-contact-button-base.php:1616
#: modules/floating-buttons/base/widget-contact-button-base.php:1747
#: modules/floating-buttons/base/widget-contact-button-base.php:1778
#: modules/floating-buttons/base/widget-contact-button-base.php:1809
#: modules/floating-buttons/base/widget-contact-button-base.php:2117
#: modules/floating-buttons/base/widget-contact-button-base.php:2324
#: modules/floating-buttons/base/widget-contact-button-base.php:2352
#: modules/floating-buttons/base/widget-contact-button-base.php:2645
#: modules/floating-buttons/base/widget-contact-button-base.php:2714
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1105
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1301
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1329
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1357
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:39
msgid "Text Color"
msgstr "Text Colour"

#: core/kits/documents/tabs/theme-style-typography.php:19
#: core/kits/documents/tabs/theme-style-typography.php:38
#: includes/controls/groups/typography.php:436 includes/elements/column.php:668
#: includes/elements/section.php:1137 assets/js/editor-modules.js:1430
#: assets/js/editor.js:42086
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:6
msgid "Typography"
msgstr "Typography"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:60
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:18
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:103
msgid "Column"
msgstr "Column"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:10274
msgid "Section"
msgstr "Section"

#: includes/editor-templates/hotkeys.php:125
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:86
#: includes/editor-templates/panel.php:90 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:31088
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Structure"
msgstr "Structure"

#: includes/elements/column.php:918 includes/elements/container.php:1867
#: includes/elements/section.php:1372 includes/managers/controls.php:332
#: includes/widgets/common-base.php:1220
#: modules/floating-buttons/base/widget-contact-button-base.php:3062
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "Responsive"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:757
#: includes/elements/section.php:1225 includes/managers/controls.php:331
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2922
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:92
#: modules/floating-buttons/module.php:97 assets/js/editor.js:9545
#: assets/js/editor.js:36405
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3184
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1441
msgid "Advanced"
msgstr "Advanced"

#: includes/managers/controls.php:330 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:297 assets/js/ai-admin.js:10373
#: assets/js/ai-gutenberg.js:12221 assets/js/ai-media-library.js:12002
#: assets/js/ai-unify-product-images.js:12002 assets/js/ai.js:13449
#: assets/js/atomic-widgets-editor.js:1189 assets/js/editor.js:9542
#: assets/js/editor.js:36402
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:34
msgid "Style"
msgstr "Style"

#: includes/managers/controls.php:329 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:489 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:596 includes/widgets/image-box.php:517
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:118 includes/widgets/testimonial.php:261
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:513
#: modules/atomic-widgets/widgets/atomic-heading.php:74
#: modules/atomic-widgets/widgets/atomic-image.php:53
#: modules/nested-accordion/widgets/nested-accordion.php:492
#: modules/nested-tabs/widgets/nested-tabs.php:990 assets/js/app.js:10350
#: assets/js/app.js:10842 assets/js/editor.js:36399
msgid "Content"
msgstr "Content"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:150
msgid "Columns"
msgstr "Columns"

#: modules/floating-buttons/base/widget-contact-button-base.php:1210
#: modules/floating-buttons/base/widget-contact-button-base.php:1262
#: modules/floating-buttons/base/widget-contact-button-base.php:1349
#: modules/floating-buttons/base/widget-contact-button-base.php:1558
#: modules/floating-buttons/base/widget-contact-button-base.php:1724
#: modules/floating-buttons/base/widget-contact-button-base.php:2614
#: modules/floating-buttons/base/widget-contact-button-base.php:2683
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:444
msgid "Colors"
msgstr "Colours"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:175
#: includes/editor-templates/templates.php:216
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:10184 assets/js/editor.js:37568
msgid "Apply"
msgstr "Apply"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:44274
msgid "Discard"
msgstr "Discard"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "Reset"

#: core/editor/loader/v1/templates/editor-body-v1.view.php:31
#: core/editor/loader/v2/templates/editor-body-v2.view.php:33
#: includes/editor-templates/templates.php:181 assets/js/ai-admin.js:7433
#: assets/js/ai-gutenberg.js:9281 assets/js/ai-layout.js:3044
#: assets/js/ai-media-library.js:9062 assets/js/ai-unify-product-images.js:9062
#: assets/js/ai.js:10509 assets/js/editor.js:27556
msgid "Preview"
msgstr "Preview"

#: includes/editor-templates/hotkeys.php:81
#: includes/editor-templates/templates.php:20
#: includes/editor-templates/templates.php:21
#: includes/editor-templates/templates.php:244
#: includes/editor-templates/templates.php:255 assets/js/e-home-screen.js:246
#: assets/js/editor.js:44273 assets/js/element-manager-admin.js:2423
#: assets/js/kit-elements-defaults-editor.js:598
msgid "Save"
msgstr "Save"

#: core/admin/admin.php:622 core/admin/menu/main.php:41
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "Help"

#: core/settings/editor-preferences/model.php:127
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4345
msgid "Tablet"
msgstr "Tablet"

#: core/base/traits/shared-widget-controls-trait.php:269
#: core/settings/editor-preferences/model.php:128
#: includes/base/element-base.php:1375 includes/editor-templates/panel.php:283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4338
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "Desktop"

#: includes/editor-templates/hotkeys.php:172
#: includes/editor-templates/panel.php:99
msgid "Responsive Mode"
msgstr "Responsive Mode"

#: includes/editor-templates/panel.php:74
#: includes/editor-templates/panel.php:75
msgid "Widgets Panel"
msgstr "Widgets Panel"

#: includes/editor-templates/panel.php:69
#: includes/editor-templates/panel.php:70
msgid "Menu"
msgstr "Menu"

#: includes/editor-templates/panel-elements.php:79
msgid "Search Widget..."
msgstr "Search Widget..."

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "Add New Section"

#: core/admin/admin.php:243
#: core/editor/loader/v1/templates/editor-body-v1.view.php:23
#: core/editor/loader/v2/templates/editor-body-v2.view.php:23
#: includes/editor-templates/templates.php:52 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1638 assets/js/ai-gutenberg.js:3406
#: assets/js/ai-media-library.js:3267 assets/js/ai-unify-product-images.js:3267
#: assets/js/ai.js:4046 assets/js/app-packages.js:5283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:236
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:46
msgid "Loading"
msgstr "Loading"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:293
#: core/admin/admin.php:410 core/admin/admin.php:488
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:386 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:43 includes/editor-templates/navigator.php:101
#: includes/editor-templates/panel-elements.php:99
#: includes/editor-templates/panel.php:208
#: includes/editor-templates/templates.php:142 includes/plugin.php:880
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:487 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:1821 assets/js/app.js:2010
msgid "Elementor"
msgstr "Elementor"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1008
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:161
msgid "Add Item"
msgstr "Add Item"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:66
#: includes/editor-templates/templates.php:194 assets/js/editor.js:10409
#: assets/js/editor.js:27617 assets/js/editor.js:30069
#: assets/js/editor.js:46992 assets/js/import-export-admin.js:272
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:21
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:29
msgid "Delete"
msgstr "Delete"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "Select Icon"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "Style"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "Transform"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "Weight"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "Family"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "Size"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:303
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:129
msgid "Dashed"
msgstr "Dashed"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:130
msgid "Dotted"
msgstr "Dotted"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1428 includes/elements/container.php:1472
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:144 includes/widgets/common-base.php:234
#: includes/widgets/common-base.php:306 includes/widgets/common-base.php:350
#: includes/widgets/common-base.php:1046 includes/widgets/common-base.php:1103
#: includes/widgets/image-carousel.php:764
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1215
#: modules/floating-buttons/base/widget-contact-button-base.php:1267
#: modules/floating-buttons/base/widget-contact-button-base.php:1354
#: modules/floating-buttons/base/widget-contact-button-base.php:1563
#: modules/floating-buttons/base/widget-contact-button-base.php:1729
#: modules/floating-buttons/base/widget-contact-button-base.php:2293
#: modules/floating-buttons/base/widget-contact-button-base.php:2619
#: modules/floating-buttons/base/widget-contact-button-base.php:2688
#: modules/floating-buttons/base/widget-contact-button-base.php:2819
#: modules/shapes/module.php:52 assets/js/editor.js:44824
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:92
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:99
msgid "Custom"
msgstr "Custom"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:120
#: includes/base/element-base.php:872
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:280
#: includes/elements/column.php:395 includes/elements/column.php:444
#: includes/elements/column.php:551 includes/elements/column.php:887
#: includes/elements/container.php:642 includes/elements/container.php:771
#: includes/elements/container.php:845 includes/elements/container.php:990
#: includes/elements/container.php:1829 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1340
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:744
#: includes/widgets/common-base.php:791 includes/widgets/common-base.php:866
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:313
#: includes/widgets/heading.php:345 includes/widgets/icon-box.php:392
#: includes/widgets/icon-list.php:426 includes/widgets/icon-list.php:655
#: includes/widgets/icon.php:213 includes/widgets/image-box.php:414
#: includes/widgets/image.php:434 includes/widgets/traits/button-trait.php:339
#: modules/floating-buttons/base/widget-contact-button-base.php:1202
#: modules/floating-buttons/base/widget-contact-button-base.php:1408
#: modules/floating-buttons/base/widget-contact-button-base.php:1980
#: modules/floating-buttons/base/widget-contact-button-base.php:2475
#: modules/floating-buttons/base/widget-contact-button-base.php:2606
#: modules/floating-buttons/base/widget-contact-button-base.php:2791
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:493
#: modules/nested-tabs/widgets/nested-tabs.php:737
#: modules/nested-tabs/widgets/nested-tabs.php:937
#: modules/shapes/widgets/text-path.php:414
#: modules/shapes/widgets/text-path.php:506
msgid "Normal"
msgstr "Normal"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:131
msgid "Double"
msgstr "Double"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:300 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:128
msgid "Solid"
msgstr "Solid"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "Repeat"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "Fixed"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "Scroll"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "Attachment"

#: includes/fonts.php:76
msgid "Google"
msgstr "Google"

#: includes/fonts.php:71
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:1
msgid "System"
msgstr "System"

#: core/admin/admin.php:224 core/admin/admin.php:232 core/base/document.php:651
#: modules/admin-bar/module.php:123 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2677
msgid "Edit with Elementor"
msgstr "Edit with Elementor"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; Back to WordPress Editor"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/elements/container.php:1167
#: includes/elements/section.php:964 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:218
#: includes/widgets/image-carousel.php:341
#: includes/widgets/image-carousel.php:393
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:592
#: modules/nested-tabs/widgets/nested-tabs.php:406 assets/js/ai-admin.js:11095
#: assets/js/ai-admin.js:11101 assets/js/ai-admin.js:11113
#: assets/js/ai-admin.js:11124 assets/js/ai-admin.js:11135
#: assets/js/ai-admin.js:11151 assets/js/ai-gutenberg.js:12943
#: assets/js/ai-gutenberg.js:12949 assets/js/ai-gutenberg.js:12961
#: assets/js/ai-gutenberg.js:12972 assets/js/ai-gutenberg.js:12983
#: assets/js/ai-gutenberg.js:12999 assets/js/ai-media-library.js:12724
#: assets/js/ai-media-library.js:12730 assets/js/ai-media-library.js:12742
#: assets/js/ai-media-library.js:12753 assets/js/ai-media-library.js:12764
#: assets/js/ai-media-library.js:12780
#: assets/js/ai-unify-product-images.js:12724
#: assets/js/ai-unify-product-images.js:12730
#: assets/js/ai-unify-product-images.js:12742
#: assets/js/ai-unify-product-images.js:12753
#: assets/js/ai-unify-product-images.js:12764
#: assets/js/ai-unify-product-images.js:12780 assets/js/ai.js:14171
#: assets/js/ai.js:14177 assets/js/ai.js:14189 assets/js/ai.js:14200
#: assets/js/ai.js:14211 assets/js/ai.js:14227
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:127
msgid "None"
msgstr "None"

#: core/kits/documents/tabs/theme-style-typography.php:110
#: includes/elements/container.php:595 includes/widgets/audio.php:111
#: includes/widgets/heading.php:178 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:337
#: includes/widgets/image-carousel.php:351
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:183
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:150
#: includes/widgets/video.php:175 includes/widgets/video.php:199
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:934
#: modules/floating-buttons/base/widget-contact-button-base.php:1062
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:263
#: modules/link-in-bio/base/widget-link-in-bio-base.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:640
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1096
#: modules/shapes/widgets/text-path.php:163
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:11
msgid "Link"
msgstr "Link"

#: includes/base/element-base.php:1311 includes/controls/dimensions.php:85
#: includes/elements/column.php:729 includes/elements/container.php:1538
#: includes/elements/section.php:1197 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:256 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:297 includes/widgets/icon-list.php:264
#: includes/widgets/icon-list.php:546 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:538
#: includes/widgets/image-carousel.php:837
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:226 includes/widgets/text-editor.php:235
#: includes/widgets/traits/button-trait.php:255
#: modules/floating-buttons/base/widget-contact-button-base.php:1137
#: modules/floating-buttons/base/widget-contact-button-base.php:2403
#: modules/floating-buttons/base/widget-contact-button-base.php:2941
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:182
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:40
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:76
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:125
msgid "Left"
msgstr "Left"

#: includes/widgets/video.php:387
msgid "Player Controls"
msgstr "Player Controls"

#: includes/base/element-base.php:1347 includes/controls/dimensions.php:84
#: includes/elements/column.php:189 includes/elements/container.php:1176
#: includes/elements/container.php:1658 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:973
#: includes/widgets/common-base.php:575 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3003
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:9
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:75
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:124
msgid "Bottom"
msgstr "Bottom"

#: includes/base/element-base.php:1319 includes/controls/dimensions.php:83
#: includes/elements/column.php:737 includes/elements/container.php:1539
#: includes/elements/section.php:1205 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:264 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:305 includes/widgets/icon-list.php:272
#: includes/widgets/icon-list.php:554 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:539
#: includes/widgets/image-carousel.php:845
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:234 includes/widgets/text-editor.php:243
#: includes/widgets/traits/button-trait.php:263
#: modules/floating-buttons/base/widget-contact-button-base.php:1141
#: modules/floating-buttons/base/widget-contact-button-base.php:2407
#: modules/floating-buttons/base/widget-contact-button-base.php:2949
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:190
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:42
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:74
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:123
msgid "Right"
msgstr "Right"

#: includes/base/element-base.php:1339 includes/controls/dimensions.php:82
#: includes/elements/column.php:187 includes/elements/container.php:1175
#: includes/elements/container.php:1654 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:972
#: includes/widgets/common-base.php:571 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:269
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:205 includes/widgets/video.php:980
#: modules/floating-buttons/base/widget-contact-button-base.php:2995
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-controls/editor-controls.strings.js:7
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:73
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:122
msgid "Top"
msgstr "Top"

#: core/experiments/manager.php:411
#: core/kits/documents/tabs/settings-background.php:78
#: core/settings/editor-preferences/model.php:125
#: includes/base/widget-base.php:296 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:254 includes/elements/column.php:186
#: includes/elements/column.php:214 includes/elements/column.php:252
#: includes/elements/container.php:545 includes/elements/container.php:568
#: includes/elements/container.php:1526 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:386
#: includes/widgets/common-base.php:231 includes/widgets/common-base.php:448
#: includes/widgets/divider.php:835 includes/widgets/heading.php:195
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-carousel.php:167
#: includes/widgets/image-carousel.php:185
#: includes/widgets/image-carousel.php:376
#: includes/widgets/image-carousel.php:763
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:157
#: includes/widgets/text-editor.php:154 includes/widgets/text-editor.php:330
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:109
#: modules/floating-buttons/base/widget-contact-button-base.php:1214
#: modules/floating-buttons/base/widget-contact-button-base.php:1266
#: modules/floating-buttons/base/widget-contact-button-base.php:1353
#: modules/floating-buttons/base/widget-contact-button-base.php:1562
#: modules/floating-buttons/base/widget-contact-button-base.php:1728
#: modules/floating-buttons/base/widget-contact-button-base.php:2292
#: modules/floating-buttons/base/widget-contact-button-base.php:2618
#: modules/floating-buttons/base/widget-contact-button-base.php:2687
#: modules/floating-buttons/base/widget-contact-button-base.php:2818
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:299
#: modules/shapes/widgets/text-path.php:208 assets/js/editor.js:44816
#: assets/js/editor.js:44827
msgid "Default"
msgstr "Default"

#: modules/system-info/module.php:202
msgid "You do not have permission to download this file."
msgstr "You do not have permission to download this file."

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "Themes"

#: core/experiments/manager.php:413 core/experiments/manager.php:705
#: modules/element-cache/module.php:110 assets/js/editor.js:27349
#: assets/js/element-manager-admin.js:2235
msgid "Inactive"
msgstr "Inactive"

#: core/experiments/manager.php:412 core/experiments/manager.php:704
#: modules/element-cache/module.php:111
#: modules/floating-buttons/base/widget-contact-button-base.php:1313
#: modules/nested-accordion/widgets/nested-accordion.php:667
#: modules/nested-accordion/widgets/nested-accordion.php:730
#: modules/nested-tabs/widgets/nested-tabs.php:629
#: modules/nested-tabs/widgets/nested-tabs.php:809
#: modules/nested-tabs/widgets/nested-tabs.php:971 assets/js/editor.js:27351
#: assets/js/element-manager-admin.js:2232
msgid "Active"
msgstr "Active"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "Laptop"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "Posts"

#: modules/nested-tabs/widgets/nested-tabs.php:230
#: modules/nested-tabs/widgets/nested-tabs.php:272
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:43
msgid "Justify"
msgstr "Justify"

#: includes/controls/icons.php:80 includes/controls/media.php:238
msgid "Add"
msgstr "Add"

#: includes/editor-templates/hotkeys.php:135
msgid "Page Settings"
msgstr "Page Settings"

#: core/breakpoints/manager.php:314
msgid "Mobile Portrait"
msgstr "Mobile Portrait"

#: core/breakpoints/manager.php:319
msgid "Mobile Landscape"
msgstr "Mobile Landscape"