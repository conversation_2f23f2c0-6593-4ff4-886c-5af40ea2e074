<?php
/**
 * Auction System Integration for GoCardless Deposit Flow
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GCDF_Auction_Integration {

    /**
     * Constructor
     */
    public function __construct() {
        // Hook early into the request to intercept auction actions
        add_action('init', array($this, 'intercept_auction_actions'), 5);
        
        // Hook into deposit completion
        add_action('gcdf_deposit_completed', array($this, 'complete_auction_purchase'), 10, 1);
        
        // Add custom post meta to track deposit requirement
        add_action('save_post', array($this, 'save_deposit_requirement'), 10, 2);
        
        // Add admin meta box for deposit settings
        add_action('add_meta_boxes', array($this, 'add_deposit_meta_box'));
    }

    /**
     * Intercept auction actions before they're processed
     */
    public function intercept_auction_actions() {
        // Only intercept if we have a buy now action
        if (!isset($_POST['auction_action']) || $_POST['auction_action'] !== 'buynow') {
            return;
        }

        // Check if plugin is configured
        if (!gcdf()->is_configured()) {
            return;
        }

        // Get current post
        global $post;
        if (!$post) {
            return;
        }

        // Check if this post requires deposit
        if (!$this->requires_deposit($post->ID)) {
            return;
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            return;
        }

        $user_id = get_current_user_id();

        // Check if user already has completed deposit
        if (gcdf()->get_database()->has_completed_deposit($post->ID, $user_id)) {
            // User has paid deposit, allow normal buy now process
            gcdf()->log("User {$user_id} has completed deposit for post {$post->ID}, allowing normal buy now");
            return;
        }

        // User hasn't paid deposit, block the buy now action
        gcdf()->log("Blocking buy now for post {$post->ID}, user {$user_id} - deposit required");
        
        // Remove the auction action to prevent processing
        unset($_POST['auction_action']);
        
        // Set error message
        $GLOBALS['error_message'] = __('Please pay the required deposit first by clicking the Buy Now button.', 'gocardless-deposit-flow');
        
        // Redirect back to the post
        wp_redirect(get_permalink($post->ID));
        exit;
    }

    /**
     * Complete auction purchase after deposit is confirmed
     */
    public function complete_auction_purchase($deposit) {
        gcdf()->log("Starting auction completion for deposit {$deposit->id}");

        // Get post and user
        $post = get_post($deposit->post_id);
        $user = get_userdata($deposit->user_id);

        if (!$post || !$user) {
            gcdf()->log("Failed to complete auction: missing post or user data", 'error');
            return;
        }

        // Check if auction is still available
        if (get_post_meta($post->ID, 'listing_expiry_date', true) == "") {
            gcdf()->log("Auction {$post->ID} is no longer available", 'error');
            return;
        }

        // Set up environment for auction processing
        global $userdata;
        $original_userdata = $userdata;
        $userdata = $user;

        // Set up POST data for auction system
        $_POST['auction_action'] = 'buynow';

        try {
            // Get the auction class
            if (!class_exists('AT_AUCTION')) {
                throw new Exception('AT_AUCTION class not found');
            }

            // Create auction instance
            $auction = new AT_AUCTION();

            // Check if the auction_actions method exists
            if (!method_exists($auction, 'auction_actions')) {
                throw new Exception('auction_actions method not found');
            }

            // Call the auction actions method
            $auction->auction_actions();

            gcdf()->log("Successfully completed auction purchase for deposit {$deposit->id}");

            // Send completion notification
            $this->send_completion_notification($deposit, $post, $user);

        } catch (Exception $e) {
            gcdf()->log("Failed to complete auction: " . $e->getMessage(), 'error');
            
            // Update deposit status to indicate completion failure
            gcdf()->get_database()->update_deposit($deposit->id, [
                'error_message' => 'Auction completion failed: ' . $e->getMessage()
            ]);
        } finally {
            // Clean up
            unset($_POST['auction_action']);
            $userdata = $original_userdata;
        }
    }

    /**
     * Check if a post requires deposit payment
     */
    public function requires_deposit($post_id) {
        // Check if deposit is globally enabled for all auctions
        $settings = gcdf()->get_settings();
        $global_deposit = !empty($settings['require_deposit_all']);

        // Check if deposit is specifically enabled for this post
        $post_deposit = get_post_meta($post_id, 'gcdf_require_deposit', true);

        // Check if this is an auction post with buy now price
        $bin_price = get_post_meta($post_id, 'price_bin', true);
        $has_buy_now = !empty($bin_price) && is_numeric($bin_price) && $bin_price > 0;

        return $has_buy_now && ($global_deposit || $post_deposit === '1');
    }

    /**
     * Add meta box for deposit settings
     */
    public function add_deposit_meta_box() {
        $post_types = array('post'); // Add other post types as needed
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'gcdf_deposit_settings',
                __('Deposit Settings', 'gocardless-deposit-flow'),
                array($this, 'render_deposit_meta_box'),
                $post_type,
                'side',
                'default'
            );
        }
    }

    /**
     * Render deposit meta box
     */
    public function render_deposit_meta_box($post) {
        // Add nonce for security
        wp_nonce_field('gcdf_deposit_meta', 'gcdf_deposit_nonce');

        $require_deposit = get_post_meta($post->ID, 'gcdf_require_deposit', true);
        $custom_amount = get_post_meta($post->ID, 'gcdf_custom_deposit_amount', true);
        $default_amount = gcdf()->get_deposit_amount();

        ?>
        <table class="form-table">
            <tr>
                <td>
                    <label>
                        <input type="checkbox" name="gcdf_require_deposit" value="1" <?php checked($require_deposit, '1'); ?> />
                        <?php _e('Require deposit for this item', 'gocardless-deposit-flow'); ?>
                    </label>
                </td>
            </tr>
            <tr>
                <td>
                    <label for="gcdf_custom_deposit_amount"><?php _e('Custom deposit amount:', 'gocardless-deposit-flow'); ?></label>
                    <input type="number" 
                           id="gcdf_custom_deposit_amount" 
                           name="gcdf_custom_deposit_amount" 
                           value="<?php echo esc_attr($custom_amount); ?>" 
                           step="0.01" 
                           min="0" 
                           placeholder="<?php echo esc_attr($default_amount); ?>" 
                           style="width: 100%;" />
                    <p class="description">
                        <?php printf(__('Leave empty to use default amount (%s %s)', 'gocardless-deposit-flow'), gcdf()->get_currency(), number_format($default_amount, 2)); ?>
                    </p>
                </td>
            </tr>
        </table>
        <?php
    }

    /**
     * Save deposit requirement meta
     */
    public function save_deposit_requirement($post_id, $post) {
        // Check if nonce is valid
        if (!isset($_POST['gcdf_deposit_nonce']) || !wp_verify_nonce($_POST['gcdf_deposit_nonce'], 'gcdf_deposit_meta')) {
            return;
        }

        // Check if user has permission to edit post
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save deposit requirement
        $require_deposit = isset($_POST['gcdf_require_deposit']) ? '1' : '0';
        update_post_meta($post_id, 'gcdf_require_deposit', $require_deposit);

        // Save custom deposit amount
        $custom_amount = sanitize_text_field($_POST['gcdf_custom_deposit_amount'] ?? '');
        if (!empty($custom_amount) && is_numeric($custom_amount)) {
            update_post_meta($post_id, 'gcdf_custom_deposit_amount', floatval($custom_amount));
        } else {
            delete_post_meta($post_id, 'gcdf_custom_deposit_amount');
        }
    }

    /**
     * Get deposit amount for specific post
     */
    public function get_post_deposit_amount($post_id) {
        $custom_amount = get_post_meta($post_id, 'gcdf_custom_deposit_amount', true);
        
        if (!empty($custom_amount) && is_numeric($custom_amount)) {
            return floatval($custom_amount);
        }
        
        return gcdf()->get_deposit_amount();
    }

    /**
     * Send completion notification
     */
    private function send_completion_notification($deposit, $post, $user) {
        $subject = sprintf(__('Purchase Completed - %s', 'gocardless-deposit-flow'), $post->post_title);
        
        $message = sprintf(
            __('Congratulations! Your purchase of "%s" has been completed successfully.

Deposit Amount: %s %s
Item: %s
Purchase Date: %s

You will receive further instructions about collection/delivery shortly.

Thank you for your purchase!', 'gocardless-deposit-flow'),
            $post->post_title,
            $deposit->currency,
            number_format($deposit->amount, 2),
            $post->post_title,
            current_time('Y-m-d H:i:s')
        );

        wp_mail($user->user_email, $subject, $message);
        gcdf()->log("Sent completion notification to {$user->user_email}");
    }

    /**
     * Check if user can bypass deposit (admin override)
     */
    public function can_bypass_deposit($user_id, $post_id) {
        // Allow administrators to bypass deposit requirement
        if (user_can($user_id, 'manage_options')) {
            return true;
        }

        // Check for specific bypass capability
        if (user_can($user_id, 'gcdf_bypass_deposit')) {
            return true;
        }

        // Check for post-specific bypass
        $bypass_users = get_post_meta($post_id, 'gcdf_bypass_users', true);
        if (is_array($bypass_users) && in_array($user_id, $bypass_users)) {
            return true;
        }

        return false;
    }

    /**
     * Get auction status for deposit tracking
     */
    public function get_auction_status($post_id) {
        $expiry_date = get_post_meta($post_id, 'listing_expiry_date', true);
        $winner_string = get_post_meta($post_id, 'bidwinnerstring', true);
        
        if (empty($expiry_date)) {
            return 'sold';
        }
        
        if (!empty($winner_string)) {
            return 'won';
        }
        
        $expiry_timestamp = strtotime($expiry_date);
        if ($expiry_timestamp && $expiry_timestamp < time()) {
            return 'expired';
        }
        
        return 'active';
    }

    /**
     * Prevent duplicate purchases
     */
    public function prevent_duplicate_purchase($post_id, $user_id) {
        // Check if user already has a completed deposit
        if (gcdf()->get_database()->has_completed_deposit($post_id, $user_id)) {
            return true;
        }

        // Check if auction is already sold
        $status = $this->get_auction_status($post_id);
        if (in_array($status, ['sold', 'won', 'expired'])) {
            return true;
        }

        return false;
    }
}
