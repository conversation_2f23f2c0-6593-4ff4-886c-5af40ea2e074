# Translation of Themes - Twenty Twenty-One in English (UK)
# This file is distributed under the same license as the Themes - Twenty Twenty-One package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-04 23:55:20+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.4\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Twenty Twenty-One\n"

#. Description of the theme
#, gp-priority: high
msgid "Twenty Twenty-One is a blank canvas for your ideas and it makes the block editor your best brush. With new block patterns, which allow you to create a beautiful layout in a matter of seconds, this theme’s soft colors and eye-catching — yet timeless — design will let your work shine. Take it for a spin! See how Twenty Twenty-One elevates your portfolio, business website, or personal blog."
msgstr "Twenty Twenty-One is a blank canvas for your ideas, and it makes the block editor your best brush. With new block patterns, which allow you to create a beautiful layout in a matter of seconds, this theme’s soft colours and eye-catching – yet timeless – design will let your work shine. Take it for a spin! See how Twenty Twenty-One elevates your portfolio, business website, or personal blog."

#. Theme Name of the theme
#: classes/class-twenty-twenty-one-dark-mode.php:381 inc/block-patterns.php:24
#, gp-priority: high
msgid "Twenty Twenty-One"
msgstr "Twenty Twenty-One"

#: inc/template-tags.php:241
msgid "Older <span class=\"nav-short\">posts</span>"
msgstr "Older <span class=\"nav-short\">posts</span>"

#: inc/template-tags.php:230
msgid "Newer <span class=\"nav-short\">posts</span>"
msgstr "Newer <span class=\"nav-short\">posts</span>"

#: classes/class-twenty-twenty-one-customize-notice-control.php:40
#: classes/class-twenty-twenty-one-dark-mode.php:178
msgid "https://wordpress.org/documentation/article/twenty-twenty-one/#dark-mode-support"
msgstr "https://wordpress.org/documentation/article/twenty-twenty-one/#dark-mode-support"

#. translators: %s: Twenty Twenty-One support article URL.
#: classes/class-twenty-twenty-one-dark-mode.php:177
msgid "Dark Mode is a device setting. If a visitor to your site requests it, your site will be shown with a dark background and light text. <a href=\"%s\">Learn more about Dark Mode.</a>"
msgstr "Dark Mode is a device setting. If a visitor to your site requests it, your site will be shown with a dark background and light text. <a href=\"%s\">Learn more about Dark Mode</a>."

#: classes/class-twenty-twenty-one-dark-mode.php:181
msgid "Dark Mode can also be turned on and off with a button that you can find in the bottom corner of the page."
msgstr "Dark Mode can also be turned on and off with a button that you can find in the bottom corner of the page."

#: classes/class-twenty-twenty-one-dark-mode.php:380
msgid "This website uses LocalStorage to save the setting when Dark Mode support is turned on or off.<br> LocalStorage is necessary for the setting to work and is only used when a user clicks on the Dark Mode button.<br> No data is saved in the database or transferred."
msgstr "This website uses LocalStorage to save the setting when Dark Mode support is turned on or off.<br> LocalStorage is necessary for the setting to work and is only used when a user clicks on the Dark Mode button.<br> No data is saved in the database or transferred."

#: classes/class-twenty-twenty-one-dark-mode.php:379
msgid "Suggested text:"
msgstr "Suggested text:"

#: classes/class-twenty-twenty-one-dark-mode.php:378
msgid "Twenty Twenty-One uses LocalStorage when Dark Mode support is enabled."
msgstr "Twenty Twenty-One uses LocalStorage when Dark Mode support is enabled."

#: classes/class-twenty-twenty-one-dark-mode.php:188
msgid "Dark Mode support"
msgstr "Dark Mode support"

#: classes/class-twenty-twenty-one-customize-notice-control.php:41
msgid "Learn more about Dark Mode."
msgstr "Learn more about Dark Mode."

#: classes/class-twenty-twenty-one-customize-notice-control.php:39
msgid "To access the Dark Mode settings, select a light background color."
msgstr "To access the Dark Mode settings, select a light background colour."

#: classes/class-twenty-twenty-one-dark-mode.php:134
msgid "Colors & Dark Mode"
msgstr "Colours and Dark Mode"

#: inc/template-tags.php:77
msgctxt "Label for sticky posts"
msgid "Featured post"
msgstr "Featured post"

#: inc/template-functions.php:424
msgctxt "Post password form"
msgid "Submit"
msgstr "Submit"

#: inc/template-functions.php:424
msgctxt "Post password form"
msgid "Password"
msgstr "Password"

#: inc/template-functions.php:183
msgctxt "Added to posts and pages that are missing titles"
msgid "Untitled"
msgstr "Untitled"

#: inc/block-patterns.php:136
msgctxt "Block pattern sample content"
msgid "Cambridge, MA, 02139"
msgstr "Cambridge, MA, 02139"

#: inc/block-patterns.php:136
msgctxt "Block pattern sample content"
msgid "123 Main Street"
msgstr "123 Main Street"

#: inc/block-patterns.php:136
msgctxt "Block pattern sample content"
msgid "123-456-7890"
msgstr "123-456-7890"

#: inc/block-patterns.php:136
msgctxt "Block pattern sample content"
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: classes/class-twenty-twenty-one-customize.php:138
msgctxt "Customizer control"
msgid "Background color"
msgstr "Background colour"

#. translators: %s: WordPress Version.
#: inc/back-compat.php:42 inc/back-compat.php:61 inc/back-compat.php:86
msgid "This theme requires WordPress 5.3 or newer. You are running version %s. Please upgrade."
msgstr "This theme requires WordPress 5.3 or newer. You are running version %s. Please upgrade."

#. translators: %: Page number.
#: image.php:48 template-parts/content/content-page.php:36
#: template-parts/content/content-single.php:30
#: template-parts/content/content.php:36
msgid "Page %"
msgstr "Page %"

#: functions.php:187
msgctxt "Font size"
msgid "XXXL"
msgstr "XXXL"

#: functions.php:181
msgctxt "Font size"
msgid "XXL"
msgstr "XXL"

#: functions.php:175
msgctxt "Font size"
msgid "XL"
msgstr "XL"

#: functions.php:169
msgctxt "Font size"
msgid "L"
msgstr "L"

#: functions.php:163
msgctxt "Font size"
msgid "M"
msgstr "M"

#: functions.php:157
msgctxt "Font size"
msgid "S"
msgstr "S"

#: functions.php:151
msgctxt "Font size"
msgid "XS"
msgstr "XS"

#: comments.php:85
msgid "Leave a comment"
msgstr "Leave a comment"

#. translators: %s: Comment count number.
#: comments.php:39
msgctxt "Comments title"
msgid "%s comment"
msgid_plural "%s comments"
msgstr[0] "%s comment"
msgstr[1] "%s comments"

#: comments.php:34
msgid "1 comment"
msgstr "One comment"

#: classes/class-twenty-twenty-one-dark-mode.php:334
msgid "On"
msgstr "On"

#: classes/class-twenty-twenty-one-dark-mode.php:331
msgid "Off"
msgstr "Off"

#. translators: %s: On/Off
#: classes/class-twenty-twenty-one-dark-mode.php:321
msgid "Dark Mode: %s"
msgstr "Dark Mode: %s"

#: inc/template-functions.php:422
msgid "This content is password protected. Please enter a password to view."
msgstr "This content is password protected. Please enter a password to view."

#. translators: Hidden accessibility text.
#: inc/menu-functions.php:35
msgid "Open menu"
msgstr "Open menu"

#: inc/block-patterns.php:125
msgid "&#8220;Reading&#8221; by Berthe Morisot"
msgstr "&#8220;Reading&#8221; by Berthe Morisot"

#: inc/block-patterns.php:101
msgid "&#8220;Self portrait&#8221; by Berthe Morisot"
msgstr "&#8220;Self portrait&#8221; by Berthe Morisot"

#: inc/block-patterns.php:101
msgid "&#8220;Daffodils&#8221; by Berthe Morisot"
msgstr "&#8220;Daffodils&#8221; by Berthe Morisot"

#: inc/block-patterns.php:89 inc/block-patterns.php:125
#: inc/starter-content.php:43
msgid "&#8220;Roses Trémières&#8221; by Berthe Morisot"
msgstr "&#8220;Roses Trémières&#8221; by Berthe Morisot"

#: inc/template-functions.php:424
msgctxt "Post password form"
msgid "Enter"
msgstr "Enter"

#: inc/starter-content.php:128
msgctxt "Theme starter content"
msgid "Check out the Support Forums"
msgstr "Check out the Support Forums"

#: inc/starter-content.php:122
msgctxt "Theme starter content"
msgid "Read the Theme Documentation"
msgstr "Read the Theme Documentation"

#: inc/starter-content.php:112
msgctxt "Theme starter content"
msgid "Need help?"
msgstr "Need help?"

#: inc/starter-content.php:97
msgctxt "Theme starter content"
msgid "Twenty Twenty-One also includes an overlap style for column blocks. With a Columns block selected, open the \"Styles\" panel within the Editor sidebar. Choose the \"Overlap\" block style to try it out."
msgstr "Twenty Twenty-One also includes an overlap style for column blocks. With a Columns block selected, open the \"Styles\" panel within the Editor sidebar. Choose the \"Overlap\" block style to try it out."

#: inc/starter-content.php:93
msgctxt "Theme starter content"
msgid "Overlap columns"
msgstr "Overlap columns"

#: inc/starter-content.php:87
msgctxt "Theme starter content"
msgid "Twenty Twenty-One includes stylish borders for your content. With an Image block selected, open the \"Styles\" panel within the Editor sidebar. Select the \"Frame\" block style to activate it."
msgstr "Twenty Twenty-One includes stylish borders for your content. With an Image block selected, open the \"Styles\" panel within the Editor sidebar. Select the \"Frame\" block style to activate it."

#: inc/starter-content.php:83
msgctxt "Theme starter content"
msgid "Frame your images"
msgstr "Frame your images"

#: inc/starter-content.php:77
msgctxt "Theme starter content"
msgid "Block patterns are pre-designed groups of blocks. To add one, select the Add Block button [+] in the toolbar at the top of the editor. Switch to the Patterns tab underneath the search bar, and choose a pattern."
msgstr "Block patterns are pre-designed groups of blocks. To add one, select the Add Block button [+] in the toolbar at the top of the editor. Switch to the Patterns tab underneath the search bar, and choose a pattern."

#: inc/starter-content.php:73
msgctxt "Theme starter content"
msgid "Add block patterns"
msgstr "Add block patterns"

#: inc/starter-content.php:30 inc/starter-content.php:33
msgctxt "Theme starter content"
msgid "Create your website with blocks"
msgstr "Create your website with blocks"

#: inc/block-patterns.php:125
msgid "Reading"
msgstr "Reading"

#: inc/block-patterns.php:125
msgid "Young Woman in Mauve"
msgstr "Young Woman in Mauve"

#: inc/block-patterns.php:125
msgid "The Garden at Bougival"
msgstr "The Garden at Bougival"

#: inc/block-patterns.php:125
msgid "In the Bois de Boulogne"
msgstr "In the Bois de Boulogne"

#: inc/block-patterns.php:125
msgid "Villa with Orange Trees, Nice"
msgstr "Villa with Orange Trees, Nice"

#: inc/block-patterns.php:125
msgid "Roses Trémières"
msgstr "Roses Trémières"

#: inc/block-patterns.php:114
msgid "&#8220;Villa with Orange Trees, Nice&#8221; by Berthe Morisot"
msgstr "&#8220;Villa with Orange Trees, Nice&#8221; by Berthe Morisot"

#: inc/block-patterns.php:114
msgid "Beautiful gardens painted by Berthe Morisot in the late 1800s"
msgstr "Beautiful gardens painted by Berthe Morisot in the late 1800s"

#: inc/block-patterns.php:114 inc/block-patterns.php:125
msgid "&#8220;The Garden at Bougival&#8221; by Berthe Morisot"
msgstr "&#8220;The Garden at Bougival&#8221; by Berthe Morisot"

#: inc/block-patterns.php:89 inc/block-patterns.php:125
#: inc/starter-content.php:61
msgid "&#8220;Young Woman in Mauve&#8221; by Berthe Morisot"
msgstr "&#8220;Young Woman in Mauve&#8221; by Berthe Morisot"

#: inc/block-patterns.php:89 inc/block-patterns.php:125
#: inc/starter-content.php:51
msgid "&#8220;In the Bois de Boulogne&#8221; by Berthe Morisot"
msgstr "&#8220;In the Bois de Boulogne&#8221; by Berthe Morisot"

#: inc/block-patterns.php:76
msgid "Berthe Morisot<br>(French, 1841-1895)"
msgstr "Berthe Morisot<br>(French, 1841–1895)"

#: inc/block-patterns.php:76
msgid "Playing in the Sand"
msgstr "Playing in the Sand"

#: inc/block-patterns.php:76
msgid "&#8220;Playing in the Sand&#8221; by Berthe Morisot"
msgstr "&#8220;Playing in the Sand&#8221; by Berthe Morisot"

#. translators: %s: Parent post.
#: image.php:61
msgid "Published in %s"
msgstr "Published in %s"

#: classes/class-twenty-twenty-one-customize.php:108
msgid "Summary"
msgstr "Summary"

#: classes/class-twenty-twenty-one-customize.php:85
msgid "Excerpt Settings"
msgstr "Excerpt Settings"

#: inc/block-styles.php:98
msgid "Thick"
msgstr "Thick"

#: classes/class-twenty-twenty-one-customize.php:106
msgid "On Archive Pages, posts show:"
msgstr "On Archive Pages, posts show:"

#. translators: %s: Author name.
#: template-parts/post/author-bio.php:31
msgid "View all of %s's posts."
msgstr "View all of %s's posts."

#. translators: %s: Author name.
#: inc/template-tags.php:49 template-parts/post/author-bio.php:19
msgid "By %s"
msgstr "By %s"

#: template-parts/content/content-none.php:61
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."

#: template-parts/content/content-none.php:56
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Sorry, but nothing matched your search terms. Please try again with some different keywords."

#. translators: %s: Link to WP admin new post page.
#: template-parts/content/content-none.php:43
msgid "Ready to publish your first post? <a href=\"%s\">Get started here</a>."
msgstr "Ready to publish your first post? <a href=\"%s\">Get started here</a>."

#: single.php:40
msgid "Previous post"
msgstr "Previous post"

#: single.php:39
msgid "Next post"
msgstr "Next post"

#. translators: %s: Parent post link.
#: single.php:25
msgid "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%s</span>"
msgstr "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%s</span>"

#: searchform.php:26
msgctxt "submit button"
msgid "Search"
msgstr "Search"

#: searchform.php:24
msgid "Search&hellip;"
msgstr "Search&hellip;"

#. translators: %d: The number of search results.
#: search.php:33
msgid "We found %d result for your search."
msgid_plural "We found %d results for your search."
msgstr[0] "We found %d result for your search."
msgstr[1] "We found %d results for your search."

#. translators: %s: Search term.
#: search.php:21 template-parts/content/content-none.php:22
msgid "Results for \"%s\""
msgstr "Results for \"%s\""

#. translators: %s: List of tags.
#: inc/template-tags.php:116 inc/template-tags.php:158
msgid "Tagged %s"
msgstr "Tagged %s"

#. translators: %s: List of categories.
#: inc/template-tags.php:107 inc/template-tags.php:149
msgid "Categorized as %s"
msgstr "Categorised as %s"

#. translators: Used between list items, there is a space after the comma.
#: functions.php:654
msgid ", "
msgstr ", "

#. translators: %s: Post title. Only visible to screen readers.
#: image.php:70 image.php:96 inc/template-tags.php:92 inc/template-tags.php:133
#: template-parts/content/content-page.php:48
msgid "Edit %s"
msgstr "Edit %s"

#. translators: %s: Post title. Only visible to screen readers.
#: inc/template-functions.php:138
msgid "Continue reading %s"
msgstr "Continue reading %s"

#: inc/block-styles.php:71
msgid "Dividers"
msgstr "Dividers"

#: inc/block-styles.php:62
msgid "Frame"
msgstr "Frame"

#: inc/block-styles.php:35 inc/block-styles.php:44 inc/block-styles.php:53
#: inc/block-styles.php:80 inc/block-styles.php:89
msgid "Borders"
msgstr "Borders"

#: inc/block-styles.php:26
msgid "Overlap"
msgstr "Overlap"

#: inc/block-patterns.php:135
msgctxt "Block pattern description"
msgid "A block with 3 columns that display contact information and social media links."
msgstr "A block with three columns that displays contact information and social media links."

#: inc/block-patterns.php:132
msgid "Contact information"
msgstr "Contact information"

#: inc/block-patterns.php:124
msgctxt "Block pattern description"
msgid "A list of projects with thumbnail images."
msgstr "A list of projects with thumbnail images."

#: inc/block-patterns.php:122
msgid "Portfolio list"
msgstr "Portfolio list"

#: inc/block-patterns.php:113
msgctxt "Block pattern description"
msgid "An overlapping columns block with two images and a text description."
msgstr "An overlapping columns block with two images and a text description."

#: inc/block-patterns.php:109
msgid "Overlapping images and text"
msgstr "Overlapping images and text"

#: inc/block-patterns.php:100
msgctxt "Block pattern description"
msgid "A media & text block with a big image on the left and a smaller one with bordered frame on the right."
msgstr "A media and text block with a big image on the left and a smaller one with bordered frame on the right."

#: inc/block-patterns.php:97
msgid "Two images showcase"
msgstr "Two images showcase"

#: inc/block-patterns.php:88
msgctxt "Block pattern description"
msgid "Three images inside an overlapping columns block."
msgstr "Three images inside an overlapping columns block."

#: inc/block-patterns.php:84
msgid "Overlapping images"
msgstr "Overlapping images"

#: inc/block-patterns.php:75
msgctxt "Block pattern description"
msgid "A Media & Text block with a big image on the left and a heading on the right. The heading is followed by a separator and a description paragraph."
msgstr "A Media and Text block with a big image on the left and a heading on the right. The heading is followed by a separator and a description paragraph."

#: inc/block-patterns.php:72
msgid "Media and text article title"
msgstr "Media and text article title"

#: inc/block-patterns.php:64
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: inc/block-patterns.php:64
msgid "Dribbble"
msgstr "Dribbble"

#: inc/block-patterns.php:64
msgid "Instagram"
msgstr "Instagram"

#: inc/block-patterns.php:64
msgid "Twitter"
msgstr "Twitter"

#: inc/block-patterns.php:64
msgid "Let&#8217;s Connect."
msgstr "Let&#8217;s Connect."

#: inc/block-patterns.php:63
msgctxt "Block pattern description"
msgid "A huge text followed by social networks and email address links."
msgstr "A huge text followed by social networks and email address links."

#: inc/block-patterns.php:59
msgid "Links area"
msgstr "Links area"

#: inc/block-patterns.php:51
msgid "A new portfolio default theme for WordPress"
msgstr "A new portfolio default theme for WordPress"

#: inc/block-patterns.php:47
msgid "Large text"
msgstr "Large text"

#. translators: Hidden accessibility text.
#: image.php:84
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr "Full size"

#. translators: %s: Publish date.
#: inc/template-tags.php:29
msgid "Published %s"
msgstr "Published %s"

#: comments.php:61 image.php:45 inc/template-tags.php:224
#: template-parts/content/content-page.php:33
#: template-parts/content/content-single.php:27
#: template-parts/content/content.php:33
msgid "Page"
msgstr "Page"

#: template-parts/header/site-nav.php:19
msgid "Close"
msgstr "Close"

#: template-parts/header/site-nav.php:16
msgid "Menu"
msgstr "Menu"

#: functions.php:76 inc/starter-content.php:154
#: template-parts/header/site-nav.php:13
msgid "Primary menu"
msgstr "Primary menu"

#. translators: Hidden accessibility text.
#: header.php:29
msgid "Skip to content"
msgstr "Skip to content"

#: functions.php:363
msgid "Add widgets here to appear in your footer."
msgstr "Add widgets here to appear in your footer."

#: functions.php:361
msgid "Footer"
msgstr "Footer"

#: functions.php:309
msgid "Red to purple"
msgstr "Red to purple"

#: functions.php:304
msgid "Purple to red"
msgstr "Purple to red"

#: functions.php:299
msgid "Yellow to red"
msgstr "Yellow to red"

#: functions.php:294
msgid "Red to yellow"
msgstr "Red to yellow"

#: functions.php:289
msgid "Yellow to green"
msgstr "Yellow to green"

#: functions.php:284
msgid "Green to yellow"
msgstr "Green to yellow"

#: functions.php:279
msgid "Yellow to purple"
msgstr "Yellow to purple"

#: functions.php:274
msgid "Purple to yellow"
msgstr "Purple to yellow"

#: functions.php:263
msgid "White"
msgstr "White"

#: functions.php:258
msgid "Yellow"
msgstr "Yellow"

#: functions.php:253
msgid "Orange"
msgstr "Orange"

#: functions.php:248
msgid "Red"
msgstr "Red"

#: functions.php:243
msgid "Purple"
msgstr "Purple"

#: functions.php:238
msgid "Blue"
msgstr "Blue"

#: functions.php:233
msgid "Green"
msgstr "Green"

#: functions.php:228
msgid "Gray"
msgstr "Grey"

#: functions.php:223 inc/block-styles.php:107
msgid "Dark gray"
msgstr "Dark grey"

#: functions.php:218
msgid "Black"
msgstr "Black"

#: functions.php:186
msgid "Gigantic"
msgstr "Gigantic"

#: functions.php:180
msgid "Huge"
msgstr "Huge"

#: functions.php:174
msgid "Extra large"
msgstr "Extra large"

#: functions.php:168
msgid "Large"
msgstr "Large"

#: functions.php:162
msgid "Normal"
msgstr "Normal"

#: functions.php:156
msgid "Small"
msgstr "Small"

#: functions.php:150
msgid "Extra small"
msgstr "Extra Small"

#. translators: %s: WordPress.
#: footer.php:67
msgid "Proudly powered by %s."
msgstr "Proudly powered by %s."

#: footer.php:24 functions.php:77 inc/starter-content.php:165
msgid "Secondary menu"
msgstr "Secondary menu"

#: comments.php:78
msgid "Comments are closed."
msgstr "Comments are closed."

#: comments.php:70
msgid "Newer comments"
msgstr "Newer comments"

#: comments.php:66
msgid "Older comments"
msgstr "Older comments"

#: classes/class-twenty-twenty-one-customize.php:109
msgid "Full text"
msgstr "Full text"

#: classes/class-twenty-twenty-one-customize.php:75
msgid "Display Site Title & Tagline"
msgstr "Display Site Title and Tagline"

#: 404.php:21
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "It looks like nothing was found at this location. Maybe try a search?"

#: 404.php:16 template-parts/content/content-none.php:30
msgid "Nothing here"
msgstr "Nothing here"

#. Author URI of the theme
#: footer.php:68
#, gp-priority: low
msgid "https://wordpress.org/"
msgstr "https://en-gb.wordpress.org/"

#. Author of the theme
#, gp-priority: low
msgid "the WordPress team"
msgstr "the WordPress team"

#. Theme URI of the theme
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyone/"
msgstr "https://en-gb.wordpress.org/themes/twentytwentyone/"