{"mc_conf_version": "2", "ipheader": {"hdr": "HTTP_X_FORWARDED_FOR", "pos": 0}, "brandname": "Bot Protection", "confkey": "6b155b1655e1161a97f04c738ac9d71e", "fw": {"mode": 3, "reqprofilingmode": 2, "bypasslevel": 2, "cookiemode": 1, "loggingmode": 1, "admincookiemode": 1, "iswpusercookieenabled": true, "wpusercapstoconsider": {"manage_options": 28, "unfiltered_html": 56, "edit_others_posts": 41, "upload_files": 58, "edit_posts": 61, "read": 63, "manage_network": 3, "edit_users": 22, "create_users": 14, "promote_users": 29, "delete_users": 17, "manage_woocommerce": 101}, "wpfruleinitmode": 1, "ipcookiemode": 2, "customroles": [], "cookiekey": "d65d37f22171d0a5f06fc6f2b90240a1", "cookiepath": "/", "cookiedomain": "", "rulesmode": 1, "cansetcachepreventioncookie": false, "isgeoblocking": false, "logconfig": {"reqprofilingmode": 2, "loggingmode": 1, "except": {"cookies": ["wordpress_sec_df73a7ff17974d433b0540aa7583896c", "wordpress_logged_in_df73a7ff17974d433b0540aa7583896c", "wordpress_df73a7ff17974d433b0540aa7583896c", "wp-postpass_df73a7ff17974d433b0540aa7583896c", "wp-resetpass-df73a7ff17974d433b0540aa7583896c"], "headers": ["<PERSON><PERSON>"], "post": ["password", "passwd", "pwd"]}}}, "lp": {"ptplug": "malcare", "mode": 3, "captchalimit": 10, "tempblocklimit": 15, "blockalllimit": 100, "failedlogingap": 1800, "successlogingap": 3600, "allblockedgap": 1800}, "time": 1707052654}