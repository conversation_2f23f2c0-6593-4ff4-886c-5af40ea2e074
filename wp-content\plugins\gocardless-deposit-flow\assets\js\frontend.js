/**
 * Frontend JavaScript for GoCardless Deposit Flow
 */

(function($) {
    'use strict';

    var GCDF = {
        modal: null,
        overlay: null,
        form: null,
        termsCheckbox: null,
        continueBtn: null,
        
        init: function() {
            this.bindElements();
            this.bindEvents();
            this.showNotifications();
        },

        bindElements: function() {
            this.modal = $('#gcdf-deposit-modal');
            this.overlay = $('#gcdf-modal-overlay');
            this.form = $('#gcdf-deposit-form');
            this.termsCheckbox = $('#gcdf-terms-checkbox');
            this.continueBtn = $('#gcdf-continue-btn');
        },

        bindEvents: function() {
            var self = this;

            // Modal close events
            $('.gcdf-modal-close, #gcdf-cancel-btn').on('click', function(e) {
                e.preventDefault();
                self.hideModal();
            });

            // Close modal when clicking overlay
            this.overlay.on('click', function() {
                self.hideModal();
            });

            // Prevent modal close when clicking inside modal content
            $('.gcdf-modal-content').on('click', function(e) {
                e.stopPropagation();
            });

            // Terms checkbox change
            this.termsCheckbox.on('change', function() {
                self.toggleContinueButton();
            });

            // Form submission
            this.form.on('submit', function(e) {
                e.preventDefault();
                self.handleFormSubmission();
            });

            // Retry button
            $(document).on('click', '#gcdf-retry-btn', function(e) {
                e.preventDefault();
                self.resetModal();
            });

            // ESC key to close modal
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && self.modal.is(':visible')) {
                    self.hideModal();
                }
            });
        },

        showModal: function() {
            console.log('GCDF: Showing modal');
            this.overlay.show();
            this.modal.show();
            $('body').addClass('gcdf-modal-open');

            // Focus management for accessibility
            this.modal.find('.gcdf-modal-close').focus();

            // Trap focus within modal
            this.trapFocus();
        },

        hideModal: function() {
            this.modal.hide();
            this.overlay.hide();
            $('body').removeClass('gcdf-modal-open');
            this.resetModal();
        },

        resetModal: function() {
            // Reset form
            this.form.show();
            $('#gcdf-loading').hide();
            $('#gcdf-error').hide();
            
            // Reset form fields
            this.termsCheckbox.prop('checked', false);
            this.toggleContinueButton();
            
            // Clear error messages
            $('.gcdf-error-message').text('');
        },

        toggleContinueButton: function() {
            var isChecked = this.termsCheckbox.is(':checked');
            this.continueBtn.prop('disabled', !isChecked);
        },

        handleFormSubmission: function() {
            var self = this;

            // Validate terms acceptance
            if (!this.termsCheckbox.is(':checked')) {
                this.showError(gcdf_frontend.strings.terms_required);
                return;
            }

            // Show confirmation
            if (!confirm(gcdf_frontend.strings.confirm_deposit)) {
                return;
            }

            // Show loading state
            this.showLoading();

            // Prepare data
            var formData = {
                action: 'gcdf_create_deposit',
                nonce: gcdf_frontend.nonce,
                post_id: gcdf_frontend.post_id,
                terms_accepted: this.termsCheckbox.is(':checked') ? 1 : 0
            };

            // Make AJAX request
            $.ajax({
                url: gcdf_frontend.ajax_url,
                type: 'POST',
                data: formData,
                dataType: 'json',
                timeout: 30000,
                success: function(response) {
                    if (response.success) {
                        self.handleSuccess(response.data);
                    } else {
                        self.handleError(response.data.message || gcdf_frontend.strings.error);
                    }
                },
                error: function(xhr, status, error) {
                    var message = gcdf_frontend.strings.error;
                    
                    if (status === 'timeout') {
                        message = 'Request timed out. Please try again.';
                    } else if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                        message = xhr.responseJSON.data.message;
                    }
                    
                    self.handleError(message);
                }
            });
        },

        showLoading: function() {
            this.form.hide();
            $('#gcdf-error').hide();
            $('#gcdf-loading').show();
        },

        handleSuccess: function(data) {
            // Redirect to GoCardless payment page
            if (data.redirect_url) {
                window.location.href = data.redirect_url;
            } else {
                this.handleError('No redirect URL provided');
            }
        },

        handleError: function(message) {
            $('#gcdf-loading').hide();
            $('#gcdf-error').show();
            $('.gcdf-error-message').text(message);
        },

        showError: function(message) {
            alert(message);
        },

        trapFocus: function() {
            var self = this;
            var focusableElements = this.modal.find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            var firstElement = focusableElements.first();
            var lastElement = focusableElements.last();

            this.modal.on('keydown', function(e) {
                if (e.keyCode === 9) { // Tab key
                    if (e.shiftKey) {
                        if (document.activeElement === firstElement[0]) {
                            e.preventDefault();
                            lastElement.focus();
                        }
                    } else {
                        if (document.activeElement === lastElement[0]) {
                            e.preventDefault();
                            firstElement.focus();
                        }
                    }
                }
            });
        },

        showNotifications: function() {
            var urlParams = new URLSearchParams(window.location.search);
            
            // Check for success message
            if (urlParams.get('deposit_success')) {
                this.showSuccessNotification('Your deposit has been confirmed! The vehicle is now secured for you.');
                this.cleanUrl();
            }
            
            // Check for error message
            var errorMessage = urlParams.get('deposit_error');
            if (errorMessage) {
                this.showErrorNotification('Deposit failed: ' + decodeURIComponent(errorMessage));
                this.cleanUrl();
            }
            
            // Check for cancelled message
            if (urlParams.get('deposit_cancelled')) {
                this.showInfoNotification('Deposit payment was cancelled. The vehicle is still available.');
                this.cleanUrl();
            }
        },

        showSuccessNotification: function(message) {
            this.showNotification(message, 'success');
        },

        showErrorNotification: function(message) {
            this.showNotification(message, 'error');
        },

        showInfoNotification: function(message) {
            this.showNotification(message, 'info');
        },

        showNotification: function(message, type) {
            var className = 'gcdf-notification gcdf-notification-' + type;
            var notification = $('<div class="' + className + '">' + message + '<button class="gcdf-notification-close">&times;</button></div>');
            
            $('body').prepend(notification);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                notification.fadeOut(function() {
                    notification.remove();
                });
            }, 5000);
            
            // Manual close
            notification.find('.gcdf-notification-close').on('click', function() {
                notification.fadeOut(function() {
                    notification.remove();
                });
            });
        },

        cleanUrl: function() {
            // Remove query parameters from URL
            var url = window.location.protocol + "//" + window.location.host + window.location.pathname;
            window.history.replaceState({path: url}, '', url);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        GCDF.init();
    });

    // Make GCDF available globally for debugging
    window.GCDF = GCDF;

    // Global function to show deposit modal
    window.showDepositModal = function() {
        console.log('GCDF: Global showDepositModal called');
        if (GCDF.modal && GCDF.modal.length) {
            GCDF.showModal();
        } else {
            alert('Deposit payment required. Please refresh the page and try again.');
        }
    };

})(jQuery);

// Add notification styles dynamically
jQuery(document).ready(function($) {
    var notificationStyles = `
        <style>
        .gcdf-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .gcdf-notification-success {
            background-color: #48bb78;
        }
        .gcdf-notification-error {
            background-color: #f56565;
        }
        .gcdf-notification-info {
            background-color: #4299e1;
        }
        .gcdf-notification-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-left: 12px;
            padding: 0;
            line-height: 1;
        }
        .gcdf-notification-close:hover {
            opacity: 0.8;
        }
        body.gcdf-modal-open {
            overflow: hidden;
        }
        @media (max-width: 480px) {
            .gcdf-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }
        </style>
    `;
    
    $('head').append(notificationStyles);
});
