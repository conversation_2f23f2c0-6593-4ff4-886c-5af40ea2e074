# Translation of Themes - Twenty Sixteen in English (UK)
# This file is distributed under the same license as the Themes - Twenty Sixteen package.
msgid ""
msgstr ""
"PO-Revision-Date: 2018-12-13 09:14:21+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Twenty Sixteen\n"

#. Theme Name of the theme
msgid "Twenty Sixteen"
msgstr "Twenty Sixteen"

#. Description of the theme
msgid "Twenty Sixteen is a modernized take on an ever-popular WordPress layout — the horizontal masthead with an optional right sidebar that works perfectly for blogs and websites. It has custom color options with beautiful default color schemes, a harmonious fluid grid using a mobile-first approach, and impeccable polish in every detail. Twenty Sixteen will make your WordPress look beautiful everywhere."
msgstr "Twenty Sixteen is a modernised take on an ever-popular WordPress layout — the horizontal masthead with an optional right sidebar that works perfectly for blogs and websites. It has custom colour options with beautiful default colour schemes, a harmonious fluid grid using a mobile-first approach, and impeccable polish in every detail. Twenty Sixteen will make your WordPress look beautiful everywhere."

#: functions.php:202
msgid "Bright Red"
msgstr "Bright Red"

#: functions.php:197
msgid "Dark Red"
msgstr "Dark Red"

#: functions.php:192
msgid "Medium Brown"
msgstr "Medium Brown"

#: functions.php:187
msgid "Dark Brown"
msgstr "Dark Brown"

#: functions.php:182
msgid "Light Blue"
msgstr "Light Blue"

#: functions.php:177
msgid "Bright Blue"
msgstr "Bright Blue"

#: functions.php:172
msgid "Blue Gray"
msgstr "Blue Grey"

#: functions.php:167
msgid "White"
msgstr "White"

#: functions.php:162
msgid "Light Gray"
msgstr "Light Grey"

#: functions.php:157
msgid "Medium Gray"
msgstr "Medium Grey"

#: functions.php:152
msgid "Dark Gray"
msgstr "Dark Grey"

#. translators: %s: Name of current post
#: template-parts/content.php:29 inc/template-tags.php:194
msgid "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"

#. translators: %s: Name of current post
#: template-parts/content.php:53 template-parts/content-page.php:39
#: template-parts/content-search.php:28 template-parts/content-search.php:43
#: template-parts/content-single.php:47 image.php:88
msgid "Edit<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "Edit<span class=\"screen-reader-text\"> \"%s\"</span>"

#: inc/customizer.php:345
msgid "Red"
msgstr "Red"

#. translators: %s: post title
#: comments.php:31
msgctxt "comments title"
msgid "One thought on &ldquo;%s&rdquo;"
msgstr "One thought on &ldquo;%s&rdquo;"

#: searchform.php:16
msgctxt "submit button"
msgid "Search"
msgstr "Search"

#: searchform.php:14
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr "Search &hellip;"

#: searchform.php:13
msgctxt "label"
msgid "Search for:"
msgstr "Search for:"

#: footer.php:30
msgid "Footer Social Links Menu"
msgstr "Footer social links menu"

#: footer.php:17
msgid "Footer Primary Menu"
msgstr "Footer primary menu"

#: functions.php:267
msgid "Add widgets here to appear in your sidebar."
msgstr "Add widgets here to appear in your sidebar."

#: template-parts/content.php:14
msgid "Featured"
msgstr "Featured"

#: template-parts/content-none.php:28
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."

#: template-parts/content-none.php:23
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Sorry, but nothing matched your search terms. Please try again with some different keywords."

#: template-parts/content-none.php:19
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."

#: template-parts/content-none.php:13
msgid "Nothing Found"
msgstr "404 Nothing Found"

#: template-parts/biography.php:33
msgid "View all posts by %s"
msgstr "View all posts by %s"

#: template-parts/biography.php:28
msgid "Author:"
msgstr "Author:"

#: single.php:42
msgid "Previous post:"
msgstr "Previous post:"

#: single.php:41
msgid "Previous"
msgstr "Previous"

#: single.php:39
msgid "Next post:"
msgstr "Next post:"

#: single.php:38
msgid "Next"
msgstr "Next"

#: search.php:18
msgid "Search Results for: %s"
msgstr "Search results for: %s"

#: inc/template-tags.php:112
msgctxt "Used before tag names."
msgid "Tags"
msgstr "Tags"

#: inc/template-tags.php:103
msgctxt "Used before category names."
msgid "Categories"
msgstr "Categories"

#: inc/template-tags.php:99 inc/template-tags.php:108
msgctxt "Used between list items, there is a space after the comma."
msgid ", "
msgstr ", "

#: inc/template-tags.php:83
msgctxt "Used before publish date."
msgid "Posted on"
msgstr "Posted on"

#: inc/template-tags.php:40
msgctxt "Used before post format."
msgid "Format"
msgstr "Format"

#: inc/template-tags.php:52
msgid "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"

#: inc/template-tags.php:26
msgctxt "Used before post author name."
msgid "Author"
msgstr "Author"

#: functions.php:207 inc/customizer.php:355
msgid "Yellow"
msgstr "Yellow"

#: inc/customizer.php:335
msgid "Gray"
msgstr "Grey"

#: inc/customizer.php:325
msgid "Dark"
msgstr "Dark"

#: inc/customizer.php:221
msgid "Main Text Color"
msgstr "Main text colour"

#: inc/customizer.php:315
msgid "Default"
msgstr "Default"

#: inc/customizer.php:242
msgid "Secondary Text Color"
msgstr "Secondary text colour"

#: inc/customizer.php:200
msgid "Link Color"
msgstr "Link colour"

#: inc/customizer.php:176
msgid "Page Background Color"
msgstr "Page background colour"

#: single.php:31 image.php:107
msgctxt "Parent post link"
msgid "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%title</span>"
msgstr "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%title</span>"

#: inc/back-compat.php:41 inc/back-compat.php:54 inc/back-compat.php:72
msgid "Twenty Sixteen requires at least WordPress version 4.4. You are running version %s. Please upgrade and try again."
msgstr "Twenty Sixteen requires at least WordPress version 4.4. You are running version %s. Please upgrade and try again."

#: inc/customizer.php:153
msgid "Base Color Scheme"
msgstr "Base Colour Scheme"

#: image.php:77
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr "Full size"

#: template-parts/content.php:36 template-parts/content-page.php:24
#: template-parts/content-single.php:26 image.php:58
msgid "Pages:"
msgstr "Pages:"

#: image.php:26
msgid "Next Image"
msgstr "Next image"

#: image.php:25
msgid "Previous Image"
msgstr "Previous image"

#: header.php:50
msgid "Menu"
msgstr "Menu"

#: header.php:28
msgid "Skip to content"
msgstr "Skip to content"

#: functions.php:407
msgid "expand child menu"
msgstr "expand child menu"

#: functions.php:408
msgid "collapse child menu"
msgstr "collapse child menu"

#. translators: If there are characters in your language that are not supported
#. by Inconsolata, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:327
msgctxt "Inconsolata font: on or off"
msgid "on"
msgstr "on"

#: functions.php:277
msgid "Content Bottom 1"
msgstr "Content bottom 1"

#: functions.php:279 functions.php:291
msgid "Appears at the bottom of the content on posts and pages."
msgstr "Appears at the bottom of the content on posts and pages."

#: functions.php:289
msgid "Content Bottom 2"
msgstr "Content bottom 2"

#. translators: If there are characters in your language that are not supported
#. by Merriweather, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:317
msgctxt "Merriweather font: on or off"
msgid "on"
msgstr "on"

#. translators: If there are characters in your language that are not supported
#. by Montserrat, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:322
msgctxt "Montserrat font: on or off"
msgid "on"
msgstr "on"

#: functions.php:265
msgid "Sidebar"
msgstr "Sidebar"

#: functions.php:93 header.php:67
msgid "Social Links Menu"
msgstr "Social links menu"

#: functions.php:92 header.php:54
msgid "Primary Menu"
msgstr "Primary menu"

#: footer.php:61
msgid "Proudly powered by %s"
msgstr "Proudly powered by %s"

#: comments.php:71
msgid "Comments are closed."
msgstr "Comments are closed."

#. translators: 1: number of comments, 2: post title
#: comments.php:35
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] "%1$s thought on &ldquo;%2$s&rdquo;"
msgstr[1] "%1$s thoughts on &ldquo;%2$s&rdquo;"

#: archive.php:53 template-parts/content.php:40
#: template-parts/content-page.php:28 template-parts/content-single.php:30
#: search.php:41 index.php:50 image.php:62
msgid "Page"
msgstr "Page"

#: archive.php:52 search.php:40 index.php:49
msgid "Next page"
msgstr "Next page"

#: archive.php:51 search.php:39 index.php:48
msgid "Previous page"
msgstr "Previous page"

#: 404.php:21
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "It looks like nothing was found at this location. Maybe try a search?"

#: 404.php:17
msgid "Oops! That page can&rsquo;t be found."
msgstr "Oops! That page can&rsquo;t be found."

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentysixteen/"
msgstr "https://wordpress.org/themes/twentysixteen/"

#. Author of the theme
msgid "the WordPress team"
msgstr "The WordPress Team"

#. Author URI of the theme
#: footer.php:60
msgid "https://wordpress.org/"
msgstr "https://en-gb.wordpress.org/"