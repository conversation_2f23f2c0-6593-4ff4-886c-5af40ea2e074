# Translation of Plugins - Better Search Replace - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - Better Search Replace - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-01-11 17:42:45+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - Better Search Replace - Stable (latest release)\n"

#. Plugin URI of the plugin
#. Author URI of the plugin
#: better-search-replace.php
msgid "https://bettersearchreplace.com"
msgstr "https://bettersearchreplace.com"

#: includes/class-bsr-ajax.php:159
msgid "Processing table %d of %d: %s"
msgstr "正在处理第 %d 张数据表 (总%d 张数据表): %s"

#: includes/class-bsr-admin.php:75
msgid "Processing..."
msgstr "正在处理..."

#. Description of the plugin
#: better-search-replace.php
msgid "A small plugin for running a search/replace on your WordPress database."
msgstr "一个小插件，用于在WordPress数据库上运行搜索/替换。"

#: templates/bsr-settings.php:39
msgid "Max Page Size"
msgstr "最大页面大小"

#: templates/bsr-search-replace.php:118
msgid "Run Search/Replace"
msgstr "运行搜索/替换"

#: templates/bsr-search-replace.php:107
msgid "If checked, no changes will be made to the database, allowing you to check the results beforehand."
msgstr "如果选中，则不会对数据库进行任何更改，从而允许您事先检查结果。"

#: templates/bsr-search-replace.php:96
msgid "If left unchecked, all database columns titled 'guid' will be skipped."
msgstr "如果未选中，则将跳过所有标题为 'guid' 的数据库列。"

#: templates/bsr-search-replace.php:85
msgid "Searches are case-sensitive by default."
msgstr "默认情况下，搜索区分大小写。"

#: templates/bsr-search-replace.php:61
msgid "Select multiple tables with Ctrl-Click for Windows or Cmd-Click for Mac."
msgstr "对于Windows，请按住Ctrl键单击，而对于Mac，请按Cmd单击键选择多个表。"

#: templates/bsr-search-replace.php:59
msgid "Select tables"
msgstr "选择数据表"

#: templates/bsr-search-replace.php:51
msgid "Replace with"
msgstr "替换为"

#: templates/bsr-search-replace.php:46
msgid "Search for"
msgstr "搜索"

#: templates/bsr-help.php:51
msgid "Found a bug or have a feature request? Please submit an issue on <a href=\"%s\">GitHub</a>!"
msgstr "发现错误或有功能要求？ 请在<a href=\"%s\"> GitHub </a>上提交问题！"

#: templates/bsr-help.php:35
msgid "Free support is available on the <a href=\"%s\">plugin support forums</a>."
msgstr "<a href=\"%s\">插件支持论坛</a>上提供免费支持。"

#: templates/bsr-help.php:26
msgid "Help & Troubleshooting"
msgstr "帮助&疑难解答"

#: templates/bsr-dashboard.php:67
msgid "Help"
msgstr "帮助"

#: templates/bsr-dashboard.php:66 templates/bsr-settings.php:30
msgid "Settings"
msgstr "设置"

#: templates/bsr-dashboard.php:65 templates/bsr-search-replace.php:32
msgid "Search/Replace"
msgstr "搜索 / 替换"

#: includes/class-bsr-db.php:295
msgid "Error updating row: %d."
msgstr "更新行时出错： %d。"

#: includes/class-bsr-db.php:85
msgid "(%s MB)"
msgstr "(%s MB)"

#: includes/class-bsr-admin.php:303
msgid "Upgrade to Pro"
msgstr "升级成Pro版"

#: includes/class-bsr-admin.php:234
msgid "Time"
msgstr "时间"

#: includes/class-bsr-admin.php:234
msgid "Rows Updated"
msgstr "行已更新"

#: includes/class-bsr-admin.php:234
msgid "Changes Found"
msgstr "变更数量"

#: includes/class-bsr-admin.php:234
msgid "Table"
msgstr "表"

#. Plugin Name of the plugin
#: better-search-replace.php includes/class-bsr-admin.php:87
msgid "Better Search Replace"
msgstr "Better Search Replace"

#: includes/class-bsr-admin.php:74
msgid "An error occurred processing your request. Try decreasing the \"Max Page Size\", or contact support."
msgstr "处理请求时出错。尝试减少“最大页面大小”，或联系支持。"

#: includes/class-bsr-admin.php:73
msgid "Please select the tables that you want to update."
msgstr "请选择要更新的数据表。"

#: includes/class-bsr-admin.php:72
msgid "No search string was defined, please enter a URL or string to search for."
msgstr "未定义搜索字符串，请输入要搜索的URL或字符串。"