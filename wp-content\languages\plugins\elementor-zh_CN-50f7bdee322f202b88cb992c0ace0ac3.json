{"translation-revision-date": "2025-06-09 04:34:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "zh_CN"}, "Get total control, consistency and a faster workflow by designing the recurring parts that make up a complete website like the Header & Footer, Archive, 404, WooCommerce pages and more.": ["通过设计构成完整网站的重复部分（例如页眉和页脚、存档、404、WooCommerce 页面等），获得完全控制、一致性和更快的工作流程。"], "Customize every part of your site": ["自定义网站的每个部分"], "Upgrade Now": ["立即升级"], "Upgrade": ["升级"], "search results page": ["搜索结果页面"], "This file type is not allowed": ["不允许此文件类型"], "404 page": ["404 页面"], "First, enable unfiltered file uploads.": ["首先，启用未过滤的文件上传。"], "Archive": ["归档"], "Single Post": ["文章详情页"], "Single Page": ["页面详情页"], "Footer": ["页脚"], "Header": ["页眉"], "Error:": ["错误："], "Products Archive": ["产品归档"], "What is a 404 Page Template?": ["什么是 404 页面模板？"], "What is a Products Archive Template?": ["什么是产品存档模板？"], "What is a Single Product Template?": ["什么是单个产品模板？"], "What is an Archive Template?": ["什么是归档模板？"], "What is a Footer Template?": ["什么是页脚模板？"], "What is a Single Post Template?": ["什么是单个文章模板？"], "What is a Single Page Template?": ["什么是单个页面模板？"], "What is a Header Template?": ["什么是页眉模板？"], "App could not be loaded": ["无法加载应用"], "Keep your site's visitors happy when they get lost by displaying your recent posts, a search bar, or any information that might help the user find what they were looking for.": ["通过显示您最近的文章、搜索栏或任何可能有助于用户查找的内容的信息，让网站访问者在迷路时保持快乐。"], "A 404 page template allows you to easily design the layout and style of the page that is displayed when a visitor arrives at a page that does not exist.": ["404 页模板允许您轻松设计当访问者到达不存在的页面时显示的页面的布局和样式。"], "Product": ["商品"], "You can customize the message if there are no results for the search term.": ["如果搜索词没有结果，则可以自定义消息。"], "You can easily control the layout and design of the Search Results page with the Search Results template, which is simply a special archive template just for displaying search results.": ["您可以使用\"搜索结果\"模板轻松控制搜索结果页面的布局和设计，该模板只是用于显示搜索结果的特殊归档模板。"], "What is a Search Results Template?": ["什么是搜索结果模板？"], "You can create multiple archive product templates, and assign each to different categories of products. This gives you the freedom to customize the appearance for each type of product being shown.": ["您可以创建多个全局归档产品模板，并将每个模板分配给不同类别的产品。这样，您就可以自由地为所显示的每种类型的产品自定义外观。"], "A products archive template allows you to easily design the layout and style of your WooCommerce shop page or other product archive pages - those pages that show a list of products, which may be filtered by terms such as categories, tags, etc.": ["全局归档产品模板允许您轻松设计 WooCommerce 商店页面或其他产品归档页面的布局和样式 - 显示产品列表的页面，这些页面可能按类别、标签等术语进行筛选。"], "You can create multiple single product templates, and assign each to different types of products, enabling a custom design for each group of similar products.": ["您可以创建多个全局产品模板，并将每个模板分配给不同类型的产品，从而为每组类似产品启用自定义设计。"], "A single product template allows you to easily design the layout and style of WooCommerce single product pages, and apply that template to various conditions that you assign.": ["全局产品模板允许您轻松设计 WooCommerce 单个产品页面的布局和样式，并将该模板应用于您分配的各种条件。"], "If you’d like a different style for a specific category, it’s easy to create a separate archive template whose condition is to only display when users are viewing that category’s list of posts.": ["如果您希望为特定类别使用不同的样式，则很容易创建单独的全局归档模板，其条件是仅在用户查看该类别的文章列表时显示该模板。"], "An archive template allows you to easily design the layout and style of archive pages - those pages that show a list of posts (e.g. a blog’s list of recent posts), which may be filtered by terms such as authors, categories, tags, search results, etc.": ["全局归档模板允许您轻松设计归档页面的布局和样式 - 显示文章列表的页面（例如，博客最近的文章列表），这些页面可能由作者、类别、标签、搜索结果等术语进行筛选。"], "You can create multiple footers, and assign each to different areas of your site.": ["您可以创建多个页脚，并将每个页脚分配给站点的不同区域。"], "You can create multiple headers, and assign each to different areas of your site.": ["您可以创建多个页眉，并将每个页眉分配给站点的不同区域。"], "You can create multiple single post templates, and assign each to a different category.": ["您可以创建多个全局发布模板，并将每个模板分配给不同的类别。"], "A single post template allows you to easily design the layout and style of posts, ensuring a design consistency throughout all your blog posts, for example.": ["例如，全局文章模板允许您轻松设计文章的布局和样式，从而确保所有博客文章的设计一致性。"], "You can create multiple single page templates, and assign each to different areas of your site.": ["您可以创建多个全局页面模板，并将每个模板分配给网站的不同区域。"], "A single page template allows you to easily create the layout and style of pages, ensuring design consistency across all the pages of your site.": ["全局页面模板允许您轻松创建页面的布局和样式，确保网站所有页面的设计一致性。"], "The footer template allows you to easily design and edit custom WordPress footers without the limits of your theme’s footer design constraints": ["全局页脚允许您轻松设计和编辑自定义 WordPress 页脚，不受主题页脚设计限制的限制"], "The header template allows you to easily design and edit custom WordPress headers so you are no longer constrained by your theme’s header design limitations.": ["全局标题允许您轻松设计和编辑自定义 WordPress 页眉，以便您不再受主题标题设计限制的限制。"], "Site Parts": ["站点部分"], "We’re sorry, but something went wrong. Click on ‘Learn more’ and follow each of the steps to quickly solve it.": ["很抱歉，出了问题。单击\"了解更多\"，然后按照每个步骤快速解决它。"], "Theme Builder could not be loaded": ["无法加载主题生成器"], "Watch Video": ["观看视频"], "Tip": ["提示"], "All Parts": ["所有部分"], "Not Found": ["未找到"], "Continue": ["继续"], "Theme Builder": ["主题生成器"], "Skip": ["跳过"], "Something went wrong.": ["出了些问题。"], "Add New": ["添加"], "Select File": ["选择文件"], "Enable": ["启用"], "Close": ["关闭"], "Learn More": ["了解更多"], "Info": ["信息"], "Go Back": ["返回"], "Elementor": ["<PERSON><PERSON><PERSON>"], "Loading": ["加载中…"]}}, "comment": {"reference": "assets/js/app-packages.js"}}