# Translation of Themes - Twenty Fifteen in Chinese (China)
# This file is distributed under the same license as the Themes - Twenty Fifteen package.
msgid ""
msgstr ""
"PO-Revision-Date: 2018-12-21 00:21:56+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Twenty Fifteen\n"

#. Description of the theme
msgid "Our 2015 default theme is clean, blog-focused, and designed for clarity. Twenty Fifteen's simple, straightforward typography is readable on a wide variety of screen sizes, and suitable for multiple languages. We designed it using a mobile-first approach, meaning your content takes center-stage, regardless of whether your visitors arrive by smartphone, tablet, laptop, or desktop computer."
msgstr "我们的2015默认主题干净、面向博客、并以明晰为设计中心。Twenty Fifteen简单的排班在多种屏幕尺寸上都清晰易读，并能适应多种语言。我们在设计中使用了移动优先的方式，这意味着不管访问者使用智能手机、平板、膝上型电脑还是台式机，您的内容都将居于最中。"

#. Theme Name of the theme
msgid "Twenty Fifteen"
msgstr "Twenty Fifteen"

#: functions.php:243
msgid "Light Blue"
msgstr "浅蓝色"

#: functions.php:238
msgid "Bright Blue"
msgstr "亮蓝色"

#: functions.php:193
msgid "Light Gray"
msgstr "亮灰色"

#: functions.php:188
msgid "Dark Gray"
msgstr "暗灰色"

#: functions.php:208
msgid "Dark Brown"
msgstr "暗棕色"

#: functions.php:198
msgid "White"
msgstr "白色"

#: functions.php:218
msgid "Light Pink"
msgstr "浅粉色"

#: functions.php:213
msgid "Medium Pink"
msgstr "粉色"

#: functions.php:233
msgid "Blue Gray"
msgstr "蓝灰色"

#: functions.php:223
msgid "Dark Purple"
msgstr "暗紫色"

#. translators: %s: post title
#: comments.php:31
msgctxt "comments title"
msgid "One thought on &ldquo;%s&rdquo;"
msgstr "《%s》上有1条评论"

#. translators: %s: post title
#: inc/template-tags.php:130
msgid "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr "<span class=\"screen-reader-text\">于%s</span>留下评论"

#: single.php:39
msgid "Previous post:"
msgstr "上篇文章："

#: single.php:38
msgid "Previous"
msgstr "上一篇"

#: single.php:36
msgid "Next post:"
msgstr "下篇文章："

#: single.php:35
msgid "Next"
msgstr "下一篇"

#: search.php:18
msgid "Search Results for: %s"
msgstr "%s的搜索结果"

#: inc/template-tags.php:120
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr "原始尺寸"

#: inc/template-tags.php:108
msgctxt "Used before tag names."
msgid "Tags"
msgstr "标签"

#: inc/template-tags.php:99
msgctxt "Used before category names."
msgid "Categories"
msgstr "分类"

#: inc/template-tags.php:95 inc/template-tags.php:104
msgctxt "Used between list items, there is a space after the comma."
msgid ", "
msgstr "、"

#: inc/template-tags.php:89
msgctxt "Used before post author name."
msgid "Author"
msgstr "作者"

#: inc/template-tags.php:79
msgctxt "Used before publish date."
msgid "Posted on"
msgstr "发布于"

#: inc/template-tags.php:56
msgctxt "Used before post format."
msgid "Format"
msgstr "格式"

#: inc/template-tags.php:49
msgid "Featured"
msgstr "特色"

#: inc/template-tags.php:30
msgid "Newer Comments"
msgstr "较新评论"

#: inc/template-tags.php:26
msgid "Older Comments"
msgstr "较早评论"

#: inc/template-tags.php:23
msgid "Comment navigation"
msgstr "评论导航"

#: inc/customizer.php:237
msgid "Blue"
msgstr "蓝色"

#: inc/customizer.php:226 functions.php:228
msgid "Purple"
msgstr "紫色"

#: inc/customizer.php:215
msgid "Pink"
msgstr "粉色"

#: inc/customizer.php:204 functions.php:203
msgid "Yellow"
msgstr "黄色"

#: inc/customizer.php:193
msgid "Dark"
msgstr "暗色"

#: inc/customizer.php:182
msgid "Default"
msgstr "默认"

#: inc/customizer.php:103
msgid "Header and Sidebar Background Color"
msgstr "顶部和侧边栏背景颜色"

#: inc/customizer.php:79 inc/customizer.php:104 inc/customizer.php:111
msgid "Applied to the header on small screens and the sidebar on wide screens."
msgstr "在较小的屏幕上将应用到顶部，在较宽的屏幕上将应用到侧边栏。"

#: inc/customizer.php:78
msgid "Header and Sidebar Text Color"
msgstr "顶部和侧边栏文字颜色"

#: inc/customizer.php:55
msgid "Base Color Scheme"
msgstr "基础配色方案"

#: inc/back-compat.php:37 inc/back-compat.php:48 inc/back-compat.php:64
msgid "Twenty Fifteen requires at least WordPress version 4.1. You are running version %s. Please upgrade and try again."
msgstr "Twenty Fifteen要求WordPress 4.1版或以上，您在运行%s版。请升级并重试。"

#: image.php:88
msgctxt "Parent post link"
msgid "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%title</span>"
msgstr "<span class=\"meta-nav\">发布于</span><span class=\"post-title\">%title</span>"

#: image.php:25
msgid "Next Image"
msgstr "下一图片"

#: image.php:25
msgid "Previous Image"
msgstr "上一图片"

#: header.php:50
msgid "Menu and widgets"
msgstr "菜单和挂件"

#: header.php:27
msgid "Skip to content"
msgstr "跳至内容"

#: functions.php:399
msgid "collapse child menu"
msgstr "折叠子菜单"

#: functions.php:398
msgid "expand child menu"
msgstr "展开子菜单"

#: functions.php:319
msgctxt "Add new subset (greek, cyrillic, devanagari, vietnamese)"
msgid "no-subset"
msgstr "no-subset"

#: functions.php:311
msgctxt "Inconsolata font: on or off"
msgid "on"
msgstr "on"

#: functions.php:303
msgctxt "Noto Serif font: on or off"
msgid "on"
msgstr "on"

#: functions.php:295
msgctxt "Noto Sans font: on or off"
msgid "on"
msgstr "on"

#: functions.php:268
msgid "Add widgets here to appear in your sidebar."
msgstr "将挂件加入此处来在侧边栏中显示。"

#: functions.php:266
msgid "Widget Area"
msgstr "挂件区域"

#: functions.php:87
msgid "Social Links Menu"
msgstr "社交网络链接菜单"

#: functions.php:86
msgid "Primary Menu"
msgstr "主菜单"

#: footer.php:31
msgid "Proudly powered by %s"
msgstr "自豪地采用%s"

#: content-none.php:31
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "我们可能无法找到您需要的内容。或许搜索功能可以帮到您。"

#: content-none.php:26
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "抱歉，没有符合您搜索条件的结果。请换其它关键词再试。"

#: content-none.php:22
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "准备好发布第一篇文章了？<a href=\"%1$s\">从这里开始</a>。"

#: content-none.php:15
msgid "Nothing Found"
msgstr "未找到"

#: content-link.php:60 content-page.php:37 image.php:74 content-search.php:28
#: content-search.php:33 content.php:61
msgid "Edit"
msgstr "编辑"

#: content-link.php:39 content-page.php:26 image.php:61 content.php:41
msgid "Pages:"
msgstr "页码："

#. translators: %s: Name of current post
#: inc/template-tags.php:249 content-link.php:32 content.php:34
msgid "Continue reading %s"
msgstr "继续阅读%s"

#: comments.php:71
msgid "Comments are closed."
msgstr "评论已关闭。"

#. translators: 1: number of comments, 2: post title
#: comments.php:35
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] "《%2$s》上有%1$s条评论"

#: author-bio.php:34
msgid "View all posts by %s"
msgstr "查看%s的所有文章"

#: author-bio.php:12
msgid "Published by"
msgstr "发布者"

#: archive.php:53 content-link.php:43 index.php:50 content-page.php:30
#: image.php:65 content.php:45 search.php:43
msgid "Page"
msgstr "页"

#: archive.php:52 index.php:49 search.php:42
msgid "Next page"
msgstr "下一页"

#: archive.php:51 index.php:48 search.php:41
msgid "Previous page"
msgstr "上一页"

#: 404.php:21
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "这儿似乎什么都没有，试试搜索？"

#: 404.php:17
msgid "Oops! That page can&rsquo;t be found."
msgstr "有点尴尬诶！该页无法显示。"

#. Author of the theme
msgid "the WordPress team"
msgstr "WordPress团队"

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentyfifteen/"
msgstr "https://wordpress.org/themes/twentyfifteen/"

#. Author URI of the theme
#: footer.php:30
msgid "https://wordpress.org/"
msgstr "https://cn.wordpress.org/"